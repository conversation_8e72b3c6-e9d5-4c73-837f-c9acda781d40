package io.sx.model.customfields;

import io.sx.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomFieldDefinition extends BaseModel {
    private String name;
    private String label;
    private String description;
    private String fieldType; // text, number, date, dropdown, checkbox, multi_select
    private String dataType; // string, integer, decimal, boolean, date, timestamp
    private Boolean isRequired;
    private Boolean isUnique;
    private String defaultValue;
    private String placeholder;
    private String helpText;
    private Integer displayOrder;
    private Long groupId;
    private Integer version;
    private Boolean isActive;
    private Boolean isTemplate;
    private Long templateId;
    private Map<String, Object> validationRules;
    private Map<String, Object> fieldOptions;
} 