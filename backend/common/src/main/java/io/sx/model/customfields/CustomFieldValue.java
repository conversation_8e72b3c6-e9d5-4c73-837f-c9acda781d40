package io.sx.model.customfields;

import io.sx.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomFieldValue extends BaseModel {
    private Long fieldDefinitionId;
    private Long ticketId;
    private String valueText;
    private BigDecimal valueNumber;
    private Boolean valueBoolean;
    private LocalDate valueDate;
    private LocalDateTime valueTimestamp;
    private Map<String, Object> valueJson; // for complex data like multi-select, file references, etc.
} 