package io.sx.model.customfields;

import io.sx.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomFieldCondition extends BaseModel {
    private Long fieldDefinitionId;
    private String conditionType; // show, hide
    private String logicOperator; // AND, OR
    private List<Map<String, Object>> conditions; // array of condition objects
} 