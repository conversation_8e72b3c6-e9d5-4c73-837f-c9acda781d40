package io.sx.model.customfields;

/**
 * Enum for custom field UI types
 */
public enum CustomFieldType {
    TEXT("text"),
    NUMBER("number"),
    DATE("date"),
    DROPDOWN("dropdown"),
    CHECKBOX("checkbox"),
    MULTI_SELECT("multi_select");

    private final String value;

    CustomFieldType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static CustomFieldType fromValue(String value) {
        for (CustomFieldType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown field type: " + value);
    }
} 