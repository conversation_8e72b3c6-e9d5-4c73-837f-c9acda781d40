package io.sx.model.customfields;

/**
 * Enum for custom field data storage types
 */
public enum CustomFieldDataType {
    STRING("string"),
    INTEGER("integer"),
    DECIMAL("decimal"),
    BOOLEAN("boolean"),
    DATE("date"),
    TIMESTAMP("timestamp");

    private final String value;

    CustomFieldDataType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static CustomFieldDataType fromValue(String value) {
        for (CustomFieldDataType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown data type: " + value);
    }
} 