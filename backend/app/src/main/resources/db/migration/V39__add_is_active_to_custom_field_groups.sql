-- Add is_active column to custom_field_groups table
-- This allows groups to be activated/deactivated similar to field definitions

ALTER TABLE custom_field_groups 
ADD COLUMN is_active BOOLEAN DEFAULT true;

-- Add index for better query performance
CREATE INDEX idx_custom_field_groups_active ON custom_field_groups(is_active);

-- Update existing groups to be active by default
UPDATE custom_field_groups SET is_active = true WHERE is_active IS NULL; 