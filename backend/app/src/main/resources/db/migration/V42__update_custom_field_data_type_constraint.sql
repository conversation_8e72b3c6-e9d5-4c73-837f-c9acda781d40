-- Update custom field data type constraint to accept uppercase values
-- This aligns the database constraint with the Java enum validation

-- Drop the existing constraint
ALTER TABLE custom_field_definitions DROP CONSTRAINT IF EXISTS chk_custom_field_definitions_data_type;

-- Add the new constraint that accepts uppercase values (matching enum names)
ALTER TABLE custom_field_definitions ADD CONSTRAINT chk_custom_field_definitions_data_type 
CHECK (data_type IN ('STRING', 'INTEGER', 'DECIMAL', 'BOOLEAN', 'DATE', 'TIMESTAMP'));

-- Update any existing lowercase values to uppercase to maintain consistency
UPDATE custom_field_definitions SET data_type = UPPER(data_type) WHERE data_type IS NOT NULL; 