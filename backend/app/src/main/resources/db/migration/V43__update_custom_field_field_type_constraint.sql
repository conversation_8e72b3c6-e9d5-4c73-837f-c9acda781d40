-- Update custom field field_type constraint to accept uppercase values
-- This aligns the database constraint with the Java enum validation

-- Drop the existing constraint
ALTER TABLE custom_field_definitions DROP CONSTRAINT IF EXISTS chk_custom_field_definitions_field_type;

-- Add the new constraint that accepts uppercase values (matching enum names)
ALTER TABLE custom_field_definitions ADD CONSTRAINT chk_custom_field_definitions_field_type 
CHECK (field_type IN ('TEXT', 'NUMBER', 'DATE', 'DROPDOWN', 'CHECKBOX', 'MULTI_SELECT'));

-- Update any existing lowercase values to uppercase to maintain consistency
UPDATE custom_field_definitions SET field_type = UPPER(field_type) WHERE field_type IS NOT NULL;
