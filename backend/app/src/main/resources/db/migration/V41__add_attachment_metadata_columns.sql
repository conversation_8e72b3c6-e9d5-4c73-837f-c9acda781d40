-- Add enhanced metadata columns to attachments table
ALTER TABLE attachments 
ADD COLUMN original_filename VA<PERSON>HA<PERSON>(255),
ADD COLUMN content_type VARCHAR(100),
ADD COLUMN file_size BIGINT,
ADD COLUMN file_extension VARCHAR(20),
ADD COLUMN file_type_category VARCHAR(50);

-- Add comments for the new columns
COMMENT ON COLUMN attachments.original_filename IS 'Original filename as uploaded by user';
COMMENT ON COLUMN attachments.content_type IS 'MIME type of the file';
COMMENT ON COLUMN attachments.file_size IS 'File size in bytes';
COMMENT ON COLUMN attachments.file_extension IS 'File extension (e.g., pdf, jpg, docx)';
COMMENT ON COLUMN attachments.file_type_category IS 'File type category for UI icons (e.g., image, document, video)';

-- Create index on file_type_category for filtering
CREATE INDEX idx_attachments_file_type_category ON attachments(file_type_category);

-- Create index on file_extension for filtering
CREATE INDEX idx_attachments_file_extension ON attachments(file_extension);

-- Grant necessary permissions to debezium user for new columns
GRANT SELECT, INSERT, UPDATE, DELETE ON attachments TO debezium;
