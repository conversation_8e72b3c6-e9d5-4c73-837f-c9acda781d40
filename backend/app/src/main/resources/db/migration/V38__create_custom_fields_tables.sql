-- Custom Fields Feature - Database Schema
-- This migration creates the foundation for the custom fields feature
-- Supports multiple data types, validations, conditional logic, and organizational templates

-- 1. Custom Field Groups Table
-- Groups fields together for better organization and UI presentation
CREATE TABLE custom_field_groups (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    display_order INTEGER NOT NULL DEFAULT 0,
    is_collapsible BOOLEAN DEFAULT false,
    is_collapsed_by_default BOOLEAN DEFAULT false,
    organization_id BIGINT NOT NULL,
    support_organization_id BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT,
    updated_by_id BIGINT,
    created_by <PERSON><PERSON>HA<PERSON>(255),
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    on_behalf_of_id BIGINT,
    CONSTRAINT fk_custom_field_groups_organization FOREIGN KEY (organization_id) REFERENCES organization(id),
    CONSTRAINT fk_custom_field_groups_support_org FOREIGN KEY (support_organization_id) REFERENCES organization(id),
    CONSTRAINT fk_custom_field_groups_created_by FOREIGN KEY (created_by_id) REFERENCES users(id),
    CONSTRAINT fk_custom_field_groups_updated_by FOREIGN KEY (updated_by_id) REFERENCES users(id),
    CONSTRAINT fk_custom_field_groups_on_behalf_of FOREIGN KEY (on_behalf_of_id) REFERENCES users(id)
);

-- 2. Custom Field Definitions Table
-- Defines the structure and configuration of custom fields
CREATE TABLE custom_field_definitions (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    label VARCHAR(255) NOT NULL,
    description TEXT,
    field_type VARCHAR(50) NOT NULL, -- text, number, date, dropdown, checkbox, multi_select
    data_type VARCHAR(50) NOT NULL, -- string, integer, decimal, boolean, date, timestamp
    is_required BOOLEAN DEFAULT false,
    is_unique BOOLEAN DEFAULT false,
    default_value TEXT,
    placeholder TEXT,
    help_text TEXT,
    display_order INTEGER NOT NULL DEFAULT 0,
    group_id BIGINT,
    organization_id BIGINT NOT NULL,
    support_organization_id BIGINT,
    version INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    is_template BOOLEAN DEFAULT false, -- true if this is a template created by SX admin
    template_id BIGINT, -- reference to original template if copied
    validation_rules JSONB, -- stores validation configuration
    field_options JSONB, -- stores dropdown options, min/max values, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT,
    updated_by_id BIGINT,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    on_behalf_of_id BIGINT,
    CONSTRAINT fk_custom_field_definitions_group FOREIGN KEY (group_id) REFERENCES custom_field_groups(id),
    CONSTRAINT fk_custom_field_definitions_organization FOREIGN KEY (organization_id) REFERENCES organization(id),
    CONSTRAINT fk_custom_field_definitions_support_org FOREIGN KEY (support_organization_id) REFERENCES organization(id),
    CONSTRAINT fk_custom_field_definitions_template FOREIGN KEY (template_id) REFERENCES custom_field_definitions(id),
    CONSTRAINT fk_custom_field_definitions_created_by FOREIGN KEY (created_by_id) REFERENCES users(id),
    CONSTRAINT fk_custom_field_definitions_updated_by FOREIGN KEY (updated_by_id) REFERENCES users(id),
    CONSTRAINT fk_custom_field_definitions_on_behalf_of FOREIGN KEY (on_behalf_of_id) REFERENCES users(id),
    CONSTRAINT chk_custom_field_definitions_field_type CHECK (field_type IN ('text', 'number', 'date', 'dropdown', 'checkbox', 'multi_select')),
    CONSTRAINT chk_custom_field_definitions_data_type CHECK (data_type IN ('string', 'integer', 'decimal', 'boolean', 'date', 'timestamp'))
);

-- 3. Custom Field Conditions Table
-- Defines conditional logic for showing/hiding fields based on other field values
CREATE TABLE custom_field_conditions (
    id BIGSERIAL PRIMARY KEY,
    field_definition_id BIGINT NOT NULL,
    condition_type VARCHAR(20) NOT NULL, -- show, hide
    logic_operator VARCHAR(10) NOT NULL, -- AND, OR
    conditions JSONB NOT NULL, -- array of condition objects
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT,
    updated_by_id BIGINT,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    on_behalf_of_id BIGINT,
    CONSTRAINT fk_custom_field_conditions_field_definition FOREIGN KEY (field_definition_id) REFERENCES custom_field_definitions(id),
    CONSTRAINT fk_custom_field_conditions_created_by FOREIGN KEY (created_by_id) REFERENCES users(id),
    CONSTRAINT fk_custom_field_conditions_updated_by FOREIGN KEY (updated_by_id) REFERENCES users(id),
    CONSTRAINT fk_custom_field_conditions_on_behalf_of FOREIGN KEY (on_behalf_of_id) REFERENCES users(id),
    CONSTRAINT chk_custom_field_conditions_condition_type CHECK (condition_type IN ('show', 'hide')),
    CONSTRAINT chk_custom_field_conditions_logic_operator CHECK (logic_operator IN ('AND', 'OR'))
);

-- 4. Custom Field Values Table
-- Stores the actual values for custom fields on tickets
CREATE TABLE custom_field_values (
    id BIGSERIAL PRIMARY KEY,
    field_definition_id BIGINT NOT NULL,
    ticket_id BIGINT NOT NULL,
    value_text TEXT,
    value_number DECIMAL(20, 6),
    value_boolean BOOLEAN,
    value_date DATE,
    value_timestamp TIMESTAMP WITH TIME ZONE,
    value_json JSONB, -- for complex data like multi-select, file references, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT,
    updated_by_id BIGINT,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    on_behalf_of_id BIGINT,
    CONSTRAINT fk_custom_field_values_field_definition FOREIGN KEY (field_definition_id) REFERENCES custom_field_definitions(id),
    CONSTRAINT fk_custom_field_values_ticket FOREIGN KEY (ticket_id) REFERENCES ticket(id),
    CONSTRAINT fk_custom_field_values_created_by FOREIGN KEY (created_by_id) REFERENCES users(id),
    CONSTRAINT fk_custom_field_values_updated_by FOREIGN KEY (updated_by_id) REFERENCES users(id),
    CONSTRAINT fk_custom_field_values_on_behalf_of FOREIGN KEY (on_behalf_of_id) REFERENCES users(id),
    CONSTRAINT uk_custom_field_values_unique UNIQUE (field_definition_id, ticket_id)
);

-- Create indexes for better query performance
CREATE INDEX idx_custom_field_groups_organization ON custom_field_groups(organization_id);
CREATE INDEX idx_custom_field_groups_support_org ON custom_field_groups(support_organization_id);
CREATE INDEX idx_custom_field_groups_display_order ON custom_field_groups(display_order);

CREATE INDEX idx_custom_field_definitions_organization ON custom_field_definitions(organization_id);
CREATE INDEX idx_custom_field_definitions_support_org ON custom_field_definitions(support_organization_id);
CREATE INDEX idx_custom_field_definitions_group ON custom_field_definitions(group_id);
CREATE INDEX idx_custom_field_definitions_template ON custom_field_definitions(template_id);
CREATE INDEX idx_custom_field_definitions_active ON custom_field_definitions(is_active);
CREATE INDEX idx_custom_field_definitions_display_order ON custom_field_definitions(display_order);
CREATE INDEX idx_custom_field_definitions_field_type ON custom_field_definitions(field_type);

CREATE INDEX idx_custom_field_conditions_field_definition ON custom_field_conditions(field_definition_id);

CREATE INDEX idx_custom_field_values_field_definition ON custom_field_values(field_definition_id);
CREATE INDEX idx_custom_field_values_ticket ON custom_field_values(ticket_id);
CREATE INDEX idx_custom_field_values_value_text ON custom_field_values(value_text);
CREATE INDEX idx_custom_field_values_value_number ON custom_field_values(value_number);
CREATE INDEX idx_custom_field_values_value_boolean ON custom_field_values(value_boolean);
CREATE INDEX idx_custom_field_values_value_date ON custom_field_values(value_date);

-- Add comments to document the tables
COMMENT ON TABLE custom_field_groups IS 'Groups custom fields together for better organization and UI presentation';
COMMENT ON TABLE custom_field_definitions IS 'Defines the structure and configuration of custom fields with validation rules and field options';
COMMENT ON TABLE custom_field_conditions IS 'Defines conditional logic for showing/hiding fields based on other field values';
COMMENT ON TABLE custom_field_values IS 'Stores the actual values for custom fields on tickets with support for multiple data types';

COMMENT ON COLUMN custom_field_definitions.field_type IS 'UI field type: text, number, date, dropdown, checkbox, multi_select';
COMMENT ON COLUMN custom_field_definitions.data_type IS 'Data storage type: string, integer, decimal, boolean, date, timestamp';
COMMENT ON COLUMN custom_field_definitions.validation_rules IS 'JSON object containing validation rules (required, min/max, regex, etc.)';
COMMENT ON COLUMN custom_field_definitions.field_options IS 'JSON object containing field-specific options (dropdown choices, min/max values, etc.)';
COMMENT ON COLUMN custom_field_definitions.is_template IS 'True if this is a template created by SX admin that can be copied by support orgs';
COMMENT ON COLUMN custom_field_definitions.template_id IS 'Reference to original template if this field was copied from a template';

COMMENT ON COLUMN custom_field_conditions.condition_type IS 'Whether to show or hide the field when conditions are met';
COMMENT ON COLUMN custom_field_conditions.logic_operator IS 'AND/OR logic for combining multiple conditions';
COMMENT ON COLUMN custom_field_conditions.conditions IS 'JSON array of condition objects with field references and operators';

COMMENT ON COLUMN custom_field_values.value_json IS 'JSON object for complex data like multi-select values, file references, etc.';

-- Set replica identity for CDC
ALTER TABLE custom_field_groups REPLICA IDENTITY FULL;
ALTER TABLE custom_field_definitions REPLICA IDENTITY FULL;
ALTER TABLE custom_field_conditions REPLICA IDENTITY FULL;
ALTER TABLE custom_field_values REPLICA IDENTITY FULL;

-- Grant necessary permissions to debezium user
GRANT SELECT, INSERT, UPDATE, DELETE ON custom_field_groups TO debezium;
GRANT SELECT, INSERT, UPDATE, DELETE ON custom_field_definitions TO debezium;
GRANT SELECT, INSERT, UPDATE, DELETE ON custom_field_conditions TO debezium;
GRANT SELECT, INSERT, UPDATE, DELETE ON custom_field_values TO debezium;

GRANT USAGE, SELECT ON SEQUENCE custom_field_groups_id_seq TO debezium;
GRANT USAGE, SELECT ON SEQUENCE custom_field_definitions_id_seq TO debezium;
GRANT USAGE, SELECT ON SEQUENCE custom_field_conditions_id_seq TO debezium;
GRANT USAGE, SELECT ON SEQUENCE custom_field_values_id_seq TO debezium; 