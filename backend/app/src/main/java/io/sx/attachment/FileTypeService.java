package io.sx.attachment;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

/**
 * Service for file type detection, validation, and metadata extraction
 */
@Service
@Slf4j
public class FileTypeService {

    // Allowed file extensions (case-insensitive)
    private static final Set<String> ALLOWED_EXTENSIONS = Set.of(
        // Images
        "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico", "tiff", "tif",
        // Documents
        "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "rtf", "odt", "ods", "odp",
        // Archives
        "zip", "rar", "7z", "tar", "gz", "bz2",
        // Audio
        "mp3", "wav", "flac", "aac", "ogg", "m4a",
        // Video
        "mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v",
        // Code/Text
        "json", "xml", "csv", "log", "md", "yml", "yaml",
        // Other
        "eml", "msg"
    );

    // Blocked executable extensions
    private static final Set<String> BLOCKED_EXTENSIONS = Set.of(
        "exe", "bat", "cmd", "com", "scr", "pif", "vbs", "js", "jar", "app", "deb", "rpm",
        "msi", "dmg", "pkg", "run", "bin", "sh", "ps1", "psm1", "psd1", "ps1xml",
        "cpl", "inf", "reg", "dll", "sys", "drv", "ocx"
    );

    // MIME type mappings for common file types
    private static final Map<String, String> MIME_TYPE_MAP = Map.ofEntries(
        Map.entry("pdf", "application/pdf"),
        Map.entry("jpg", "image/jpeg"),
        Map.entry("jpeg", "image/jpeg"),
        Map.entry("png", "image/png"),
        Map.entry("gif", "image/gif"),
        Map.entry("txt", "text/plain"),
        Map.entry("doc", "application/msword"),
        Map.entry("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
        Map.entry("xls", "application/vnd.ms-excel"),
        Map.entry("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
        Map.entry("zip", "application/zip")
    );

    // File type categories for icon mapping
    private static final Map<String, String> FILE_TYPE_CATEGORIES = Map.ofEntries(
        Map.entry("jpg", "image"), Map.entry("jpeg", "image"), Map.entry("png", "image"), Map.entry("gif", "image"), Map.entry("bmp", "image"), Map.entry("webp", "image"), Map.entry("svg", "image"),
        Map.entry("pdf", "document"), Map.entry("doc", "document"), Map.entry("docx", "document"), Map.entry("txt", "text"), Map.entry("rtf", "document"),
        Map.entry("xls", "spreadsheet"), Map.entry("xlsx", "spreadsheet"), Map.entry("csv", "spreadsheet"),
        Map.entry("ppt", "presentation"), Map.entry("pptx", "presentation"),
        Map.entry("zip", "archive"), Map.entry("rar", "archive"), Map.entry("7z", "archive"), Map.entry("tar", "archive"), Map.entry("gz", "archive"),
        Map.entry("mp3", "audio"), Map.entry("wav", "audio"), Map.entry("flac", "audio"), Map.entry("aac", "audio"), Map.entry("ogg", "audio"),
        Map.entry("mp4", "video"), Map.entry("avi", "video"), Map.entry("mkv", "video"), Map.entry("mov", "video"), Map.entry("wmv", "video"),
        Map.entry("json", "code"), Map.entry("xml", "code"), Map.entry("yml", "code"), Map.entry("yaml", "code"), Map.entry("md", "code")
    );

    /**
     * Validates if a file type is allowed for upload
     */
    public boolean isFileTypeAllowed(String filename, String contentType) {
        if (filename == null || filename.trim().isEmpty()) {
            log.warn("Filename is null or empty");
            return false;
        }

        String extension = getFileExtension(filename).toLowerCase();
        
        // Check if extension is blocked
        if (BLOCKED_EXTENSIONS.contains(extension)) {
            log.warn("File type blocked: {}", extension);
            return false;
        }

        // Check if extension is allowed
        if (!ALLOWED_EXTENSIONS.contains(extension)) {
            log.warn("File type not allowed: {}", extension);
            return false;
        }

        // Additional MIME type validation if available
        if (contentType != null && !contentType.trim().isEmpty()) {
            return isContentTypeAllowed(contentType);
        }

        return true;
    }

    /**
     * Validates if a content type is allowed
     */
    public boolean isContentTypeAllowed(String contentType) {
        if (contentType == null || contentType.trim().isEmpty()) {
            return true; // Allow if no content type specified
        }

        String lowerContentType = contentType.toLowerCase();
        
        // Block executable content types
        if (lowerContentType.contains("application/x-executable") ||
            lowerContentType.contains("application/x-msdownload") ||
            lowerContentType.contains("application/x-msdos-program") ||
            lowerContentType.contains("application/x-winexe")) {
            return false;
        }

        return true;
    }

    /**
     * Extracts file extension from filename
     */
    public String getFileExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1);
        }
        return "";
    }

    /**
     * Gets the file type category for icon mapping
     */
    public String getFileTypeCategory(String filename) {
        String extension = getFileExtension(filename).toLowerCase();
        return FILE_TYPE_CATEGORIES.getOrDefault(extension, "file");
    }

    /**
     * Gets the MIME type for a file extension
     */
    public String getMimeType(String filename) {
        String extension = getFileExtension(filename).toLowerCase();
        return MIME_TYPE_MAP.getOrDefault(extension, "application/octet-stream");
    }

    /**
     * Formats file size in human-readable format
     */
    public String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }

    /**
     * Validates file size against maximum allowed size
     */
    public boolean isFileSizeAllowed(long fileSize, long maxSizeBytes) {
        return fileSize <= maxSizeBytes;
    }

    /**
     * Gets validation error message for a file
     */
    public String getValidationError(String filename, String contentType, long fileSize, long maxSizeBytes) {
        if (!isFileTypeAllowed(filename, contentType)) {
            String extension = getFileExtension(filename);
            if (BLOCKED_EXTENSIONS.contains(extension.toLowerCase())) {
                return "Executable files are not allowed for security reasons";
            }
            return "File type '" + extension + "' is not allowed";
        }
        
        if (!isFileSizeAllowed(fileSize, maxSizeBytes)) {
            return "File size " + formatFileSize(fileSize) + " exceeds maximum allowed size of " + formatFileSize(maxSizeBytes);
        }
        
        return null; // No validation errors
    }

    /**
     * Gets all allowed file extensions
     */
    public Set<String> getAllowedExtensions() {
        return new HashSet<>(ALLOWED_EXTENSIONS);
    }

    /**
     * Gets all blocked file extensions
     */
    public Set<String> getBlockedExtensions() {
        return new HashSet<>(BLOCKED_EXTENSIONS);
    }
}
