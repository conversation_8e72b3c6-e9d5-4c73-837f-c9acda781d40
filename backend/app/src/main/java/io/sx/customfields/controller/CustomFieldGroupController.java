package io.sx.customfields.controller;

import io.sx.customfields.dto.CustomFieldGroupDTO;
import io.sx.customfields.service.CustomFieldGroupService;
import io.sx.context.RequestContext;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Collections;

@RestController
@RequestMapping("/api/custom-fields/groups")
public class CustomFieldGroupController {

    private final CustomFieldGroupService customFieldGroupService;

    @Autowired
    public CustomFieldGroupController(CustomFieldGroupService customFieldGroupService) {
        this.customFieldGroupService = customFieldGroupService;
    }

    @GetMapping
    public ResponseEntity<List<CustomFieldGroupDTO>> getCustomFieldGroups(
            @RequestParam(required = false) Long organizationId) {
        try {
            Long targetOrgId = organizationId != null ? organizationId : RequestContext.getOrgId();
            if (targetOrgId == null) {
                // If no organization ID is provided and no authenticated user, return empty list
                return ResponseEntity.ok(Collections.emptyList());
            }
            List<CustomFieldGroupDTO> groups = customFieldGroupService.findByOrganizationId(targetOrgId);
            return ResponseEntity.ok(groups);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<CustomFieldGroupDTO> getCustomFieldGroup(@PathVariable Long id) {
        try {
            CustomFieldGroupDTO group = customFieldGroupService.findById(id);
            return ResponseEntity.ok(group);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping
    public ResponseEntity<CustomFieldGroupDTO> createCustomFieldGroup(
            @Valid @RequestBody CustomFieldGroupDTO groupDTO) {
        try {
            // Let AuditAspect handle organization context population
            CustomFieldGroupDTO createdGroup = customFieldGroupService.save(groupDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdGroup);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<CustomFieldGroupDTO> updateCustomFieldGroup(
            @PathVariable Long id,
            @Valid @RequestBody CustomFieldGroupDTO groupDTO) {
        try {
            // Verify the group exists and belongs to the authenticated user's organization
            CustomFieldGroupDTO existingGroup = customFieldGroupService.findById(id);
            Long userOrgId = RequestContext.getOrgId();
            
            if (userOrgId == null || !userOrgId.equals(existingGroup.getOrganizationId())) {
                return ResponseEntity.badRequest().build();
            }
            
            // Let AuditAspect handle organization context preservation
            Long updatedGroup = customFieldGroupService.update(id, groupDTO);

            return ResponseEntity.ok(customFieldGroupService.findById(id));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCustomFieldGroup(@PathVariable Long id) {
        try {
            customFieldGroupService.deleteGroup(id);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/reorder")
    public ResponseEntity<Void> reorderCustomFieldGroups(
            @RequestBody List<Long> groupIds) {
        try {
            customFieldGroupService.reorderGroups(groupIds);
            return ResponseEntity.ok().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/active")
    public ResponseEntity<List<CustomFieldGroupDTO>> getActiveCustomFieldGroups(
            @RequestParam(required = false) Long organizationId) {
        try {
            Long targetOrgId = organizationId != null ? organizationId : RequestContext.getOrgId();
            List<CustomFieldGroupDTO> groups = customFieldGroupService.findByOrganizationId(targetOrgId);
            // Filter active groups - CustomFieldGroupDTO doesn't have isActive field, so return all groups
            return ResponseEntity.ok(groups);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
} 