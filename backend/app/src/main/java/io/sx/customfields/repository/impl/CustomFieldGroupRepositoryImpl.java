package io.sx.customfields.repository.impl;

import io.sx.base.rowmapper.BaseRowMapper;
import io.sx.repository.NullHandlingResultSet;
import io.sx.customfields.repository.CustomFieldGroupRepository;
import io.sx.model.customfields.CustomFieldGroup;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class CustomFieldGroupRepositoryImpl implements CustomFieldGroupRepository {

    private final JdbcTemplate jdbcTemplate;
    
    private final RowMapper<CustomFieldGroup> rowMapper = new BaseRowMapper<CustomFieldGroup>() {
        @Override
        protected CustomFieldGroup mapRowInternal(NullHandlingResultSet rs, int rowNum) throws SQLException {
            CustomFieldGroup group = new CustomFieldGroup();
            group.setId(rs.getLong("id"));
            group.setName(rs.getString("name"));
            group.setLabel(rs.getString("label"));
            group.setDescription(rs.getString("description"));
            group.setDisplayOrder(rs.getInt("display_order"));
            group.setIsCollapsible(rs.getBoolean("is_collapsible"));
            group.setIsCollapsedByDefault(rs.getBoolean("is_collapsed_by_default"));
            group.setIsActive(rs.getBoolean("is_active"));
            group.setOrganizationId(rs.getLong("organization_id"));
            group.setSupportOrganizationId(rs.getLong("support_organization_id"));
            group.setCreatedAt(rs.getLocalDateTime("created_at"));
            group.setUpdatedAt(rs.getLocalDateTime("updated_at"));
            group.setCreatedById(rs.getLong("created_by_id"));
            group.setUpdatedById(rs.getLong("updated_by_id"));
            group.setCreatedBy(rs.getString("created_by"));
            group.setUpdatedBy(rs.getString("updated_by"));
            group.setOnBehalfOfId(rs.getLong("on_behalf_of_id"));
            return group;
        }
    };

    @Override
    public List<CustomFieldGroup> findAll() {
        String sql = "SELECT * FROM custom_field_groups ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper);
    }

    @Override
    public Optional<CustomFieldGroup> findById(Long id) {
        String sql = "SELECT * FROM custom_field_groups WHERE id = ?";
        List<CustomFieldGroup> results = jdbcTemplate.query(sql, rowMapper, id);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public List<CustomFieldGroup> findByOrganizationId(Long organizationId) {
        String sql = "SELECT * FROM custom_field_groups WHERE organization_id = ? ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper, organizationId);
    }

    @Override
    public List<CustomFieldGroup> findBySupportOrganizationId(Long supportOrganizationId) {
        String sql = "SELECT * FROM custom_field_groups WHERE support_organization_id = ? ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper, supportOrganizationId);
    }

    @Override
    public List<CustomFieldGroup> findByOrganizationIdOrderByDisplayOrder(Long organizationId) {
        String sql = "SELECT * FROM custom_field_groups WHERE organization_id = ? ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper, organizationId);
    }

    @Override
    public Optional<CustomFieldGroup> findByNameAndOrganizationId(String name, Long organizationId) {
        String sql = "SELECT * FROM custom_field_groups WHERE name = ? AND organization_id = ?";
        List<CustomFieldGroup> results = jdbcTemplate.query(sql, rowMapper, name, organizationId);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public List<CustomFieldGroup> findAll(Long organizationId) {
        return findByOrganizationId(organizationId);
    }

    @Override
    public CustomFieldGroup save(CustomFieldGroup group) {
        if (group.getId() == null) {
            // Insert new group and return with generated ID
            String sql = "INSERT INTO custom_field_groups (name, label, description, display_order, is_collapsible, " +
                    "is_collapsed_by_default, is_active, organization_id, support_organization_id, created_at, updated_at, " +
                    "created_by_id, updated_by_id, created_by, updated_by, on_behalf_of_id) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING *";

            CustomFieldGroup savedGroup = jdbcTemplate.queryForObject(sql, rowMapper,
                group.getName(),
                group.getLabel(),
                group.getDescription(),
                group.getDisplayOrder(),
                group.getIsCollapsible(),
                group.getIsCollapsedByDefault(),
                group.getIsActive(),
                group.getOrganizationId(),
                group.getSupportOrganizationId(),
                group.getCreatedAt(),
                group.getUpdatedAt(),
                group.getCreatedById(),
                group.getUpdatedById(),
                group.getCreatedBy(),
                group.getUpdatedBy(),
                group.getOnBehalfOfId()
            );

            return savedGroup;
        } else {
            // Update existing group
            String sql = "UPDATE custom_field_groups SET name = ?, label = ?, description = ?, display_order = ?, " +
                    "is_collapsible = ?, is_collapsed_by_default = ?, is_active = ?, organization_id = ?, support_organization_id = ?, " +
                    "updated_at = ?, updated_by_id = ?, updated_by = ?, on_behalf_of_id = ? WHERE id = ?";
            
            jdbcTemplate.update(sql,
                group.getName(),
                group.getLabel(),
                group.getDescription(),
                group.getDisplayOrder(),
                group.getIsCollapsible(),
                group.getIsCollapsedByDefault(),
                group.getIsActive(),
                group.getOrganizationId(),
                group.getSupportOrganizationId(),
                group.getUpdatedAt(),
                group.getUpdatedById(),
                group.getUpdatedBy(),
                group.getOnBehalfOfId(),
                group.getId()
            );

            // Fetch and return the updated group
            return findById(group.getId()).orElse(group);
        }
    }

    @Override
    public Long update(CustomFieldGroup group) {
        save(group);
        return group.getId();
    }

    @Override
    public void deleteById(Long id) {
        String sql = "DELETE FROM custom_field_groups WHERE id = ?";
        jdbcTemplate.update(sql, id);
    }
} 