package io.sx.customfields.dto;

import io.sx.dto.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomFieldGroupDTO extends BaseDTO {
    private String name;
    private String label;
    private String description;
    private Integer displayOrder;
    private Boolean isCollapsible;
    private Boolean isCollapsedByDefault;
    private Boolean isActive;
} 