package io.sx.customfields.mapper;

import io.sx.customfields.dto.CustomFieldValueDTO;
import io.sx.model.customfields.CustomFieldValue;
import org.springframework.stereotype.Component;

@Component
public class CustomFieldValueMapper {

    public CustomFieldValueDTO toDTO(CustomFieldValue entity) {
        if (entity == null) {
            return null;
        }

        return CustomFieldValueDTO.builder()
                .id(entity.getId())
                .organizationId(entity.getOrganizationId())
                .supportOrganizationId(entity.getSupportOrganizationId())
                .fieldDefinitionId(entity.getFieldDefinitionId())
                .ticketId(entity.getTicketId())
                .valueText(entity.getValueText())
                .valueNumber(entity.getValueNumber())
                .valueBoolean(entity.getValueBoolean())
                .valueDate(entity.getValueDate())
                .valueTimestamp(entity.getValueTimestamp())
                .valueJson(entity.getValueJson())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .createdById(entity.getCreatedById())
                .updatedById(entity.getUpdatedById())
                .createdBy(entity.getCreatedBy())
                .updatedBy(entity.getUpdatedBy())
                .onBehalfOfId(entity.getOnBehalfOfId())
                .build();
    }

    public CustomFieldValue toEntity(CustomFieldValueDTO dto) {
        if (dto == null) {
            return null;
        }

        return CustomFieldValue.builder()
                .id(dto.getId())
                .organizationId(dto.getOrganizationId())
                .supportOrganizationId(dto.getSupportOrganizationId())
                .fieldDefinitionId(dto.getFieldDefinitionId())
                .ticketId(dto.getTicketId())
                .valueText(dto.getValueText())
                .valueNumber(dto.getValueNumber())
                .valueBoolean(dto.getValueBoolean())
                .valueDate(dto.getValueDate())
                .valueTimestamp(dto.getValueTimestamp())
                .valueJson(dto.getValueJson())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .createdById(dto.getCreatedById())
                .updatedById(dto.getUpdatedById())
                .createdBy(dto.getCreatedBy())
                .updatedBy(dto.getUpdatedBy())
                .onBehalfOfId(dto.getOnBehalfOfId())
                .build();
    }
} 