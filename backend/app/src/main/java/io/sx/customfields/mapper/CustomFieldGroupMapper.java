package io.sx.customfields.mapper;

import io.sx.customfields.dto.CustomFieldGroupDTO;
import io.sx.model.customfields.CustomFieldGroup;
import org.springframework.stereotype.Component;

@Component
public class CustomFieldGroupMapper {

    public CustomFieldGroupDTO toDTO(CustomFieldGroup entity) {
        if (entity == null) {
            return null;
        }

        return CustomFieldGroupDTO.builder()
                .id(entity.getId())
                .name(entity.getName())
                .label(entity.getLabel())
                .description(entity.getDescription())
                .displayOrder(entity.getDisplayOrder())
                .isCollapsible(entity.getIsCollapsible())
                .isCollapsedByDefault(entity.getIsCollapsedByDefault())
                .isActive(entity.getIsActive())
                .organizationId(entity.getOrganizationId())
                .supportOrganizationId(entity.getSupportOrganizationId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .createdById(entity.getCreatedById())
                .updatedById(entity.getUpdatedById())
                .createdBy(entity.getCreatedBy())
                .updatedBy(entity.getUpdatedBy())
                .onBehalfOfId(entity.getOnBehalfOfId())
                .build();
    }

    public CustomFieldGroup toEntity(CustomFieldGroupDTO dto) {
        if (dto == null) {
            return null;
        }

        return CustomFieldGroup.builder()
                .id(dto.getId())
                .name(dto.getName())
                .label(dto.getLabel())
                .description(dto.getDescription())
                .displayOrder(dto.getDisplayOrder())
                .isCollapsible(dto.getIsCollapsible())
                .isCollapsedByDefault(dto.getIsCollapsedByDefault())
                .isActive(dto.getIsActive())
                .organizationId(dto.getOrganizationId())
                .supportOrganizationId(dto.getSupportOrganizationId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .createdById(dto.getCreatedById())
                .updatedById(dto.getUpdatedById())
                .createdBy(dto.getCreatedBy())
                .updatedBy(dto.getUpdatedBy())
                .onBehalfOfId(dto.getOnBehalfOfId())
                .build();
    }
} 