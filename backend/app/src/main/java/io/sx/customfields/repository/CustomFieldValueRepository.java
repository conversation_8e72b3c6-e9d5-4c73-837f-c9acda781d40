package io.sx.customfields.repository;

import io.sx.repository.BaseRepository;
import io.sx.model.customfields.CustomFieldValue;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomFieldValueRepository extends BaseRepository<CustomFieldValue> {
    List<CustomFieldValue> findAll();
    Optional<CustomFieldValue> findById(Long id);
    List<CustomFieldValue> findByTicketId(Long ticketId);
    List<CustomFieldValue> findByFieldDefinitionId(Long fieldDefinitionId);
    Optional<CustomFieldValue> findByFieldDefinitionIdAndTicketId(Long fieldDefinitionId, Long ticketId);
    List<CustomFieldValue> findByOrganizationId(Long organizationId);
    List<CustomFieldValue> findBySupportOrganizationId(Long supportOrganizationId);
    void deleteByTicketId(Long ticketId);
    void deleteByFieldDefinitionId(Long fieldDefinitionId);
} 