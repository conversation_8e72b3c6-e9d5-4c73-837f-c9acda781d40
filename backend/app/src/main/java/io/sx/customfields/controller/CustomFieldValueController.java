package io.sx.customfields.controller;

import io.sx.customfields.dto.CustomFieldValueDTO;
import io.sx.customfields.service.CustomFieldValueService;
import io.sx.context.RequestContext;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/custom-fields/values")
public class CustomFieldValueController {

    private final CustomFieldValueService customFieldValueService;

    @Autowired
    public CustomFieldValueController(CustomFieldValueService customFieldValueService) {
        this.customFieldValueService = customFieldValueService;
    }

    @GetMapping
    public ResponseEntity<List<CustomFieldValueDTO>> getCustomFieldValues(
            @RequestParam Long ticketId) {
        try {
            List<CustomFieldValueDTO> values = customFieldValueService.getValuesForTicket(ticketId);
            return ResponseEntity.ok(values);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<CustomFieldValueDTO> getCustomFieldValue(@PathVariable Long id) {
        try {
            CustomFieldValueDTO value = customFieldValueService.findById(id);
            return ResponseEntity.ok(value);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping
    public ResponseEntity<Map<Long, CustomFieldValueDTO>> saveCustomFieldValues(
            @RequestParam Long ticketId,
            @Valid @RequestBody Map<Long, Object> values) {
        try {
            Map<Long, CustomFieldValueDTO> savedValues = customFieldValueService.saveValuesForTicket(ticketId, values);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedValues);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<CustomFieldValueDTO> updateCustomFieldValue(
            @PathVariable Long id,
            @Valid @RequestBody CustomFieldValueDTO valueDTO) {
        try {
            CustomFieldValueDTO updatedValue = customFieldValueService.saveValue(valueDTO);
            return ResponseEntity.ok(updatedValue);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping
    public ResponseEntity<Void> deleteCustomFieldValues(@RequestParam Long ticketId) {
        try {
            customFieldValueService.deleteByTicketId(ticketId);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCustomFieldValue(@PathVariable Long id) {
        try {
            customFieldValueService.deleteById(id);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/by-field-definition/{fieldDefinitionId}")
    public ResponseEntity<List<CustomFieldValueDTO>> getCustomFieldValuesByFieldDefinition(
            @PathVariable Long fieldDefinitionId) {
        try {
            List<CustomFieldValueDTO> values = customFieldValueService.findByFieldDefinitionId(fieldDefinitionId);
            return ResponseEntity.ok(values);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/by-field-definition/{fieldDefinitionId}/ticket/{ticketId}")
    public ResponseEntity<CustomFieldValueDTO> getCustomFieldValueByFieldDefinitionAndTicket(
            @PathVariable Long fieldDefinitionId,
            @PathVariable Long ticketId) {
        try {
            Optional<CustomFieldValueDTO> valueOpt = customFieldValueService.findByFieldDefinitionIdAndTicketId(fieldDefinitionId, ticketId);
            if (valueOpt.isPresent()) {
                return ResponseEntity.ok(valueOpt.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping("/by-field-definition/{fieldDefinitionId}")
    public ResponseEntity<Void> deleteCustomFieldValuesByFieldDefinition(@PathVariable Long fieldDefinitionId) {
        try {
            customFieldValueService.deleteByFieldDefinitionId(fieldDefinitionId);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/organization/{organizationId}")
    public ResponseEntity<List<CustomFieldValueDTO>> getCustomFieldValuesByOrganization(
            @PathVariable Long organizationId) {
        try {
            List<CustomFieldValueDTO> values = customFieldValueService.findByOrganizationId(organizationId);
            return ResponseEntity.ok(values);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/support-organization/{supportOrganizationId}")
    public ResponseEntity<List<CustomFieldValueDTO>> getCustomFieldValuesBySupportOrganization(
            @PathVariable Long supportOrganizationId) {
        try {
            List<CustomFieldValueDTO> values = customFieldValueService.findBySupportOrganizationId(supportOrganizationId);
            return ResponseEntity.ok(values);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
} 