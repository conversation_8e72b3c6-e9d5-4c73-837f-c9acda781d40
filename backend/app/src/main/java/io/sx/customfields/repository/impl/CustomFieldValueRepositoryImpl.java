package io.sx.customfields.repository.impl;

import io.sx.customfields.repository.CustomFieldValueRepository;
import io.sx.model.customfields.CustomFieldValue;
import io.sx.repository.impl.BaseRepositoryImpl;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

@Repository
public class CustomFieldValueRepositoryImpl extends BaseRepositoryImpl<CustomFieldValue> implements CustomFieldValueRepository {

    private final JdbcTemplate jdbcTemplate;
    private final RowMapper<CustomFieldValue> rowMapper;

    public CustomFieldValueRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.rowMapper = new CustomFieldValueRowMapper();
    }

    @Override
    public List<CustomFieldValue> findAll(Long organizationId) {
        String sql = "SELECT * FROM custom_field_values WHERE organization_id = ? ORDER BY id";
        return jdbcTemplate.query(sql, rowMapper, organizationId);
    }

    @Override
    public Optional<CustomFieldValue> findById(Long id) {
        String sql = "SELECT * FROM custom_field_values WHERE id = ?";
        List<CustomFieldValue> results = jdbcTemplate.query(sql, rowMapper, id);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public CustomFieldValue save(CustomFieldValue entity) {
        if (entity.getId() == null) {
            String sql = """
                INSERT INTO custom_field_values (
                    field_definition_id, ticket_id, value_text, value_number, value_boolean, 
                    value_date, value_timestamp, value_json, organization_id, support_organization_id,
                    created_by_id, created_by, on_behalf_of_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                RETURNING *
                """;
            
            return jdbcTemplate.queryForObject(sql, rowMapper,
                    entity.getFieldDefinitionId(),
                    entity.getTicketId(),
                    entity.getValueText(),
                    entity.getValueNumber(),
                    entity.getValueBoolean(),
                    entity.getValueDate(),
                    entity.getValueTimestamp(),
                    entity.getValueJson() != null ? entity.getValueJson().toString() : null,
                    entity.getOrganizationId(),
                    entity.getSupportOrganizationId(),
                    entity.getCreatedById(),
                    entity.getCreatedBy(),
                    entity.getOnBehalfOfId()
            );
        } else {
            String sql = """
                UPDATE custom_field_values SET 
                    field_definition_id = ?, ticket_id = ?, value_text = ?, value_number = ?, 
                    value_boolean = ?, value_date = ?, value_timestamp = ?, value_json = ?,
                    organization_id = ?, support_organization_id = ?, updated_by_id = ?, updated_by = ?
                WHERE id = ?
                """;
            
            jdbcTemplate.update(sql,
                    entity.getFieldDefinitionId(),
                    entity.getTicketId(),
                    entity.getValueText(),
                    entity.getValueNumber(),
                    entity.getValueBoolean(),
                    entity.getValueDate(),
                    entity.getValueTimestamp(),
                    entity.getValueJson() != null ? entity.getValueJson().toString() : null,
                    entity.getOrganizationId(),
                    entity.getSupportOrganizationId(),
                    entity.getUpdatedById(),
                    entity.getUpdatedBy(),
                    entity.getId()
            );
            
            return findById(entity.getId()).orElse(entity);
        }
    }

    @Override
    public Long update(CustomFieldValue entity) {
        save(entity);
        return entity.getId();
    }

    @Override
    public void deleteById(Long id) {
        String sql = "DELETE FROM custom_field_values WHERE id = ?";
        jdbcTemplate.update(sql, id);
    }

    @Override
    public List<CustomFieldValue> findByTicketId(Long ticketId) {
        String sql = "SELECT * FROM custom_field_values WHERE ticket_id = ? ORDER BY id";
        return jdbcTemplate.query(sql, rowMapper, ticketId);
    }

    @Override
    public List<CustomFieldValue> findByFieldDefinitionId(Long fieldDefinitionId) {
        String sql = "SELECT * FROM custom_field_values WHERE field_definition_id = ? ORDER BY id";
        return jdbcTemplate.query(sql, rowMapper, fieldDefinitionId);
    }

    @Override
    public Optional<CustomFieldValue> findByFieldDefinitionIdAndTicketId(Long fieldDefinitionId, Long ticketId) {
        String sql = "SELECT * FROM custom_field_values WHERE field_definition_id = ? AND ticket_id = ?";
        List<CustomFieldValue> results = jdbcTemplate.query(sql, rowMapper, fieldDefinitionId, ticketId);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public List<CustomFieldValue> findByOrganizationId(Long organizationId) {
        String sql = "SELECT * FROM custom_field_values WHERE organization_id = ? ORDER BY id";
        return jdbcTemplate.query(sql, rowMapper, organizationId);
    }

    @Override
    public List<CustomFieldValue> findBySupportOrganizationId(Long supportOrganizationId) {
        String sql = "SELECT * FROM custom_field_values WHERE support_organization_id = ? ORDER BY id";
        return jdbcTemplate.query(sql, rowMapper, supportOrganizationId);
    }

    @Override
    public void deleteByTicketId(Long ticketId) {
        String sql = "DELETE FROM custom_field_values WHERE ticket_id = ?";
        jdbcTemplate.update(sql, ticketId);
    }

    @Override
    public void deleteByFieldDefinitionId(Long fieldDefinitionId) {
        String sql = "DELETE FROM custom_field_values WHERE field_definition_id = ?";
        jdbcTemplate.update(sql, fieldDefinitionId);
    }

    @Override
    public List<CustomFieldValue> findAll() {
        String sql = "SELECT * FROM custom_field_values ORDER BY id";
        return jdbcTemplate.query(sql, rowMapper);
    }

    private static class CustomFieldValueRowMapper implements RowMapper<CustomFieldValue> {
        @Override
        public CustomFieldValue mapRow(ResultSet rs, int rowNum) throws SQLException {
            return CustomFieldValue.builder()
                    .id(rs.getLong("id"))
                    .fieldDefinitionId(rs.getLong("field_definition_id"))
                    .ticketId(rs.getLong("ticket_id"))
                    .valueText(rs.getString("value_text"))
                    .valueNumber(rs.getBigDecimal("value_number"))
                    .valueBoolean(rs.getBoolean("value_boolean"))
                    .valueDate(rs.getDate("value_date") != null ? rs.getDate("value_date").toLocalDate() : null)
                    .valueTimestamp(rs.getTimestamp("value_timestamp") != null ? rs.getTimestamp("value_timestamp").toLocalDateTime() : null)
                    .valueJson(rs.getString("value_json") != null ? java.util.Map.of() : null) // TODO: Parse JSON properly
                    .organizationId(rs.getLong("organization_id"))
                    .supportOrganizationId(rs.getLong("support_organization_id"))
                    .createdAt(rs.getTimestamp("created_at") != null ? rs.getTimestamp("created_at").toLocalDateTime() : null)
                    .updatedAt(rs.getTimestamp("updated_at") != null ? rs.getTimestamp("updated_at").toLocalDateTime() : null)
                    .createdById(rs.getLong("created_by_id"))
                    .updatedById(rs.getLong("updated_by_id"))
                    .createdBy(rs.getString("created_by"))
                    .updatedBy(rs.getString("updated_by"))
                    .onBehalfOfId(rs.getLong("on_behalf_of_id"))
                    .build();
        }
    }
} 