package io.sx.customfields.mapper;

import io.sx.customfields.dto.CustomFieldDefinitionDTO;
import io.sx.model.customfields.CustomFieldDefinition;
import org.springframework.stereotype.Component;

@Component
public class CustomFieldDefinitionMapper {

    public CustomFieldDefinitionDTO toDTO(CustomFieldDefinition entity) {
        if (entity == null) {
            return null;
        }

        return CustomFieldDefinitionDTO.builder()
                .id(entity.getId())
                .name(entity.getName())
                .label(entity.getLabel())
                .description(entity.getDescription())
                .fieldType(entity.getFieldType())
                .dataType(entity.getDataType())
                .isRequired(entity.getIsRequired())
                .isUnique(entity.getIsUnique())
                .defaultValue(entity.getDefaultValue())
                .placeholder(entity.getPlaceholder())
                .helpText(entity.getHelpText())
                .displayOrder(entity.getDisplayOrder())
                .groupId(entity.getGroupId())
                .version(entity.getVersion())
                .isActive(entity.getIsActive())
                .isTemplate(entity.getIsTemplate())
                .templateId(entity.getTemplateId())
                .validationRules(entity.getValidationRules())
                .fieldOptions(entity.getFieldOptions())
                .organizationId(entity.getOrganizationId())
                .supportOrganizationId(entity.getSupportOrganizationId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .createdById(entity.getCreatedById())
                .updatedById(entity.getUpdatedById())
                .createdBy(entity.getCreatedBy())
                .updatedBy(entity.getUpdatedBy())
                .onBehalfOfId(entity.getOnBehalfOfId())
                .build();
    }

    public CustomFieldDefinition toEntity(CustomFieldDefinitionDTO dto) {
        if (dto == null) {
            return null;
        }

        return CustomFieldDefinition.builder()
                .id(dto.getId())
                .name(dto.getName())
                .label(dto.getLabel())
                .description(dto.getDescription())
                .fieldType(dto.getFieldType())
                .dataType(dto.getDataType())
                .isRequired(dto.getIsRequired())
                .isUnique(dto.getIsUnique())
                .defaultValue(dto.getDefaultValue())
                .placeholder(dto.getPlaceholder())
                .helpText(dto.getHelpText())
                .displayOrder(dto.getDisplayOrder())
                .groupId(dto.getGroupId())
                .version(dto.getVersion())
                .isActive(dto.getIsActive())
                .isTemplate(dto.getIsTemplate())
                .templateId(dto.getTemplateId())
                .validationRules(dto.getValidationRules())
                .fieldOptions(dto.getFieldOptions())
                .organizationId(dto.getOrganizationId())
                .supportOrganizationId(dto.getSupportOrganizationId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .createdById(dto.getCreatedById())
                .updatedById(dto.getUpdatedById())
                .createdBy(dto.getCreatedBy())
                .updatedBy(dto.getUpdatedBy())
                .onBehalfOfId(dto.getOnBehalfOfId())
                .build();
    }
} 