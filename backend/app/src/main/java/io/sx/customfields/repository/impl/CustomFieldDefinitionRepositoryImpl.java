package io.sx.customfields.repository.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sx.base.rowmapper.BaseRowMapper;
import io.sx.repository.NullHandlingResultSet;
import io.sx.customfields.repository.CustomFieldDefinitionRepository;
import io.sx.model.customfields.CustomFieldDefinition;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class CustomFieldDefinitionRepositoryImpl implements CustomFieldDefinitionRepository {

    private final JdbcTemplate jdbcTemplate;
    private final ObjectMapper objectMapper;
    
    private final RowMapper<CustomFieldDefinition> rowMapper = new BaseRowMapper<CustomFieldDefinition>() {
        @Override
        protected CustomFieldDefinition mapRowInternal(NullHandlingResultSet rs, int rowNum) throws SQLException {
            CustomFieldDefinition definition = new CustomFieldDefinition();
            definition.setId(rs.getLong("id"));
            definition.setName(rs.getString("name"));
            definition.setLabel(rs.getString("label"));
            definition.setDescription(rs.getString("description"));
            definition.setFieldType(rs.getString("field_type"));
            definition.setDataType(rs.getString("data_type"));
            definition.setIsRequired(rs.getBoolean("is_required"));
            definition.setIsUnique(rs.getBoolean("is_unique"));
            definition.setDefaultValue(rs.getString("default_value"));
            definition.setPlaceholder(rs.getString("placeholder"));
            definition.setHelpText(rs.getString("help_text"));
            definition.setDisplayOrder(rs.getInt("display_order"));
            definition.setGroupId(rs.getLong("group_id"));
            definition.setVersion(rs.getInt("version"));
            definition.setIsActive(rs.getBoolean("is_active"));
            definition.setIsTemplate(rs.getBoolean("is_template"));
            definition.setTemplateId(rs.getLong("template_id"));
            definition.setOrganizationId(rs.getLong("organization_id"));
            definition.setSupportOrganizationId(rs.getLong("support_organization_id"));
            definition.setCreatedAt(rs.getLocalDateTime("created_at"));
            definition.setUpdatedAt(rs.getLocalDateTime("updated_at"));
            definition.setCreatedById(rs.getLong("created_by_id"));
            definition.setUpdatedById(rs.getLong("updated_by_id"));
            definition.setCreatedBy(rs.getString("created_by"));
            definition.setUpdatedBy(rs.getString("updated_by"));
            definition.setOnBehalfOfId(rs.getLong("on_behalf_of_id"));

            // Parse JSONB fields
            try {
                String validationRulesJson = rs.getString("validation_rules");
                if (validationRulesJson != null) {
                    definition.setValidationRules(objectMapper.readValue(validationRulesJson, new TypeReference<Map<String, Object>>() {}));
                }

                String fieldOptionsJson = rs.getString("field_options");
                if (fieldOptionsJson != null) {
                    definition.setFieldOptions(objectMapper.readValue(fieldOptionsJson, new TypeReference<Map<String, Object>>() {}));
                }
            } catch (JsonProcessingException e) {
                // Log error but don't fail the mapping
                System.err.println("Error parsing JSONB fields: " + e.getMessage());
            }

            return definition;
        }
    };

    @Override
    public List<CustomFieldDefinition> findAll() {
        String sql = "SELECT * FROM custom_field_definitions ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper);
    }

    @Override
    public Optional<CustomFieldDefinition> findById(Long id) {
        String sql = "SELECT * FROM custom_field_definitions WHERE id = ?";
        List<CustomFieldDefinition> results = jdbcTemplate.query(sql, rowMapper, id);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public List<CustomFieldDefinition> findByOrganizationId(Long organizationId) {
        String sql = "SELECT * FROM custom_field_definitions WHERE organization_id = ? ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper, organizationId);
    }

    @Override
    public List<CustomFieldDefinition> findBySupportOrganizationId(Long supportOrganizationId) {
        String sql = "SELECT * FROM custom_field_definitions WHERE support_organization_id = ? ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper, supportOrganizationId);
    }

    @Override
    public List<CustomFieldDefinition> findByOrganizationIdAndIsActiveOrderByDisplayOrder(Long organizationId, Boolean isActive) {
        String sql = "SELECT * FROM custom_field_definitions WHERE organization_id = ? AND is_active = ? ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper, organizationId, isActive);
    }

    @Override
    public List<CustomFieldDefinition> findByGroupId(Long groupId) {
        String sql = "SELECT * FROM custom_field_definitions WHERE group_id = ? ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper, groupId);
    }

    @Override
    public List<CustomFieldDefinition> findByTemplateId(Long templateId) {
        String sql = "SELECT * FROM custom_field_definitions WHERE template_id = ? ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper, templateId);
    }

    @Override
    public List<CustomFieldDefinition> findByIsTemplateAndIsActive(Boolean isTemplate, Boolean isActive) {
        String sql = "SELECT * FROM custom_field_definitions WHERE is_template = ? AND is_active = ? ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper, isTemplate, isActive);
    }

    @Override
    public Optional<CustomFieldDefinition> findByNameAndOrganizationId(String name, Long organizationId) {
        String sql = "SELECT * FROM custom_field_definitions WHERE name = ? AND organization_id = ?";
        List<CustomFieldDefinition> results = jdbcTemplate.query(sql, rowMapper, name, organizationId);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public List<CustomFieldDefinition> findByFieldType(String fieldType) {
        String sql = "SELECT * FROM custom_field_definitions WHERE field_type = ? ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper, fieldType);
    }

    @Override
    public List<CustomFieldDefinition> findByIsTemplateTrue() {
        String sql = "SELECT * FROM custom_field_definitions WHERE is_template = true ORDER BY display_order";
        return jdbcTemplate.query(sql, rowMapper);
    }

    @Override
    public List<CustomFieldDefinition> findAll(Long organizationId) {
        return findByOrganizationId(organizationId);
    }

    @Override
    public CustomFieldDefinition save(CustomFieldDefinition definition) {
        try {
            if (definition.getId() == null) {
                // Insert new definition and return with generated ID
                String sql = "INSERT INTO custom_field_definitions (name, label, description, field_type, data_type, " +
                        "is_required, is_unique, default_value, placeholder, help_text, display_order, group_id, " +
                        "version, is_active, is_template, template_id, organization_id, support_organization_id, " +
                        "validation_rules, field_options, created_at, updated_at, created_by_id, updated_by_id, " +
                        "created_by, updated_by, on_behalf_of_id) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING *";

                CustomFieldDefinition savedDefinition = jdbcTemplate.queryForObject(sql, rowMapper,
                    definition.getName(),
                    definition.getLabel(),
                    definition.getDescription(),
                    definition.getFieldType(),
                    definition.getDataType() != null ? definition.getDataType().toUpperCase() : null,
                    definition.getIsRequired(),
                    definition.getIsUnique(),
                    definition.getDefaultValue(),
                    definition.getPlaceholder(),
                    definition.getHelpText(),
                    definition.getDisplayOrder(),
                    definition.getGroupId(),
                    definition.getVersion(),
                    definition.getIsActive(),
                    definition.getIsTemplate(),
                    definition.getTemplateId(),
                    definition.getOrganizationId(),
                    definition.getSupportOrganizationId(),
                    definition.getValidationRules() != null ? objectMapper.writeValueAsString(definition.getValidationRules()) : null,
                    definition.getFieldOptions() != null ? objectMapper.writeValueAsString(definition.getFieldOptions()) : null,
                    definition.getCreatedAt(),
                    definition.getUpdatedAt(),
                    definition.getCreatedById(),
                    definition.getUpdatedById(),
                    definition.getCreatedBy(),
                    definition.getUpdatedBy(),
                    definition.getOnBehalfOfId()
                );

                return savedDefinition;
            } else {
                // Update existing definition
                String sql = "UPDATE custom_field_definitions SET name = ?, label = ?, description = ?, field_type = ?, " +
                        "data_type = ?, is_required = ?, is_unique = ?, default_value = ?, placeholder = ?, help_text = ?, " +
                        "display_order = ?, group_id = ?, version = ?, is_active = ?, is_template = ?, template_id = ?, " +
                        "organization_id = ?, support_organization_id = ?, validation_rules = ?, field_options = ?, " +
                        "updated_at = ?, updated_by_id = ?, updated_by = ?, on_behalf_of_id = ? WHERE id = ?";
                
                jdbcTemplate.update(sql,
                    definition.getName(),
                    definition.getLabel(),
                    definition.getDescription(),
                    definition.getFieldType(),
                    definition.getDataType() != null ? definition.getDataType().toUpperCase() : null,
                    definition.getIsRequired(),
                    definition.getIsUnique(),
                    definition.getDefaultValue(),
                    definition.getPlaceholder(),
                    definition.getHelpText(),
                    definition.getDisplayOrder(),
                    definition.getGroupId(),
                    definition.getVersion(),
                    definition.getIsActive(),
                    definition.getIsTemplate(),
                    definition.getTemplateId(),
                    definition.getOrganizationId(),
                    definition.getSupportOrganizationId(),
                    definition.getValidationRules() != null ? objectMapper.writeValueAsString(definition.getValidationRules()) : null,
                    definition.getFieldOptions() != null ? objectMapper.writeValueAsString(definition.getFieldOptions()) : null,
                    definition.getUpdatedAt(),
                    definition.getUpdatedById(),
                    definition.getUpdatedBy(),
                    definition.getOnBehalfOfId(),
                    definition.getId()
                );

                // Fetch and return the updated definition
                return findById(definition.getId()).orElse(definition);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error serializing JSONB fields", e);
        }
    }

    @Override
    public Long update(CustomFieldDefinition definition) {
        save(definition);
        return definition.getId();
    }

    @Override
    public void deleteById(Long id) {
        String sql = "DELETE FROM custom_field_definitions WHERE id = ?";
        jdbcTemplate.update(sql, id);
    }
} 