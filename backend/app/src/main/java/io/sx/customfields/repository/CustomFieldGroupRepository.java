package io.sx.customfields.repository;

import io.sx.repository.BaseRepository;
import io.sx.model.customfields.CustomFieldGroup;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomFieldGroupRepository extends BaseRepository<CustomFieldGroup> {
    List<CustomFieldGroup> findAll();
    Optional<CustomFieldGroup> findById(Long id);
    List<CustomFieldGroup> findByOrganizationId(Long organizationId);
    List<CustomFieldGroup> findBySupportOrganizationId(Long supportOrganizationId);
    List<CustomFieldGroup> findByOrganizationIdOrderByDisplayOrder(Long organizationId);
    Optional<CustomFieldGroup> findByNameAndOrganizationId(String name, Long organizationId);
} 