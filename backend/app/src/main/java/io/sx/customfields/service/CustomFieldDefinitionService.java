package io.sx.customfields.service;

import io.sx.base.service.BaseService;
import io.sx.customfields.dto.CustomFieldDefinitionDTO;
import io.sx.customfields.dto.CreateCustomFieldDefinitionRequest;
import io.sx.model.customfields.CustomFieldDefinition;

import java.util.List;
import java.util.Optional;

public interface CustomFieldDefinitionService extends BaseService<CustomFieldDefinition, CustomFieldDefinitionDTO> {
    List<CustomFieldDefinitionDTO> findByOrganizationId(Long organizationId);
    List<CustomFieldDefinitionDTO> findBySupportOrganizationId(Long supportOrganizationId);
    List<CustomFieldDefinitionDTO> findByOrganizationIdAndIsActive(Long organizationId, Boolean isActive);
    List<CustomFieldDefinitionDTO> findByGroupId(Long groupId);
    List<CustomFieldDefinitionDTO> findByTemplateId(Long templateId);
    List<CustomFieldDefinitionDTO> findTemplates(Boolean isActive);
    Optional<CustomFieldDefinitionDTO> findByNameAndOrganizationId(String name, Long organizationId);
    List<CustomFieldDefinitionDTO> findByFieldType(String fieldType);
    CustomFieldDefinitionDTO createField(CreateCustomFieldDefinitionRequest request);
    CustomFieldDefinitionDTO createField(CustomFieldDefinitionDTO fieldDTO);
    CustomFieldDefinitionDTO updateField(Long id, CustomFieldDefinitionDTO fieldDTO);
    CustomFieldDefinitionDTO copyFromTemplate(Long templateId, Long targetOrganizationId);
    void deleteField(Long id);
    void reorderFields(List<Long> fieldIds);
    void activateField(Long id);
    void deactivateField(Long id);
} 