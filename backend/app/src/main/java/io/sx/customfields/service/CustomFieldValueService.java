package io.sx.customfields.service;

import io.sx.base.service.BaseService;
import io.sx.customfields.dto.CustomFieldValueDTO;
import io.sx.model.customfields.CustomFieldValue;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface CustomFieldValueService extends BaseService<CustomFieldValue, CustomFieldValueDTO> {
    List<CustomFieldValueDTO> findByTicketId(Long ticketId);
    List<CustomFieldValueDTO> findByFieldDefinitionId(Long fieldDefinitionId);
    Optional<CustomFieldValueDTO> findByFieldDefinitionIdAndTicketId(Long fieldDefinitionId, Long ticketId);
    List<CustomFieldValueDTO> findByOrganizationId(Long organizationId);
    List<CustomFieldValueDTO> findBySupportOrganizationId(Long supportOrganizationId);
    CustomFieldValueDTO saveValue(CustomFieldValueDTO valueDTO);
    Map<Long, CustomFieldValueDTO> saveValuesForTicket(Long ticketId, Map<Long, Object> fieldValues);
    void deleteByTicketId(Long ticketId);
    void deleteByFieldDefinitionId(Long fieldDefinitionId);
    List<CustomFieldValueDTO> getValuesForTicket(Long ticketId);
} 