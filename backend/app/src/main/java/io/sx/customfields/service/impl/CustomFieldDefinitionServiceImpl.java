package io.sx.customfields.service.impl;

import io.sx.base.service.impl.BaseServiceImpl;
import io.sx.context.RequestContext;
import io.sx.customfields.dto.CustomFieldDefinitionDTO;
import io.sx.customfields.dto.CreateCustomFieldDefinitionRequest;
import io.sx.customfields.dto.UpdateCustomFieldDefinitionRequest;
import io.sx.customfields.mapper.CustomFieldDefinitionMapper;
import io.sx.customfields.repository.CustomFieldDefinitionRepository;
import io.sx.customfields.service.CustomFieldDefinitionService;
import io.sx.model.customfields.CustomFieldDefinition;
import io.sx.model.customfields.CustomFieldType;
import io.sx.model.customfields.CustomFieldDataType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CustomFieldDefinitionServiceImpl extends BaseServiceImpl<CustomFieldDefinition, CustomFieldDefinitionDTO> 
        implements CustomFieldDefinitionService {

    private final CustomFieldDefinitionRepository repository;
    private final CustomFieldDefinitionMapper mapper;

    public CustomFieldDefinitionServiceImpl(CustomFieldDefinitionRepository repository, CustomFieldDefinitionMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    public List<CustomFieldDefinitionDTO> findByOrganizationId(Long organizationId) {
        List<CustomFieldDefinition> fields = repository.findByOrganizationId(organizationId);
        return fields.stream()
                .map(mapper::toDTO)
                .toList();
    }

    public List<CustomFieldDefinitionDTO> findBySupportOrganizationId(Long supportOrganizationId) {
        List<CustomFieldDefinition> fields = repository.findBySupportOrganizationId(supportOrganizationId);
        return fields.stream()
                .map(mapper::toDTO)
                .toList();
    }

    public List<CustomFieldDefinitionDTO> findByOrganizationIdAndIsActive(Long organizationId, Boolean isActive) {
        List<CustomFieldDefinition> fields = repository.findByOrganizationIdAndIsActiveOrderByDisplayOrder(organizationId, isActive);
        return fields.stream()
                .map(mapper::toDTO)
                .toList();
    }

    public List<CustomFieldDefinitionDTO> findByGroupId(Long groupId) {
        List<CustomFieldDefinition> fields = repository.findByGroupId(groupId);
        return fields.stream()
                .map(mapper::toDTO)
                .toList();
    }

    public CustomFieldDefinitionDTO createField(CreateCustomFieldDefinitionRequest request) {
        // Validate field type and data type
        validateFieldTypeAndDataType(request.getFieldType(), request.getDataType());
        
        // Get organization context from RequestContext
        Long organizationId = RequestContext.getOrgId();
        Long supportOrganizationId = RequestContext.getSupportOrgId();
        
        if (organizationId == null) {
            throw new IllegalArgumentException("Organization ID not found in request context");
        }
        
        // Check if field name already exists for this organization
        Optional<CustomFieldDefinition> existingField = repository.findByNameAndOrganizationId(request.getName(), organizationId);
        if (existingField.isPresent()) {
            throw new IllegalArgumentException("A field with name '" + request.getName() + "' already exists for this organization");
        }

        // Create new field definition - let AuditAspect handle audit fields
        CustomFieldDefinition field = CustomFieldDefinition.builder()
                .name(request.getName())
                .label(request.getLabel())
                .description(request.getDescription())
                .fieldType(request.getFieldType())
                .dataType(request.getDataType())
                .isRequired(request.getIsRequired() != null ? request.getIsRequired() : false)
                .isUnique(request.getIsUnique() != null ? request.getIsUnique() : false)
                .defaultValue(request.getDefaultValue())
                .placeholder(request.getPlaceholder())
                .helpText(request.getHelpText())
                .displayOrder(request.getDisplayOrder() != null ? request.getDisplayOrder() : 0)
                .groupId(request.getGroupId())
                .organizationId(organizationId)
                .supportOrganizationId(supportOrganizationId != null ? supportOrganizationId : organizationId)
                .version(request.getVersion() != null ? request.getVersion() : 1)
                .isActive(true)
                .isTemplate(request.getIsTemplate() != null ? request.getIsTemplate() : false)
                .templateId(request.getTemplateId())
                .validationRules(request.getValidationRules())
                .fieldOptions(request.getFieldOptions())
                .build();

        CustomFieldDefinition savedField = repository.save(field);
        return mapper.toDTO(savedField);
    }

    public CustomFieldDefinitionDTO save(CreateCustomFieldDefinitionRequest request, Long organizationId, Long supportOrganizationId, Long createdById, String createdBy) {
        // Validate field type and data type
        validateFieldTypeAndDataType(request.getFieldType(), request.getDataType());
        
        // Check if field name already exists for this organization
        Optional<CustomFieldDefinition> existingField = repository.findByNameAndOrganizationId(request.getName(), organizationId);
        if (existingField.isPresent()) {
            throw new IllegalArgumentException("A field with name '" + request.getName() + "' already exists for this organization");
        }

        // Create new field definition - let AuditAspect handle audit fields
        CustomFieldDefinition field = CustomFieldDefinition.builder()
                .name(request.getName())
                .label(request.getLabel())
                .description(request.getDescription())
                .fieldType(request.getFieldType() != null ? request.getFieldType().toUpperCase() : null)
                .dataType(request.getDataType() != null ? request.getDataType().toUpperCase() : null)
                .isRequired(request.getIsRequired() != null ? request.getIsRequired() : false)
                .isUnique(request.getIsUnique() != null ? request.getIsUnique() : false)
                .defaultValue(request.getDefaultValue())
                .placeholder(request.getPlaceholder())
                .helpText(request.getHelpText())
                .displayOrder(request.getDisplayOrder() != null ? request.getDisplayOrder() : 0)
                .groupId(request.getGroupId())
                .organizationId(organizationId)
                .supportOrganizationId(supportOrganizationId)
                .version(request.getVersion() != null ? request.getVersion() : 1)
                .isActive(true)
                .isTemplate(request.getIsTemplate() != null ? request.getIsTemplate() : false)
                .templateId(request.getTemplateId())
                .validationRules(request.getValidationRules())
                .fieldOptions(request.getFieldOptions())
                .build();

        CustomFieldDefinition savedField = repository.save(field);
        return mapper.toDTO(savedField);
    }

    public Long update(Long fieldId, UpdateCustomFieldDefinitionRequest request, Long updatedById, String updatedBy) {
        CustomFieldDefinition existingField = repository.findById(fieldId)
                .orElseThrow(() -> new IllegalArgumentException("Field definition not found with id: " + fieldId));

        // Validate field type and data type if provided
        if (StringUtils.hasText(request.getFieldType())) {
            validateFieldTypeAndDataType(request.getFieldType(), request.getDataType());
        }

        // Check if name change conflicts with existing field
        if (StringUtils.hasText(request.getName()) && !request.getName().equals(existingField.getName())) {
            Optional<CustomFieldDefinition> nameConflict = repository.findByNameAndOrganizationId(request.getName(), existingField.getOrganizationId());
            if (nameConflict.isPresent() && !nameConflict.get().getId().equals(fieldId)) {
                throw new IllegalArgumentException("A field with name '" + request.getName() + "' already exists for this organization");
            }
        }

        // Update fields if provided
        if (StringUtils.hasText(request.getName())) {
            existingField.setName(request.getName());
        }
        if (StringUtils.hasText(request.getLabel())) {
            existingField.setLabel(request.getLabel());
        }
        if (request.getDescription() != null) {
            existingField.setDescription(request.getDescription());
        }
        if (StringUtils.hasText(request.getFieldType())) {
            existingField.setFieldType(request.getFieldType().toUpperCase());
        }
        if (StringUtils.hasText(request.getDataType())) {
            existingField.setDataType(request.getDataType().toUpperCase());
        }
        if (request.getIsRequired() != null) {
            existingField.setIsRequired(request.getIsRequired());
        }
        if (request.getIsUnique() != null) {
            existingField.setIsUnique(request.getIsUnique());
        }
        if (request.getDefaultValue() != null) {
            existingField.setDefaultValue(request.getDefaultValue());
        }
        if (request.getPlaceholder() != null) {
            existingField.setPlaceholder(request.getPlaceholder());
        }
        if (request.getHelpText() != null) {
            existingField.setHelpText(request.getHelpText());
        }
        if (request.getDisplayOrder() != null) {
            existingField.setDisplayOrder(request.getDisplayOrder());
        }
        if (request.getGroupId() != null) {
            existingField.setGroupId(request.getGroupId());
        }
        if (request.getIsActive() != null) {
            existingField.setIsActive(request.getIsActive());
        }
        if (request.getValidationRules() != null) {
            existingField.setValidationRules(request.getValidationRules());
        }
        if (request.getFieldOptions() != null) {
            existingField.setFieldOptions(request.getFieldOptions());
        }

        // Let AuditAspect handle audit field updates
        return repository.update(existingField);
    }

    public CustomFieldDefinitionDTO copyFromTemplate(Long templateId, Long organizationId, Long supportOrganizationId, Long createdById, String createdBy) {
        CustomFieldDefinition template = repository.findById(templateId)
                .orElseThrow(() -> new IllegalArgumentException("Template not found with id: " + templateId));

        if (!template.getIsTemplate()) {
            throw new IllegalArgumentException("Field with id " + templateId + " is not a template");
        }

        // Create a copy of the template
        CustomFieldDefinition copiedField = CustomFieldDefinition.builder()
                .name(template.getName())
                .label(template.getLabel())
                .description(template.getDescription())
                .fieldType(template.getFieldType())
                .dataType(template.getDataType())
                .isRequired(template.getIsRequired())
                .isUnique(template.getIsUnique())
                .defaultValue(template.getDefaultValue())
                .placeholder(template.getPlaceholder())
                .helpText(template.getHelpText())
                .displayOrder(template.getDisplayOrder())
                .groupId(template.getGroupId())
                .organizationId(organizationId)
                .supportOrganizationId(supportOrganizationId)
                .version(1)
                .isActive(true)
                .isTemplate(false) // Copied fields are not templates
                .templateId(templateId) // Reference to original template
                .validationRules(template.getValidationRules())
                .fieldOptions(template.getFieldOptions())
                .createdById(createdById)
                .createdBy(createdBy)
                .build();

        CustomFieldDefinition savedField = repository.save(copiedField);
        return mapper.toDTO(savedField);
    }

    public void deleteField(Long fieldId) {
        CustomFieldDefinition field = repository.findById(fieldId)
                .orElseThrow(() -> new IllegalArgumentException("Field definition not found with id: " + fieldId));

        // Check if field is in use (has values)
        // TODO: Add check for existing values before deletion
        // For now, we'll allow deletion but this should be enhanced

        repository.deleteById(fieldId);
    }

    public List<CustomFieldDefinitionDTO> reorderFields(Long organizationId, List<Long> fieldIds) {
        List<CustomFieldDefinition> fields = repository.findByOrganizationId(organizationId);
        
        // Update display order based on provided order
        for (int i = 0; i < fieldIds.size(); i++) {
            Long fieldId = fieldIds.get(i);
            CustomFieldDefinition field = fields.stream()
                    .filter(f -> f.getId().equals(fieldId))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("Field not found with id: " + fieldId));
            
            field.setDisplayOrder(i);
            repository.update(field);
        }

        return findByOrganizationId(organizationId);
    }

    public void activateField(Long fieldId) {
        CustomFieldDefinition field = repository.findById(fieldId)
                .orElseThrow(() -> new IllegalArgumentException("Field definition not found with id: " + fieldId));
        
        field.setIsActive(true);
        repository.update(field);
    }

    public void deactivateField(Long fieldId) {
        CustomFieldDefinition field = repository.findById(fieldId)
                .orElseThrow(() -> new IllegalArgumentException("Field definition not found with id: " + fieldId));
        
        field.setIsActive(false);
        repository.update(field);
    }

    public List<CustomFieldDefinitionDTO> findTemplates(Boolean isActive) {
        List<CustomFieldDefinition> templates = repository.findByIsTemplateTrue();
        if (isActive != null) {
            templates = templates.stream()
                    .filter(template -> template.getIsActive().equals(isActive))
                    .toList();
        }
        return templates.stream()
                .map(mapper::toDTO)
                .toList();
    }

    public List<CustomFieldDefinitionDTO> findByTemplateId(Long templateId) {
        List<CustomFieldDefinition> fields = repository.findByTemplateId(templateId);
        return fields.stream()
                .map(mapper::toDTO)
                .toList();
    }

    @Override
    public List<CustomFieldDefinitionDTO> findAll() {
        List<CustomFieldDefinition> definitions = repository.findAll(null);
        return definitions.stream()
                .map(mapper::toDTO)
                .toList();
    }

    @Override
    public CustomFieldDefinitionDTO findById(Long id) {
        CustomFieldDefinition definition = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Field definition not found with id: " + id));
        return mapper.toDTO(definition);
    }

    @Override
    public CustomFieldDefinitionDTO save(CustomFieldDefinitionDTO dto) {
        CustomFieldDefinition entity = mapper.toEntity(dto);
        CustomFieldDefinition saved = repository.save(entity);
        return mapper.toDTO(saved);
    }

    @Override
    public Long update(Long id, CustomFieldDefinitionDTO dto) {
        CustomFieldDefinition entity = mapper.toEntity(dto);
        entity.setId(id);
        return repository.update(entity);
    }

    @Override
    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    public void reorderFields(List<Long> fieldIds) {
        // Update display order based on provided order
        for (int i = 0; i < fieldIds.size(); i++) {
            Long fieldId = fieldIds.get(i);
            CustomFieldDefinition field = repository.findById(fieldId)
                    .orElseThrow(() -> new IllegalArgumentException("Field not found with id: " + fieldId));
            
            field.setDisplayOrder(i);
            repository.update(field);
        }
    }

    public CustomFieldDefinitionDTO copyFromTemplate(Long templateId, Long targetOrganizationId) {
        // This is a simplified version - in a real implementation, you'd need to handle support org context
        CustomFieldDefinition template = repository.findById(templateId)
                .orElseThrow(() -> new IllegalArgumentException("Template not found with id: " + templateId));

        if (!template.getIsTemplate()) {
            throw new IllegalArgumentException("Field with id " + templateId + " is not a template");
        }

        // Create a copy of the template
        CustomFieldDefinition copiedField = CustomFieldDefinition.builder()
                .name(template.getName())
                .label(template.getLabel())
                .description(template.getDescription())
                .fieldType(template.getFieldType())
                .dataType(template.getDataType())
                .isRequired(template.getIsRequired())
                .isUnique(template.getIsUnique())
                .defaultValue(template.getDefaultValue())
                .placeholder(template.getPlaceholder())
                .helpText(template.getHelpText())
                .displayOrder(template.getDisplayOrder())
                .groupId(template.getGroupId())
                .organizationId(targetOrganizationId)
                .supportOrganizationId(template.getSupportOrganizationId())
                .version(1)
                .isActive(true)
                .isTemplate(false) // Copied fields are not templates
                .templateId(templateId) // Reference to original template
                .validationRules(template.getValidationRules())
                .fieldOptions(template.getFieldOptions())
                .build();

        CustomFieldDefinition savedField = repository.save(copiedField);
        return mapper.toDTO(savedField);
    }

    public CustomFieldDefinitionDTO updateField(Long id, CustomFieldDefinitionDTO fieldDTO) {
        CustomFieldDefinition existingField = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Field definition not found with id: " + id));

        // Update fields from DTO
        if (fieldDTO.getName() != null) {
            existingField.setName(fieldDTO.getName());
        }
        if (fieldDTO.getLabel() != null) {
            existingField.setLabel(fieldDTO.getLabel());
        }
        if (fieldDTO.getDescription() != null) {
            existingField.setDescription(fieldDTO.getDescription());
        }
        if (fieldDTO.getFieldType() != null) {
            existingField.setFieldType(fieldDTO.getFieldType());
        }
        if (fieldDTO.getDataType() != null) {
            existingField.setDataType(fieldDTO.getDataType());
        }
        if (fieldDTO.getIsRequired() != null) {
            existingField.setIsRequired(fieldDTO.getIsRequired());
        }
        if (fieldDTO.getIsUnique() != null) {
            existingField.setIsUnique(fieldDTO.getIsUnique());
        }
        if (fieldDTO.getDefaultValue() != null) {
            existingField.setDefaultValue(fieldDTO.getDefaultValue());
        }
        if (fieldDTO.getPlaceholder() != null) {
            existingField.setPlaceholder(fieldDTO.getPlaceholder());
        }
        if (fieldDTO.getHelpText() != null) {
            existingField.setHelpText(fieldDTO.getHelpText());
        }
        if (fieldDTO.getDisplayOrder() != null) {
            existingField.setDisplayOrder(fieldDTO.getDisplayOrder());
        }
        if (fieldDTO.getGroupId() != null) {
            existingField.setGroupId(fieldDTO.getGroupId());
        }
        if (fieldDTO.getIsActive() != null) {
            existingField.setIsActive(fieldDTO.getIsActive());
        }
        if (fieldDTO.getValidationRules() != null) {
            existingField.setValidationRules(fieldDTO.getValidationRules());
        }
        if (fieldDTO.getFieldOptions() != null) {
            existingField.setFieldOptions(fieldDTO.getFieldOptions());
        }

        repository.update(existingField);
        return mapper.toDTO(existingField);
    }

    public CustomFieldDefinitionDTO createField(CustomFieldDefinitionDTO fieldDTO) {
        // Use the save method to trigger AuditAspect
        return save(fieldDTO);
    }

    public List<CustomFieldDefinitionDTO> findByFieldType(String fieldType) {
        List<CustomFieldDefinition> definitions = repository.findByFieldType(fieldType);
        return definitions.stream()
                .map(mapper::toDTO)
                .toList();
    }

    public Optional<CustomFieldDefinitionDTO> findByNameAndOrganizationId(String name, Long organizationId) {
        return repository.findByNameAndOrganizationId(name, organizationId)
                .map(mapper::toDTO);
    }

    private void validateFieldTypeAndDataType(String fieldType, String dataType) {
        try {
            CustomFieldType.valueOf(fieldType.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid field type: " + fieldType);
        }

        try {
            CustomFieldDataType.valueOf(dataType.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid data type: " + dataType);
        }
    }
} 