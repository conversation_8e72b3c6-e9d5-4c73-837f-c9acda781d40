package io.sx.customfields.controller;

import io.sx.customfields.dto.CustomFieldDefinitionDTO;
import io.sx.customfields.dto.CreateCustomFieldDefinitionRequest;
import io.sx.customfields.dto.UpdateCustomFieldDefinitionRequest;
import io.sx.customfields.service.CustomFieldDefinitionService;
import io.sx.context.RequestContext;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@Slf4j
@RequestMapping("/api/custom-fields/definitions")
public class CustomFieldDefinitionController {

    private final CustomFieldDefinitionService customFieldDefinitionService;

    @Autowired
    public CustomFieldDefinitionController(CustomFieldDefinitionService customFieldDefinitionService) {
        this.customFieldDefinitionService = customFieldDefinitionService;
    }

    @GetMapping
    public ResponseEntity<List<CustomFieldDefinitionDTO>> getCustomFieldDefinitions(
            @RequestParam(required = false) Long organizationId) {
        try {
            Long targetOrgId = organizationId != null ? organizationId : RequestContext.getOrgId();
            List<CustomFieldDefinitionDTO> definitions = customFieldDefinitionService.findByOrganizationId(targetOrgId);
            return ResponseEntity.ok(definitions);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<CustomFieldDefinitionDTO> getCustomFieldDefinition(@PathVariable Long id) {
        try {
            CustomFieldDefinitionDTO definition = customFieldDefinitionService.findById(id);
            return ResponseEntity.ok(definition);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping
    public ResponseEntity<CustomFieldDefinitionDTO> createCustomFieldDefinition(
            @Valid @RequestBody CreateCustomFieldDefinitionRequest request) {
        try {
            // Convert request to DTO and use save method to trigger AuditAspect
            CustomFieldDefinitionDTO fieldDTO = CustomFieldDefinitionDTO.builder()
                    .name(request.getName())
                    .label(request.getLabel())
                    .description(request.getDescription())
                    .fieldType(request.getFieldType())
                    .dataType(request.getDataType())
                    .isRequired(request.getIsRequired())
                    .isUnique(request.getIsUnique())
                    .defaultValue(request.getDefaultValue())
                    .placeholder(request.getPlaceholder())
                    .helpText(request.getHelpText())
                    .displayOrder(request.getDisplayOrder())
                    .groupId(request.getGroupId())
                    .version(request.getVersion() != null ? request.getVersion() : 1)
                    .isTemplate(request.getIsTemplate())
                    .isActive(request.getIsActive())
                    .templateId(request.getTemplateId())
                    .validationRules(request.getValidationRules())
                    .fieldOptions(request.getFieldOptions())
                    .organizationId(RequestContext.getOrgId())
                    .supportOrganizationId(RequestContext.getSupportOrgId())
                    .build();
            
            CustomFieldDefinitionDTO createdDefinition = customFieldDefinitionService.save(fieldDTO);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(createdDefinition);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            // Add logging
             log.error("Error creating custom field definition", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<CustomFieldDefinitionDTO> updateCustomFieldDefinition(
            @PathVariable Long id,
            @Valid @RequestBody UpdateCustomFieldDefinitionRequest request) {
        try {
            // Convert request to DTO and use update method to trigger AuditAspect
            CustomFieldDefinitionDTO fieldDTO = CustomFieldDefinitionDTO.builder()
                    .name(request.getName())
                    .label(request.getLabel())
                    .description(request.getDescription())
                    .fieldType(request.getFieldType())
                    .dataType(request.getDataType())
                    .isRequired(request.getIsRequired())
                    .isUnique(request.getIsUnique())
                    .defaultValue(request.getDefaultValue())
                    .placeholder(request.getPlaceholder())
                    .helpText(request.getHelpText())
                    .displayOrder(request.getDisplayOrder())
                    .groupId(request.getGroupId())
                    .isActive(request.getIsActive())
                    .templateId(request.getTemplateId())
                    .validationRules(request.getValidationRules())
                    .fieldOptions(request.getFieldOptions())
                    .build();
            
            Long updatedId = customFieldDefinitionService.update(id, fieldDTO);
            CustomFieldDefinitionDTO updatedDefinition = customFieldDefinitionService.findById(updatedId);
            return ResponseEntity.ok(updatedDefinition);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCustomFieldDefinition(@PathVariable Long id) {
        try {
            customFieldDefinitionService.deleteField(id);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/{id}/activate")
    public ResponseEntity<Void> activateCustomFieldDefinition(@PathVariable Long id) {
        try {
            customFieldDefinitionService.activateField(id);
            return ResponseEntity.ok().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/{id}/deactivate")
    public ResponseEntity<Void> deactivateCustomFieldDefinition(@PathVariable Long id) {
        try {
            customFieldDefinitionService.deactivateField(id);
            return ResponseEntity.ok().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/templates")
    public ResponseEntity<List<CustomFieldDefinitionDTO>> getTemplates(
            @RequestParam(required = false) Boolean isActive) {
        try {
            List<CustomFieldDefinitionDTO> templates = customFieldDefinitionService.findTemplates(isActive);
            return ResponseEntity.ok(templates);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping("/templates/{templateId}/copy")
    public ResponseEntity<CustomFieldDefinitionDTO> copyFromTemplate(
            @PathVariable Long templateId,
            @RequestParam Long targetOrganizationId) {
        try {
            CustomFieldDefinitionDTO copiedDefinition = customFieldDefinitionService.copyFromTemplate(templateId, targetOrganizationId);
            return ResponseEntity.status(HttpStatus.CREATED).body(copiedDefinition);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/reorder")
    public ResponseEntity<Void> reorderCustomFieldDefinitions(
            @RequestBody List<Long> fieldIds) {
        try {
            customFieldDefinitionService.reorderFields(fieldIds);
            return ResponseEntity.ok().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/by-type/{fieldType}")
    public ResponseEntity<List<CustomFieldDefinitionDTO>> getCustomFieldDefinitionsByType(
            @PathVariable String fieldType,
            @RequestParam(required = false) Long organizationId) {
        try {
            Long targetOrgId = organizationId != null ? organizationId : RequestContext.getOrgId();
            List<CustomFieldDefinitionDTO> definitions = customFieldDefinitionService.findByFieldType(fieldType);
            return ResponseEntity.ok(definitions);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/by-name/{name}")
    public ResponseEntity<CustomFieldDefinitionDTO> getCustomFieldDefinitionByName(
            @PathVariable String name,
            @RequestParam(required = false) Long organizationId) {
        try {
            Long targetOrgId = organizationId != null ? organizationId : RequestContext.getOrgId();
            Optional<CustomFieldDefinitionDTO> definitionOpt = customFieldDefinitionService.findByNameAndOrganizationId(name, targetOrgId);
            if (definitionOpt.isPresent()) {
                return ResponseEntity.ok(definitionOpt.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/active")
    public ResponseEntity<List<CustomFieldDefinitionDTO>> getActiveCustomFieldDefinitions(
            @RequestParam(required = false) Long organizationId) {
        try {
            Long targetOrgId = organizationId != null ? organizationId : RequestContext.getOrgId();
            List<CustomFieldDefinitionDTO> definitions = customFieldDefinitionService.findByOrganizationIdAndIsActive(targetOrgId, true);
            return ResponseEntity.ok(definitions);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/by-group/{groupId}")
    public ResponseEntity<List<CustomFieldDefinitionDTO>> getCustomFieldDefinitionsByGroup(@PathVariable Long groupId) {
        try {
            List<CustomFieldDefinitionDTO> definitions = customFieldDefinitionService.findByGroupId(groupId);
            return ResponseEntity.ok(definitions);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
} 