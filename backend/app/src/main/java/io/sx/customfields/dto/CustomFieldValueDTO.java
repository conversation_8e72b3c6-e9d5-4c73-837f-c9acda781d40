package io.sx.customfields.dto;

import io.sx.dto.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomFieldValueDTO extends BaseDTO {
    private Long fieldDefinitionId;
    private Long ticketId;
    private String valueText;
    private BigDecimal valueNumber;
    private Boolean valueBoolean;
    private LocalDate valueDate;
    private LocalDateTime valueTimestamp;
    private Map<String, Object> valueJson;
} 