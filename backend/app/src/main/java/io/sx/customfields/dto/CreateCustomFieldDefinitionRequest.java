package io.sx.customfields.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Map;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CreateCustomFieldDefinitionRequest {
    @NotBlank(message = "Field name is required")
    @Size(max = 255, message = "Field name must not exceed 255 characters")
    private String name;

    @NotBlank(message = "Field label is required")
    @Size(max = 255, message = "Field label must not exceed 255 characters")
    private String label;

    private String description;

    @NotBlank(message = "Field type is required")
    private String fieldType; // text, number, date, dropdown, checkbox, multi_select

    @NotBlank(message = "Data type is required")
    private String dataType; // string, integer, decimal, boolean, date, timestamp

    private Boolean isRequired = false;
    private Boolean isUnique = false;
    private String defaultValue;
    private String placeholder;
    private String helpText;
    private Integer displayOrder = 0;
    private Long groupId;
    private Integer version = 1;
    private Boolean isTemplate = false;
    private Boolean isActive = true;
    private Long templateId;
    private Map<String, Object> validationRules;
    private Map<String, Object> fieldOptions;
} 