package io.sx.customfields.repository;

import io.sx.repository.BaseRepository;
import io.sx.model.customfields.CustomFieldDefinition;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomFieldDefinitionRepository extends BaseRepository<CustomFieldDefinition> {
    List<CustomFieldDefinition> findAll();
    Optional<CustomFieldDefinition> findById(Long id);
    List<CustomFieldDefinition> findByOrganizationId(Long organizationId);
    List<CustomFieldDefinition> findBySupportOrganizationId(Long supportOrganizationId);
    List<CustomFieldDefinition> findByOrganizationIdAndIsActiveOrderByDisplayOrder(Long organizationId, Boolean isActive);
    List<CustomFieldDefinition> findByGroupId(Long groupId);
    List<CustomFieldDefinition> findByTemplateId(Long templateId);
    List<CustomFieldDefinition> findByIsTemplateAndIsActive(Boolean isTemplate, Boolean isActive);
    Optional<CustomFieldDefinition> findByNameAndOrganizationId(String name, Long organizationId);
    List<CustomFieldDefinition> findByFieldType(String fieldType);
    List<CustomFieldDefinition> findByIsTemplateTrue();
} 