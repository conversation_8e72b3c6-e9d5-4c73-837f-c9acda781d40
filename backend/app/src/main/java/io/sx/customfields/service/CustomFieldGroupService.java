package io.sx.customfields.service;

import io.sx.base.service.BaseService;
import io.sx.customfields.dto.CustomFieldGroupDTO;
import io.sx.model.customfields.CustomFieldGroup;

import java.util.List;
import java.util.Optional;

public interface CustomFieldGroupService extends BaseService<CustomFieldGroup, CustomFieldGroupDTO> {
    List<CustomFieldGroupDTO> findByOrganizationId(Long organizationId);
    List<CustomFieldGroupDTO> findBySupportOrganizationId(Long supportOrganizationId);
    List<CustomFieldGroupDTO> findByOrganizationIdOrderByDisplayOrder(Long organizationId);
    Optional<CustomFieldGroupDTO> findByNameAndOrganizationId(String name, Long organizationId);
    void deleteGroup(Long id);
    void reorderGroups(List<Long> groupIds);
} 