package io.sx.customfields.service.impl;

import io.sx.base.service.impl.BaseServiceImpl;
import io.sx.customfields.dto.CustomFieldValueDTO;
import io.sx.customfields.mapper.CustomFieldValueMapper;
import io.sx.customfields.repository.CustomFieldValueRepository;
import io.sx.customfields.service.CustomFieldValueService;
import io.sx.model.customfields.CustomFieldValue;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Transactional
public class CustomFieldValueServiceImpl extends BaseServiceImpl<CustomFieldValue, CustomFieldValueDTO> 
        implements CustomFieldValueService {

    private final CustomFieldValueRepository repository;
    private final CustomFieldValueMapper mapper;

    public CustomFieldValueServiceImpl(CustomFieldValueRepository repository, CustomFieldValueMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    public List<CustomFieldValueDTO> findByTicketId(Long ticketId) {
        List<CustomFieldValue> values = repository.findByTicketId(ticketId);
        return values.stream()
                .map(mapper::toDTO)
                .toList();
    }

    public CustomFieldValueDTO saveValue(CustomFieldValueDTO valueDTO, Long createdById, String createdBy) {
        // Validate that the value doesn't already exist for this field and ticket
        repository.findByFieldDefinitionIdAndTicketId(valueDTO.getFieldDefinitionId(), valueDTO.getTicketId())
                .ifPresent(existingValue -> {
                    throw new IllegalArgumentException("A value already exists for field " + valueDTO.getFieldDefinitionId() + " and ticket " + valueDTO.getTicketId());
                });

        CustomFieldValue value = mapper.toEntity(valueDTO);
        // Let AuditAspect handle audit field population

        CustomFieldValue savedValue = repository.save(value);
        return mapper.toDTO(savedValue);
    }

    public List<CustomFieldValueDTO> saveValuesForTicket(Long ticketId, List<CustomFieldValueDTO> values, Long createdById, String createdBy) {
        // First, delete any existing values for this ticket
        repository.deleteByTicketId(ticketId);

        // Then save the new values
        List<CustomFieldValueDTO> savedValues = values.stream()
                .map(valueDTO -> {
                    CustomFieldValue value = mapper.toEntity(valueDTO);
                    value.setTicketId(ticketId); // Ensure ticket ID is set
                    // Let AuditAspect handle audit field population
                    
                    CustomFieldValue savedValue = repository.save(value);
                    return mapper.toDTO(savedValue);
                })
                .toList();

        return savedValues;
    }

    public void deleteByTicketId(Long ticketId) {
        repository.deleteByTicketId(ticketId);
    }

    public CustomFieldValueDTO updateValue(Long valueId, CustomFieldValueDTO valueDTO, Long updatedById, String updatedBy) {
        CustomFieldValue existingValue = repository.findById(valueId)
                .orElseThrow(() -> new IllegalArgumentException("Custom field value not found with id: " + valueId));

        // Update the value fields
        if (valueDTO.getValueText() != null) {
            existingValue.setValueText(valueDTO.getValueText());
        }
        if (valueDTO.getValueNumber() != null) {
            existingValue.setValueNumber(valueDTO.getValueNumber());
        }
        if (valueDTO.getValueBoolean() != null) {
            existingValue.setValueBoolean(valueDTO.getValueBoolean());
        }
        if (valueDTO.getValueDate() != null) {
            existingValue.setValueDate(valueDTO.getValueDate());
        }
        if (valueDTO.getValueTimestamp() != null) {
            existingValue.setValueTimestamp(valueDTO.getValueTimestamp());
        }
        if (valueDTO.getValueJson() != null) {
            existingValue.setValueJson(valueDTO.getValueJson());
        }

        // Let AuditAspect handle audit field updates
        repository.update(existingValue);
        return mapper.toDTO(existingValue);
    }

    public List<CustomFieldValueDTO> findByFieldDefinitionId(Long fieldDefinitionId) {
        List<CustomFieldValue> values = repository.findByFieldDefinitionId(fieldDefinitionId);
        return values.stream()
                .map(mapper::toDTO)
                .toList();
    }

    public Optional<CustomFieldValueDTO> findByFieldDefinitionIdAndTicketId(Long fieldDefinitionId, Long ticketId) {
        return repository.findByFieldDefinitionIdAndTicketId(fieldDefinitionId, ticketId)
                .map(mapper::toDTO);
    }

    @Override
    public List<CustomFieldValueDTO> findAll() {
        List<CustomFieldValue> values = repository.findAll(null);
        return values.stream()
                .map(mapper::toDTO)
                .toList();
    }

    @Override
    public CustomFieldValueDTO findById(Long id) {
        CustomFieldValue value = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Custom field value not found with id: " + id));
        return mapper.toDTO(value);
    }

    @Override
    public CustomFieldValueDTO save(CustomFieldValueDTO dto) {
        CustomFieldValue entity = mapper.toEntity(dto);
        CustomFieldValue saved = repository.save(entity);
        return mapper.toDTO(saved);
    }

    @Override
    public Long update(Long id, CustomFieldValueDTO dto) {
        CustomFieldValue entity = mapper.toEntity(dto);
        entity.setId(id);
        return repository.update(entity);
    }

    @Override
    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    public List<CustomFieldValueDTO> getValuesForTicket(Long ticketId) {
        return findByTicketId(ticketId);
    }

    public void deleteByFieldDefinitionId(Long fieldDefinitionId) {
        repository.deleteByFieldDefinitionId(fieldDefinitionId);
    }

    public Map<Long, CustomFieldValueDTO> saveValuesForTicket(Long ticketId, Map<Long, Object> fieldValues) {
        // First, delete any existing values for this ticket
        repository.deleteByTicketId(ticketId);

        // Then save the new values
        Map<Long, CustomFieldValueDTO> savedValues = new java.util.HashMap<>();
        
        fieldValues.forEach((fieldDefinitionId, value) -> {
            CustomFieldValue valueEntity = CustomFieldValue.builder()
                    .fieldDefinitionId(fieldDefinitionId)
                    .ticketId(ticketId)
                    .valueText(value instanceof String ? (String) value : null)
                    .valueNumber(value instanceof Number ? new java.math.BigDecimal(value.toString()) : null)
                    .valueBoolean(value instanceof Boolean ? (Boolean) value : null)
                    .build();

            CustomFieldValue savedValue = repository.save(valueEntity);
            savedValues.put(fieldDefinitionId, mapper.toDTO(savedValue));
        });

        return savedValues;
    }

    public CustomFieldValueDTO saveValue(CustomFieldValueDTO valueDTO) {
        CustomFieldValue value = mapper.toEntity(valueDTO);
        CustomFieldValue savedValue = repository.save(value);
        return mapper.toDTO(savedValue);
    }

    public List<CustomFieldValueDTO> findBySupportOrganizationId(Long supportOrganizationId) {
        List<CustomFieldValue> values = repository.findBySupportOrganizationId(supportOrganizationId);
        return values.stream()
                .map(mapper::toDTO)
                .toList();
    }

    public List<CustomFieldValueDTO> findByOrganizationId(Long organizationId) {
        List<CustomFieldValue> values = repository.findByOrganizationId(organizationId);
        return values.stream()
                .map(mapper::toDTO)
                .toList();
    }
} 