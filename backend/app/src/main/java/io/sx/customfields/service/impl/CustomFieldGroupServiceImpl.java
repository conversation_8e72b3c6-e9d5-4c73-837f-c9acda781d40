package io.sx.customfields.service.impl;

import io.sx.base.service.impl.BaseServiceImpl;
import io.sx.customfields.dto.CustomFieldGroupDTO;
import io.sx.customfields.mapper.CustomFieldGroupMapper;
import io.sx.customfields.repository.CustomFieldGroupRepository;
import io.sx.customfields.service.CustomFieldGroupService;
import io.sx.exception.NotFoundException;
import io.sx.model.customfields.CustomFieldGroup;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class CustomFieldGroupServiceImpl extends BaseServiceImpl<CustomFieldGroup, CustomFieldGroupDTO> implements CustomFieldGroupService {

    private final CustomFieldGroupRepository customFieldGroupRepository;
    private final CustomFieldGroupMapper customFieldGroupMapper;

    @Override
    public CustomFieldGroupDTO findById(Long id) {
        Optional<CustomFieldGroup> entity = customFieldGroupRepository.findById(id);
        return entity.map(customFieldGroupMapper::toDTO)
                .orElseThrow(() -> new NotFoundException("Custom field group not found with id: " + id));
    }

    @Override
    public List<CustomFieldGroupDTO> findAll() {
        List<CustomFieldGroup> entities = customFieldGroupRepository.findAll();
        return entities.stream()
                .map(customFieldGroupMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteById(Long id) {
        if (!customFieldGroupRepository.findById(id).isPresent()) {
            throw new NotFoundException("Custom field group not found with id: " + id);
        }
        customFieldGroupRepository.deleteById(id);
    }

    @Override
    public List<CustomFieldGroupDTO> findByOrganizationId(Long organizationId) {
        List<CustomFieldGroup> entities = customFieldGroupRepository.findByOrganizationId(organizationId);
        return entities.stream()
                .map(customFieldGroupMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<CustomFieldGroupDTO> findBySupportOrganizationId(Long supportOrganizationId) {
        List<CustomFieldGroup> entities = customFieldGroupRepository.findBySupportOrganizationId(supportOrganizationId);
        return entities.stream()
                .map(customFieldGroupMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<CustomFieldGroupDTO> findByOrganizationIdOrderByDisplayOrder(Long organizationId) {
        List<CustomFieldGroup> entities = customFieldGroupRepository.findByOrganizationIdOrderByDisplayOrder(organizationId);
        return entities.stream()
                .map(customFieldGroupMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<CustomFieldGroupDTO> findByNameAndOrganizationId(String name, Long organizationId) {
        Optional<CustomFieldGroup> entity = customFieldGroupRepository.findByNameAndOrganizationId(name, organizationId);
        return entity.map(customFieldGroupMapper::toDTO);
    }

    @Override
    public CustomFieldGroupDTO save(CustomFieldGroupDTO groupDTO) {
        // Check if group with same name already exists
        Optional<CustomFieldGroupDTO> existingGroup = findByNameAndOrganizationId(
                groupDTO.getName(), groupDTO.getOrganizationId());
        if (existingGroup.isPresent()) {
            throw new IllegalArgumentException("Group with name '" + groupDTO.getName() + "' already exists in this organization");
        }

        CustomFieldGroup entity = customFieldGroupMapper.toEntity(groupDTO);
        // Let AuditAspect handle audit field population
        CustomFieldGroup savedEntity = customFieldGroupRepository.save(entity);
        return customFieldGroupMapper.toDTO(savedEntity);
    }

    @Override
    public Long update(Long id, CustomFieldGroupDTO groupDTO) {
        // Check if group exists
        findById(id); // This will throw NotFoundException if not found
        
        // Check if name is being changed and if new name conflicts
        CustomFieldGroupDTO existingGroup = findById(id);
        if (!existingGroup.getName().equals(groupDTO.getName())) {
            Optional<CustomFieldGroupDTO> conflictingGroup = findByNameAndOrganizationId(
                    groupDTO.getName(), groupDTO.getOrganizationId());
            if (conflictingGroup.isPresent() && !conflictingGroup.get().getId().equals(id)) {
                throw new IllegalArgumentException("Group with name '" + groupDTO.getName() + "' already exists in this organization");
            }
        }

        CustomFieldGroup existingEntity = customFieldGroupRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Custom field group not found with id: " + id));

        CustomFieldGroup entity = customFieldGroupMapper.toEntity(groupDTO);
        entity.setId(id);
        // Preserve original audit fields for updates
        entity.setCreatedAt(existingEntity.getCreatedAt());
        entity.setCreatedById(existingEntity.getCreatedById());
        entity.setCreatedBy(existingEntity.getCreatedBy());
        // Let AuditAspect handle updated audit fields

        return customFieldGroupRepository.update(entity);
    }

    @Override
    public void deleteGroup(Long id) {
        deleteById(id);
    }

    @Override
    public void reorderGroups(List<Long> groupIds) {
        for (int i = 0; i < groupIds.size(); i++) {
            Long groupId = groupIds.get(i);
            CustomFieldGroupDTO groupDTO = findById(groupId);
            groupDTO.setDisplayOrder(i);
            CustomFieldGroup existingEntity = customFieldGroupRepository.findById(groupId)
                    .orElseThrow(() -> new NotFoundException("Custom field group not found with id: " + groupId));

            CustomFieldGroup entity = customFieldGroupMapper.toEntity(groupDTO);
            entity.setId(groupId);
            // Preserve original audit fields for updates
            entity.setCreatedAt(existingEntity.getCreatedAt());
            entity.setCreatedById(existingEntity.getCreatedById());
            entity.setCreatedBy(existingEntity.getCreatedBy());
            // Let AuditAspect handle updated audit fields

            CustomFieldGroup updatedEntity = customFieldGroupRepository.save(entity);
        }
    }
} 