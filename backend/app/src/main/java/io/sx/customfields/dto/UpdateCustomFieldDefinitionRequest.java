package io.sx.customfields.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Size;
import java.util.Map;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCustomFieldDefinitionRequest {
    @Size(max = 255, message = "Field name must not exceed 255 characters")
    private String name;

    @Size(max = 255, message = "Field label must not exceed 255 characters")
    private String label;

    private String description;
    private String fieldType; // text, number, date, dropdown, checkbox, multi_select
    private String dataType; // string, integer, decimal, boolean, date, timestamp
    private Boolean isRequired;
    private Boolean isUnique;
    private String defaultValue;
    private String placeholder;
    private String helpText;
    private Integer displayOrder;
    private Long groupId;
    private Boolean isActive;
    private Long templateId;
    private Map<String, Object> validationRules;
    private Map<String, Object> fieldOptions;
} 