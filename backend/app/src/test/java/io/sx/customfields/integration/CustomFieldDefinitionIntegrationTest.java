package io.sx.customfields.integration;

import io.sx.OrganizationDbOperations;
import io.sx.config.TestConfig;
import io.sx.config.TestcontainersConfig;
import io.sx.context.RequestContext;
import io.sx.customfields.dto.CustomFieldDefinitionDTO;
import io.sx.customfields.service.CustomFieldDefinitionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Import({TestConfig.class, TestcontainersConfig.class})
@ActiveProfiles("test")
@Transactional
class CustomFieldDefinitionIntegrationTest {
    
    @Autowired
    private CustomFieldDefinitionService customFieldDefinitionService;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    private Long testOrgId;
    private Long testSupportOrgId;
    private Long testUserId;
    
    @BeforeEach
    void setUp() {
        testOrgId = OrganizationDbOperations.createOrganizationInDb(jdbcTemplate);
        testSupportOrgId = testOrgId;
        testUserId = 1L;
        
        RequestContext.setOrgId(testOrgId);
        RequestContext.setSupportOrgId(testSupportOrgId);
        RequestContext.setUserId(testUserId);
        
        // Set up mock authentication
        TestingAuthenticationToken auth = new TestingAuthenticationToken("testuser", "password", "ROLE_USER");
        SecurityContextHolder.getContext().setAuthentication(auth);
    }
    
    @Test
    void testCreateCustomFieldDefinition_WithAuditAspect() {
        // Given
        CustomFieldDefinitionDTO fieldDTO = CustomFieldDefinitionDTO.builder()
                .name("test_field")
                .label("Test Field")
                .description("A test field for audit aspect testing")
                .fieldType("text")
                .dataType("string")
                .isRequired(false)
                .isUnique(false)
                .displayOrder(1)
                .isTemplate(false)
                .version(1)
                .build(); // Note: no organizationId set
        
        // When
        CustomFieldDefinitionDTO createdField = customFieldDefinitionService.save(fieldDTO);
        
        // Then
        assertNotNull(createdField);
        assertNotNull(createdField.getId());
        assertEquals("test_field", createdField.getName());
        assertEquals("Test Field", createdField.getLabel());
        assertEquals("A test field for audit aspect testing", createdField.getDescription());
        assertEquals("text", createdField.getFieldType());
        assertEquals("string", createdField.getDataType());
        assertEquals(1, createdField.getDisplayOrder());
        
        // Verify that AuditAspect populated the organization fields
        assertNotNull(createdField.getOrganizationId(), "OrganizationId should be populated by AuditAspect");
        assertNotNull(createdField.getSupportOrganizationId(), "SupportOrganizationId should be populated by AuditAspect");
        assertEquals(testOrgId, createdField.getOrganizationId());
        assertEquals(testSupportOrgId, createdField.getSupportOrganizationId());
        
        // Verify that AuditAspect populated the audit fields
        assertNotNull(createdField.getCreatedById(), "CreatedById should be populated by AuditAspect");
        assertNotNull(createdField.getCreatedBy(), "CreatedBy should be populated by AuditAspect");
        assertNotNull(createdField.getCreatedAt(), "CreatedAt should be populated by AuditAspect");
        assertNotNull(createdField.getUpdatedAt(), "UpdatedAt should be populated by AuditAspect");
        assertEquals(testUserId, createdField.getCreatedById());
        assertEquals("testuser", createdField.getCreatedBy()); // Assert createdBy
        assertEquals(testUserId, createdField.getUpdatedById());
    }
    
    @Test
    void testUpdateCustomFieldDefinition_WithAuditAspect() {
        // Given - Create a field first
        CustomFieldDefinitionDTO originalField = CustomFieldDefinitionDTO.builder()
                .name("original_field")
                .label("Original Field")
                .description("Original description")
                .fieldType("text")
                .dataType("string")
                .isRequired(false)
                .isUnique(false)
                .displayOrder(1)
                .isTemplate(false)
                .version(1)
                .build();
        CustomFieldDefinitionDTO createdField = customFieldDefinitionService.save(originalField);
        assertNotNull(createdField.getId());
        
        // When - Update the field
        CustomFieldDefinitionDTO updateDTO = CustomFieldDefinitionDTO.builder()
                .name("updated_field")
                .label("Updated Field")
                .description("Updated description")
                .fieldType("text")
                .dataType("string")
                .displayOrder(2)
                .version(createdField.getVersion() + 1)
                .build();
        Long updatedId = customFieldDefinitionService.update(createdField.getId(), updateDTO);
        
        // Then
        assertEquals(createdField.getId(), updatedId);
        
        // Retrieve the updated field
        CustomFieldDefinitionDTO updatedField = customFieldDefinitionService.findById(updatedId);
        assertNotNull(updatedField);
        assertEquals("updated_field", updatedField.getName());
        assertEquals("Updated Field", updatedField.getLabel());
        assertEquals("Updated description", updatedField.getDescription());
        assertEquals(2, updatedField.getDisplayOrder());
        
        // Verify that AuditAspect preserved original audit fields
        assertEquals(createdField.getCreatedById(), updatedField.getCreatedById(), "CreatedById should be preserved");
        assertEquals(createdField.getCreatedBy(), updatedField.getCreatedBy(), "CreatedBy should be preserved");
        assertEquals(createdField.getCreatedAt(), updatedField.getCreatedAt(), "CreatedAt should be preserved");
        
        // Verify that AuditAspect updated current audit fields
        assertNotNull(updatedField.getUpdatedAt(), "UpdatedAt should be populated by AuditAspect");
        assertNotNull(updatedField.getUpdatedById(), "UpdatedById should be populated by AuditAspect");
        assertNotNull(updatedField.getUpdatedBy(), "UpdatedBy should be populated by AuditAspect");
        assertEquals(testUserId, updatedField.getUpdatedById());
        assertEquals("testuser", updatedField.getUpdatedBy()); // Assert updatedBy
        
        // Verify that the updated timestamp is newer than the created timestamp
        assertTrue(updatedField.getUpdatedAt().isAfter(updatedField.getCreatedAt()), 
                "UpdatedAt should be newer than CreatedAt");
    }
    
    @Test
    void testCreateCustomFieldDefinition_WithoutOrganizationId_ShouldUseRequestContext() {
        // Given
        CustomFieldDefinitionDTO fieldDTO = CustomFieldDefinitionDTO.builder()
                .name("context_field")
                .label("Context Field")
                .description("A field that relies on RequestContext")
                .fieldType("text")
                .dataType("string")
                .isRequired(false)
                .isUnique(false)
                .displayOrder(1)
                .isTemplate(false)
                .version(1)
                .build(); // No organizationId set
        
        // When
        CustomFieldDefinitionDTO createdField = customFieldDefinitionService.save(fieldDTO);
        
        // Then
        assertNotNull(createdField);
        assertEquals(testOrgId, createdField.getOrganizationId());
        assertEquals(testSupportOrgId, createdField.getSupportOrganizationId());
        assertEquals(testUserId, createdField.getCreatedById());
    }
    
    @Test
    void testCreateCustomFieldDefinition_WithOrganizationId_ShouldPreserveIt() {
        // Given
        CustomFieldDefinitionDTO fieldDTO = CustomFieldDefinitionDTO.builder()
                .name("preserved_field")
                .label("Preserved Field")
                .description("A field with pre-set organizationId")
                .fieldType("text")
                .dataType("string")
                .isRequired(false)
                .isUnique(false)
                .displayOrder(1)
                .isTemplate(false)
                .version(1)
                .organizationId(testOrgId) // Pre-set organizationId
                .build();
        
        // When
        CustomFieldDefinitionDTO createdField = customFieldDefinitionService.save(fieldDTO);
        
        // Then
        assertNotNull(createdField);
        assertEquals(testOrgId, createdField.getOrganizationId(), 
                "Pre-set organizationId should be preserved");
        assertEquals(testUserId, createdField.getCreatedById(), 
                "CreatedById should still be populated by AuditAspect");
    }
    
    @Test
    void testCreateCustomFieldDefinition_WithoutAnyOrganizationFields_ShouldUseRequestContext() {
        // Given - This simulates what the frontend should send (no organization fields)
        CustomFieldDefinitionDTO fieldDTO = CustomFieldDefinitionDTO.builder()
                .name("no_org_fields_field")
                .label("No Org Fields Field")
                .description("A field with no organization fields set")
                .fieldType("text")
                .dataType("string")
                .isRequired(false)
                .isUnique(false)
                .displayOrder(1)
                .isTemplate(false)
                .version(1)
                .build(); // No organizationId or supportOrganizationId set
        
        // When
        CustomFieldDefinitionDTO createdField = customFieldDefinitionService.save(fieldDTO);
        
        // Then
        assertNotNull(createdField);
        assertNotNull(createdField.getId());
        assertEquals("no_org_fields_field", createdField.getName());
        
        // Verify that AuditAspect populated the organization fields from RequestContext
        assertNotNull(createdField.getOrganizationId(), "OrganizationId should be populated by AuditAspect");
        assertNotNull(createdField.getSupportOrganizationId(), "SupportOrganizationId should be populated by AuditAspect");
        assertEquals(testOrgId, createdField.getOrganizationId(), "Should use RequestContext orgId");
        assertEquals(testSupportOrgId, createdField.getSupportOrganizationId(), "Should use RequestContext supportOrgId");
        
        // Verify that AuditAspect populated the audit fields
        assertNotNull(createdField.getCreatedById(), "CreatedById should be populated by AuditAspect");
        assertNotNull(createdField.getCreatedBy(), "CreatedBy should be populated by AuditAspect");
        assertNotNull(createdField.getCreatedAt(), "CreatedAt should be populated by AuditAspect");
        assertNotNull(createdField.getUpdatedAt(), "UpdatedAt should be populated by AuditAspect");
        assertEquals(testUserId, createdField.getCreatedById());
        assertEquals("testuser", createdField.getCreatedBy()); // From mock authentication
    }
} 