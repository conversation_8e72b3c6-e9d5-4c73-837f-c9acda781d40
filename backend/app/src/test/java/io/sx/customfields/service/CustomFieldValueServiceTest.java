package io.sx.customfields.service;

import io.sx.customfields.dto.CustomFieldValueDTO;
import io.sx.customfields.mapper.CustomFieldValueMapper;
import io.sx.customfields.repository.CustomFieldValueRepository;
import io.sx.customfields.service.impl.CustomFieldValueServiceImpl;
import io.sx.model.customfields.CustomFieldValue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomFieldValueServiceTest {

    @Mock
    private CustomFieldValueRepository repository;

    @Mock
    private CustomFieldValueMapper mapper;

    @InjectMocks
    private CustomFieldValueServiceImpl service;

    private CustomFieldValue testValue;
    private CustomFieldValueDTO testValueDTO;

    @BeforeEach
    void setUp() {
        testValue = CustomFieldValue.builder()
                .id(1L)
                .fieldDefinitionId(1L)
                .ticketId(1L)
                .valueText("test value")
                .organizationId(1L)
                .supportOrganizationId(1L)
                .build();

        testValueDTO = CustomFieldValueDTO.builder()
                .id(1L)
                .fieldDefinitionId(1L)
                .ticketId(1L)
                .valueText("test value")
                .organizationId(1L)
                .supportOrganizationId(1L)
                .build();
    }

    @Test
    void testFindById_Success() {
        // Given
        when(repository.findById(1L)).thenReturn(Optional.of(testValue));
        when(mapper.toDTO(testValue)).thenReturn(testValueDTO);

        // When
        CustomFieldValueDTO result = service.findById(1L);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("test value", result.getValueText());
        verify(repository).findById(1L);
        verify(mapper).toDTO(testValue);
    }

    @Test
    void testFindById_NotFound() {
        // Given
        when(repository.findById(999L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> service.findById(999L));
        verify(repository).findById(999L);
    }

    @Test
    void testSave_Success() {
        // Given
        when(mapper.toEntity(testValueDTO)).thenReturn(testValue);
        when(repository.save(testValue)).thenReturn(testValue);
        when(mapper.toDTO(testValue)).thenReturn(testValueDTO);

        // When
        CustomFieldValueDTO result = service.save(testValueDTO);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        verify(mapper).toEntity(testValueDTO);
        verify(repository).save(testValue);
        verify(mapper).toDTO(testValue);
    }

    @Test
    void testFindByTicketId_Success() {
        // Given
        List<CustomFieldValue> values = List.of(testValue);
        when(repository.findByTicketId(1L)).thenReturn(values);
        when(mapper.toDTO(testValue)).thenReturn(testValueDTO);

        // When
        List<CustomFieldValueDTO> result = service.findByTicketId(1L);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
        verify(repository).findByTicketId(1L);
        verify(mapper).toDTO(testValue);
    }

    @Test
    void testFindAll_Success() {
        // Given
        List<CustomFieldValue> values = List.of(testValue);
        when(repository.findAll(null)).thenReturn(values);
        when(mapper.toDTO(testValue)).thenReturn(testValueDTO);

        // When
        List<CustomFieldValueDTO> result = service.findAll();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
        verify(repository).findAll(null);
        verify(mapper).toDTO(testValue);
    }

    @Test
    void testDeleteById_Success() {
        // Given
        doNothing().when(repository).deleteById(1L);

        // When
        service.deleteById(1L);

        // Then
        verify(repository).deleteById(1L);
    }

    @Test
    void testUpdate_Success() {
        // Given
        when(mapper.toEntity(testValueDTO)).thenReturn(testValue);
        when(repository.update(testValue)).thenReturn(1L);

        // When
        Long result = service.update(1L, testValueDTO);

        // Then
        assertEquals(1L, result);
        assertEquals(1L, testValue.getId());
        verify(mapper).toEntity(testValueDTO);
        verify(repository).update(testValue);
    }

    @Test
    void testFindByFieldDefinitionIdAndTicketId_Success() {
        // Given
        when(repository.findByFieldDefinitionIdAndTicketId(1L, 1L)).thenReturn(Optional.of(testValue));
        when(mapper.toDTO(testValue)).thenReturn(testValueDTO);

        // When
        Optional<CustomFieldValueDTO> result = service.findByFieldDefinitionIdAndTicketId(1L, 1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals(1L, result.get().getId());
        verify(repository).findByFieldDefinitionIdAndTicketId(1L, 1L);
        verify(mapper).toDTO(testValue);
    }

    @Test
    void testFindByFieldDefinitionIdAndTicketId_NotFound() {
        // Given
        when(repository.findByFieldDefinitionIdAndTicketId(1L, 1L)).thenReturn(Optional.empty());

        // When
        Optional<CustomFieldValueDTO> result = service.findByFieldDefinitionIdAndTicketId(1L, 1L);

        // Then
        assertFalse(result.isPresent());
        verify(repository).findByFieldDefinitionIdAndTicketId(1L, 1L);
    }
} 