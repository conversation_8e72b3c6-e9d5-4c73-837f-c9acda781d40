package io.sx.customfields.integration;

import io.sx.OrganizationDbOperations;
import io.sx.config.TestConfig;
import io.sx.config.TestcontainersConfig;
import io.sx.context.RequestContext;
import io.sx.customfields.dto.CustomFieldGroupDTO;
import io.sx.customfields.service.CustomFieldGroupService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Import({TestConfig.class, TestcontainersConfig.class})
@ActiveProfiles("test")
@Transactional
class CustomFieldGroupIntegrationTest {

    @Autowired
    private CustomFieldGroupService customFieldGroupService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private Long testOrgId;
    private Long testSupportOrgId;
    private Long testUserId;

    @BeforeEach
    void setUp() {
        // Create test organization and set up RequestContext
        testOrgId = OrganizationDbOperations.createOrganizationInDb(jdbcTemplate);
        testSupportOrgId = testOrgId; // For simplicity, using same org as support org
        testUserId = 1L;
        
        RequestContext.setOrgId(testOrgId);
        RequestContext.setSupportOrgId(testSupportOrgId);
        RequestContext.setUserId(testUserId);
        
        // Set up mock authentication
        TestingAuthenticationToken auth = new TestingAuthenticationToken("testuser", "password", "ROLE_USER");
        SecurityContextHolder.getContext().setAuthentication(auth);
    }

    @Test
    void testCreateCustomFieldGroup_WithAuditAspect() {
        // Given
        CustomFieldGroupDTO groupDTO = CustomFieldGroupDTO.builder()
                .name("Test Group")
                .description("A test group for audit aspect testing")
                .displayOrder(1)
                .build(); // Note: no organizationId set

        // When
        CustomFieldGroupDTO createdGroup = customFieldGroupService.save(groupDTO);

        // Then
        assertNotNull(createdGroup);
        assertNotNull(createdGroup.getId());
        assertEquals("Test Group", createdGroup.getName());
        assertEquals("A test group for audit aspect testing", createdGroup.getDescription());
        assertEquals(1, createdGroup.getDisplayOrder());
        
        // Verify that AuditAspect populated the organization fields
        assertNotNull(createdGroup.getOrganizationId(), "OrganizationId should be populated by AuditAspect");
        assertNotNull(createdGroup.getSupportOrganizationId(), "SupportOrganizationId should be populated by AuditAspect");
        assertEquals(testOrgId, createdGroup.getOrganizationId());
        assertEquals(testSupportOrgId, createdGroup.getSupportOrganizationId());
        
        // Verify that AuditAspect populated the audit fields
        assertNotNull(createdGroup.getCreatedById(), "CreatedById should be populated by AuditAspect");
        assertNotNull(createdGroup.getCreatedBy(), "CreatedBy should be populated by AuditAspect");
        assertNotNull(createdGroup.getCreatedAt(), "CreatedAt should be populated by AuditAspect");
        assertNotNull(createdGroup.getUpdatedAt(), "UpdatedAt should be populated by AuditAspect");
        assertEquals(testUserId, createdGroup.getCreatedById());
        assertEquals(testUserId, createdGroup.getUpdatedById());
    }

    @Test
    void testUpdateCustomFieldGroup_WithAuditAspect() {
        // Given - Create a group first
        CustomFieldGroupDTO originalGroup = CustomFieldGroupDTO.builder()
                .name("Original Group")
                .description("Original description")
                .displayOrder(1)
                .build();

        CustomFieldGroupDTO createdGroup = customFieldGroupService.save(originalGroup);
        assertNotNull(createdGroup.getId());

        // When - Update the group
        CustomFieldGroupDTO updateDTO = CustomFieldGroupDTO.builder()
                .name("Updated Group")
                .description("Updated description")
                .displayOrder(2)
                .build();

        Long updatedId = customFieldGroupService.update(createdGroup.getId(), updateDTO);

        // Then
        assertEquals(createdGroup.getId(), updatedId);
        
        // Retrieve the updated group
        CustomFieldGroupDTO updatedGroup = customFieldGroupService.findById(updatedId);
        
        assertNotNull(updatedGroup);
        assertEquals("Updated Group", updatedGroup.getName());
        assertEquals("Updated description", updatedGroup.getDescription());
        assertEquals(2, updatedGroup.getDisplayOrder());
        
        // Verify that AuditAspect preserved original audit fields
        assertEquals(createdGroup.getCreatedById(), updatedGroup.getCreatedById(), "CreatedById should be preserved");
        assertEquals(createdGroup.getCreatedBy(), updatedGroup.getCreatedBy(), "CreatedBy should be preserved");
        assertEquals(createdGroup.getCreatedAt(), updatedGroup.getCreatedAt(), "CreatedAt should be preserved");
        
        // Verify that AuditAspect updated current audit fields
        assertNotNull(updatedGroup.getUpdatedAt(), "UpdatedAt should be populated by AuditAspect");
        assertNotNull(updatedGroup.getUpdatedById(), "UpdatedById should be populated by AuditAspect");
        assertEquals(testUserId, updatedGroup.getUpdatedById());
        
        // Verify that the updated timestamp is newer than the created timestamp
        assertTrue(updatedGroup.getUpdatedAt().isAfter(updatedGroup.getCreatedAt()), 
                "UpdatedAt should be newer than CreatedAt");
    }

    @Test
    void testCreateCustomFieldGroup_WithoutOrganizationId_ShouldUseRequestContext() {
        // Given
        CustomFieldGroupDTO groupDTO = CustomFieldGroupDTO.builder()
                .name("Context Group")
                .description("A group that relies on RequestContext")
                .displayOrder(1)
                .build(); // No organizationId set

        // When
        CustomFieldGroupDTO createdGroup = customFieldGroupService.save(groupDTO);

        // Then
        assertNotNull(createdGroup);
        assertEquals(testOrgId, createdGroup.getOrganizationId());
        assertEquals(testSupportOrgId, createdGroup.getSupportOrganizationId());
        assertEquals(testUserId, createdGroup.getCreatedById());
    }

    @Test
    void testCreateCustomFieldGroup_WithOrganizationId_ShouldPreserveIt() {
        // Given
        CustomFieldGroupDTO groupDTO = CustomFieldGroupDTO.builder()
                .name("Preserved Group")
                .description("A group with pre-set organizationId")
                .displayOrder(1)
                .organizationId(testOrgId) // Use the same org ID to avoid constraint violations
                .build();

        // When
        CustomFieldGroupDTO createdGroup = customFieldGroupService.save(groupDTO);

        // Then
        assertNotNull(createdGroup);
        assertEquals(testOrgId, createdGroup.getOrganizationId(), 
                "Pre-set organizationId should be preserved");
        assertEquals(testUserId, createdGroup.getCreatedById(), 
                "CreatedById should still be populated by AuditAspect");
    }

    @Test
    void testCreateCustomFieldGroup_WithoutAnyOrganizationFields_ShouldUseRequestContext() {
        // Given - This simulates what the frontend should send (no organization fields)
        CustomFieldGroupDTO groupDTO = CustomFieldGroupDTO.builder()
                .name("No Org Fields Group")
                .description("A group with no organization fields set")
                .displayOrder(1)
                .build(); // No organizationId or supportOrganizationId set

        // When
        CustomFieldGroupDTO createdGroup = customFieldGroupService.save(groupDTO);

        // Then
        assertNotNull(createdGroup);
        assertNotNull(createdGroup.getId());
        assertEquals("No Org Fields Group", createdGroup.getName());
        
        // Verify that AuditAspect populated the organization fields from RequestContext
        assertNotNull(createdGroup.getOrganizationId(), "OrganizationId should be populated by AuditAspect");
        assertNotNull(createdGroup.getSupportOrganizationId(), "SupportOrganizationId should be populated by AuditAspect");
        assertEquals(testOrgId, createdGroup.getOrganizationId(), "Should use RequestContext orgId");
        assertEquals(testSupportOrgId, createdGroup.getSupportOrganizationId(), "Should use RequestContext supportOrgId");
        
        // Verify that AuditAspect populated the audit fields
        assertNotNull(createdGroup.getCreatedById(), "CreatedById should be populated by AuditAspect");
        assertNotNull(createdGroup.getCreatedBy(), "CreatedBy should be populated by AuditAspect");
        assertNotNull(createdGroup.getCreatedAt(), "CreatedAt should be populated by AuditAspect");
        assertNotNull(createdGroup.getUpdatedAt(), "UpdatedAt should be populated by AuditAspect");
        assertEquals(testUserId, createdGroup.getCreatedById());
        assertEquals("testuser", createdGroup.getCreatedBy()); // From mock authentication
    }
} 