package io.sx.customfields.service;

import io.sx.customfields.dto.CustomFieldDefinitionDTO;
import io.sx.customfields.dto.CreateCustomFieldDefinitionRequest;
import io.sx.customfields.mapper.CustomFieldDefinitionMapper;
import io.sx.customfields.repository.CustomFieldDefinitionRepository;
import io.sx.customfields.service.impl.CustomFieldDefinitionServiceImpl;
import io.sx.model.customfields.CustomFieldDefinition;
import io.sx.model.customfields.CustomFieldDataType;
import io.sx.model.customfields.CustomFieldType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomFieldDefinitionServiceTest {

    @Mock
    private CustomFieldDefinitionRepository repository;

    @Mock
    private CustomFieldDefinitionMapper mapper;

    @InjectMocks
    private CustomFieldDefinitionServiceImpl service;

    private CustomFieldDefinition testField;
    private CustomFieldDefinitionDTO testFieldDTO;

    @BeforeEach
    void setUp() {
        testField = CustomFieldDefinition.builder()
                .id(1L)
                .name("test_field")
                .label("Test Field")
                .description("A test field")
                .fieldType("TEXT")
                .dataType("STRING")
                .isRequired(true)
                .isActive(true)
                .organizationId(1L)
                .supportOrganizationId(1L)
                .build();

        testFieldDTO = CustomFieldDefinitionDTO.builder()
                .id(1L)
                .name("test_field")
                .label("Test Field")
                .description("A test field")
                .fieldType("TEXT")
                .dataType("STRING")
                .isRequired(true)
                .isActive(true)
                .organizationId(1L)
                .supportOrganizationId(1L)
                .build();
    }

    @Test
    void testFindById_Success() {
        // Given
        when(repository.findById(1L)).thenReturn(Optional.of(testField));
        when(mapper.toDTO(testField)).thenReturn(testFieldDTO);

        // When
        CustomFieldDefinitionDTO result = service.findById(1L);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("test_field", result.getName());
        verify(repository).findById(1L);
        verify(mapper).toDTO(testField);
    }

    @Test
    void testFindById_NotFound() {
        // Given
        when(repository.findById(999L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> service.findById(999L));
        verify(repository).findById(999L);
    }

    @Test
    void testSave_Success() {
        // Given
        when(mapper.toEntity(testFieldDTO)).thenReturn(testField);
        when(repository.save(testField)).thenReturn(testField);
        when(mapper.toDTO(testField)).thenReturn(testFieldDTO);

        // When
        CustomFieldDefinitionDTO result = service.save(testFieldDTO);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        verify(mapper).toEntity(testFieldDTO);
        verify(repository).save(testField);
        verify(mapper).toDTO(testField);
    }

    @Test
    void testFindByOrganizationId_Success() {
        // Given
        List<CustomFieldDefinition> fields = List.of(testField);
        when(repository.findByOrganizationId(1L)).thenReturn(fields);
        when(mapper.toDTO(testField)).thenReturn(testFieldDTO);

        // When
        List<CustomFieldDefinitionDTO> result = service.findByOrganizationId(1L);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
        verify(repository).findByOrganizationId(1L);
        verify(mapper).toDTO(testField);
    }

    @Test
    void testFindAll_Success() {
        // Given
        List<CustomFieldDefinition> fields = List.of(testField);
        when(repository.findAll(null)).thenReturn(fields);
        when(mapper.toDTO(testField)).thenReturn(testFieldDTO);

        // When
        List<CustomFieldDefinitionDTO> result = service.findAll();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
        verify(repository).findAll(null);
        verify(mapper).toDTO(testField);
    }

    @Test
    void testDeleteById_Success() {
        // Given
        doNothing().when(repository).deleteById(1L);

        // When
        service.deleteById(1L);

        // Then
        verify(repository).deleteById(1L);
    }

    @Test
    void testUpdate_Success() {
        // Given
        when(mapper.toEntity(testFieldDTO)).thenReturn(testField);
        when(repository.update(testField)).thenReturn(1L);

        // When
        Long result = service.update(1L, testFieldDTO);

        // Then
        assertEquals(1L, result);
        assertEquals(1L, testField.getId());
        verify(mapper).toEntity(testFieldDTO);
        verify(repository).update(testField);
    }
} 