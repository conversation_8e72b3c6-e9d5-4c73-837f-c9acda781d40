name: Backend CI

on:
  push:
    branches: [ main, develop ]
    paths:
      - '**/*.java'
      - '**/*.kt'
      - '**/*.gradle'
      - '**/*.gradle.kts'
      - '**/*.sql'
      - '**/*.yml'
      - '**/*.yaml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - '**/*.java'
      - '**/*.kt'
      - '**/*.gradle'
      - '**/*.gradle.kts'
      - '**/*.sql'
      - '**/*.yml'
      - '**/*.yaml'

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: sx_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: gradle

      - name: Setup Gradle
        uses: gradle/gradle-build-action@v2
        with:
          gradle-version: wrapper

      - name: Cache Gradle packages
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Run tests
        run: ./gradlew test
        env:
          SPRING_PROFILES_ACTIVE: test
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: sx_test
          DB_USER: postgres
          DB_PASSWORD: postgres

      - name: Build project
        run: ./gradlew build -x test
        env:
          SPRING_PROFILES_ACTIVE: test
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: sx_test
          DB_USER: postgres
          DB_PASSWORD: postgres

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: |
            **/build/test-results/
            **/build/reports/tests/

  security:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: gradle

      - name: Run security scan
        run: ./gradlew dependencyCheckAnalyze
        continue-on-error: true

      - name: Upload security report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-report
          path: build/reports/dependency-check-report.html 