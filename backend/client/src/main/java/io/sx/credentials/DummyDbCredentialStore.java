package io.sx.credentials;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * Dummy DB-based credential store (returns nulls for all fields).
 * Replace with real DB logic as needed.
 */
@Component
@ConditionalOnProperty(name = "sx.app.credentials.store.type", havingValue = "db")
public class DummyDbCredentialStore implements CredentialStore {
    @Override
    public Credential getCredential(String context) {
        // TODO: Implement DB lookup
        return new Credential(null, null);
    }
} 