package io.sx.credentials;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * CredentialStore implementation that reads from Spring properties.
 */
@Component
@ConditionalOnProperty(name = "sx.app.credentials.store.type", havingValue = "spring", matchIfMissing = true)
public class SpringPropertyCredentialStore implements CredentialStore {
    private final Environment env;

    public SpringPropertyCredentialStore(Environment env) {
        this.env = env;
    }

    @Override
    public Credential getCredential(String context) {
        String key = env.getProperty(context + ".api-key");
        String secret = env.getProperty(context + ".api-secret");
        if (key == null && secret == null) {
            return null;
        }
        return new Credential(key, secret);
    }
} 