package io.sx.credentials;

/**
 * Abstraction for credential storage and retrieval.
 * Implementations may use properties, DB, AWS, GCP, etc.
 */
public interface CredentialStore {
    /**
     * Retrieve a credential (key and secret) for a given context/service.
     * @param context the context or service name (e.g., 'cdc', 'ai')
     * @return the Credential object, or null if not found
     */
    Credential getCredential(String context);
} 