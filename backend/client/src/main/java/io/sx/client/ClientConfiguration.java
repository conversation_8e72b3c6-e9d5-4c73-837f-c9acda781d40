package io.sx.client;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Configuration class for the client module.
 * This ensures that components from the client module are properly exposed
 * when the module is used as a dependency.
 */
@Configuration
@ComponentScan(basePackages = {"io.sx.client", "io.sx.credentials"})
public class ClientConfiguration {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
} 