debezium.source.slot.name=sx_slot
# Production: do not drop slot on stop
debezium.source.slot.drop.on.stop=false
debezium.source.publication.name=sx_publication
debezium.source.connector.class=io.debezium.connector.postgresql.PostgresConnector
debezium.source.offset.storage.file.filename=data/offsets.dat
debezium.source.offset.flush.interval.ms=0
debezium.source.plugin.name=pgoutput
# Connection details
# Set this to your production database host
debezium.source.database.hostname=PROD_DB_HOST
debezium.source.database.port=5432
debezium.source.database.user=debezium
debezium.source.database.password=debezium
debezium.source.database.dbname=sx_main_db
# NATS configuration
debezium.sink.type=nats-jetstream
debezium.sink.nats-jetstream.url=nats://nats:4222
debezium.sink.nats-jetstream.create-stream=true
debezium.sink.nats-jetstream.subjects=src.>
debezium.source.topic.prefix=src
debezium.source.publication.autocreate.mode=filtered
debezium.source.table.include.list=public.ticket,public.internal_comments,public.public_comment,public.ticket_participants,public.organization 