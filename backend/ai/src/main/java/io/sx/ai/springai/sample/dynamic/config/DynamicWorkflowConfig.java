package io.sx.ai.springai.sample.dynamic.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * Configuration for dynamic workflow components
 */
@Slf4j
@Configuration
public class DynamicWorkflowConfig {
    
    @PostConstruct
    public void logConfiguration() {
        log.info("Dynamic workflow configuration initialized");
    }
} 