package io.sx.ai.repository;

import io.sx.ai.model.AISuggestion;
import io.sx.repository.BaseRepository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for AI suggestions
 */
public interface AISuggestionRepository extends BaseRepository<AISuggestion> {
    
    /**
     * Find suggestions by session ID
     */
    List<AISuggestion> findBySessionId(String sessionId);
    
    /**
     * Find suggestions by ticket ID
     */
    List<AISuggestion> findByTicketId(Long ticketId);
    
    /**
     * Find suggestions by status
     */
    List<AISuggestion> findByStatus(AISuggestion.SuggestionStatus status);
    
    /**
     * Find suggestions by organization
     */
    List<AISuggestion> findByOrganization(Long organizationId, Long supportOrgId);
    
    /**
     * Find suggestion by suggestion ID
     */
    Optional<AISuggestion> findBySuggestionId(String suggestionId);
    
    /**
     * Save multiple suggestions
     */
    List<AISuggestion> saveAll(List<AISuggestion> suggestions);
} 