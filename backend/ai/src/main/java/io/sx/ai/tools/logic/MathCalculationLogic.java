package io.sx.ai.tools.logic;

import java.util.regex.Pattern;

/**
 * Plain tool logic for mathematical calculations.
 * Framework-agnostic implementation that can be used by any AI framework.
 */
public class MathCalculationLogic {
    
    private static final Pattern NUMBER_PATTERN = Pattern.compile("-?\\d+(\\.\\d+)?");
    
    /**
     * Evaluate a mathematical expression with specified precision
     */
    public double evaluateExpression(String expression, int precision) {
        if (expression == null || expression.trim().isEmpty()) {
            throw new IllegalArgumentException("Expression cannot be null or empty");
        }
        
        if (precision < 0) {
            throw new IllegalArgumentException("Precision must be non-negative");
        }
        
        try {
            double result = evaluateSimpleMath(expression.trim());
            return Math.round(result * Math.pow(10, precision)) / Math.pow(10, precision);
        } catch (Exception e) {
            throw new RuntimeException("Failed to evaluate expression: " + expression, e);
        }
    }
    
    /**
     * Calculate percentage of a value relative to total
     */
    public double calculatePercentage(double value, double total) {
        if (total == 0) {
            throw new IllegalArgumentException("Total cannot be zero");
        }
        return (value / total) * 100;
    }
    
    /**
     * Round a number to specified decimal places
     */
    public double roundNumber(double number, int decimalPlaces) {
        if (decimalPlaces < 0) {
            throw new IllegalArgumentException("Decimal places must be non-negative");
        }
        return Math.round(number * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
    }
    
    /**
     * Calculate the absolute value
     */
    public double absoluteValue(double number) {
        return Math.abs(number);
    }
    
    /**
     * Calculate the square root
     */
    public double squareRoot(double number) {
        if (number < 0) {
            throw new IllegalArgumentException("Cannot calculate square root of negative number");
        }
        return Math.sqrt(number);
    }
    
    /**
     * Calculate power (base^exponent)
     */
    public double power(double base, double exponent) {
        return Math.pow(base, exponent);
    }
    
    /**
     * Simple mathematical expression evaluator
     * Supports basic operations: +, -, *, /
     */
    private double evaluateSimpleMath(String expression) {
        // Remove all whitespace
        expression = expression.replaceAll("\\s+", "");
        
        // Handle parentheses first (simple implementation)
        if (expression.contains("(")) {
            return evaluateWithParentheses(expression);
        }
        
        // Handle basic operations in order of precedence
        if (expression.contains("*") || expression.contains("/")) {
            return evaluateMultiplicationDivision(expression);
        } else if (expression.contains("+") || expression.contains("-")) {
            return evaluateAdditionSubtraction(expression);
        } else {
            // Single number
            if (!NUMBER_PATTERN.matcher(expression).matches()) {
                throw new IllegalArgumentException("Invalid number: " + expression);
            }
            return Double.parseDouble(expression);
        }
    }
    
    private double evaluateWithParentheses(String expression) {
        // Simple implementation - find innermost parentheses
        int start = expression.lastIndexOf("(");
        int end = expression.indexOf(")", start);
        
        if (start == -1 || end == -1) {
            throw new IllegalArgumentException("Mismatched parentheses in expression: " + expression);
        }
        
        String innerExpression = expression.substring(start + 1, end);
        double innerResult = evaluateSimpleMath(innerExpression);
        
        String newExpression = expression.substring(0, start) + innerResult + expression.substring(end + 1);
        return evaluateSimpleMath(newExpression);
    }
    
    private double evaluateMultiplicationDivision(String expression) {
        // Find first * or /
        int multiplyIndex = expression.indexOf("*");
        int divideIndex = expression.indexOf("/");
        
        if (multiplyIndex == -1 && divideIndex == -1) {
            return evaluateAdditionSubtraction(expression);
        }
        
        int operationIndex;
        boolean isMultiply;
        
        if (multiplyIndex == -1) {
            operationIndex = divideIndex;
            isMultiply = false;
        } else if (divideIndex == -1) {
            operationIndex = multiplyIndex;
            isMultiply = true;
        } else {
            operationIndex = Math.min(multiplyIndex, divideIndex);
            isMultiply = multiplyIndex < divideIndex;
        }
        
        // Find left operand
        String leftOperand = findLeftOperand(expression, operationIndex);
        // Find right operand
        String rightOperand = findRightOperand(expression, operationIndex);
        
        double left = evaluateSimpleMath(leftOperand);
        double right = evaluateSimpleMath(rightOperand);
        
        double result = isMultiply ? left * right : left / right;
        
        // Replace the operation with result
        String newExpression = expression.substring(0, operationIndex - leftOperand.length()) 
            + result 
            + expression.substring(operationIndex + 1 + rightOperand.length());
        
        return evaluateSimpleMath(newExpression);
    }
    
    private double evaluateAdditionSubtraction(String expression) {
        // Handle unary minus
        if (expression.startsWith("-")) {
            return -evaluateSimpleMath(expression.substring(1));
        }
        
        // Find first + or -
        int plusIndex = expression.indexOf("+");
        int minusIndex = expression.indexOf("-");
        
        if (plusIndex == -1 && minusIndex == -1) {
            if (!NUMBER_PATTERN.matcher(expression).matches()) {
                throw new IllegalArgumentException("Invalid number: " + expression);
            }
            return Double.parseDouble(expression);
        }
        
        int operationIndex;
        boolean isAdd;
        
        if (plusIndex == -1) {
            operationIndex = minusIndex;
            isAdd = false;
        } else if (minusIndex == -1) {
            operationIndex = plusIndex;
            isAdd = true;
        } else {
            operationIndex = Math.min(plusIndex, minusIndex);
            isAdd = plusIndex < minusIndex;
        }
        
        // Find left operand
        String leftOperand = findLeftOperand(expression, operationIndex);
        // Find right operand
        String rightOperand = findRightOperand(expression, operationIndex);
        
        double left = evaluateSimpleMath(leftOperand);
        double right = evaluateSimpleMath(rightOperand);
        
        double result = isAdd ? left + right : left - right;
        
        // Replace the operation with result
        String newExpression = expression.substring(0, operationIndex - leftOperand.length()) 
            + result 
            + expression.substring(operationIndex + 1 + rightOperand.length());
        
        return evaluateSimpleMath(newExpression);
    }
    
    private String findLeftOperand(String expression, int operationIndex) {
        int start = operationIndex - 1;
        while (start >= 0 && (Character.isDigit(expression.charAt(start)) || expression.charAt(start) == '.' || expression.charAt(start) == '-')) {
            start--;
        }
        return expression.substring(start + 1, operationIndex);
    }
    
    private String findRightOperand(String expression, int operationIndex) {
        int end = operationIndex + 1;
        while (end < expression.length() && (Character.isDigit(expression.charAt(end)) || expression.charAt(end) == '.')) {
            end++;
        }
        return expression.substring(operationIndex + 1, end);
    }
} 