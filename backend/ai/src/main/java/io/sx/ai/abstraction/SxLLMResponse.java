package io.sx.ai.abstraction;

import java.util.List;
import java.util.Map;

/**
 * Framework-agnostic LLM response interface
 * Abstracts LLM responses across different AI frameworks
 */
public interface SxLLMResponse<T> {
    /**
     * Type-safe method to get the structured output as the default type T.
     */
    T getOutput();

    /**
     * Flexible method to parse the output as a different type.
     */
    <U> U getStructuredOutput(Class<U> outputType);

    /**
     * Get the raw content as a string.
     */
    String getContent();
    
    /**
     * Get the response messages
     */
    List<SxMessage> getMessages();
    
    /**
     * Get the first message content
     */
    String getFirstMessageContent();
    
    /**
     * Get the response metadata
     */
    Map<String, Object> getMetadata();
    
    /**
     * Get the usage information
     */
    Map<String, Object> getUsage();
    
    /**
     * Check if the response was successful
     */
    boolean isSuccessful();
    
    /**
     * Get any error information
     */
    String getError();
    
    /**
     * Get the model used for this response
     */
    String getModel();
    
    /**
     * Get the finish reason
     */
    String getFinishReason();
    
    /**
     * Get the response ID
     */
    String getId();
    
    /**
     * Usage information for the response
     */
    class UsageInfo {
        private final int promptTokens;
        private final int completionTokens;
        private final int totalTokens;
        
        public UsageInfo(int promptTokens, int completionTokens, int totalTokens) {
            this.promptTokens = promptTokens;
            this.completionTokens = completionTokens;
            this.totalTokens = totalTokens;
        }
        
        // Getters
        public long getPromptTokens() { return ((Number) promptTokens).longValue(); }
        public long getCompletionTokens() { return ((Number) completionTokens).longValue(); }
        public long getTotalTokens() { return ((Number) totalTokens).longValue(); }
    }
} 