package io.sx.ai.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import io.sx.ai.abstraction.SxLLMClient;
import io.sx.ai.abstraction.SxLLMResponse;
import io.sx.ai.abstraction.PromptResolutionService;
import io.sx.ai.abstraction.ResolvedPrompt;
import io.sx.ai.dto.TicketCategorization;
import io.sx.ai.service.TicketCategorizationService;
import io.sx.client.SXClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Uses LLM to categorize tickets (Bug, Feature Request, Question, Incident, Task, Other)
 * Focused on categorization only - other aspects like priority, assignment, etc. are handled separately
 */
@Slf4j
@Service
public class TicketCategorizationServiceImpl implements TicketCategorizationService {

    private final SxLLMClient llmClient;
    private final SXClientService sxClientService;
    private final PromptResolutionService promptResolutionService;

    public TicketCategorizationServiceImpl(SxLLMClient llmClient, SXClientService sxClientService, PromptResolutionService promptResolutionService) {
        this.llmClient = llmClient;
        this.sxClientService = sxClientService;
        this.promptResolutionService = promptResolutionService;
        sxClientService.clientType("AI"); //TODO have a factory for client service that resolved the service
    }


    @Override
    public Map<String, Object> categorize(Long ticketId, Long organizationId, Long supportOrgId) {
        log.debug("Categorizing ticket: {}, org: {}, supportOrg: {}", ticketId, organizationId, supportOrgId);
        
        try {
            // 1. Get ticket data from main app
            JsonNode ticketData = sxClientService.getTicket(ticketId, supportOrgId, 1L);
            if (ticketData == null) {
                log.warn("Could not retrieve ticket data for ticket: {}", ticketId);
                return getDefaultClassification();
            }

            // 2. Get organization data for context
            // TODO on behalf of ID
            JsonNode orgData = sxClientService.getOrganization(organizationId, supportOrgId, 1L);
            JsonNode supportOrgData = sxClientService.getOrganization(supportOrgId, null, 1L);

            // 3. Build categorization prompt (now returns ResolvedPrompt)
            ResolvedPrompt categorizationPrompt = buildCategorizationPrompt(ticketData, orgData, supportOrgData);

                    // 4. Generate categorization using LLM (ResolvedPrompt)
        SxLLMResponse<TicketCategorization> response = llmClient.generate(categorizationPrompt, TicketCategorization.class);
        
        if (!response.isSuccessful()) {
            log.error("LLM categorization failed for ticket {}: {}", ticketId, response.getError());
            return getDefaultClassification();
        }

        // 5. Convert structured response to map
        TicketCategorization categorization = response.getOutput();
        Map<String, Object> result = convertCategorizationToMap(categorization);
            
            log.debug("Successfully categorized ticket {} as: {}", ticketId, categorization.getCategory());
            return result;
            
        } catch (Exception e) {
            log.error("Error categorizing ticket: {}", ticketId, e);
            return getDefaultClassification();
        }
    }

    @Override
    public Map<String, Object> getClassificationAnalysis(Long ticketId, Long organizationId, Long supportOrgId) {
        log.debug("Getting categorization analysis for ticket: {}", ticketId);
        
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            Map<String, Object> categorization = categorize(ticketId, organizationId, supportOrgId);
            
            analysis.put("categorization", categorization);
            analysis.put("category", categorization.get("category"));
            analysis.put("confidence", categorization.get("confidence"));
            analysis.put("reasoning", categorization.get("reasoning"));
            
            // Note: For categorization-only flow, we don't provide SLA, escalation, or assignment recommendations
            // These would be handled by separate services when needed
            
        } catch (Exception e) {
            log.error("Error getting categorization analysis for ticket: {}", ticketId, e);
            analysis.put("error", "Failed to analyze ticket categorization: " + e.getMessage());
        }
        
        return analysis;
    }

    /**
     * Build categorization prompt using templates and resolve to ResolvedPrompt
     */
    private ResolvedPrompt buildCategorizationPrompt(JsonNode ticketData, JsonNode orgData, JsonNode supportOrgData) {
        try {
            // Load prompt templates
            String systemTemplate = loadTemplate("templates/prompts/categorization/system.st");
            String userTemplate = loadTemplate("templates/prompts/categorization/user.st");
            
            // Build variables for user template
            Map<String, Object> variables = buildTemplateVariables(ticketData, orgData, supportOrgData);
            
            // Resolve prompts
            // Provide placeholder variables for system template if needed
            Map<String, Object> systemVariables = new HashMap<>();
            systemVariables.put("category", "PLACEHOLDER"); // Placeholder for JSON example in template
            ResolvedPrompt systemPrompt = promptResolutionService.resolveSystemPrompt(systemTemplate, systemVariables);
            ResolvedPrompt userPrompt = promptResolutionService.resolveUserPrompt(userTemplate, variables);
            
            // Combine prompts (system + user)
            java.util.List<ResolvedPrompt> prompts = java.util.List.of(systemPrompt, userPrompt);
            return promptResolutionService.combinePrompts(prompts);
        } catch (Exception e) {
            log.error("Error building categorization prompt", e);
            throw new RuntimeException("Failed to build categorization prompt", e);
        }
    }

    /**
     * Build template variables from ticket and organization data
     * For categorization only: only include ticket content fields
     */
    private Map<String, Object> buildTemplateVariables(JsonNode ticketData, JsonNode orgData, JsonNode supportOrgData) {
        Map<String, Object> variables = new HashMap<>();
        
        // Try both camelCase and snake_case field names
        variables.put("title", ticketData != null ? ticketData.path("title").asText("") : "");
        variables.put("description", ticketData != null ? ticketData.path("description").asText("") : "");
        variables.put("priority", ticketData != null ? ticketData.path("priority").asText("") : "");
        variables.put("status", ticketData != null ? ticketData.path("status").asText("") : "");
        
        // Handle date fields - try both formats
        String createdAt = "";
        if (ticketData != null) {
            if (ticketData.has("createdAt")) {
                createdAt = ticketData.path("createdAt").asText("");
            } else if (ticketData.has("created_at")) {
                createdAt = ticketData.path("created_at").asText("");
            }
        }
        variables.put("createdAt", createdAt);
        
        String updatedAt = "";
        if (ticketData != null) {
            if (ticketData.has("updatedAt")) {
                updatedAt = ticketData.path("updatedAt").asText("");
            } else if (ticketData.has("updated_at")) {
                updatedAt = ticketData.path("updated_at").asText("");
            }
        }
        variables.put("updatedAt", updatedAt);
        
        return variables;
    }

    /**
     * Convert TicketCategorization DTO to Map
     */
    private Map<String, Object> convertCategorizationToMap(TicketCategorization categorization) {
        Map<String, Object> result = new HashMap<>();
        result.put("category", categorization.getCategory());
        result.put("confidence", categorization.getConfidence());
        result.put("reasoning", categorization.getReasoning());
        return result;
    }

    /**
     * Load template from classpath
     */
    private String loadTemplate(String templatePath) {
        try {
            ClassPathResource resource = new ClassPathResource(templatePath);
            return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Failed to load template: {}", templatePath, e);
            throw new RuntimeException("Failed to load template: " + templatePath, e);
        }
    }



    private Map<String, Object> getDefaultClassification() {
        Map<String, Object> classification = new HashMap<>();
        classification.put("category", "Other");
        classification.put("confidence", 0.5);
        classification.put("reasoning", "Default categorization due to insufficient data");
        return classification;
    }
} 