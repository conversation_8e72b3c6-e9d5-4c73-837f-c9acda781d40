package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Numeric input wrapper for Double values with Spring AI integration
 * Supports mathematical operations and structured output
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NumericInput implements WorkflowInput<Double> {
    
    /**
     * The numeric value
     */
    private Double value;
    
    /**
     * Unit of measurement (e.g., "percentage", "currency", "count")
     */
    private String unit;
    
    /**
     * Format specification (e.g., "decimal", "percentage", "currency")
     */
    private String format;
    
    /**
     * Additional metadata
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    @Override
    public String getType() {
        return "NUMERIC";
    }
    
    @Override
    public Map<String, Object> getMetadata() {
        Map<String, Object> enhancedMetadata = new HashMap<>(metadata);
        enhancedMetadata.put("unit", unit);
        enhancedMetadata.put("format", format);
        return enhancedMetadata;
    }
    
    @Override
    public Prompt createPrompt(String template, Map<String, Object> variables) {
        Map<String, Object> enhancedVariables = new HashMap<>(variables);
        enhancedVariables.put("numericValue", value);
        enhancedVariables.put("unit", unit);
        enhancedVariables.put("format", format);
        enhancedVariables.put("inputValue", value);
        enhancedVariables.put("inputType", getType());
        enhancedVariables.putAll(metadata);
        
        PromptTemplate promptTemplate = new PromptTemplate(template);
        return promptTemplate.create(enhancedVariables);
    }
    
    /**
     * Create a structured prompt for mathematical operations
     */
    public Prompt createMathematicalPrompt(String operation, String description) {
        String template = """
            You are a mathematical calculator. Perform the operation: {operation}
            Input: {numericValue} {unit}
            Format: {format}
            
            Return the result as a JSON object with:
            - result: the calculated value
            - unit: the unit of measurement
            - confidence: confidence score (0.0 to 1.0)
            - explanation: brief explanation of the calculation
            """;
        
        Map<String, Object> variables = Map.of(
            "operation", description,
            "numericValue", value,
            "unit", unit != null ? unit : "",
            "format", format != null ? format : "decimal"
        );
        
        return createPrompt(template, variables);
    }
    
    /**
     * Static factory method for creating numeric inputs
     */
    public static NumericInput of(Double value) {
        return NumericInput.builder()
            .value(value)
            .unit("")
            .format("decimal")
            .build();
    }
    
    /**
     * Static factory method for creating numeric inputs with unit
     */
    public static NumericInput of(Double value, String unit) {
        return NumericInput.builder()
            .value(value)
            .unit(unit)
            .format("decimal")
            .build();
    }
    
    /**
     * Static factory method for creating percentage inputs
     */
    public static NumericInput percentage(Double value) {
        return NumericInput.builder()
            .value(value)
            .unit("percentage")
            .format("percentage")
            .build();
    }
    
    /**
     * Static factory method for creating currency inputs
     */
    public static NumericInput currency(Double value, String currencyCode) {
        return NumericInput.builder()
            .value(value)
            .unit(currencyCode)
            .format("currency")
            .build();
    }
} 