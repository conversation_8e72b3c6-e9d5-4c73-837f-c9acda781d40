package io.sx.ai.dto;

import io.sx.model.ticket.Ticket;
import io.sx.model.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParticipantRemovedEvent {
    private Long eventId;
    private Ticket ticket;
    private User removedUser;
    private String removalReason;
    private User removedBy;
    private LocalDateTime removedAt;
    private Long organizationId;
} 