package io.sx.ai.decision.rules;

import io.sx.ai.decision.conditions.AIConditionEvaluator;
import io.sx.ai.decision.model.AIDecisionContext;
import io.sx.ai.decision.strategies.AIDecisionStrategyFactory;
import io.sx.ai.model.TicketEventType;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Decision rule for ticket classification
 */
@Component
public class TicketClassificationDecisionRule extends BaseAIDecisionRule {
    
    public TicketClassificationDecisionRule(AIConditionEvaluator conditionEvaluator, 
                                          AIDecisionStrategyFactory strategyFactory) {
        super(conditionEvaluator, strategyFactory);
    }
    
    @Override
    public String getRuleId() {
        return "ticket-classification-rule";
    }
    
    @Override
    public String getRuleName() {
        return "Ticket Classification Decision Rule";
    }
    
    @Override
    public String getDescription() {
        return "Determines when and how to apply AI classification to tickets";
    }
    
    @Override
    public int getPriority() {
        return 100; // High priority
    }
    
    @Override
    public boolean isApplicable(String triggerEvent, AIDecisionContext context) {
        return TicketEventType.TICKET_CREATED.getValue().equals(triggerEvent) || 
               TicketEventType.TICKET_UPDATED.getValue().equals(triggerEvent);
    }
    
    @Override
    protected boolean evaluateConditions(AIDecisionContext context) {
        // Check if classification is enabled for this org
        boolean classificationEnabled = conditionEvaluator.evaluateBoolean(
            "enabled", context.getAiConfiguration());
        
        if (!classificationEnabled) {
            return false;
        }
        
        // Check minimum description length
        if (!conditionEvaluator.evaluateBoolean("ai.classification.has_sufficient_content", context)) {
            return false;
        }
        
        return true;
    }
    
    @Override
    protected String determineStrategy(AIDecisionContext context) {
        // Get customer importance
        Double customerImportance = extractCustomerImportance(context);
        
        // Get ticket priority
        String priority = extractPriority(context);
        
        // Get org configuration
        String defaultStrategy = conditionEvaluator.evaluateString(
            "default_strategy", context.getAiConfiguration(), "enhanced");
        
        // Use enhanced strategy for all tickets (App Only + LLM)
        // This provides the best balance of performance and intelligence
        return "enhanced";
    }
    
    @Override
    protected Map<String, Object> getStrategyConfig(String strategy, AIDecisionContext context) {
        Map<String, Object> config = new HashMap<>();
        
        switch (strategy) {
            case "enhanced":
                config.put("confidence_threshold", 0.7);
                config.put("vector_search_limit", 5);
                config.put("use_rules", true);
                config.put("llm_fallback", true);
                config.put("hybrid_threshold", 0.8);
                config.put("enable_customer_importance", true);
                config.put("enable_similar_tickets", true);
                config.put("enable_classification", true);
                break;
            case "app_only":
                config.put("confidence_threshold", 0.8);
                config.put("vector_search_limit", 10);
                config.put("use_rules", true);
                break;
            case "hybrid":
                config.put("confidence_threshold", 0.7);
                config.put("vector_search_limit", 5);
                config.put("llm_fallback", true);
                config.put("hybrid_threshold", 0.8);
                break;
            case "llm_only":
                config.put("confidence_threshold", 0.6);
                config.put("use_advanced_prompting", true);
                break;
        }
        
        return config;
    }
    
    @Override
    protected String getReasoning(AIDecisionContext context) {
        Double customerImportance = extractCustomerImportance(context);
        String priority = extractPriority(context);
        String strategy = determineStrategy(context);
        
        return String.format(
            "Customer importance: %.1f, Priority: %s, Strategy: %s",
            customerImportance != null ? customerImportance : 0.0, 
            priority != null ? priority : "UNKNOWN", 
            strategy
        );
    }
    
    private Double extractCustomerImportance(AIDecisionContext context) {
        if (context.getEntityData() != null) {
            Object importance = context.getEntityData().get("customerImportance");
            if (importance instanceof Number) {
                return ((Number) importance).doubleValue();
            }
        }
        return null;
    }
    
    private String extractPriority(AIDecisionContext context) {
        if (context.getEntityData() != null) {
            Object priority = context.getEntityData().get("priority");
            return priority != null ? priority.toString() : null;
        }
        return null;
    }
} 