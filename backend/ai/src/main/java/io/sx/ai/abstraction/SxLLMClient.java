package io.sx.ai.abstraction;

import java.util.List;
import java.util.Map;

/**
 * Framework-agnostic LLM client interface
 * Abstracts LLM interactions across different AI frameworks
 */
public interface SxLLMClient {
    
    /**
     * Generate a response for the given prompt
     */
    SxLLMResponse<Object> generate(SxPrompt prompt);
    
    /**
     * Generate a type-safe response for the given prompt
     */
    <T> SxLLMResponse<T> generate(SxPrompt prompt, Class<T> outputType);
    
    /**
     * Generate a type-safe response for the given resolved prompt
     */
    <T> SxLLMResponse<T> generate(ResolvedPrompt resolvedPrompt, Class<T> outputType);
    
    /**
     * Generate a structured response (convenience method)
     */
    <T> T generateStructured(SxPrompt prompt, Class<T> outputType);
    
    /**
     * Generate a response with function calling
     */
    SxLLMResponse<Object> generateWithFunctions(SxPrompt prompt, List<SxFunction> functions);
    
    /**
     * Generate a response with vector store context
     */
    SxLLMResponse<Object> generateWithContext(SxPrompt prompt, SxVectorStore vectorStore, String query);
    
    /**
     * Generate a response with chat memory
     */
    SxLLMResponse<Object> generateWithMemory(SxPrompt prompt, SxChatMemory chatMemory);
    
    /**
     * Generate a type-safe response with chat memory
     */
    <T> SxLLMResponse<T> generateWithMemory(SxPrompt prompt, Class<T> outputType, SxChatMemory chatMemory);
    
    // Factory methods for creating messages and prompts
    
    /**
     * Create a system message
     */
    SxMessage createSystemMessage(String content);
    
    /**
     * Create a user message
     */
    SxMessage createUserMessage(String content);
    
    /**
     * Create an assistant message
     */
    SxMessage createAssistantMessage(String content);
    
    /**
     * Create a prompt template from a string
     */
    SxPromptTemplate createPromptTemplate(String template);
    
    /**
     * Create a prompt from a list of messages
     */
    SxPrompt createPrompt(List<SxMessage> messages);
    
    /**
     * Convert Spring AI messages to SxMessages
     * TODO: This should be moved to a factory class
     */
    List<SxMessage> convertToSxMessages(List<?> springMessages);
    
    /**
     * Get the model name
     */
    String getModelName();
    
    /**
     * Get the model configuration
     */
    Map<String, Object> getModelConfig();
    
    /**
     * Check if the client is available
     */
    boolean isAvailable();
    
    /**
     * Get usage statistics
     */
    Map<String, Object> getUsageStats();
    
    /**
     * Client capabilities
     */
    class ClientCapabilities {
        private final boolean supportsStructuredOutput;
        private final boolean supportsFunctionCalling;
        private final boolean supportsEmbeddings;
        private final boolean supportsStreaming;
        private final int maxTokens;
        private final int maxInputTokens;
        
        public ClientCapabilities(boolean supportsStructuredOutput, boolean supportsFunctionCalling,
                                boolean supportsEmbeddings, boolean supportsStreaming,
                                int maxTokens, int maxInputTokens) {
            this.supportsStructuredOutput = supportsStructuredOutput;
            this.supportsFunctionCalling = supportsFunctionCalling;
            this.supportsEmbeddings = supportsEmbeddings;
            this.supportsStreaming = supportsStreaming;
            this.maxTokens = maxTokens;
            this.maxInputTokens = maxInputTokens;
        }
        
        // Getters
        public boolean supportsStructuredOutput() { return supportsStructuredOutput; }
        public boolean supportsFunctionCalling() { return supportsFunctionCalling; }
        public boolean supportsEmbeddings() { return supportsEmbeddings; }
        public boolean supportsStreaming() { return supportsStreaming; }
        public int getMaxTokens() { return maxTokens; }
        public int getMaxInputTokens() { return maxInputTokens; }
    }
}