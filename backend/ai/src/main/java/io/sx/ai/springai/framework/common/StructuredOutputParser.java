package io.sx.ai.springai.framework.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatResponse;

import java.util.List;

/**
 * Utility class for parsing structured outputs from Spring AI responses
 * Uses Jackson for JSON parsing and provides fallback mechanisms
 */
@Slf4j
public class StructuredOutputParser {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Parse numeric result from Spring AI response
     * @deprecated Use SxLLMResponse<T> pattern instead
     */
    @Deprecated
    public static StructuredOutputResults.NumericResult parseNumericResult(ChatResponse response) {
        try {
            String jsonResponse = response.getResult().getOutput().getText();
            return parseNumericResult(jsonResponse);
        } catch (Exception e) {
            log.warn("Failed to parse numeric result from response: {}", e.getMessage());
            return createFallbackNumericResult(response.getResult().getOutput().getText());
        }
    }
    
    /**
     * Parse numeric result from JSON string
     * @deprecated Use SxLLMResponse<T> pattern instead
     */
    @Deprecated
    public static StructuredOutputResults.NumericResult parseNumericResult(String jsonResponse) {
        try {
            return objectMapper.readValue(jsonResponse, StructuredOutputResults.NumericResult.class);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse JSON for numeric result: {}", e.getMessage());
            return createFallbackNumericResult(jsonResponse);
        }
    }
    
    /**
     * Parse text analysis result from Spring AI response
     * @deprecated Use SxLLMResponse<T> pattern instead
     */
    @Deprecated
    public static StructuredOutputResults.TextAnalysisResult parseTextAnalysisResult(ChatResponse response) {
        try {
            String jsonResponse = response.getResult().getOutput().getText();
            return parseTextAnalysisResult(jsonResponse);
        } catch (Exception e) {
            log.warn("Failed to parse text analysis result from response: {}", e.getMessage());
            return createFallbackTextAnalysisResult(response.getResult().getOutput().getText());
        }
    }
    
    /**
     * Parse text analysis result from JSON string
     * @deprecated Use SxLLMResponse<T> pattern instead
     */
    @Deprecated
    public static StructuredOutputResults.TextAnalysisResult parseTextAnalysisResult(String jsonResponse) {
        try {
            return objectMapper.readValue(jsonResponse, StructuredOutputResults.TextAnalysisResult.class);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse JSON for text analysis result: {}", e.getMessage());
            return createFallbackTextAnalysisResult(jsonResponse);
        }
    }
    
    /**
     * Parse sentiment result from Spring AI response
     * @deprecated Use SxLLMResponse<T> pattern instead
     */
    @Deprecated
    public static StructuredOutputResults.SentimentResult parseSentimentResult(ChatResponse response) {
        try {
            String jsonResponse = response.getResult().getOutput().getText();
            return parseSentimentResult(jsonResponse);
        } catch (Exception e) {
            log.warn("Failed to parse sentiment result from response: {}", e.getMessage());
            return createFallbackSentimentResult(response.getResult().getOutput().getText());
        }
    }
    
    /**
     * Parse sentiment result from JSON string
     * @deprecated Use SxLLMResponse<T> pattern instead
     */
    @Deprecated
    public static StructuredOutputResults.SentimentResult parseSentimentResult(String jsonResponse) {
        try {
            return objectMapper.readValue(jsonResponse, StructuredOutputResults.SentimentResult.class);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse JSON for sentiment result: {}", e.getMessage());
            return createFallbackSentimentResult(jsonResponse);
        }
    }
    
    /**
     * Parse summary result from Spring AI response
     * @deprecated Use SxLLMResponse<T> pattern instead
     */
    @Deprecated
    public static StructuredOutputResults.SummaryResult parseSummaryResult(ChatResponse response) {
        try {
            String jsonResponse = response.getResult().getOutput().getText();
            return parseSummaryResult(jsonResponse);
        } catch (Exception e) {
            log.warn("Failed to parse summary result from response: {}", e.getMessage());
            return createFallbackSummaryResult(response.getResult().getOutput().getText());
        }
    }
    
    /**
     * Parse summary result from JSON string
     * @deprecated Use SxLLMResponse<T> pattern instead
     */
    @Deprecated
    public static StructuredOutputResults.SummaryResult parseSummaryResult(String jsonResponse) {
        try {
            return objectMapper.readValue(jsonResponse, StructuredOutputResults.SummaryResult.class);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse JSON for summary result: {}", e.getMessage());
            return createFallbackSummaryResult(jsonResponse);
        }
    }
    
    /**
     * Generic parser for any structured output
     * @deprecated Use SxLLMResponse<T> pattern instead
     */
    @Deprecated
    public static <T> T parseStructuredOutput(String jsonResponse, Class<T> resultClass) {
        try {
            return objectMapper.readValue(jsonResponse, resultClass);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse JSON for {}: {}", resultClass.getSimpleName(), e.getMessage());
            throw new RuntimeException("Failed to parse structured output", e);
        }
    }
    
    /**
     * Extract numeric value from response text (fallback method)
     */
    private static StructuredOutputResults.NumericResult createFallbackNumericResult(String response) {
        try {
            // Try to extract a number from the response
            String numericString = response.replaceAll("[^0-9.-]", "");
            Double result = Double.parseDouble(numericString);
            
            return StructuredOutputResults.NumericResult.builder()
                .result(result)
                .unit("")
                .confidence(0.8)
                .explanation("Extracted from response: " + response)
                .operation("fallback_extraction")
                .build();
        } catch (Exception e) {
            log.warn("Failed to extract numeric value from response: {}", response);
            return StructuredOutputResults.NumericResult.builder()
                .result(0.0)
                .unit("")
                .confidence(0.0)
                .explanation("Failed to parse response: " + response)
                .operation("error")
                .build();
        }
    }
    
    /**
     * Create fallback text analysis result
     */
    private static StructuredOutputResults.TextAnalysisResult createFallbackTextAnalysisResult(String response) {
        return StructuredOutputResults.TextAnalysisResult.builder()
            .analysis(response)
            .sentiment("neutral")
            .confidence(0.5)
            .keywords(List.of())
            .wordCount(response.split("\\s+").length)
            .language("en")
            .build();
    }
    
    /**
     * Create fallback sentiment result
     */
    private static StructuredOutputResults.SentimentResult createFallbackSentimentResult(String response) {
        return StructuredOutputResults.SentimentResult.builder()
            .sentiment("neutral")
            .confidence(0.5)
            .explanation("Fallback sentiment analysis")
            .intensity("medium")
            .emotions(List.of())
            .build();
    }
    
    /**
     * Create fallback summary result
     */
    private static StructuredOutputResults.SummaryResult createFallbackSummaryResult(String response) {
        return StructuredOutputResults.SummaryResult.builder()
            .summary(response)
            .wordCount(response.split("\\s+").length)
            .confidence(0.5)
            .keyPoints(List.of())
            .build();
    }
} 