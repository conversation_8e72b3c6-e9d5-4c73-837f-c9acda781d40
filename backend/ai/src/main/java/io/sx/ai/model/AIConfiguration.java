package io.sx.ai.model;

import com.fasterxml.jackson.databind.JsonNode;
import io.sx.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * AI Configuration entity for storing organization-specific AI settings
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AIConfiguration extends BaseModel {
    
    private String configurationType;
    private Long supportOrganizationId;
    private JsonNode configurationData;
    private Boolean isActive;
} 