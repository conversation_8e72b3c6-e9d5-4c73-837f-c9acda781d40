package io.sx.ai.service;

import io.sx.dto.AISuggestionDTO;

import java.util.List;

/**
 * Service interface for AI suggestions
 */
public interface AISuggestionService {
    
    /**
     * Get suggestions for a specific ticket
     */
    List<AISuggestionDTO> getSuggestionsForTicket(Long ticketId);
    
    /**
     * Get suggestions by status
     */
    List<AISuggestionDTO> getSuggestionsByStatus(String status);
    
    /**
     * Get suggestions by type
     */
    List<AISuggestionDTO> getSuggestionsByType(String type);
    
    /**
     * Accept a suggestion
     */
    AISuggestionDTO acceptSuggestion(String suggestionId, Long userId);
    
    /**
     * Reject a suggestion
     */
    AISuggestionDTO rejectSuggestion(String suggestionId, Long userId, String reason);
    
    /**
     * Execute a suggestion
     */
    AISuggestionDTO executeSuggestion(String suggestionId);
    
    /**
     * Get suggestion by suggestion ID
     */
    AISuggestionDTO getBySuggestionId(String suggestionId);
    
    /**
     * Save a new suggestion
     */
    AISuggestionDTO save(AISuggestionDTO dto);
    
    /**
     * Update an existing suggestion
     */
    AISuggestionDTO update(AISuggestionDTO dto);
    
    /**
     * Delete a suggestion by ID
     */
    void delete(Long id);
    
    /**
     * Find suggestion by ID
     */
    AISuggestionDTO findById(Long id);
    
    /**
     * Find all suggestions
     */
    List<AISuggestionDTO> findAll();
} 