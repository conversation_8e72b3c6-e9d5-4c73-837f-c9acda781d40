package io.sx.ai.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sx.ai.model.TicketEventType;
import io.sx.ai.orchestration.TicketOrchestrator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class TicketEventMessageHandler implements MessageHandler {
    private final TicketOrchestrator ticketOrchestrator;
    private final ObjectMapper objectMapper;

    @Override
    public boolean canHandle(String eventType) {
        return TicketEventType.isValid(eventType);
    }

    @Override
    public void handle(JsonNode eventData, String subject) {
        TicketEventType ticketEventType = TicketEventType.fromValue(eventTypeFromSubject(subject));
        switch (ticketEventType) {
            case TICKET_CREATED:
                handleTicketCreated(eventData);
                break;
            case TICKET_UPDATED:
                handleTicketUpdated(eventData);
                break;
            default:
                log.debug("Unhandled ticket event type: {}", ticketEventType);
        }
    }

    private void handleTicketCreated(JsonNode eventData) {
        JsonNode ticketNode = eventData.get("event");
        if (ticketNode == null) {
            log.warn("No 'event' object found in message payload");
            return;
        }
        Map<String, Object> ticketData = extractTicketData(ticketNode);
        Long ticketId = extractLongValue(ticketNode, "id");
        Long organizationId = extractLongValue(ticketNode, "organization_id");
        Long supportOrgId = extractLongValue(ticketNode, "support_organization_id");
        Long userId = extractLongValue(ticketNode, "created_by_id");
        if (ticketId == null || organizationId == null || supportOrgId == null) {
            log.warn("Missing required fields for ticket processing: ticketId={}, orgId={}, supportOrgId={}", ticketId, organizationId, supportOrgId);
            return;
        }
        var result = ticketOrchestrator.processTicketCreation(ticketId, ticketData, organizationId, supportOrgId, userId);
        log.info("Ticket processing completed: sessionId={}, success={}, strategy={}", result.getSessionId(), result.isSuccess(), result.getStrategy());
    }

    private void handleTicketUpdated(JsonNode eventData) {
        JsonNode ticketNode = eventData.get("event");
        if (ticketNode == null) {
            log.warn("No 'event' object found in message payload");
            return;
        }
        Map<String, Object> ticketData = extractTicketData(ticketNode);
        Long ticketId = extractLongValue(ticketNode, "id");
        Long organizationId = extractLongValue(ticketNode, "organization_id");
        Long supportOrgId = extractLongValue(ticketNode, "support_organization_id");
        Long userId = extractLongValue(ticketNode, "updated_by_id");
        if (ticketId == null || organizationId == null || supportOrgId == null) {
            log.warn("Missing required fields for ticket update processing: ticketId={}, orgId={}, supportOrgId={}", ticketId, organizationId, supportOrgId);
            return;
        }
        var result = ticketOrchestrator.processTicketUpdate(ticketId, ticketData, organizationId, supportOrgId, userId);
        log.info("Ticket update processing completed: sessionId={}, success={}, strategy={}", result.getSessionId(), result.isSuccess(), result.getStrategy());
    }

    private String eventTypeFromSubject(String subject) {
        String[] parts = subject.split("\\.");
        if (parts.length >= 5) {
            String entity = parts[3];
            String operation = parts[4];
            if ("ticket".equals(entity)) {
                switch (operation) {
                    case "INSERT": return "ticket.c";
                    case "UPDATE": return "ticket.u";
                    default: return entity + "." + operation;
                }
            }
            return entity + "." + operation;
        }
        return subject;
    }

    private Map<String, Object> extractTicketData(JsonNode eventData) {
        return objectMapper.convertValue(eventData, Map.class);
    }

    private Long extractLongValue(JsonNode node, String fieldName) {
        JsonNode field = node.get(fieldName);
        if (field != null && !field.isNull()) {
            return field.asLong();
        }
        return null;
    }
} 