package io.sx.ai.abstraction;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Base abstract class for resolved prompts using Lombok patterns.
 * Provides common functionality for all resolved prompt implementations.
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
public abstract class BaseResolvedPrompt implements ResolvedPrompt {
    
    protected String resolvedText;
    protected List<SxMessage> resolvedMessages;
    protected Map<String, Object> originalVariables;
    protected SxPrompt.PromptType promptType;
    protected SxMessage.PromptRole messageRole;
    protected String templateSource;
    protected boolean fullyResolved;
    @Builder.Default
    protected Map<String, Object> metadata = new HashMap<>();
    
    /**
     * Validate that the prompt is fully resolved
     */
    protected void validateResolved() {
        if (!isFullyResolved()) {
            throw new IllegalStateException("Prompt is not fully resolved: " + getTemplateSource());
        }
    }
    
    /**
     * Get a human-readable description of this resolved prompt
     */
    public String getDescription() {
        return String.format("ResolvedPrompt[promptType=%s, messageRole=%s, resolved=%s, variables=%d]", 
            getPromptType(), getMessageRole(), isFullyResolved(), getOriginalVariables().size());
    }
} 