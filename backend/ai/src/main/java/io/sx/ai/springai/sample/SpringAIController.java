package io.sx.ai.springai.sample;

import io.sx.ai.springai.sample.orchestrator.SpringAISamplesOrchestrator;
import io.sx.ai.springai.framework.chain.workflow.ChainWorkflowResult;
import io.sx.ai.springai.framework.common.TextInput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * REST Controller for Spring AI workflow operations
 * Exposes endpoints for executing chain workflows
 */
@Slf4j
@RestController
@RequestMapping("/api/springai")
@RequiredArgsConstructor
public class SpringAIController {
    
    private final SpringAISamplesOrchestrator springAISamplesOrchestrator;
    
    /**
     * Execute mathematical chain workflow with direct input
     */
    @PostMapping("/chain/mathematical")
    public ResponseEntity<ChainWorkflowResult> executeMathematicalChain(@RequestBody Map<String, String> request) {
        log.info("REST: Executing mathematical chain with request: {}", request);
        
        String input = request.get("input");
        if (input == null) {
            return ResponseEntity.badRequest().body(ChainWorkflowResult.<String, String>chainBuilder()
                .originalInput(TextInput.of("null"))
                .success(false)
                .error("Input parameter is required")
                .sessionId("error")
                .workflowType("error")
                .build());
        }
        
        ChainWorkflowResult result = (ChainWorkflowResult) springAISamplesOrchestrator.executeMathematicalChain(input);
        return ResponseEntity.ok(result);
    }
    
    /**
     * Execute mathematical chain workflow with event data
     */
    @PostMapping("/chain/mathematical/event")
    public ResponseEntity<ChainWorkflowResult> executeMathematicalChainFromEvent(@RequestBody Map<String, Object> eventData) {
        log.info("REST: Executing mathematical chain from event data: {}", eventData);
        
        ChainWorkflowResult result = (ChainWorkflowResult) springAISamplesOrchestrator.executeMathematicalChainFromEvent(eventData);
        return ResponseEntity.ok(result);
    }
    
    /**
     * Get workflow statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getWorkflowStats() {
        log.info("REST: Getting workflow statistics");
        
        Map<String, Object> stats = springAISamplesOrchestrator.getWorkflowStats();
        return ResponseEntity.ok(stats);
    }
    
    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        return ResponseEntity.ok(Map.of(
            "status", "healthy",
            "framework", "Spring AI",
            "service", "Chain Workflow"
        ));
    }
} 