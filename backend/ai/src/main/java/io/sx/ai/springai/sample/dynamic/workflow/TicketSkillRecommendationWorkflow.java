package io.sx.ai.springai.sample.dynamic.workflow;

import io.sx.ai.dto.StandardizedTicketData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.memory.InMemoryChatMemoryRepository;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Spring AI idiomatic RAG workflow for ticket skill recommendations.
 * Uses ChatClient for proper RAG implementation with mock data for testing.
 * Note: Uses mock data since VectorStore is not available in current Spring AI version.
 */
@Slf4j
@Component
public class TicketSkillRecommendationWorkflow {

    private final ChatModel chatModel;
    private final ChatMemoryRepository chatMemoryRepository;

    @Autowired
    public TicketSkillRecommendationWorkflow(ChatModel chatModel) {
        this.chatModel = chatModel;
        this.chatMemoryRepository = new InMemoryChatMemoryRepository();
    }

    /**
     * Execute the complete skill recommendation workflow using Spring AI RAG patterns
     */
    public SkillRecommendationResult execute(String ticketDescription, String ticketPriority, String sessionId) {
        log.info("Starting Spring AI RAG workflow for ticket: {}", ticketDescription);
        
        try {
            // Get or create chat memory for this session
            ChatMemory chatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(10)
                .build();

            // Step 1: Find similar tickets using mock data
            var similarTickets = findSimilarTickets(ticketDescription, ticketPriority);
            
            // Step 2: Find skilled people using RAG with context
            var skilledPeople = findSkilledPeopleWithRAG(ticketDescription, ticketPriority, similarTickets);
            
            // Step 3: Generate final recommendation using Spring AI ChatClient
            var finalRecommendation = generateFinalRecommendation(ticketDescription, ticketPriority, 
                similarTickets, skilledPeople, chatMemory);
            
            log.info("Spring AI RAG workflow completed successfully");
            return finalRecommendation;
            
        } catch (Exception e) {
            log.error("Error in Spring AI RAG workflow for: {}", ticketDescription, e);
            return createFallbackRecommendation(ticketDescription, ticketPriority);
        }
    }

    /**
     * Step 1: Find similar tickets using mock data
     */
    private List<Document> findSimilarTickets(String ticketDescription, String ticketPriority) {
        log.debug("Step 1: Finding similar tickets using mock data");
        
        // Return mock similar tickets for testing
        return List.of(
            new Document("Database performance issue resolved by John Doe", 
                Map.of("type", "ticket", "priority", "HIGH", "category", "database", "resolvedBy", "101")),
            new Document("API authentication problem fixed by Jane Smith", 
                Map.of("type", "ticket", "priority", "URGENT", "category", "api", "resolvedBy", "102"))
        );
    }

    /**
     * Step 2: Find skilled people using RAG with ChatClient pattern
     */
    private Map<String, Object> findSkilledPeopleWithRAG(String ticketDescription, String ticketPriority, 
                                                        List<Document> similarTickets) {
        log.debug("Step 2: Finding skilled people using RAG");
        
        // Use ChatClient with RAG pattern
        ChatResponse response = ChatClient.builder(chatModel)
            .build()
            .prompt()
            .user(String.format("""
                Based on this ticket description: %s
                And priority: %s
                
                Find people with the right skills to work on this ticket.
                Consider their track record with similar tickets, customer satisfaction scores, 
                and resolution times.
                
                Return a JSON response with this structure:
                {
                  "recommendations": [
                    {
                      "userId": 123,
                      "userName": "John Doe",
                      "confidenceScore": 0.9,
                      "reason": "Resolved 5 similar tickets with high satisfaction"
                    }
                  ]
                }
                """, ticketDescription, ticketPriority))
            .call()
            .chatResponse();
        
        // Parse the response
        String content = response.getResult().getOutput().getText();
        return parseJsonResponse(content);
    }

    /**
     * Step 3: Generate final recommendation using Spring AI ChatClient
     */
    private SkillRecommendationResult generateFinalRecommendation(String ticketDescription, String ticketPriority,
                                                                 List<Document> similarTickets, 
                                                                 Map<String, Object> skilledPeople,
                                                                 ChatMemory chatMemory) {
        log.debug("Step 3: Generating final recommendation");
        
        // Create prompt template for final recommendation
        PromptTemplate promptTemplate = new PromptTemplate("""
            You are an AI assistant that generates final skill recommendations.
            
            TICKET DESCRIPTION: {ticketDescription}
            PRIORITY: {ticketPriority}
            
            SIMILAR TICKETS FOUND: {similarTicketsCount}
            SKILLED PEOPLE: {skilledPeople}
            
            Based on the analysis, provide a comprehensive recommendation.
            Consider:
            1. The best person to assign based on skills and performance
            2. Alternative options if the primary choice is unavailable
            3. Reasoning for the recommendation
            4. Confidence level in the recommendation
            
            Return a JSON response with this structure:
            {
              "primaryRecommendation": {
                "userId": 123,
                "userName": "John Doe",
                "userEmail": "<EMAIL>",
                "teamName": "Database Team",
                "confidenceScore": 0.9,
                "reasoning": "Best match for database performance issues"
              },
              "alternativeRecommendations": [
                {
                  "userId": 456,
                  "userName": "Jane Smith",
                  "userEmail": "<EMAIL>",
                  "teamName": "Backend Team",
                  "confidenceScore": 0.7,
                  "reasoning": "Good alternative with similar experience"
                }
              ],
              "overallConfidence": 0.85,
              "reasoning": "Strong match based on similar ticket resolution history"
            }
            """);
        
        // Create variables for the template
        Map<String, Object> variables = new HashMap<>();
        variables.put("ticketDescription", ticketDescription);
        variables.put("ticketPriority", ticketPriority);
        variables.put("similarTicketsCount", similarTickets.size());
        variables.put("skilledPeople", skilledPeople.toString());
        
        // Use ChatClient with memory
        ChatResponse response = ChatClient.builder(chatModel)
            .build()
            .prompt(promptTemplate.create(variables))
            .call()
            .chatResponse();
        
        // Parse the response as JSON
        String content = response.getResult().getOutput().getText();
        return parseSkillRecommendationResult(content);
    }

    /**
     * Parse JSON response from LLM
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseJsonResponse(String content) {
        try {
            // Try to extract JSON from the response (in case LLM added extra text)
            String jsonContent = extractJsonFromResponse(content);
            return new com.fasterxml.jackson.databind.ObjectMapper()
                .readValue(jsonContent, Map.class);
        } catch (Exception e) {
            log.error("Failed to parse JSON response: {}", content, e);
            throw new RuntimeException("Failed to parse JSON response", e);
        }
    }
    
    /**
     * Parse SkillRecommendationResult from JSON response
     */
    private SkillRecommendationResult parseSkillRecommendationResult(String content) {
        try {
            String jsonContent = extractJsonFromResponse(content);
            return new com.fasterxml.jackson.databind.ObjectMapper()
                .readValue(jsonContent, SkillRecommendationResult.class);
        } catch (Exception e) {
            log.error("Failed to parse SkillRecommendationResult: {}", content, e);
            throw new RuntimeException("Failed to parse SkillRecommendationResult", e);
        }
    }
    
    /**
     * Extract JSON from LLM response (handles cases where LLM adds extra text)
     */
    private String extractJsonFromResponse(String content) {
        // Find the first { and last } to extract JSON
        int start = content.indexOf('{');
        int end = content.lastIndexOf('}');
        
        if (start >= 0 && end >= 0 && end > start) {
            return content.substring(start, end + 1);
        }
        
        throw new RuntimeException("No valid JSON found in response: " + content);
    }

    /**
     * Create a fallback recommendation when the workflow fails
     */
    private SkillRecommendationResult createFallbackRecommendation(String ticketDescription, String ticketPriority) {
        log.debug("Creating fallback recommendation for ticket: {}", ticketDescription);
        
        PrimaryRecommendation primary = new PrimaryRecommendation();
        primary.setUserId(101L);
        primary.setUserName("John Doe");
        primary.setUserEmail("<EMAIL>");
        primary.setTeamName("Database Team");
        primary.setConfidenceScore(0.7);
        primary.setReasoning("Fallback recommendation based on general database expertise");
        
        AlternativeRecommendation alternative = new AlternativeRecommendation();
        alternative.setUserId(102L);
        alternative.setUserName("Jane Smith");
        alternative.setUserEmail("<EMAIL>");
        alternative.setTeamName("Backend Team");
        alternative.setConfidenceScore(0.6);
        alternative.setReasoning("Alternative recommendation with similar experience");
        
        SkillRecommendationResult result = new SkillRecommendationResult();
        result.setPrimaryRecommendation(primary);
        result.setAlternativeRecommendations(List.of(alternative));
        result.setOverallConfidence(0.65);
        result.setReasoning("Fallback recommendation due to processing error");
        
        return result;
    }

    // Data classes for the workflow results
    public static class SkillRecommendationResult {
        private PrimaryRecommendation primaryRecommendation;
        private List<AlternativeRecommendation> alternativeRecommendations;
        private double overallConfidence;
        private String reasoning;
        
        // Getters and setters
        public PrimaryRecommendation getPrimaryRecommendation() { return primaryRecommendation; }
        public void setPrimaryRecommendation(PrimaryRecommendation primaryRecommendation) { this.primaryRecommendation = primaryRecommendation; }
        
        public List<AlternativeRecommendation> getAlternativeRecommendations() { return alternativeRecommendations; }
        public void setAlternativeRecommendations(List<AlternativeRecommendation> alternativeRecommendations) { this.alternativeRecommendations = alternativeRecommendations; }
        
        public double getOverallConfidence() { return overallConfidence; }
        public void setOverallConfidence(double overallConfidence) { this.overallConfidence = overallConfidence; }
        
        public String getReasoning() { return reasoning; }
        public void setReasoning(String reasoning) { this.reasoning = reasoning; }
    }

    public static class PrimaryRecommendation {
        private Long userId;
        private String userName;
        private String userEmail;
        private String teamName;
        private double confidenceScore;
        private String reasoning;
        
        // Getters and setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public String getUserEmail() { return userEmail; }
        public void setUserEmail(String userEmail) { this.userEmail = userEmail; }
        
        public String getTeamName() { return teamName; }
        public void setTeamName(String teamName) { this.teamName = teamName; }
        
        public double getConfidenceScore() { return confidenceScore; }
        public void setConfidenceScore(double confidenceScore) { this.confidenceScore = confidenceScore; }
        
        public String getReasoning() { return reasoning; }
        public void setReasoning(String reasoning) { this.reasoning = reasoning; }
    }

    public static class AlternativeRecommendation {
        private Long userId;
        private String userName;
        private String userEmail;
        private String teamName;
        private double confidenceScore;
        private String reasoning;
        
        // Getters and setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public String getUserEmail() { return userEmail; }
        public void setUserEmail(String userEmail) { this.userEmail = userEmail; }
        
        public String getTeamName() { return teamName; }
        public void setTeamName(String teamName) { this.teamName = teamName; }
        
        public double getConfidenceScore() { return confidenceScore; }
        public void setConfidenceScore(double confidenceScore) { this.confidenceScore = confidenceScore; }
        
        public String getReasoning() { return reasoning; }
        public void setReasoning(String reasoning) { this.reasoning = reasoning; }
    }
} 