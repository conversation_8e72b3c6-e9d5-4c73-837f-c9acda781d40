package io.sx.ai.springai.sample.manual.numeric;

import io.sx.ai.springai.framework.chain.workflow.ChainStepResult;
import io.sx.ai.springai.framework.chain.workflow.ChainWorkflowResult;
import io.sx.ai.springai.framework.common.BaseWorkflowContext;
import io.sx.ai.springai.framework.common.BaseWorkflowOrchestrator;
import io.sx.ai.springai.framework.common.BaseWorkflowResult;
import io.sx.ai.springai.framework.common.BaseStepResult;
import io.sx.ai.springai.framework.common.NumericInput;
import io.sx.ai.springai.framework.common.NumericOutput;
import io.sx.ai.springai.framework.common.WorkflowInput;
import io.sx.ai.springai.framework.common.WorkflowUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.sx.ai.abstraction.ResolvedPrompt;
import io.sx.ai.abstraction.SxLLMClient;
import io.sx.ai.abstraction.PromptResolutionService;
import io.sx.ai.springai.spring.SxSpringLLMClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import io.sx.ai.springai.framework.common.TextInput;
import io.sx.ai.springai.framework.common.TextOutput;
import io.sx.ai.abstraction.SxLLMResponse;
import io.sx.ai.springai.framework.common.LLMDecision;

@Component
public class ArithmeticChainWorkflow implements BaseWorkflowOrchestrator<Double, Double> {

    private static final Logger log = LoggerFactory.getLogger(ArithmeticChainWorkflow.class);
    
    private final SxLLMClient llmClient;
    private final PromptResolutionService promptResolutionService;
    
    // Template constants
    private static final String SYSTEM_TEMPLATE_PATH = "templates/prompts/sample/mathematical-expert-system.st";
    private static final String OPERATION_TEMPLATE_PATH = "templates/prompts/sample/mathematical-operation.st";
    private static final String CHAIN_STEP_TEMPLATE_PATH = "templates/prompts/sample/chain-workflow-step.st";

    @Autowired
    public ArithmeticChainWorkflow(SxLLMClient llmClient, PromptResolutionService promptResolutionService) {
        this.llmClient = llmClient;
        this.promptResolutionService = promptResolutionService;
    }

    /**
     * Execute the mathematical chain workflow
     * Input -> Divide by 2 -> Multiply by 4
     */
    public ChainWorkflowResult<String, String> executeMathematicalChain(String input) {
        log.info("Starting mathematical chain workflow with input: {}", input);
        
        List<ChainStepResult<String, String>> steps = new ArrayList<>();
        
        // Step 1: Divide by 2
        ChainStepResult<String, String> step1 = executeStep("halving_step", "Divide the input by 2", input, null);
        steps.add(step1);
        
        // Step 2: Multiply by 4 (using result from step 1)
        ChainStepResult<String, String> step2 = executeStepWithHistory("quadrupling_step", "Multiply the input by 4", 
            step1.getOutput().toString(), step1);
        steps.add(step2);
        
        ChainWorkflowResult<String, String> result = ChainWorkflowResult.<String, String>chainBuilder()
            .originalInput(TextInput.of(input))
            .finalOutput(TextOutput.of(step2.getOutput().toString()))
            .steps(new ArrayList<BaseStepResult<String, String>>(steps))
            .success(true)
            .sessionId("arithmetic-chain")
            .workflowType("ARITHMETIC_CHAIN")
            .build();
            
        log.info("Mathematical chain workflow completed - Input: {}, Final Output: {}, Steps: {}", 
            result.getInputValue(), result.getOutputValue(), result.getStepCount());
            
        return result;
    }
    
    /**
     * Execute workflow using the common BaseWorkflowOrchestrator interface
     */
    @Override
    public BaseWorkflowResult<Double, Double> executeWorkflow(WorkflowInput<Double> input, String sessionId, Long organizationId, Long onBehalfOfId) {
        LocalDateTime startTime = LocalDateTime.now();
        log.debug("Starting chain workflow with input: {}, sessionId: {}", input.getValue(), sessionId);
        
        try {
            ChainWorkflowResult<String, String> chainResult = executeMathematicalChain(input.getValue().toString());
            
            // Convert ChainStep to BaseStepResult
            List<BaseStepResult<Double, Double>> baseSteps = new ArrayList<>();
            List<BaseStepResult<String, String>> chainStepResults = chainResult.getSteps();
            for (BaseStepResult<String, String> step : chainStepResults) {
                // Since we know these are ChainStep results, we can extract the data
                BaseStepResult<Double, Double> baseStep = BaseStepResult.success(
                    step.getStepNumber(),
                    step.getStepName(),
                    Double.parseDouble(step.getInput().toString()),
                    Double.parseDouble(step.getOutput().toString()),
                    step.getOperation()
                );
                baseSteps.add(baseStep);
            }
            
            NumericOutput finalOutput = NumericOutput.of(Double.parseDouble(chainResult.getOutputValue().toString()));
            return BaseWorkflowResult.success(input, finalOutput, baseSteps, sessionId, "MATHEMATICAL_CHAIN");
            
        } catch (Exception e) {
            log.error("Error executing chain workflow: {}", e.getMessage(), e);
            return BaseWorkflowResult.error(input, e.getMessage());
        }
    }

    /**
     * Execute a single step with system and user messages
     */
    private ChainStepResult<String, String> executeStep(String operation, String operationDescription, String input, String previousResult) {
        log.debug("Executing step: {} with input: {}", operation, input);
        
        try {
            // Load templates
            String systemTemplate = loadTemplate(SYSTEM_TEMPLATE_PATH);
            String operationTemplate = loadTemplate(OPERATION_TEMPLATE_PATH);
            
            // Create user message with variables
            Map<String, Object> variables = Map.of(
                "input", input,
                "operation", operationDescription
            );
            
            // Resolve prompts using the new abstraction
            ResolvedPrompt systemPrompt = promptResolutionService.resolveSystemPrompt(systemTemplate, new HashMap<>());
            ResolvedPrompt userPrompt = promptResolutionService.resolveUserPrompt(operationTemplate, variables);
            
            // Execute the step using resolved prompts
            SxSpringLLMClient springLLMClient = (SxSpringLLMClient) llmClient;
            List<ResolvedPrompt> prompts = List.of(systemPrompt, userPrompt);
            ResolvedPrompt combinedPrompt = promptResolutionService.combinePrompts(prompts);
            SxLLMResponse<LLMDecision> response = springLLMClient.generate(combinedPrompt, LLMDecision.class);
            LLMDecision decision = response.getOutput();
            
            log.debug("Step completed - Input: {}, Output: {}", input, decision.getFinalResult());
            
            return ChainStepResult.<String, String>builder()
                .stepNumber(1)
                .stepName(operation)
                .input(input)
                .output(decision.getFinalResult() != null ? decision.getFinalResult().toString() : "0.0")
                .operation(operationDescription)
                .prompt(userPrompt.getResolvedText())
                .success(true)
                .build();
                
        } catch (IOException e) {
            log.error("Failed to load template for step: {}", operation, e);
            throw new RuntimeException("Template loading failed", e);
        }
    }

    /**
     * Execute a step with conversation history (for multi-step chains)
     */
    private ChainStepResult<String, String> executeStepWithHistory(String operation, String operationDescription, 
                                           String input, ChainStepResult<String, String> previousStep) {
        log.debug("Executing step: {} with input: {} and history", operation, input);
        
        try {
            // Load templates
            String systemTemplate = loadTemplate(SYSTEM_TEMPLATE_PATH);
            String chainStepTemplate = loadTemplate(CHAIN_STEP_TEMPLATE_PATH);
            
            // Create current step message
            Map<String, Object> variables = Map.of(
                "stepNumber", previousStep.getStepNumber() + 1,
                "operation", operationDescription,
                "input", input,
                "previousResult", previousStep.getOutput().toString()
            );
            
            // Resolve prompts using the new abstraction
            ResolvedPrompt systemPrompt = promptResolutionService.resolveSystemPrompt(systemTemplate, new HashMap<>());
            ResolvedPrompt userPrompt = promptResolutionService.resolveUserPrompt(chainStepTemplate, variables);
            
            // Execute the step using resolved prompts
            SxSpringLLMClient springLLMClient = (SxSpringLLMClient) llmClient;
            List<ResolvedPrompt> prompts = List.of(systemPrompt, userPrompt);
            ResolvedPrompt combinedPrompt = promptResolutionService.combinePrompts(prompts);
            SxLLMResponse<LLMDecision> response = springLLMClient.generate(combinedPrompt, LLMDecision.class);
            LLMDecision decision = response.getOutput();
            
            log.debug("Step with history completed - Input: {}, Output: {}", input, decision.getFinalResult());
            
            return ChainStepResult.<String, String>builder()
                .stepNumber(previousStep.getStepNumber() + 1)
                .stepName(operation)
                .input(input)
                .output(decision.getFinalResult() != null ? decision.getFinalResult().toString() : "0.0")
                .operation(operationDescription)
                .prompt(userPrompt.getResolvedText())
                .success(true)
                .build();
                
        } catch (IOException e) {
            log.error("Failed to load template for step with history: {}", operation, e);
            throw new RuntimeException("Template loading failed", e);
        }
    }

    /**
     * Load template from classpath resources
     */
    private String loadTemplate(String templatePath) throws IOException {
        return WorkflowUtils.loadTemplate(templatePath);
    }

    /**
     * Extract numeric output from LLM response
     */
    private String extractNumericOutput(String response) {
        return WorkflowUtils.extractNumericOutput(response);
    }
    
    /**
     * Execute a step and return as BaseStepResult
     */
    private BaseStepResult<Double, Double> executeStepAsBaseStep(String operation, String operationDescription, String input, String previousResult) {
        try {
            ChainStepResult<String, String> step = executeStep(operation, operationDescription, input, previousResult);
            return BaseStepResult.success(
                step.getStepNumber(),
                step.getStepName(),
                Double.parseDouble(step.getInput().toString()),
                Double.parseDouble(step.getOutput().toString()),
                step.getOperation()
            );
        } catch (Exception e) {
            log.error("Error executing step as base step: {}", e.getMessage(), e);
            return BaseStepResult.error(1, operation, Double.parseDouble(input), e.getMessage());
        }
    }
    
    // BaseWorkflowOrchestrator interface implementations
    
    @Override
    public BaseWorkflowResult<Double, Double> executeWorkflowFromEvent(Map<String, Object> eventData, String sessionId, Long organizationId, Long onBehalfOfId) {
        String inputValue = WorkflowUtils.extractInputFromEvent(eventData);
        if (inputValue == null) {
            NumericInput nullInput = NumericInput.of(0.0);
            return WorkflowUtils.createErrorResult(nullInput, "Could not extract input from event data", sessionId);
        }
        NumericInput input = NumericInput.of(Double.parseDouble(inputValue));
        return executeWorkflow(input, sessionId, organizationId, onBehalfOfId);
    }
    
    @Override
    public BaseStepResult<Double, Double> executeWorkflowStep(String sessionId, String stepId, Map<String, Object> context, Long organizationId, Long onBehalfOfId) {
        // For chain workflow, we execute the specific step
        String inputValue = (String) context.get("input");
        if (inputValue == null) {
            return BaseStepResult.error(0, stepId, 0.0, "Input not found in context");
        }
        
        // Execute the step based on stepId
        String operation = stepId;
        String operationDescription = "Execute " + stepId;
        
        return executeStepAsBaseStep(operation, operationDescription, inputValue, null);
    }
    
    @Override
    public BaseWorkflowContext<Double, Double> getWorkflowStatus(String sessionId) {
        // For chain workflow, we don't maintain persistent state
        NumericInput unknownInput = NumericInput.of(0.0);
        return BaseWorkflowContext.initialize(sessionId, unknownInput, 2);
    }
    
    @Override
    public Map<String, String> getAvailableWorkflowTypes() {
        return Map.of(
            "MATHEMATICAL_CHAIN", "Mathematical chain workflow: divide by 2, then multiply by 4"
        );
    }
    
    @Override
    public boolean validateWorkflowParameters(String workflowType, Map<String, Object> parameters) {
        if (!"MATHEMATICAL_CHAIN".equals(workflowType)) {
            return false;
        }
        
        Object input = parameters.get("input");
        return input != null && !input.toString().trim().isEmpty();
    }
    
    @Override
    public Map<String, Object> getWorkflowStats(Long organizationId) {
        return Map.of(
            "workflow_type", "MATHEMATICAL_CHAIN",
            "total_executions", 0, // Would be tracked in a real implementation
            "success_rate", 1.0,
            "average_execution_time_ms", 1000
        );
    }
    
    @Override
    public boolean cancelWorkflow(String sessionId, String reason) {
        // Chain workflow executes quickly and doesn't support cancellation
        return false;
    }
    
    @Override
    public List<BaseWorkflowResult<Double, Double>> getWorkflowHistory(Long organizationId, int limit) {
        // Would return actual history in a real implementation
        return new ArrayList<>();
    }
} 