package io.sx.ai.springai.spring;

import io.sx.ai.abstraction.BaseResolvedPrompt;
import io.sx.ai.abstraction.ResolvedPromptValidator;
import io.sx.ai.abstraction.SxMessage;
import io.sx.ai.abstraction.SxPrompt;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Spring AI implementation of ResolvedPrompt.
 * Extends BaseResolvedPrompt with Spring AI specific functionality.
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SpringResolvedPrompt extends BaseResolvedPrompt {
    
    // Spring AI specific fields can be added here if needed
    // For now, we inherit all functionality from BaseResolvedPrompt
    
    /**
     * Create a SpringResolvedPrompt from a template and variables
     */
    public static SpringResolvedPrompt fromTemplate(String template, Map<String, Object> variables, SxPrompt.PromptType promptType, SxMessage.PromptRole messageRole) {
        String resolvedText = resolveTemplate(template, variables);
        List<SxMessage> resolvedMessages = createMessages(resolvedText, messageRole);
        
        return SpringResolvedPrompt.builder()
            .resolvedText(resolvedText)
            .resolvedMessages(resolvedMessages)
            .originalVariables(new HashMap<>(variables))
            .promptType(promptType)
            .messageRole(messageRole)
            .templateSource(template)
            .fullyResolved(ResolvedPromptValidator.isFullyResolved(resolvedText))
            .build();
    }
    
    /**
     * Create a system prompt
     */
    public static SpringResolvedPrompt systemPrompt(String template, Map<String, Object> variables) {
        return fromTemplate(template, variables, SxPrompt.PromptType.CHAT, SxMessage.PromptRole.SYSTEM);
    }
    
    /**
     * Create a user prompt
     */
    public static SpringResolvedPrompt userPrompt(String template, Map<String, Object> variables) {
        return fromTemplate(template, variables, SxPrompt.PromptType.CHAT, SxMessage.PromptRole.USER);
    }
    
    /**
     * Create an assistant prompt
     */
    public static SpringResolvedPrompt assistantPrompt(String template, Map<String, Object> variables) {
        return fromTemplate(template, variables, SxPrompt.PromptType.CHAT, SxMessage.PromptRole.ASSISTANT);
    }
    
    /**
     * Create a structured output prompt
     */
    public static SpringResolvedPrompt structuredOutputPrompt(String template, Map<String, Object> variables) {
        return fromTemplate(template, variables, SxPrompt.PromptType.STRUCTURED_OUTPUT, SxMessage.PromptRole.USER);
    }
    
    /**
     * Resolve a template with variables using Spring AIs PromptTemplate
     */
    private static String resolveTemplate(String template, Map<String, Object> variables) {
        if (template == null || template.trim().isEmpty()) {
            return "";
        }
        
        if (variables == null || variables.isEmpty()) {
            return template;
        }
        
        try {
            org.springframework.ai.chat.prompt.PromptTemplate springTemplate = 
                new org.springframework.ai.chat.prompt.PromptTemplate(template);
            return springTemplate.render(variables);
        } catch (Exception e) {
            throw new RuntimeException("Failed to resolve template: " + template, e);
        }
    }
    
    /**
     * Create messages from resolved text
     */
    private static List<SxMessage> createMessages(String resolvedText, SxMessage.PromptRole messageRole) {
        List<SxMessage> messages = new ArrayList<>();
        
        switch (messageRole) {
            case SYSTEM:
                messages.add(new SxSpringMessage(new org.springframework.ai.chat.messages.SystemMessage(resolvedText)));
                break;
            case USER:
                messages.add(new SxSpringMessage(new org.springframework.ai.chat.messages.UserMessage(resolvedText)));
                break;
            case ASSISTANT:
                messages.add(new SxSpringMessage(new org.springframework.ai.chat.messages.AssistantMessage(resolvedText)));
                break;
            default:
                messages.add(new SxSpringMessage(new org.springframework.ai.chat.messages.UserMessage(resolvedText)));
                break;
        }
        
        return messages;
    }
} 