package io.sx.ai.springai.framework.common;

import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Generic input wrapper with Spring AI integration
 * Provides type-safe input handling for different workflow contexts
 */
public interface WorkflowInput<T> {
    
    /**
     * Get the actual input value
     */
    T getValue();
    
    /**
     * Get the input type identifier
     */
    String getType();
    
    /**
     * Get metadata associated with this input
     */
    Map<String, Object> getMetadata();
    
    /**
     * Create a Spring AI Prompt from this input using a template
     * This integrates with Spring AI's PromptTemplate system
     */
    default Prompt createPrompt(String template, Map<String, Object> variables) {
        Map<String, Object> enhancedVariables = new HashMap<>(variables);
        enhancedVariables.put("inputValue", getValue());
        enhancedVariables.put("inputType", getType());
        enhancedVariables.putAll(getMetadata());
        
        PromptTemplate promptTemplate = new PromptTemplate(template);
        return promptTemplate.create(enhancedVariables);
    }
    
    /**
     * Get a human-readable description of this input
     */
    default String getDescription() {
        return String.format("%s input: %s", getType(), getValue());
    }
} 