package io.sx.ai.tools.logic;

import java.util.Arrays;

/**
 * Plain tool logic for text decoration operations.
 * Framework-agnostic implementation that can be used by any AI framework.
 */
public class TextDecorationLogic {
    
    /**
     * Decorate text with simple borders using the specified delimiter
     */
    public String decorateSimple(String text, String delimiter) {
        if (text == null || text.trim().isEmpty()) {
            throw new IllegalArgumentException("Text cannot be null or empty");
        }
        if (delimiter == null || delimiter.isEmpty()) {
            delimiter = "=";
        }
        
        String border = delimiter.repeat(50);
        return String.format("%s\n%s\n%s", border, text, border);
    }
    
    /**
     * Decorate text with box-style borders using the specified delimiter
     */
    public String decorateBox(String text, String delimiter) {
        if (text == null || text.trim().isEmpty()) {
            throw new IllegalArgumentException("Text cannot be null or empty");
        }
        if (delimiter == null || delimiter.isEmpty()) {
            delimiter = "*";
        }
        
        String[] lines = text.split("\n");
        int maxLength = Arrays.stream(lines).mapToInt(String::length).max().orElse(0);
        String border = delimiter.repeat(maxLength + 4);
        
        StringBuilder result = new StringBuilder();
        result.append(border).append("\n");
        
        for (String line : lines) {
            result.append(delimiter).append(" ").append(line);
            result.append(" ".repeat(Math.max(0, maxLength - line.length() + 1)));
            result.append(delimiter).append("\n");
        }
        
        result.append(border);
        return result.toString();
    }
    
    /**
     * Decorate text with fancy Unicode box borders
     */
    public String decorateFancy(String text, String delimiter) {
        if (text == null || text.trim().isEmpty()) {
            throw new IllegalArgumentException("Text cannot be null or empty");
        }
        
        String border = "╔" + "═".repeat(48) + "╗\n";
        String bottomBorder = "╚" + "═".repeat(48) + "╝";
        
        StringBuilder result = new StringBuilder();
        result.append(border);
        
        String[] lines = text.split("\n");
        for (String line : lines) {
            result.append("║ ").append(line);
            result.append(" ".repeat(Math.max(0, 47 - line.length())));
            result.append(" ║\n");
        }
        
        result.append(bottomBorder);
        return result.toString();
    }
    
    /**
     * Count words in the given text
     */
    public int countWords(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 0;
        }
        return text.trim().split("\\s+").length;
    }
    
    /**
     * Count characters in the given text (excluding whitespace)
     */
    public int countCharacters(String text) {
        if (text == null) {
            return 0;
        }
        return text.replaceAll("\\s", "").length();
    }
} 