package io.sx.ai.springai.spring;

import io.sx.ai.abstraction.SxMessage;
import lombok.Data;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.messages.AssistantMessage;

import java.util.HashMap;
import java.util.Map;

/**
 * Spring AI implementation of SxMessage
 * Wraps Spring AIs Message classes
 */
@Data
public class SxSpringMessage implements SxMessage {
    
    private final Message springMessage;
    private final String content;
    private final PromptRole type;
    private final String role;
    private final Map<String, Object> metadata;
    
    public SxSpringMessage(Message springMessage) {
        this.springMessage = springMessage;
        
        // Extract content from Spring AI message
        if (springMessage instanceof SystemMessage) {
            this.content = ((SystemMessage) springMessage).getText();
            this.type = PromptRole.SYSTEM;
            this.role = "system";
        } else if (springMessage instanceof UserMessage) {
            this.content = ((UserMessage) springMessage).getText();
            this.type = PromptRole.USER;
            this.role = "user";
        } else if (springMessage instanceof AssistantMessage) {
            this.content = ((AssistantMessage) springMessage).getText();
            this.type = PromptRole.ASSISTANT;
            this.role = "assistant";
        } else {
            // Fallback for unknown message types
            this.content = springMessage.toString();
            this.type = PromptRole.USER;
            this.role = "unknown";
        }
        
        this.metadata = new HashMap<>();
        // Add Spring AI specific metadata
        this.metadata.put("spring_ai_message_type", springMessage.getClass().getSimpleName());
    }
    
    @Override
    public String getContent() {
        return content;
    }
    
    @Override
    public PromptRole getType() {
        return type;
    }
    
    @Override
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    @Override
    public String getRole() {
        return role;
    }
    
    @Override
    public boolean hasContent() {
        return content != null && !content.trim().isEmpty();
    }
    
    @Override
    public String getId() {
        // Spring AI messages don't have IDs by default
        // Generate a hash-based ID from content and type
        return String.valueOf((content + type.name()).hashCode());
    }
    
    /**
     * Get the underlying Spring AI message
     */
    public Message getSpringMessage() {
        return springMessage;
    }
    
    /**
     * Create a system message
     */
    public static SxSpringMessage createSystemMessage(String content) {
        return new SxSpringMessage(new SystemMessage(content));
    }
    
    /**
     * Create a user message
     */
    public static SxSpringMessage createUserMessage(String content) {
        return new SxSpringMessage(new UserMessage(content));
    }
    
    /**
     * Create an assistant message
     */
    public static SxSpringMessage createAssistantMessage(String content) {
        return new SxSpringMessage(new AssistantMessage(content));
    }
} 