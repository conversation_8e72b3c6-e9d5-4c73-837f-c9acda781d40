package io.sx.ai.decision.engine;

import io.sx.ai.decision.model.AIDecisionContext;
import io.sx.ai.service.AIConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Builder for AI decision context
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AIDecisionContextBuilder {
    
    private final AIConfigurationService aiConfigurationService;
    
    /**
     * Build decision context from trigger event and data
     */
    public AIDecisionContext buildContext(String triggerEvent, Map<String, Object> eventData, Long supportOrgId) {
        log.debug("Building AI decision context for event: {}, org: {}", triggerEvent, supportOrgId);
        
        // Extract entity information from event data
        Long entityId = extractEntityId(eventData);
        String entityType = extractEntityType(triggerEvent);
        Long organizationId = extractOrganizationId(eventData);
        Long userId = extractUserId(eventData);
        
        // Get AI configuration for the organization
        Map<String, Object> aiConfiguration = getAIConfiguration(supportOrgId);
        
        return AIDecisionContext.builder()
            .triggerEvent(triggerEvent)
            .entityId(entityId)
            .entityType(entityType)
            .organizationId(organizationId)
            .supportOrganizationId(supportOrgId)
            .userId(userId)
            .eventData(eventData)
            .entityData(extractEntityData(eventData))
            .organizationData(extractOrganizationData(eventData))
            .userData(extractUserData(eventData))
            .historicalData(extractHistoricalData(eventData))
            .aiConfiguration(aiConfiguration)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    /**
     * Extract entity ID from event data
     */
    private Long extractEntityId(Map<String, Object> eventData) {
        // Try different possible field names for entity ID
        Object entityId = eventData.get("id");
        if (entityId == null) {
            entityId = eventData.get("entityId");
        }
        if (entityId == null) {
            entityId = eventData.get("ticketId");
        }
        if (entityId instanceof Number) {
            return ((Number) entityId).longValue();
        }
        return null;
    }
    
    /**
     * Extract entity type from trigger event
     */
    private String extractEntityType(String triggerEvent) {
        if (triggerEvent.startsWith("ticket.")) {
            return "ticket";
        }
        return "unknown";
    }
    
    /**
     * Extract organization ID from event data
     */
    private Long extractOrganizationId(Map<String, Object> eventData) {
        // Try different possible field names for organization ID
        Object orgId = eventData.get("organization_id");
        if (orgId == null) {
            orgId = eventData.get("organizationId");
        }
        if (orgId == null) {
            orgId = eventData.get("orgId");
        }
        if (orgId instanceof Number) {
            return ((Number) orgId).longValue();
        }
        return null;
    }
    
    /**
     * Extract user ID from event data
     */
    private Long extractUserId(Map<String, Object> eventData) {
        // Try different possible field names for user ID
        Object userId = eventData.get("created_by_id");
        if (userId == null) {
            userId = eventData.get("userId");
        }
        if (userId == null) {
            userId = eventData.get("user_id");
        }
        if (userId instanceof Number) {
            return ((Number) userId).longValue();
        }
        return null;
    }
    
    /**
     * Extract entity-specific data from event data
     */
    private Map<String, Object> extractEntityData(Map<String, Object> eventData) {
        Map<String, Object> entityData = new HashMap<>();
        
        // Extract common entity fields
        if (eventData.containsKey("description")) {
            entityData.put("description", eventData.get("description"));
        }
        if (eventData.containsKey("priority")) {
            entityData.put("priority", eventData.get("priority"));
        }
        if (eventData.containsKey("customerImportance")) {
            entityData.put("customerImportance", eventData.get("customerImportance"));
        }
        if (eventData.containsKey("title")) {
            entityData.put("title", eventData.get("title"));
        }
        
        return entityData;
    }
    
    /**
     * Extract organization data from event data
     */
    private Map<String, Object> extractOrganizationData(Map<String, Object> eventData) {
        Map<String, Object> orgData = new HashMap<>();
        
        if (eventData.containsKey("organizationType")) {
            orgData.put("type", eventData.get("organizationType"));
        }
        if (eventData.containsKey("organizationName")) {
            orgData.put("name", eventData.get("organizationName"));
        }
        
        return orgData;
    }
    
    /**
     * Extract user data from event data
     */
    private Map<String, Object> extractUserData(Map<String, Object> eventData) {
        Map<String, Object> userData = new HashMap<>();
        
        if (eventData.containsKey("userRole")) {
            userData.put("role", eventData.get("userRole"));
        }
        if (eventData.containsKey("userName")) {
            userData.put("name", eventData.get("userName"));
        }
        
        return userData;
    }
    
    /**
     * Extract historical data from event data
     */
    private Map<String, Object> extractHistoricalData(Map<String, Object> eventData) {
        Map<String, Object> historicalData = new HashMap<>();
        
        if (eventData.containsKey("previousClassifications")) {
            historicalData.put("classifications", eventData.get("previousClassifications"));
        }
        if (eventData.containsKey("similarTickets")) {
            historicalData.put("similarTickets", eventData.get("similarTickets"));
        }
        
        return historicalData;
    }
    
    /**
     * Get AI configuration for the organization
     */
    private Map<String, Object> getAIConfiguration(Long supportOrgId) {
        try {
            return aiConfigurationService.getConfiguration("classification", supportOrgId);
        } catch (Exception e) {
            log.warn("Failed to get AI configuration for org: {}", supportOrgId, e);
            return new HashMap<>();
        }
    }
} 