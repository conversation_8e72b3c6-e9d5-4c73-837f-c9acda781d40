package io.sx.ai.springai.sample.dynamic.controller;

import io.sx.ai.springai.framework.common.BaseWorkflowResult;
import io.sx.ai.springai.sample.orchestrator.SpringAISamplesOrchestrator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * REST controller for dynamic workflow operations.
 * Provides endpoints for executing LLM-controlled workflows.
 */
@RestController
@RequestMapping("/api/workflows/dynamic")
@RequiredArgsConstructor
@Slf4j
public class DynamicWorkflowController {

    private final SpringAISamplesOrchestrator orchestrator;

    /**
     * Execute a dynamic workflow with the given input number.
     * 
     * @param request The workflow execution request
     * @return The workflow execution result
     */
    @PostMapping("/execute")
    public ResponseEntity<BaseWorkflowResult<Double, Double>> executeWorkflow(@RequestBody WorkflowExecutionRequest request) {
        try {
            BaseWorkflowResult<Double, Double> result = orchestrator.executeDynamicWorkflow(
                request.getInputNumber(),
                request.getMaxIterations(),
                request.getOrganizationId(),
                request.getOnBehalfOfId()
            );
            
            log.info("Dynamic workflow executed via API: {}", result.getSummary());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error executing dynamic workflow", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Execute a dynamic workflow from event data.
     * 
     * @param eventData The event data containing workflow parameters
     * @return The workflow execution result
     */
    @PostMapping("/execute-from-event")
    public ResponseEntity<BaseWorkflowResult<Double, Double>> executeFromEvent(@RequestBody Map<String, Object> eventData) {
        try {
            BaseWorkflowResult<Double, Double> result = orchestrator.executeDynamicWorkflowFromEvent(
                eventData,
                (Long) eventData.get("organizationId"),
                (Long) eventData.get("onBehalfOfId")
            );
            
            log.info("Dynamic workflow executed from event: {}", result.getSummary());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error executing dynamic workflow from event", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Request model for workflow execution.
     */
    public static class WorkflowExecutionRequest {
        private double inputNumber;
        private int maxIterations = 4;
        private Long organizationId;
        private Long onBehalfOfId;

        // Getters and setters
        public double getInputNumber() { return inputNumber; }
        public void setInputNumber(double inputNumber) { this.inputNumber = inputNumber; }

        public int getMaxIterations() { return maxIterations; }
        public void setMaxIterations(int maxIterations) { this.maxIterations = maxIterations; }

        public Long getOrganizationId() { return organizationId; }
        public void setOrganizationId(Long organizationId) { this.organizationId = organizationId; }

        public Long getOnBehalfOfId() { return onBehalfOfId; }
        public void setOnBehalfOfId(Long onBehalfOfId) { this.onBehalfOfId = onBehalfOfId; }
    }
} 