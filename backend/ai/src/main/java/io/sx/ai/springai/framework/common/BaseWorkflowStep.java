package io.sx.ai.springai.framework.common;

import org.springframework.ai.chat.prompt.Prompt;

import java.util.Map;

/**
 * Base interface for workflow steps with type safety
 * Provides common contract for step execution, identification, and metadata
 */
public interface BaseWorkflowStep<I, O> {
    
    /**
     * Execute this step with the given typed input
     * @param input The typed input value for this step
     * @param previousStep The previous step result (null for first step)
     * @return The result of executing this step
     */
    BaseStepResult<I, O> execute(WorkflowInput<I> input, BaseStepResult<?, ?> previousStep);
    
    /**
     * Get the name/identifier of this step
     * @return Step name
     */
    String getStepName();
    
    /**
     * Get the description of what this step does
     * @return Step description
     */
    String getStepDescription();
    
    /**
     * Check if this is the final step in the workflow
     * @return true if this is the final step
     */
    boolean isFinalStep();
    
    /**
     * Get the step type/category
     * @return Step type
     */
    default StepType getStepType() {
        return StepType.GENERAL;
    }
    
    /**
     * Get estimated execution time for this step
     * @return Estimated time in milliseconds
     */
    default long getEstimatedExecutionTime() {
        return 1000; // Default 1 second
    }
    
    /**
     * Validate step parameters before execution
     * @param input Input to validate
     * @return true if input is valid
     */
    default boolean validateInput(WorkflowInput<I> input) {
        return input != null && input.getValue() != null;
    }
    
    /**
     * Process the input and return a typed output
     * This is the core processing method that subclasses should implement
     */
    WorkflowOutput<O> processInput(WorkflowInput<I> input, BaseStepResult<?, ?> previousStep);
    
    /**
     * Create a Spring AI prompt for this step
     * Default implementation uses the input's createPrompt method
     */
    default Prompt createStepPrompt(WorkflowInput<I> input, String template, Map<String, Object> variables) {
        return input.createPrompt(template, variables);
    }
    
    /**
     * Step types for categorization
     */
    enum StepType {
        GENERAL,
        LLM_ANALYSIS,
        TOOL_EXECUTION,
        DECISION_POINT,
        TERMINATION,
        MATHEMATICAL_OPERATION,
        DATA_TRANSFORMATION,
        TEXT_PROCESSING,
        OBJECT_TRANSFORMATION,
        VALIDATION
    }
} 