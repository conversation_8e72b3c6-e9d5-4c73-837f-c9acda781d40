package io.sx.ai.config;

import io.nats.client.Connection;
import io.nats.client.Nats;
import io.nats.client.Options;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.time.Duration;

@Slf4j
@Configuration
public class NatsConfig {

    @Value("${nats.url:nats://localhost:4222}")
    private String natsUrl;

    @Value("${nats.connection.timeout:5000}")
    private int connectionTimeout;

    @Value("${nats.connection.reconnect.wait:1000}")
    private int reconnectWait;

    @Value("${nats.connection.max.reconnect.attempts:5}")
    private int maxReconnectAttempts;

    @Bean
    public Connection natsConnection() throws IOException, InterruptedException {
        log.info("Initializing NATS connection to: {}", natsUrl);

        Options options = new Options.Builder()
                .server(natsUrl)
                .connectionTimeout(Duration.ofMillis(connectionTimeout))
                .reconnectWait(Duration.ofMillis(reconnectWait))
                .maxReconnects(maxReconnectAttempts)
                .connectionName("ai-service")
                .build();

        Connection connection = Nats.connect(options);
        log.info("NATS connection established successfully");

        return connection;
    }
}
