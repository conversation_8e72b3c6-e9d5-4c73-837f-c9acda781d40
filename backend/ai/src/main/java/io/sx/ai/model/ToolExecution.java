package io.sx.ai.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.sx.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;
import java.util.Map;

/**
 * ToolExecution model for tracking individual tool execution attempts
 * Links to suggestions and supports retry logic with clear attempt tracking
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ToolExecution extends BaseModel {
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("suggestion_id")
    private Long suggestionId; // Foreign key to suggestions table
    
    @JsonProperty("tool_name")
    private String toolName; // Name of the tool being executed
    
    @JsonProperty("parameters")
    private Map<String, Object> parameters; // Tool parameters as JSONB
    
    @JsonProperty("execution_timestamp")
    private Instant executionTimestamp; // When this attempt was executed
    
    @JsonProperty("execution_time_ms")
    private Long executionTimeMs; // How long this attempt took
    
    @JsonProperty("success")
    private Boolean success; // Whether this attempt succeeded
    
    @JsonProperty("result")
    private Map<String, Object> result; // Tool execution result as JSONB
    
    @JsonProperty("error_message")
    private String errorMessage; // Error message if failed
    
    @JsonProperty("confidence_score")
    private Double confidenceScore; // Confidence score for this execution
    
    @JsonProperty("attempt_number")
    private Integer attemptNumber; // Which attempt this is (1st, 2nd, 3rd...)
    
    @JsonProperty("max_attempts")
    private Integer maxAttempts; // Maximum attempts allowed for this tool
    
    @JsonProperty("execution_status")
    private ExecutionStatus executionStatus; // PENDING, EXECUTING, SUCCESS, FAILED
    
    @JsonProperty("next_attempt_at")
    private Instant nextAttemptAt; // When to try next attempt (if failed)
    
    @JsonProperty("failure_reason")
    private String failureReason; // Why this attempt failed
    
    @JsonProperty("tool_config_hash")
    private String toolConfigHash; // Hash of tool configuration used
    
    @JsonProperty("depends_on_tool_execution_id")
    private Long dependsOnToolExecutionId; // Dependency on another tool execution
    
    @JsonProperty("execution_order")
    private Integer executionOrder; // Order within suggestion (0 = no order)
    
    @JsonProperty("rollback_required")
    private Boolean rollbackRequired; // Whether rollback is needed
    
    @JsonProperty("rollback_executed")
    private Boolean rollbackExecuted; // Whether rollback was performed
    
    @JsonProperty("rollback_result")
    private Map<String, Object> rollbackResult; // Result of rollback operation
    
    @JsonProperty("scheduled_at")
    private Instant scheduledAt; // When to execute (for delayed execution)
    
    @JsonProperty("execution_priority")
    private Integer executionPriority; // Priority (1=highest, 10=lowest)
    
    @JsonProperty("execution_context")
    private Map<String, Object> executionContext; // Context at execution time
    
    @JsonProperty("state_snapshot")
    private Map<String, Object> stateSnapshot; // System state at execution time
    
    /**
     * Execution status for tool attempts
     */
    public enum ExecutionStatus {
        PENDING,    // Waiting to be executed
        EXECUTING,  // Currently being executed
        SUCCESS,    // Successfully completed
        FAILED      // Failed to complete
    }
} 