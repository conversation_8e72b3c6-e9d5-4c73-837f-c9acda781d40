package io.sx.ai.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sx.ai.model.AIConfiguration;
import io.sx.ai.repository.AIConfigurationRepository;
import io.sx.ai.service.AIConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Implementation of AI configuration service using JDBC
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AIConfigurationServiceImpl implements AIConfigurationService {
    
    private final AIConfigurationRepository aiConfigurationRepository;
    private final ObjectMapper objectMapper;
    
    @Override
    public Map<String, Object> getConfiguration(String configurationType, Long supportOrgId) {
        log.debug("Getting AI configuration for type: {}, org: {}", configurationType, supportOrgId);
        
        try {
            // First try to get organization-specific configuration
            AIConfiguration orgConfig = aiConfigurationRepository.findByTypeAndOrganization(
                supportOrgId, configurationType);
            
            if (orgConfig != null && orgConfig.getIsActive()) {
                return convertJsonNodeToMap(orgConfig.getConfigurationData());
            }
            
            // Fall back to global configuration (supportOrgId = null)
            AIConfiguration globalConfig = aiConfigurationRepository.findByTypeAndOrganization(
                null, configurationType);
            
            if (globalConfig != null && globalConfig.getIsActive()) {
                return convertJsonNodeToMap(globalConfig.getConfigurationData());
            }
            
            // Return empty configuration if none found
            log.warn("No AI configuration found for type: {}, org: {}", configurationType, supportOrgId);
            return new HashMap<>();
            
        } catch (Exception e) {
            log.error("Error getting AI configuration for type: {}, org: {}", configurationType, supportOrgId, e);
            return new HashMap<>();
        }
    }
    
    @Override
    public AIConfiguration saveConfiguration(AIConfiguration configuration) {
        log.debug("Saving AI configuration: {}", configuration.getConfigurationType());
        
        try {
            return aiConfigurationRepository.save(configuration);
        } catch (Exception e) {
            log.error("Error saving AI configuration", e);
            throw new RuntimeException("Failed to save AI configuration", e);
        }
    }
    
    @Override
    public List<AIConfiguration> getConfigurationsByOrganization(Long supportOrgId) {
        log.debug("Getting AI configurations for org: {}", supportOrgId);
        
        try {
            return aiConfigurationRepository.findByOrganization(supportOrgId);
        } catch (Exception e) {
            log.error("Error getting AI configurations for org: {}", supportOrgId, e);
            throw new RuntimeException("Failed to get AI configurations", e);
        }
    }
    
    @Override
    public AIConfiguration getConfigurationById(Long id) {
        log.debug("Getting AI configuration by ID: {}", id);
        
        try {
            return aiConfigurationRepository.findById(id).orElse(null);
        } catch (Exception e) {
            log.error("Error getting AI configuration by ID: {}", id, e);
            throw new RuntimeException("Failed to get AI configuration", e);
        }
    }
    
    @Override
    public void deleteConfiguration(Long id) {
        log.debug("Deleting AI configuration: {}", id);
        
        try {
            aiConfigurationRepository.deleteById(id);
        } catch (Exception e) {
            log.error("Error deleting AI configuration: {}", id, e);
            throw new RuntimeException("Failed to delete AI configuration", e);
        }
    }
    
    @Override
    public boolean hasConfiguration(String configurationType, Long supportOrgId) {
        log.debug("Checking if AI configuration exists for type: {}, org: {}", configurationType, supportOrgId);
        
        try {
            AIConfiguration config = aiConfigurationRepository.findByTypeAndOrganization(
                supportOrgId, configurationType);
            return config != null && config.getIsActive();
        } catch (Exception e) {
            log.error("Error checking AI configuration existence", e);
            return false;
        }
    }
    
    @Override
    public AIConfiguration findByTypeAndOrganization(Long organizationId, String configKey) {
        log.debug("Finding AI configuration for org: {}, configKey: {}", organizationId, configKey);
        try {
            return aiConfigurationRepository.findByTypeAndOrganization(organizationId, configKey);
        } catch (Exception e) {
            log.error("Error finding AI configuration for org: {}, configKey: {}", organizationId, configKey, e);
            return null;
        }
    }
    
    /**
     * Convert JsonNode to Map
     */
    private Map<String, Object> convertJsonNodeToMap(JsonNode jsonNode) {
        try {
            return objectMapper.convertValue(jsonNode, Map.class);
        } catch (Exception e) {
            log.error("Error converting JsonNode to Map", e);
            return new HashMap<>();
        }
    }
} 