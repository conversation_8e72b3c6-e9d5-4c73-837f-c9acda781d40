package io.sx.ai.springai;

import io.sx.ai.tools.AITool;
import io.sx.ai.tools.AITool.ToolContext;
import io.sx.ai.tools.AITool.ToolResult;
import io.sx.ai.tools.AIToolRegistry;
import io.sx.ai.tools.ToolCategory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.messages.AssistantMessage;

import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Optional;

/**
 * Spring AI function calling service that integrates with the AITool system.
 * Handles tool execution and LLM communication using Spring AI's native capabilities.
 */
@Slf4j
@Service
public class FunctionCallingService {
    
    private final ChatModel chatModel;
    private final AIToolRegistry toolRegistry;
    
    @Autowired
    public FunctionCallingService(ChatModel chatModel, AIToolRegistry toolRegistry) {
        this.chatModel = chatModel;
        this.toolRegistry = toolRegistry;
    }
    
    /**
     * Execute a workflow with function calling capabilities.
     * 
     * @param systemPrompt The system prompt for the LLM
     * @param userPrompt The user prompt/input
     * @param context The tool execution context
     * @param maxIterations Maximum number of tool call iterations
     * @return The final result of the workflow
     */
    public WorkflowResult executeWorkflow(String systemPrompt, String userPrompt, 
                                        ToolContext context, int maxIterations) {
        List<Message> conversation = new ArrayList<>();
        List<ToolCall> toolCalls = new ArrayList<>();
        
        // Add system message
        conversation.add(new SystemMessage(systemPrompt));
        
        // Add user message
        conversation.add(new UserMessage(userPrompt));
        
        int iteration = 0;
        while (iteration < maxIterations) {
            iteration++;
            log.info("Workflow iteration {} of {}", iteration, maxIterations);
            
            // Get LLM response
            Prompt prompt = new Prompt(conversation);
            ChatResponse response = chatModel.call(prompt);
            AssistantMessage assistantMessage = response.getResult().getOutput();
            
            conversation.add(assistantMessage);
            
            // Check if response contains tool calls
            String content = assistantMessage.getText();
            Optional<ToolCall> toolCall = parseToolCall(content);
            
            if (toolCall.isPresent()) {
                ToolCall call = toolCall.get();
                toolCalls.add(call);
                
                // Execute the tool
                ToolResult toolResult = executeTool(call, context);
                
                // Add function result to conversation as a user message
                UserMessage functionResultMessage = new UserMessage(
                    "Tool result for " + call.getToolName() + ": " + serializeToolResult(toolResult)
                );
                conversation.add(functionResultMessage);
                
                log.info("Tool {} executed: {}", call.getToolName(), toolResult.isSuccess());
                
            } else {
                // No tool call - check for final result
                Optional<WorkflowResult> finalResult = parseFinalResult(content);
                if (finalResult.isPresent()) {
                    return finalResult.get().withToolCalls(toolCalls);
                }
                
                // If no final result and no tool call, this might be an error
                log.warn("LLM response contains neither tool call nor final result: {}", 
                        content);
                return WorkflowResult.error("Invalid LLM response format", toolCalls);
            }
        }
        
        return WorkflowResult.error("Maximum iterations reached", toolCalls);
    }
    
    /**
     * Execute a single tool call.
     */
    private ToolResult executeTool(ToolCall toolCall, ToolContext context) {
        try {
            Optional<AITool> tool = getAITool(toolCall.getToolName());
            if (tool.isEmpty()) {
                return ToolResult.error("Tool not found: " + toolCall.getToolName());
            }
            
            return tool.get().execute(toolCall.getParameters(), context);
            
        } catch (Exception e) {
            log.error("Error executing tool {}: {}", toolCall.getToolName(), e.getMessage(), e);
            return ToolResult.error("Tool execution failed: " + e.getMessage());
        }
    }
    
    /**
     * Get an AITool by name from the registry.
     */
    private Optional<AITool> getAITool(String toolName) {
        // Get from the AIToolRegistry by ID
        return toolRegistry.getToolById(toolName);
    }
    
    /**
     * Parse a tool call from LLM response.
     */
    private Optional<ToolCall> parseToolCall(String content) {
        // This would parse the LLM response to extract tool calls
        // Implementation depends on the expected format
        // For now, return empty - this needs to be implemented based on the prompt format
        return Optional.empty();
    }
    
    /**
     * Parse a final result from LLM response.
     */
    private Optional<WorkflowResult> parseFinalResult(String content) {
        // This would parse the LLM response to extract final results
        // Implementation depends on the expected format
        return Optional.empty();
    }
    
    /**
     * Serialize a tool result for the LLM.
     */
    private String serializeToolResult(ToolResult result) {
        // Convert ToolResult to JSON string for the LLM
        Map<String, Object> serialized = new HashMap<>();
        serialized.put("success", result.isSuccess());
        serialized.put("data", result.getData());
        if (!result.isSuccess()) {
            serialized.put("error", result.getErrorMessage());
        }
        serialized.put("executionTime", result.getExecutionTimeMs());
        
        // Simple JSON serialization - could use Jackson for more robust handling
        return serialized.toString();
    }
    
    /**
     * Represents a tool call from the LLM.
     */
    public static class ToolCall {
        private final String toolName;
        private final Map<String, Object> parameters;
        
        public ToolCall(String toolName, Map<String, Object> parameters) {
            this.toolName = toolName;
            this.parameters = parameters;
        }
        
        public String getToolName() { return toolName; }
        public Map<String, Object> getParameters() { return parameters; }
    }
    
    /**
     * Represents the result of a workflow execution.
     */
    public static class WorkflowResult {
        private final boolean success;
        private final Object data;
        private final String errorMessage;
        private final List<ToolCall> toolCalls;
        
        public WorkflowResult(boolean success, Object data, String errorMessage, List<ToolCall> toolCalls) {
            this.success = success;
            this.data = data;
            this.errorMessage = errorMessage;
            this.toolCalls = toolCalls;
        }
        
        public static WorkflowResult success(Object data, List<ToolCall> toolCalls) {
            return new WorkflowResult(true, data, null, toolCalls);
        }
        
        public static WorkflowResult error(String errorMessage, List<ToolCall> toolCalls) {
            return new WorkflowResult(false, null, errorMessage, toolCalls);
        }
        
        public WorkflowResult withToolCalls(List<ToolCall> toolCalls) {
            return new WorkflowResult(success, data, errorMessage, toolCalls);
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public Object getData() { return data; }
        public String getErrorMessage() { return errorMessage; }
        public List<ToolCall> getToolCalls() { return toolCalls; }
    }
} 