# Spring AI Sample Common Package

This package contains common classes and interfaces that can be shared between different Spring AI workflow implementations.

## Package Structure

```
springai.sample.common/
├── BaseWorkflowStep.java          # Base interface for workflow steps
├── BaseStepResult.java            # Base result model for step execution
├── BaseWorkflowResult.java        # Base result model for workflow execution
├── BaseWorkflowContext.java       # Base context for workflow state management
├── BaseWorkflowOrchestrator.java  # Base interface for workflow orchestration
├── WorkflowUtils.java             # Utility class with common helper methods
└── README.md                      # This documentation file
```

## Core Classes

### BaseWorkflowStep
Base interface for all workflow steps. Provides common contract for:
- Step execution with input and previous result
- Step identification (name, description, type)
- Step validation and metadata
- Step type categorization

### BaseStepResult
Base result model for step execution. Includes:
- Step identification (number, name)
- Input/output values
- Success/error status
- Execution metadata (timing, type)
- Utility methods for result creation

### BaseWorkflowResult
Base result model for workflow execution. Provides:
- Workflow input/output tracking
- Step execution history
- Success/error status
- Processing time and metadata
- Utility methods for result analysis

### BaseWorkflowContext
Base context for workflow state management. Manages:
- Session and workflow identification
- Current state and parameters
- LLM conversation history
- Step execution history
- Integration context (organization, user)

### BaseWorkflowOrchestrator
Base interface for workflow orchestration. Defines:
- Workflow execution methods
- Event-driven workflow execution
- Step-by-step execution
- Workflow status and history management
- Parameter validation and statistics

### WorkflowUtils
Utility class with common helper methods:
- Session ID generation
- Template loading from classpath
- Numeric output extraction from LLM responses
- Event data extraction and validation
- Processing time calculation
- Standardized result creation

## Usage Examples

### Creating a Custom Workflow Step
```java
public class CustomStep implements BaseWorkflowStep {
    @Override
    public BaseStepResult execute(String input, BaseStepResult previousStep) {
        // Custom step logic
        String output = processInput(input);
        
        return BaseStepResult.success(
            getCurrentStepNumber(), 
            getStepName(), 
            input, 
            output, 
            getStepDescription()
        );
    }
    
    @Override
    public String getStepName() {
        return "custom_step";
    }
    
    @Override
    public String getStepDescription() {
        return "Performs custom processing";
    }
    
    @Override
    public boolean isFinalStep() {
        return false;
    }
}
```

### Using WorkflowUtils
```java
// Generate session ID
String sessionId = WorkflowUtils.generateSessionId();

// Load template
String template = WorkflowUtils.loadTemplate("templates/my-template.st");

// Extract numeric output
String numericOutput = WorkflowUtils.extractNumericOutput(llmResponse);

// Extract input from event
String input = WorkflowUtils.extractInputFromEvent(eventData);

// Create error result
BaseWorkflowResult errorResult = WorkflowUtils.createErrorResult(
    input, "Processing failed", sessionId
);
```

### Implementing BaseWorkflowOrchestrator
```java
@Service
public class CustomWorkflowOrchestrator implements BaseWorkflowOrchestrator {
    
    @Override
    public BaseWorkflowResult executeWorkflow(String input, String sessionId, 
                                            Long organizationId, Long onBehalfOfId) {
        LocalDateTime startTime = LocalDateTime.now();
        List<BaseStepResult> steps = new ArrayList<>();
        
        try {
            // Execute workflow steps
            BaseStepResult step1 = step1.execute(input, null);
            steps.add(step1);
            
            BaseStepResult step2 = step2.execute(step1.getOutput(), step1);
            steps.add(step2);
            
            return WorkflowUtils.createSuccessResult(
                input, step2.getOutput(), steps, sessionId, "CUSTOM_WORKFLOW", startTime
            );
            
        } catch (Exception e) {
            return WorkflowUtils.createErrorResult(input, e.getMessage(), sessionId);
        }
    }
    
    // Implement other interface methods...
}
```

## Benefits

### Reusability
- Common interfaces and base classes can be used across different workflow types
- Shared utility methods reduce code duplication
- Standardized result models ensure consistency

### Extensibility
- Easy to create new workflow types by implementing base interfaces
- Common patterns make it simple to add new functionality
- Base classes provide default implementations where appropriate

### Maintainability
- Centralized common functionality
- Consistent error handling and validation
- Standardized logging and monitoring patterns

### Spring AI Integration
- Leverages Spring AI's native capabilities
- Uses Spring AI's message types and conversation management
- Integrates with Spring AI's template system

## Migration Guide

To migrate existing workflows to use the common package:

1. **Update imports** to use common package classes
2. **Implement BaseWorkflowStep** interface for custom steps
3. **Use BaseStepResult** and **BaseWorkflowResult** for consistent results
4. **Leverage WorkflowUtils** for common operations
5. **Extend BaseWorkflowContext** for custom context management

## Best Practices

1. **Always implement BaseWorkflowStep** for new workflow steps
2. **Use WorkflowUtils** for common operations instead of duplicating code
3. **Extend base classes** rather than creating new ones from scratch
4. **Follow the established patterns** for consistency across workflows
5. **Use the common result models** for standardized output 