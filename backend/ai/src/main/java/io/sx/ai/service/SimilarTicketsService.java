package io.sx.ai.service;

import java.util.List;
import java.util.Map;

/**
 * Service for finding and analyzing similar tickets
 */
public interface SimilarTicketsService {
    
    /**
     * Find similar tickets based on content and context
     * 
     * @param ticketId The current ticket ID
     * @param organizationId The organization ID
     * @param supportOrgId The support organization ID
     * @param maxResults Maximum number of similar tickets to return
     * @return List of similar tickets with similarity scores
     */
    List<Map<String, Object>> findSimilarTickets(Long ticketId, Long organizationId, Long supportOrgId, int maxResults);
    
    /**
     * Get detailed similarity analysis
     * 
     * @param ticketId The current ticket ID
     * @param organizationId The organization ID
     * @param supportOrgId The support organization ID
     * @return Map containing similarity analysis details
     */
    Map<String, Object> getSimilarityAnalysis(Long ticketId, Long organizationId, Long supportOrgId);
} 