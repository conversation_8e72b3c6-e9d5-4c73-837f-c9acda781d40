package io.sx.ai.service;

/**
 * Service for analyzing customer importance and generating priority suggestions
 */
public interface CustomerImportanceService {
    
    /**
     * Get customer importance score (1-10) based on various factors
     * 
     * @param organizationId The organization ID
     * @param supportOrgId The support organization ID
     * @return Importance score from 1-10, or null if unable to determine
     */
    Double getCustomerImportance(Long organizationId, Long supportOrgId);
    
    /**
     * Get detailed customer importance analysis
     * 
     * @param organizationId The organization ID
     * @param supportOrgId The support organization ID
     * @return Map containing importance details
     */
    java.util.Map<String, Object> getCustomerImportanceAnalysis(Long organizationId, Long supportOrgId);
} 