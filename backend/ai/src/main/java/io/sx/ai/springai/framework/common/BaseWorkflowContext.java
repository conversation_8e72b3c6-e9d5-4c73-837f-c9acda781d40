package io.sx.ai.springai.framework.common;

import lombok.Data;
import org.springframework.ai.chat.messages.Message;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Base context for managing workflow state across multiple steps with type safety
 * Provides common state management patterns for different workflow types
 */
@Data
public class BaseWorkflowContext<I, O> {
    // Session and identification
    private String sessionId;
    private String workflowType;
    private LocalDateTime startedAt;
    private LocalDateTime lastUpdatedAt;

    // Workflow parameters
    private WorkflowInput<I> input;
    private int maxSteps;
    private Map<String, Object> parameters;

    // Current state
    private int currentStep;
    private WorkflowOutput<O> currentOutput;
    private WorkflowOutput<O> finalOutput;
    private WorkflowStatus status;
    private String terminationReason;

    // LLM conversation management
    private List<Message> conversationHistory;
    private List<BaseStepResult<I, O>> executedSteps;

    // Results and metadata
    private O finalResult;
    private Map<String, Object> metadata;
    private Long processingTimeMs;
    private Double confidenceScore;

    // Integration context
    private Long organizationId;
    private Long onBehalfOfId;
    private String triggerEvent;
    private Long triggerEntityId;
    private String triggerEntityType;

    public enum WorkflowStatus {
        PENDING, ACTIVE, COMPLETED, FAILED, TERMINATED
    }

    // Constructors
    public BaseWorkflowContext() {
        this.conversationHistory = new ArrayList<>();
        this.executedSteps = new ArrayList<>();
        this.parameters = new HashMap<>();
        this.metadata = new HashMap<>();
    }

    public BaseWorkflowContext(String sessionId, String workflowType, 
                              LocalDateTime startedAt, LocalDateTime lastUpdatedAt,
                              WorkflowInput<I> input, int maxSteps, Map<String, Object> parameters,
                              int currentStep, WorkflowOutput<O> currentOutput, WorkflowOutput<O> finalOutput,
                              WorkflowStatus status, String terminationReason,
                              List<Message> conversationHistory, List<BaseStepResult<I, O>> executedSteps,
                              O finalResult, Map<String, Object> metadata, Long processingTimeMs,
                              Double confidenceScore, Long organizationId, Long onBehalfOfId,
                              String triggerEvent, Long triggerEntityId, String triggerEntityType) {
        this.sessionId = sessionId;
        this.workflowType = workflowType;
        this.startedAt = startedAt;
        this.lastUpdatedAt = lastUpdatedAt;
        this.input = input;
        this.maxSteps = maxSteps;
        this.parameters = parameters != null ? parameters : new HashMap<>();
        this.currentStep = currentStep;
        this.currentOutput = currentOutput;
        this.finalOutput = finalOutput;
        this.status = status;
        this.terminationReason = terminationReason;
        this.conversationHistory = conversationHistory != null ? conversationHistory : new ArrayList<>();
        this.executedSteps = executedSteps != null ? executedSteps : new ArrayList<>();
        this.finalResult = finalResult;
        this.metadata = metadata != null ? metadata : new HashMap<>();
        this.processingTimeMs = processingTimeMs;
        this.confidenceScore = confidenceScore;
        this.organizationId = organizationId;
        this.onBehalfOfId = onBehalfOfId;
        this.triggerEvent = triggerEvent;
        this.triggerEntityId = triggerEntityId;
        this.triggerEntityType = triggerEntityType;
    }

    /**
     * Initialize a new workflow context
     */
    public static <I, O> BaseWorkflowContext<I, O> initialize(String sessionId, WorkflowInput<I> input, int maxSteps) {
        BaseWorkflowContext<I, O> context = new BaseWorkflowContext<>();
        context.setSessionId(sessionId);
        context.setWorkflowType("BASE_WORKFLOW");
        context.setStartedAt(LocalDateTime.now());
        context.setLastUpdatedAt(LocalDateTime.now());
        context.setInput(input);
        context.setMaxSteps(maxSteps);
        context.setCurrentStep(0);
        context.setStatus(WorkflowStatus.PENDING);
        return context;
    }

    /**
     * Add a step to the workflow execution history
     */
    public void addStep(BaseStepResult<I, O> step) {
        if (executedSteps == null) {
            executedSteps = new ArrayList<>();
        }
        executedSteps.add(step);
        lastUpdatedAt = LocalDateTime.now();
    }

    /**
     * Add a message to the conversation history
     */
    public void addMessage(Message message) {
        if (conversationHistory == null) {
            conversationHistory = new ArrayList<>();
        }
        conversationHistory.add(message);
        lastUpdatedAt = LocalDateTime.now();
    }

    /**
     * Check if max steps have been reached
     */
    public boolean hasReachedMaxSteps() {
        return currentStep >= maxSteps;
    }

    /**
     * Increment step and update current output
     */
    public void nextStep(WorkflowOutput<O> newOutput) {
        currentStep++;
        currentOutput = newOutput;
        lastUpdatedAt = LocalDateTime.now();
    }

    /**
     * Complete the workflow with final result
     */
    public void complete(WorkflowOutput<O> finalOutput, String reason) {
        this.finalOutput = finalOutput;
        this.terminationReason = reason;
        this.status = WorkflowStatus.COMPLETED;
        this.lastUpdatedAt = LocalDateTime.now();
        this.finalResult = finalOutput != null ? finalOutput.getValue() : null;
    }

    /**
     * Get the current input value
     */
    public I getInputValue() {
        return input != null ? input.getValue() : null;
    }

    /**
     * Get the current output value
     */
    public O getCurrentOutputValue() {
        return currentOutput != null ? currentOutput.getValue() : null;
    }

    /**
     * Get the final output value
     */
    public O getFinalOutputValue() {
        return finalOutput != null ? finalOutput.getValue() : null;
    }

    /**
     * Fail the workflow with error reason
     */
    public void fail(String reason) {
        this.terminationReason = reason;
        this.status = WorkflowStatus.FAILED;
        this.lastUpdatedAt = LocalDateTime.now();
    }

    /**
     * Add a parameter to the workflow context
     */
    public void addParameter(String key, Object value) {
        if (parameters == null) {
            parameters = new HashMap<>();
        }
        parameters.put(key, value);
    }

    /**
     * Get a parameter from the workflow context
     */
    public Object getParameter(String key) {
        return parameters != null ? parameters.get(key) : null;
    }

    /**
     * Add metadata to the workflow context
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }

    /**
     * Get metadata from the workflow context
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }

    /**
     * Convert to Map for storage
     */
    public Map<String, Object> toMap() {
        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put("session_id", sessionId);
        contextMap.put("workflow_type", workflowType);
        contextMap.put("input", getInputValue());
        contextMap.put("max_steps", maxSteps);
        contextMap.put("current_step", currentStep);
        contextMap.put("current_value", getCurrentOutputValue());
        contextMap.put("status", status.name());
        contextMap.put("final_result", getFinalOutputValue());
        contextMap.put("termination_reason", terminationReason);
        contextMap.put("started_at", startedAt.toString());
        contextMap.put("last_updated_at", lastUpdatedAt.toString());
        contextMap.put("processing_time_ms", processingTimeMs);
        contextMap.put("confidence_score", confidenceScore);
        contextMap.put("parameters", parameters);
        contextMap.put("metadata", metadata);

        // Add executed steps
        if (executedSteps != null) {
            List<Map<String, Object>> stepsData = new ArrayList<>();
            for (BaseStepResult<I, O> step : executedSteps) {
                // Convert BaseStepResult to Map (simplified)
                Map<String, Object> stepMap = new HashMap<>();
                stepMap.put("step_number", step.getStepNumber());
                stepMap.put("step_name", step.getStepName());
                stepMap.put("input", step.getInputAsString());
                stepMap.put("output", step.getOutputAsString());
                stepMap.put("success", step.isSuccess());
                stepMap.put("error_message", step.getErrorMessage());
                stepsData.add(stepMap);
            }
            contextMap.put("executed_steps", stepsData);
        }

        return contextMap;
    }

    // Getter and Setter methods
    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    
    public String getWorkflowType() { return workflowType; }
    public void setWorkflowType(String workflowType) { this.workflowType = workflowType; }
    
    public LocalDateTime getStartedAt() { return startedAt; }
    public void setStartedAt(LocalDateTime startedAt) { this.startedAt = startedAt; }
    
    public LocalDateTime getLastUpdatedAt() { return lastUpdatedAt; }
    public void setLastUpdatedAt(LocalDateTime lastUpdatedAt) { this.lastUpdatedAt = lastUpdatedAt; }
    
    public WorkflowInput<I> getInput() { return input; }
    public void setInput(WorkflowInput<I> input) { this.input = input; }
    
    public int getMaxSteps() { return maxSteps; }
    public void setMaxSteps(int maxSteps) { this.maxSteps = maxSteps; }
    
    public int getCurrentStep() { return currentStep; }
    public void setCurrentStep(int currentStep) { this.currentStep = currentStep; }
    
    public WorkflowOutput<O> getCurrentOutput() { return currentOutput; }
    public void setCurrentOutput(WorkflowOutput<O> currentOutput) { this.currentOutput = currentOutput; }
    
    public WorkflowOutput<O> getFinalOutput() { return finalOutput; }
    public void setFinalOutput(WorkflowOutput<O> finalOutput) { this.finalOutput = finalOutput; }
    
    public WorkflowStatus getStatus() { return status; }
    public void setStatus(WorkflowStatus status) { this.status = status; }
    
    public String getTerminationReason() { return terminationReason; }
    public void setTerminationReason(String terminationReason) { this.terminationReason = terminationReason; }
    
    public List<Message> getConversationHistory() { return conversationHistory; }
    public void setConversationHistory(List<Message> conversationHistory) { this.conversationHistory = conversationHistory; }
    
    public List<BaseStepResult<I, O>> getExecutedSteps() { return executedSteps; }
    public void setExecutedSteps(List<BaseStepResult<I, O>> executedSteps) { this.executedSteps = executedSteps; }
    
    public O getFinalResult() { return finalResult; }
    public void setFinalResult(O finalResult) { this.finalResult = finalResult; }
    
    public Map<String, Object> getParameters() { return parameters; }
    public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
    
    public Long getProcessingTimeMs() { return processingTimeMs; }
    public void setProcessingTimeMs(Long processingTimeMs) { this.processingTimeMs = processingTimeMs; }
    
    public Double getConfidenceScore() { return confidenceScore; }
    public void setConfidenceScore(Double confidenceScore) { this.confidenceScore = confidenceScore; }
    
    public Long getOrganizationId() { return organizationId; }
    public void setOrganizationId(Long organizationId) { this.organizationId = organizationId; }
    
    public Long getOnBehalfOfId() { return onBehalfOfId; }
    public void setOnBehalfOfId(Long onBehalfOfId) { this.onBehalfOfId = onBehalfOfId; }
    
    public String getTriggerEvent() { return triggerEvent; }
    public void setTriggerEvent(String triggerEvent) { this.triggerEvent = triggerEvent; }
    
    public Long getTriggerEntityId() { return triggerEntityId; }
    public void setTriggerEntityId(Long triggerEntityId) { this.triggerEntityId = triggerEntityId; }
    
    public String getTriggerEntityType() { return triggerEntityType; }
    public void setTriggerEntityType(String triggerEntityType) { this.triggerEntityType = triggerEntityType; }
    
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    
    public void setMetadata(String key, Object value) { 
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        this.metadata.put(key, value); 
    }
} 