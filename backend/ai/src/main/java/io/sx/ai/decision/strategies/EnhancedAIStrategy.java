package io.sx.ai.decision.strategies;

import io.sx.ai.decision.model.AIDecisionContext;
import io.sx.ai.model.AISuggestion;
import io.sx.ai.model.ThinkingLog;
import io.sx.ai.orchestration.TicketOrchestrator;
import io.sx.ai.repository.AISuggestionRepository;
import io.sx.ai.repository.ThinkingLogRepository;
import io.sx.ai.service.CustomerImportanceService;
import io.sx.ai.service.SimilarTicketsService;
import io.sx.ai.classification.TicketClassificationAgent;
import io.sx.ai.assignment.TicketAssignmentAgent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Enhanced AI Strategy: App Only + LLM
 * Combines deterministic app processing with intelligent LLM analysis
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EnhancedAIStrategy implements AIDecisionStrategy {

    private final CustomerImportanceService customerImportanceService;
    private final SimilarTicketsService similarTicketsService;
    private final TicketClassificationAgent ticketClassificationAgent;
    private final TicketAssignmentAgent ticketAssignmentAgent;
    private final AISuggestionRepository aiSuggestionRepository;
    private final ThinkingLogRepository thinkingLogRepository;

    @Override
    public String getStrategyId() {
        return "enhanced";
    }

    @Override
    public String getStrategyName() {
        return "Enhanced AI Strategy (App Only + LLM)";
    }

    @Override
    public boolean isApplicable(AIDecisionContext context) {
        // Enhanced strategy is applicable for all ticket events
        return context.getTriggerEvent() != null && 
               (context.getTriggerEvent().equals("ticket.c") || 
                context.getTriggerEvent().equals("ticket.u"));
    }

    @Override
    public List<AISuggestion> execute(AIDecisionContext context, Map<String, Object> config) {
        log.info("Executing Enhanced AI Strategy for ticket: {}", context.getEntityId());
        
        List<AISuggestion> suggestions = new ArrayList<>();
        ThinkingLog thinkingLog = null;
        
        try {
            Long ticketId = context.getEntityId();
            Long organizationId = context.getOrganizationId();
            Long supportOrgId = context.getSupportOrganizationId();
            String sessionId = "session-" + ticketId + "-" + System.currentTimeMillis();
            
            // Step 1: Create thinking log to capture the reasoning process
            thinkingLog = createThinkingLog(context, sessionId, ticketId, organizationId, supportOrgId);
            
            // Step 2: Customer Importance Analysis
            List<AISuggestion> customerSuggestions = analyzeCustomerImportance(ticketId, organizationId, supportOrgId);
            suggestions.addAll(customerSuggestions);
            
            // Step 3: Similar Tickets Analysis
            List<AISuggestion> similarSuggestions = analyzeSimilarTickets(ticketId, organizationId, supportOrgId);
            suggestions.addAll(similarSuggestions);
            
            // Step 4: Ticket Classification
            List<AISuggestion> classificationSuggestions = classifyTicket(ticketId, organizationId, supportOrgId);
            suggestions.addAll(classificationSuggestions);
            
            // Step 5: Link suggestions to thinking log and persist
            if (!suggestions.isEmpty() && thinkingLog != null) {
                // Link all suggestions to the thinking log
                final Long thinkingLogId = thinkingLog.getId();
                suggestions.forEach(suggestion -> suggestion.setThinkingLogId(thinkingLogId));
                
                List<AISuggestion> savedSuggestions = aiSuggestionRepository.saveAll(suggestions);
                
                // Update thinking log with final suggestions
                updateThinkingLogWithSuggestions(thinkingLog, savedSuggestions);
                
                log.info("Enhanced AI Strategy generated and persisted {} suggestions linked to thinking log {} for ticket: {}", 
                        savedSuggestions.size(), thinkingLog.getId(), ticketId);
                return savedSuggestions;
            }
            
            log.info("Enhanced AI Strategy generated {} suggestions for ticket: {}", 
                    suggestions.size(), ticketId);
            
        } catch (Exception e) {
            log.error("Error executing Enhanced AI Strategy for ticket: {}", 
                    context.getEntityId(), e);
        }
        
        return suggestions;
    }

    @Override
    public TicketOrchestrator.TicketProcessingResult executeTicketProcessing(
            String sessionId, Long ticketId, Map<String, Object> ticketData,
            Long organizationId, Long supportOrgId, Long userId) {
        
        log.info("Enhanced AI Strategy: Processing ticket {} for session {}", ticketId, sessionId);
        
        try {
            // Execute the strategy to get suggestions
            AIDecisionContext context = AIDecisionContext.builder()
                    .triggerEvent("ticket.c")
                    .entityId(ticketId)
                    .organizationId(organizationId)
                    .supportOrganizationId(supportOrgId)
                    .entityData(ticketData)
                    .build();
            
            List<AISuggestion> suggestions = execute(context, Map.of());
            
            // Convert suggestions to result format
            Map<String, Object> suggestionsMap = Map.of(
                "suggestions", suggestions,
                "count", suggestions.size(),
                "strategy", "enhanced"
            );
            
            return TicketOrchestrator.TicketProcessingResult.success(
                sessionId, ticketId, "enhanced", 
                "Enhanced AI analysis completed with " + suggestions.size() + " suggestions",
                suggestionsMap
            );
            
        } catch (Exception e) {
            log.error("Enhanced AI Strategy: Error processing ticket {}", ticketId, e);
            return TicketOrchestrator.TicketProcessingResult.error(
                sessionId, ticketId, "Enhanced AI processing failed: " + e.getMessage()
            );
        }
    }

    /**
     * Analyze customer importance and generate suggestions
     */
    private List<AISuggestion> analyzeCustomerImportance(Long ticketId, Long organizationId, Long supportOrgId) {
        List<AISuggestion> suggestions = new ArrayList<>();
        
        try {
            // Get customer importance score
            Double importanceScore = customerImportanceService.getCustomerImportance(organizationId, supportOrgId);
            
            if (importanceScore != null) {
                AISuggestion suggestion = AISuggestion.builder()
                    .suggestionId("importance-" + ticketId + "-" + System.currentTimeMillis())
                    .sessionId("session-" + ticketId)
                    .ticketId(ticketId)
                    .autonomyLevel(AISuggestion.AutonomyLevel.ADVISOR)
                    .suggestionType(AISuggestion.SuggestionType.PRIORITY_ADJUSTMENT)
                    .title("Customer Importance Analysis")
                    .description("Customer importance score: " + importanceScore + "/10")
                    .reasoning("Based on customer value, contract size, and historical relationship")
                    .confidenceScore(BigDecimal.valueOf(0.85))
                    .priority(importanceScore >= 8.0 ? 1 : importanceScore >= 5.0 ? 3 : 5)
                    .status(AISuggestion.SuggestionStatus.PENDING)
                    .approvalRequired(true)
                    .organizationId(organizationId)
                    .supportOrganizationId(supportOrgId)
                    .createdBy("ai-system")
                    .updatedBy("ai-system")
                    .metadata(Map.of(
                        "importance_score", importanceScore,
                        "customer_tier", getCustomerTier(importanceScore),
                        "priority_adjustment", getPriorityAdjustment(importanceScore)
                    ))
                    .build();
                
                suggestions.add(suggestion);
            }
            
        } catch (Exception e) {
            log.warn("Error analyzing customer importance for ticket: {}", ticketId, e);
        }
        
        return suggestions;
    }

    /**
     * Analyze similar tickets and generate suggestions
     */
    private List<AISuggestion> analyzeSimilarTickets(Long ticketId, Long organizationId, Long supportOrgId) {
        List<AISuggestion> suggestions = new ArrayList<>();
        
        try {
            // Get similar tickets
            List<Map<String, Object>> similarTickets = similarTicketsService.findSimilarTickets(
                ticketId, organizationId, supportOrgId, 5);
            
            if (!similarTickets.isEmpty()) {
                AISuggestion suggestion = AISuggestion.builder()
                    .suggestionId("similar-" + ticketId + "-" + System.currentTimeMillis())
                    .sessionId("session-" + ticketId)
                    .ticketId(ticketId)
                    .autonomyLevel(AISuggestion.AutonomyLevel.ADVISOR)
                    .suggestionType(AISuggestion.SuggestionType.PROCESS_IMPROVEMENT)
                    .title("Similar Tickets Found")
                    .description("Found " + similarTickets.size() + " similar tickets")
                    .reasoning("Based on semantic similarity analysis of ticket content")
                    .confidenceScore(BigDecimal.valueOf(0.75))
                    .priority(4)
                    .status(AISuggestion.SuggestionStatus.PENDING)
                    .approvalRequired(false)
                    .organizationId(organizationId)
                    .supportOrganizationId(supportOrgId)
                    .createdBy("ai-system")
                    .updatedBy("ai-system")
                    .metadata(Map.of(
                        "similar_tickets_count", similarTickets.size(),
                        "similar_tickets", similarTickets,
                        "avg_resolution_time", calculateAvgResolutionTime(similarTickets)
                    ))
                    .build();
                
                suggestions.add(suggestion);
            }
            
        } catch (Exception e) {
            log.warn("Error analyzing similar tickets for ticket: {}", ticketId, e);
        }
        
        return suggestions;
    }

    /**
     * Classify ticket and generate classification suggestions
     */
    private List<AISuggestion> classifyTicket(Long ticketId, Long organizationId, Long supportOrgId) {
        log.debug("Processing ticket classification for ticket: {}", ticketId);
        
        try {
            // Use the dedicated classification agent
            return ticketClassificationAgent.processTicketClassification(ticketId, organizationId, supportOrgId);
        } catch (Exception e) {
            log.warn("Error classifying ticket: {}", ticketId, e);
            return new ArrayList<>();
        }
    }

    /**
     * Helper methods
     */
    private String getCustomerTier(Double importanceScore) {
        if (importanceScore >= 8.0) return "PREMIUM";
        if (importanceScore >= 5.0) return "STANDARD";
        return "BASIC";
    }

    private String getPriorityAdjustment(Double importanceScore) {
        if (importanceScore >= 8.0) return "INCREASE";
        if (importanceScore >= 5.0) return "MAINTAIN";
        return "DECREASE";
    }

    private Double calculateAvgResolutionTime(List<Map<String, Object>> similarTickets) {
        if (similarTickets.isEmpty()) return 0.0;
        
        return similarTickets.stream()
            .mapToDouble(ticket -> {
                Object resolutionTime = ticket.get("resolution_time_hours");
                return resolutionTime instanceof Number ? ((Number) resolutionTime).doubleValue() : 24.0;
            })
            .average()
            .orElse(24.0);
    }

    /**
     * Create thinking log to capture the reasoning process
     */
    private ThinkingLog createThinkingLog(AIDecisionContext context, String sessionId, 
                                        Long ticketId, Long organizationId, Long supportOrgId) {
        try {
            Map<String, Object> thinkingProcess = new HashMap<>();
            thinkingProcess.put("strategy", "enhanced");
            thinkingProcess.put("steps", List.of(
                Map.of("step", 1, "action", "customer_importance_analysis", "description", "Analyze customer importance for priority decisions"),
                Map.of("step", 2, "action", "similar_tickets_analysis", "description", "Find similar tickets for context and resolution patterns"),
                Map.of("step", 3, "action", "ticket_classification", "description", "Classify ticket to known labels (type, category, priority, complexity, impact)")
            ));
            thinkingProcess.put("reasoning_approach", "multi-faceted analysis combining deterministic and LLM-based approaches");
            
            ThinkingLog thinkingLog = ThinkingLog.builder()
                .sessionId(sessionId)
                .triggerEvent(context.getTriggerEvent())
                .triggerEntityId(ticketId)
                .triggerEntityType("ticket")
                .thinkingStrategy("CHAIN_OF_THOUGHT")
                .thinkingProcess(thinkingProcess)
                .sessionStatus("ACTIVE")
                .confidenceScore(BigDecimal.valueOf(0.85))
                .processingTimeMs(0L) // Will be updated after processing
                .organizationId(organizationId)
                .supportOrganizationId(supportOrgId)
                .createdBy("ai-system")
                .updatedBy("ai-system")
                .build();
            
            ThinkingLog savedThinkingLog = thinkingLogRepository.save(thinkingLog);
            log.info("Created thinking log {} for ticket {} session {}", 
                    savedThinkingLog.getId(), ticketId, sessionId);
            
            return savedThinkingLog;
            
        } catch (Exception e) {
            log.error("Error creating thinking log for ticket: {}", ticketId, e);
            return null;
        }
    }

    /**
     * Update thinking log with final suggestions and reasoning
     */
    private void updateThinkingLogWithSuggestions(ThinkingLog thinkingLog, List<AISuggestion> suggestions) {
        try {
            Map<String, Object> finalSuggestions = new HashMap<>();
            finalSuggestions.put("total_suggestions", suggestions.size());
            finalSuggestions.put("suggestion_types", suggestions.stream()
                .map(AISuggestion::getSuggestionType)
                .distinct()
                .toList());
            finalSuggestions.put("confidence_scores", suggestions.stream()
                .map(AISuggestion::getConfidenceScore)
                .toList());
            finalSuggestions.put("suggestions", suggestions.stream()
                .map(s -> Map.of(
                    "id", s.getSuggestionId(),
                    "type", s.getSuggestionType(),
                    "title", s.getTitle(),
                    "confidence", s.getConfidenceScore()
                ))
                .toList());
            
            // Update thinking process with final results
            Map<String, Object> updatedThinkingProcess = new HashMap<>(thinkingLog.getThinkingProcess());
            updatedThinkingProcess.put("final_suggestions", finalSuggestions);
            updatedThinkingProcess.put("processing_complete", true);
            
            thinkingLog.setThinkingProcess(updatedThinkingProcess);
            thinkingLog.setSuggestions(finalSuggestions);
            thinkingLog.setSessionStatus("COMPLETED");
            
            thinkingLogRepository.save(thinkingLog);
            log.info("Updated thinking log {} with {} suggestions", thinkingLog.getId(), suggestions.size());
            
        } catch (Exception e) {
            log.error("Error updating thinking log {} with suggestions", thinkingLog.getId(), e);
        }
    }
} 