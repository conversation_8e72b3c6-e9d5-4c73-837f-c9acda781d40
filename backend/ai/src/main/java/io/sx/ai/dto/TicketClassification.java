package io.sx.ai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Structured response model for ticket classification
 * Used with LLM structured output for consistent classification results
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketClassification {
    
    /**
     * The type of ticket (Bug, Feature Request, Question, etc.)
     */
    @JsonProperty("ticket_type")
    private String ticketType;
    
    /**
     * Priority level (Low, Medium, High, Critical, Urgent)
     */
    @JsonProperty("priority")
    private String priority;
    
    /**
     * Category (Technical, Billing, Account, Integration, etc.)
     */
    @JsonProperty("category")
    private String category;
    
    /**
     * Complexity level (Simple, Moderate, Complex, Expert)
     */
    @JsonProperty("complexity")
    private String complexity;
    
    /**
     * Impact scope (Individual, Team, Department, Organization, Customer-facing)
     */
    @JsonProperty("impact")
    private String impact;
    
    /**
     * Overall confidence score (0.0 to 1.0)
     */
    @JsonProperty("confidence")
    private Double confidence;
    
    /**
     * Detailed reasoning for the classification
     */
    @JsonProperty("reasoning")
    private String reasoning;
    
    /**
     * Suggested actions based on classification
     */
    @JsonProperty("suggested_actions")
    private List<String> suggestedActions;
    
    /**
     * Individual confidence scores for each classification aspect
     */
    @JsonProperty("individual_confidences")
    private Map<String, Double> individualConfidences;
    
    /**
     * Recommended team for assignment (if applicable)
     */
    @JsonProperty("recommended_team")
    private String recommendedTeam;
    
    /**
     * Estimated resolution time in hours
     */
    @JsonProperty("estimated_resolution_hours")
    private Double estimatedResolutionHours;
    
    /**
     * Whether this requires escalation
     */
    @JsonProperty("requires_escalation")
    private Boolean requiresEscalation;
    
    /**
     * Escalation reason if required
     */
    @JsonProperty("escalation_reason")
    private String escalationReason;
    
    /**
     * Tags for categorization
     */
    @JsonProperty("tags")
    private List<String> tags;
    
    /**
     * Additional metadata
     */
    @JsonProperty("metadata")
    private Map<String, Object> metadata;
} 