package io.sx.ai.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson configuration for proper handling of Java 8 date/time types.
 * This is critical for serializing/deserializing Instant fields in DTOs.
 */
@Configuration
public class JacksonConfig {

    /**
     * Primary ObjectMapper bean with JSR310 support for Java 8 date/time types.
     * This is critical for serializing/deserializing Instant fields in DTOs.
     * Configuration matches the app module to ensure consistency.
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // Register JavaTimeModule for Java 8 date/time support
        objectMapper.registerModule(new JavaTimeModule());
        
        // Disable writing dates as timestamps to ensure ISO-8601 string format
        // This matches the app module configuration exactly
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        
        return objectMapper;
    }
} 