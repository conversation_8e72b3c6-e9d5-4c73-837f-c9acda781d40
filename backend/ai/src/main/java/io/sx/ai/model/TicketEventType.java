package io.sx.ai.model;

/**
 * Enum representing ticket event types for AI processing
 */
public enum TicketEventType {
    
    /**
     * Ticket creation event
     */
    TICKET_CREATED("ticket.c"),
    
    /**
     * Ticket update event
     */
    TICKET_UPDATED("ticket.u");
    
    private final String value;
    
    TicketEventType(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    /**
     * Get enum from string value
     */
    public static TicketEventType fromValue(String value) {
        for (TicketEventType eventType : values()) {
            if (eventType.value.equals(value)) {
                return eventType;
            }
        }
        throw new IllegalArgumentException("Unknown ticket event type: " + value);
    }
    
    /**
     * Check if the given value is a valid ticket event type
     */
    public static boolean isValid(String value) {
        for (TicketEventType eventType : values()) {
            if (eventType.value.equals(value)) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public String toString() {
        return value;
    }
} 