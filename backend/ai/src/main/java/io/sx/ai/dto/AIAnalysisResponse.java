package io.sx.ai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class AIAnalysisResponse {
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("analysis_id")
    private String analysisId;
    
    @JsonProperty("reasoning")
    private String reasoning;
    
    @JsonProperty("confidence_score")
    private Double confidenceScore;
    
    @JsonProperty("decisions")
    private List<Decision> decisions;
    
    @JsonProperty("recommended_actions")
    private List<RecommendedAction> recommendedActions;
    
    @JsonProperty("next_steps")
    private List<NextStep> nextSteps;
    
    @JsonProperty("tool_calls")
    private List<ToolCall> toolCalls;
    
    @JsonProperty("information_requests")
    private List<InformationRequest> informationRequests;
    
    @JsonProperty("metadata")
    private Map<String, Object> metadata;
    
    // Additional useful fields from LLM responses
    @JsonProperty("summary")
    private String summary;
    
    @JsonProperty("detailed_analysis")
    private String detailedAnalysis;
    
    @JsonProperty("key_insights")
    private List<String> keyInsights;
    
    @JsonProperty("risk_assessment")
    private String riskAssessment;
    
    @JsonProperty("urgency_level")
    private String urgencyLevel;
    
    @JsonProperty("impact_analysis")
    private String impactAnalysis;
    
    @JsonProperty("alternative_approaches")
    private List<String> alternativeApproaches;
    
    @JsonProperty("decision_rationale")
    private String decisionRationale;
    
    @JsonProperty("confidence_breakdown")
    private Map<String, Double> confidenceBreakdown;
    
    @JsonProperty("uncertainty_factors")
    private String uncertaintyFactors;
    
    @JsonProperty("recommendations_summary")
    private String recommendationsSummary;
    
    @JsonProperty("contextual_data")
    private Map<String, Object> contextualData;
    
    @Data
    @Builder
    public static class Decision {
        @JsonProperty("decision_id")
        private String decisionId;
        
        @JsonProperty("decision_type")
        private String decisionType; // CLASSIFICATION, ASSIGNMENT, PRIORITY, etc.
        
        @JsonProperty("current_value")
        private String currentValue;
        
        @JsonProperty("recommended_value")
        private String recommendedValue;
        
        @JsonProperty("confidence")
        private Double confidence;
        
        @JsonProperty("reasoning")
        private String reasoning;
        
        @JsonProperty("requires_action")
        private Boolean requiresAction;
        
        // Additional useful decision fields
        @JsonProperty("priority")
        private String priority;
        
        @JsonProperty("urgency")
        private String urgency;
        
        @JsonProperty("impact")
        private String impact;
        
        @JsonProperty("risk_level")
        private String riskLevel;
        
        @JsonProperty("alternatives")
        private String alternatives;
        
        @JsonProperty("constraints")
        private String constraints;
        
        @JsonProperty("dependencies")
        private String dependencies;
        
        @JsonProperty("success_criteria")
        private String successCriteria;
        
        @JsonProperty("failure_modes")
        private String failureModes;
        
        @JsonProperty("contextual_factors")
        private Map<String, Object> contextualFactors;
        
        @JsonProperty("decision_tree")
        private String decisionTree;
        
        @JsonProperty("trade_offs")
        private String tradeOffs;
        
        @JsonProperty("long_term_implications")
        private String longTermImplications;
    }
    
    @Data
    @Builder
    public static class RecommendedAction {
        @JsonProperty("action_id")
        private String actionId;
        
        @JsonProperty("action_name")
        private String actionName;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("priority")
        private Integer priority;
        
        @JsonProperty("reasoning")
        private String reasoning;
        
        @JsonProperty("estimated_impact")
        private String estimatedImpact;
        
        @JsonProperty("dependencies")
        private List<String> dependencies;
        
        // Additional useful action fields
        @JsonProperty("urgency")
        private String urgency;
        
        @JsonProperty("risk_level")
        private String riskLevel;
        
        @JsonProperty("success_criteria")
        private String successCriteria;
        
        @JsonProperty("failure_handling")
        private String failureHandling;
        
        @JsonProperty("resource_requirements")
        private String resourceRequirements;
        
        @JsonProperty("timeline")
        private String timeline;
        
        @JsonProperty("stakeholders")
        private List<String> stakeholders;
        
        @JsonProperty("rollback_plan")
        private String rollbackPlan;
    }
    
    @Data
    @Builder
    public static class NextStep {
        @JsonProperty("step_id")
        private String stepId;
        
        @JsonProperty("step_name")
        private String stepName;
        
        @JsonProperty("step_type")
        private String stepType; // ANALYSIS, TOOL_CALL, WAIT, COMPLETE
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("required_data")
        private List<String> requiredData;
        
        @JsonProperty("estimated_duration")
        private String estimatedDuration;
        
        @JsonProperty("can_parallelize")
        private Boolean canParallelize;
        
        // Additional useful step fields
        @JsonProperty("priority")
        private String priority;
        
        @JsonProperty("dependencies")
        private List<String> dependencies;
        
        @JsonProperty("success_criteria")
        private String successCriteria;
        
        @JsonProperty("failure_handling")
        private String failureHandling;
        
        @JsonProperty("resource_requirements")
        private String resourceRequirements;
        
        @JsonProperty("stakeholders")
        private List<String> stakeholders;
    }
    
    @Data
    @Builder
    public static class ToolCall {
        @JsonProperty("tool_name")
        private String toolName;
        
        @JsonProperty("parameters")
        private Map<String, Object> parameters;
        
        @JsonProperty("reasoning")
        private String reasoning;
        
        @JsonProperty("expected_outcome")
        private String expectedOutcome;
        
        @JsonProperty("fallback_plan")
        private String fallbackPlan;
        
        // Additional useful tool call fields
        @JsonProperty("purpose")
        private String purpose;
        
        @JsonProperty("execution_context")
        private String executionContext;
        
        @JsonProperty("prerequisites")
        private String prerequisites;
        
        @JsonProperty("post_conditions")
        private String postConditions;
        
        @JsonProperty("error_handling")
        private String errorHandling;
        
        @JsonProperty("performance_expectations")
        private String performanceExpectations;
        
        @JsonProperty("validation_rules")
        private String validationRules;
        
        @JsonProperty("rollback_strategy")
        private String rollbackStrategy;
    }
    
    @Data
    @Builder
    public static class InformationRequest {
        @JsonProperty("request_id")
        private String requestId;
        
        @JsonProperty("information_type")
        private String informationType; // SIMILAR_TICKETS, CUSTOMER_SATISFACTION, TEAM_AVAILABILITY, etc.
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("parameters")
        private Map<String, Object> parameters;
        
        @JsonProperty("priority")
        private Integer priority; // 1-5, where 1 is highest priority
        
        @JsonProperty("required_for_decision")
        private Boolean requiredForDecision;
        
        @JsonProperty("estimated_processing_time")
        private String estimatedProcessingTime;
        
        // Additional useful information request fields
        @JsonProperty("urgency")
        private String urgency;
        
        @JsonProperty("data_quality_requirements")
        private String dataQualityRequirements;
        
        @JsonProperty("fallback_data_sources")
        private List<String> fallbackDataSources;
        
        @JsonProperty("validation_criteria")
        private String validationCriteria;
        
        @JsonProperty("stakeholders")
        private List<String> stakeholders;
    }
    
    @Data
    @Builder
    public static class WorkflowState {
        @JsonProperty("session_id")
        private String sessionId;
        
        @JsonProperty("current_step")
        private String currentStep;
        
        @JsonProperty("completed_steps")
        private List<String> completedSteps;
        
        @JsonProperty("pending_information_requests")
        private List<InformationRequest> pendingInformationRequests;
        
        @JsonProperty("gathered_context")
        private Map<String, Object> gatheredContext;
        
        @JsonProperty("workflow_status")
        private String workflowStatus; // IN_PROGRESS, WAITING_FOR_INFO, COMPLETED, FAILED
        
        @JsonProperty("confidence_score")
        private Double confidenceScore;
        
        @JsonProperty("estimated_completion_time")
        private String estimatedCompletionTime;
        
        // Additional useful workflow state fields
        @JsonProperty("progress_percentage")
        private Double progressPercentage;
        
        @JsonProperty("blocking_issues")
        private List<String> blockingIssues;
        
        @JsonProperty("next_milestone")
        private String nextMilestone;
        
        @JsonProperty("quality_metrics")
        private Map<String, Object> qualityMetrics;
        
        @JsonProperty("performance_metrics")
        private Map<String, Object> performanceMetrics;
    }
} 