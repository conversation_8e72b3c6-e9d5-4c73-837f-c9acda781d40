package io.sx.ai.springai.sample.orchestrator;

import io.sx.ai.springai.framework.common.BaseWorkflowResult;
import io.sx.ai.springai.framework.common.WorkflowUtils;
import io.sx.ai.springai.sample.manual.numeric.ArithmeticChainWorkflow;
import io.sx.ai.springai.sample.dynamic.service.DynamicWorkflowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Orchestrator that coordinates sample workflows for testing and demonstration.
 * Provides a unified interface for executing various sample workflow types.
 * This orchestrator is focused on sample workflows only, not production ticket processing.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SpringAISamplesOrchestrator {

    private final ArithmeticChainWorkflow dynamicChainWorkflow;
    private final DynamicWorkflowService dynamicWorkflowService;

    /**
     * Execute a mathematical chain workflow.
     * Input -> Divide by 2 -> Multiply by 4
     */
    public BaseWorkflowResult<String, String> executeMathematicalChain(String input) {
        log.info("SpringAISamplesOrchestrator: Executing mathematical chain with input: {}", input);
        try {
            BaseWorkflowResult<String, String> result = dynamicChainWorkflow.executeMathematicalChain(input);
            log.info("SpringAISamplesOrchestrator: Chain workflow completed successfully. Input: {}, Final Output: {}",
                    input, result.getFinalResult());
            return result;
        } catch (Exception e) {
            log.error("SpringAISamplesOrchestrator: Error executing mathematical chain: {}", e.getMessage(), e);
            return BaseWorkflowResult.error("Mathematical chain execution failed: " + e.getMessage());
        }
    }

    /**
     * Extracts input from event data and executes the chain
     */
    public BaseWorkflowResult<String, String> executeMathematicalChainFromEvent(Map<String, Object> eventData) {
        log.info("SpringAISamplesOrchestrator: Executing mathematical chain from event data: {}", eventData);
        try {
            String input = WorkflowUtils.extractInputFromEvent(eventData);
            if (input != null) {
                return executeMathematicalChain(input);
            } else {
                return BaseWorkflowResult.error("No input found in event data");
            }
        } catch (Exception e) {
            log.error("SpringAISamplesOrchestrator: Error executing chain from event: {}", e.getMessage(), e);
            return BaseWorkflowResult.error("Chain execution from event failed: " + e.getMessage());
        }
    }

    /**
     * Execute the dynamic LLM-controlled workflow
     */
    public BaseWorkflowResult<Double, Double> executeDynamicWorkflow(double inputNumber, int maxIterations, Long organizationId, Long onBehalfOfId) {
        log.info("SpringAISamplesOrchestrator: Executing dynamic workflow with input: {}, maxIterations: {}", 
                inputNumber, maxIterations);
        try {
            BaseWorkflowResult<Double, Double> result = dynamicWorkflowService.executeWorkflow(inputNumber, maxIterations, organizationId, onBehalfOfId);
            log.info("SpringAISamplesOrchestrator: Dynamic workflow completed successfully. Input: {}, Final Result: {}",
                    inputNumber, result.getFinalResult());
            return result;
        } catch (Exception e) {
            log.error("SpringAISamplesOrchestrator: Error executing dynamic workflow: {}", e.getMessage(), e);
            return BaseWorkflowResult.error("Dynamic workflow execution failed: " + e.getMessage());
        }
    }

    /**
     * Execute the dynamic workflow with default parameters
     */
    public BaseWorkflowResult<Double, Double> executeDynamicWorkflow(double inputNumber, Long organizationId, Long onBehalfOfId) {
        return executeDynamicWorkflow(inputNumber, 4, organizationId, onBehalfOfId);
    }

    /**
     * Execute the dynamic workflow from event data
     */
    public BaseWorkflowResult<Double, Double> executeDynamicWorkflowFromEvent(Map<String, Object> eventData, Long organizationId, Long onBehalfOfId) {
        log.info("SpringAISamplesOrchestrator: Executing dynamic workflow from event data: {}", eventData);
        try {
            Double inputNumber = WorkflowUtils.extractNumericInputFromEvent(eventData);
            if (inputNumber != null) {
                return executeDynamicWorkflow(inputNumber, organizationId, onBehalfOfId);
            } else {
                return BaseWorkflowResult.error("No numeric input found in event data");
            }
        } catch (Exception e) {
            log.error("SpringAISamplesOrchestrator: Error executing dynamic workflow from event: {}", e.getMessage(), e);
            return BaseWorkflowResult.error("Dynamic workflow execution from event failed: " + e.getMessage());
        }
    }

    /**
     * Get workflow statistics
     */
    public Map<String, Object> getWorkflowStats() {
        log.info("SpringAISamplesOrchestrator: Getting workflow statistics");
        Map<String, Object> stats = Map.of(
            "workflowType", "mathematical_chain",
            "description", "Input -> Divide by 2 -> Multiply by 4",
            "steps", 2,
            "framework", "Spring AI",
            "model", "Ollama"
        );
        return stats;
    }
} 