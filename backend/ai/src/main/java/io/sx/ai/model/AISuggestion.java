package io.sx.ai.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.sx.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Map;

/**
 * AI Suggestion model for multi-tier autonomy system
 * Supports Level 1 (Advisor), Level 2 (HITL), Level 3 (Full Autonomy)
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AISuggestion extends BaseModel {
    
    @JsonProperty("suggestion_id")
    private String suggestionId;
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("ticket_id")
    private Long ticketId; // Foreign key to tickets table
    
    @JsonProperty("thinking_log_id")
    private Long thinkingLogId; // Foreign key to thinking_logs table for XAI traceability
    
    @JsonProperty("autonomy_level")
    private AutonomyLevel autonomyLevel; // ADVISOR, HITL, FULL_AUTONOMY
    
    @JsonProperty("suggestion_type")
    private SuggestionType suggestionType; // ASSIGNMENT, PRIORITY, ESCALATION, etc.
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("reasoning")
    private String reasoning;
    
    @JsonProperty("confidence_score")
    private BigDecimal confidenceScore;
    
    @JsonProperty("priority")
    private Integer priority; // 1-10 scale
    
    @JsonProperty("tool_name")
    private String toolName; // The tool/function to execute
    
    @JsonProperty("tool_parameters")
    private Map<String, Object> toolParameters; // Parameters for tool execution
    
    @JsonProperty("status")
    private SuggestionStatus status; // PENDING, APPROVED, REJECTED, EXECUTED, FAILED
    
    @JsonProperty("approval_required")
    private Boolean approvalRequired;
    
    @JsonProperty("approved_by")
    private Long approvedBy;
    
    @JsonProperty("approved_at")
    private String approvedAt;
    
    @JsonProperty("executed_at")
    private String executedAt;
    
    @JsonProperty("error_message")
    private String errorMessage;
    
    @JsonProperty("metadata")
    private Map<String, Object> metadata; // Additional metadata including safety info
    
    /**
     * Autonomy levels for AI suggestions
     */
    public enum AutonomyLevel {
        ADVISOR,        // Level 1: Read-only suggestions
        HITL,          // Level 2: Human-in-the-loop approval required
        FULL_AUTONOMY  // Level 3: Automatic execution
    }
    
    /**
     * Types of suggestions the AI can make
     */
    public enum SuggestionType {
        CLASSIFICATION,       // Apply ticket classification labels
        ASSIGNMENT,           // Assign ticket to user/team
        PRIORITY_ADJUSTMENT,  // Change ticket priority
        ESCALATION,          // Escalate ticket
        NOTIFICATION,        // Send notification
        STATUS_UPDATE,       // Update ticket status
        COMMENT_ADDITION,    // Add internal comment
        SLA_ALERT,          // Alert about SLA
        PROCESS_IMPROVEMENT, // Suggest process changes
        CUSTOMER_COMMUNICATION, // Suggest customer response
        RESOURCE_ALLOCATION  // Suggest resource allocation
    }
    
    /**
     * Status of suggestion processing
     */
    public enum SuggestionStatus {
        PENDING,           // Created, waiting for processing
        APPROVED,          // Approved for execution
        REJECTED,          // Rejected by human
        EXECUTING,         // Currently being executed
        EXECUTED,          // Successfully executed
        FAILED,            // Execution failed
        PARTIAL_SUCCESS    // Some tools succeeded, some failed
    }
    

} 