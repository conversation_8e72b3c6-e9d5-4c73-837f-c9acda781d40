package io.sx.ai.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sx.ai.model.AIConfiguration;
import io.sx.ai.service.AIConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Handles organization events with specific business logic for AI configuration management.
 * 
 * Business Logic:
 * - Check the ai_enabled flag in organization update events
 * - Insert or update AI configurations table based on ai_enabled value
 * - Enable/disable configurations based on ai_enabled flag
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrganizationEventMessageHandler implements MessageHandler {
    
    private final ObjectMapper objectMapper;
    private final AIConfigurationService aiConfigurationService;
    
    @Override
    public boolean canHandle(String eventType) {
        return "organization.u".equals(eventType);
    }
    
    @Override
    public void handle(JsonNode eventData, String subject) {
        log.info("Processing organization update event: {}", subject);
        
        try {
            // Extract event type from subject
            String eventType = extractEventTypeFromSubject(subject);
            
            // Extract organization data from the event
            JsonNode organizationNode = extractOrganizationData(eventData);
            if (organizationNode == null) {
                log.warn("No organization data found in event payload");
                return;
            }
            
            // Extract required fields
            Long organizationId = extractLongValue(organizationNode, "id");
            Long supportOrgId = extractLongValue(organizationNode, "support_organization_id");
            Boolean aiEnabled = extractBooleanValue(organizationNode, "ai_enabled");
            Long updatedById = extractLongValue(organizationNode, "updated_by_id");
            
            if (organizationId == null) {
                log.warn("Missing organization ID in event data");
                return;
            }
            
            // For support organizations, supportOrgId is the same as organizationId
            if (supportOrgId == null) {
                supportOrgId = organizationId;
            }
            
            log.info("Processing organization update - orgId: {}, supportOrgId: {}, aiEnabled: {}", 
                organizationId, supportOrgId, aiEnabled);
            
            // Handle AI configuration based on ai_enabled flag
            handleAIConfiguration(supportOrgId, aiEnabled, updatedById);
            
        } catch (Exception e) {
            log.error("Error processing organization update event: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Handle AI configuration management based on ai_enabled flag
     */
    private void handleAIConfiguration(Long supportOrgId, Boolean aiEnabled, Long updatedById) {
        try {
            if (aiEnabled == null) {
                log.warn("ai_enabled flag is null, skipping AI configuration update");
                return;
            }
            
            if (aiEnabled) {
                // AI is enabled - create or update configuration to be active
                enableAIConfiguration(supportOrgId, updatedById);
            } else {
                // AI is disabled - deactivate existing configurations
                disableAIConfiguration(supportOrgId, updatedById);
            }
            
        } catch (Exception e) {
            log.error("Error handling AI configuration for organization {}: {}", supportOrgId, e.getMessage(), e);
        }
    }
    
    /**
     * Enable AI configuration for the organization
     */
    private void enableAIConfiguration(Long supportOrgId, Long updatedById) {
        log.info("Enabling AI configuration for organization: {}", supportOrgId);
        
        // Check if configuration already exists
        AIConfiguration existingConfig = aiConfigurationService.findByTypeAndOrganization(supportOrgId, "classification");
        
        if (existingConfig != null) {
            // Update existing configuration to be active
            existingConfig.setIsActive(true);
            existingConfig.setUpdatedById(updatedById);
            existingConfig.setUpdatedBy("system");
            existingConfig.setOnBehalfOfId(supportOrgId);//TODO check this
            
            aiConfigurationService.saveConfiguration(existingConfig);
            log.info("Updated existing AI configuration to active for organization: {}", supportOrgId);
        } else {
            // Create new active configuration
            AIConfiguration newConfig = createDefaultAIConfiguration(supportOrgId, updatedById);
            aiConfigurationService.saveConfiguration(newConfig);
            log.info("Created new active AI configuration for organization: {}", supportOrgId);
        }
    }
    
    /**
     * Disable AI configuration for the organization
     */
    private void disableAIConfiguration(Long supportOrgId, Long updatedById) {
        log.info("Disabling AI configuration for organization: {}", supportOrgId);
        
        // Find existing configuration
        AIConfiguration existingConfig = aiConfigurationService.findByTypeAndOrganization(supportOrgId, "classification");
        
        if (existingConfig != null) {
            // Deactivate the configuration
            existingConfig.setIsActive(false);
            existingConfig.setUpdatedById(updatedById);
            existingConfig.setUpdatedBy("system");
            existingConfig.setOnBehalfOfId(supportOrgId);
            
            aiConfigurationService.saveConfiguration(existingConfig);
            log.info("Deactivated AI configuration for organization: {}", supportOrgId);
        } else {
            log.info("No AI configuration found to deactivate for organization: {}", supportOrgId);
        }
    }
    
    /**
     * Create default AI configuration for an organization
     */
    private AIConfiguration createDefaultAIConfiguration(Long supportOrgId, Long updatedById) {
        Map<String, Object> defaultConfig = new HashMap<>();
        defaultConfig.put("enabled", true);
        defaultConfig.put("default_strategy", "hybrid");
        defaultConfig.put("min_description_length", 10);
        defaultConfig.put("confidence_thresholds", Map.of(
            "app_only", 0.8,
            "hybrid", 0.7,
            "llm_only", 0.6
        ));
        defaultConfig.put("customer_importance_thresholds", Map.of(
            "high", 8.0,
            "medium", 5.0,
            "low", 3.0
        ));
        defaultConfig.put("priority_weights", Map.of(
            "CRITICAL", 1.5,
            "HIGH", 1.2,
            "MEDIUM", 1.0,
            "LOW", 0.8
        ));
        
        return AIConfiguration.builder()
            .configurationType("classification")
            .supportOrganizationId(supportOrgId)
            .configurationData(objectMapper.valueToTree(defaultConfig))
            .isActive(true)
            .createdById(updatedById)
            .updatedById(updatedById)
            .createdBy("system")
            .updatedBy("system")
            .onBehalfOfId(supportOrgId)
            .build();
    }
    
    /**
     * Extract organization data from event payload
     */
    private JsonNode extractOrganizationData(JsonNode eventData) {
        // Try different possible field names for organization data
        JsonNode orgData = eventData.get("event");
        if (orgData == null) {
            orgData = eventData.get("event_data");
        }
        if (orgData == null) {
            orgData = eventData.get("organization");
        }
        if (orgData == null) {
            orgData = eventData; // Use the entire event data if no specific field found
        }
        return orgData;
    }
    
    /**
     * Extract long value from JsonNode with null safety
     */
    private Long extractLongValue(JsonNode node, String fieldName) {
        if (node != null && node.has(fieldName) && !node.get(fieldName).isNull()) {
            return node.get(fieldName).asLong();
        }
        return null;
    }
    
    /**
     * Extract boolean value from JsonNode with null safety
     */
    private Boolean extractBooleanValue(JsonNode node, String fieldName) {
        if (node != null && node.has(fieldName) && !node.get(fieldName).isNull()) {
            return node.get(fieldName).asBoolean();
        }
        return null;
    }
    
    /**
     * Extract event type from NATS subject
     */
    private String extractEventTypeFromSubject(String subject) {
        String[] parts = subject.split("\\.");
        if (parts.length >= 5) {
            String entity = parts[3];
            String operation = parts[4];
            if ("organization".equals(entity)) {
                switch (operation) {
                    case "UPDATE": return "organization.u";
                    case "INSERT": return "organization.c";
                    case "DELETE": return "organization.d";
                    default: return entity + "." + operation;
                }
            }
            return entity + "." + operation;
        }
        return subject;
    }
} 