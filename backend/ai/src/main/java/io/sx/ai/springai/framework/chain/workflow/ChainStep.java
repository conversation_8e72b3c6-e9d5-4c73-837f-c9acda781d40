package io.sx.ai.springai.framework.chain.workflow;

import io.sx.ai.springai.framework.common.BaseStepResult;
import io.sx.ai.springai.framework.common.BaseWorkflowStep;
import io.sx.ai.springai.framework.common.WorkflowInput;
import io.sx.ai.springai.framework.common.WorkflowOutput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Represents a step in a chain workflow execution
 * Implements BaseWorkflowStep for proper step behavior
 */
@RequiredArgsConstructor
@Slf4j
public class ChainStep<I, O> implements BaseWorkflowStep<I, O> {
    
    private final String stepName;
    private final String stepDescription;
    private final boolean isFinalStep;
    private final StepType stepType;
    
    @Override
    public BaseStepResult<I, O> execute(WorkflowInput<I> input, BaseStepResult<?, ?> previousStep) {
        log.info("Executing step: {} with input: {}", stepName, input.getValue());
        
        try {
            WorkflowOutput<O> output = processInput(input, previousStep);
            
            return ChainStepResult.success(
                previousStep != null ? previousStep.getStepNumber() + 1 : 1,
                stepName,
                input.getValue(),
                output.getValue(),
                stepDescription,
                "" // prompt will be set by the calling workflow
            );
        } catch (Exception e) {
            log.error("Error executing step {}: {}", stepName, e.getMessage(), e);
            return ChainStepResult.error(
                previousStep != null ? previousStep.getStepNumber() + 1 : 1,
                stepName,
                input.getValue(),
                "Error executing step: " + e.getMessage()
            );
        }
    }
    
    @Override
    public String getStepName() {
        return stepName;
    }
    
    @Override
    public String getStepDescription() {
        return stepDescription;
    }
    
    @Override
    public boolean isFinalStep() {
        return isFinalStep;
    }
    
    @Override
    public StepType getStepType() {
        return stepType;
    }
    
    @Override
    public WorkflowOutput<O> processInput(WorkflowInput<I> input, BaseStepResult<?, ?> previousStep) {
        // Default implementation - subclasses should override this
        log.warn("Default processInput implementation called for step: {}. Subclasses should override this method.", stepName);
        return null;
    }
}