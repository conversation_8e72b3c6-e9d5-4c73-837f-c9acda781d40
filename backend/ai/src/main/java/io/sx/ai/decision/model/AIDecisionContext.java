package io.sx.ai.decision.model;

import io.sx.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Context for AI decision evaluation
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AIDecisionContext extends BaseModel {
    
    private String triggerEvent;
    private Long entityId;
    private String entityType;
    private Long organizationId;
    private Long supportOrganizationId;
    private Long userId;
    private Map<String, Object> eventData;
    private Map<String, Object> entityData;
    private Map<String, Object> organizationData;
    private Map<String, Object> userData;
    private Map<String, Object> historicalData;
    private Map<String, Object> aiConfiguration;
    private LocalDateTime timestamp;
} 