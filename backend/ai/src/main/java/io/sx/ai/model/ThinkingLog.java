package io.sx.ai.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.sx.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Map;

/**
 * ThinkingLog model for JSON-based Chain of Thought / Tree of Thought logging
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ThinkingLog extends BaseModel {
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("trigger_event")
    private String triggerEvent;
    
    @JsonProperty("trigger_entity_id")
    private Long triggerEntityId;
    
    @JsonProperty("trigger_entity_type")
    private String triggerEntityType;
    
    @JsonProperty("thinking_strategy")
    private String thinkingStrategy; // CHAIN_OF_THOUGHT, TREE_OF_THOUGHT
    
    @JsonProperty("thinking_process")
    private Map<String, Object> thinkingProcess; // Complete thinking process as JSON
    
    @JsonProperty("suggestions")
    private Map<String, Object> suggestions; // Final suggestions as JSON
    
    @JsonProperty("session_status")
    private String sessionStatus; // ACTIVE, COMPLETED, FAILED
    
    @JsonProperty("confidence_score")
    private BigDecimal confidenceScore;
    
    @JsonProperty("processing_time_ms")
    private Long processingTimeMs;
} 