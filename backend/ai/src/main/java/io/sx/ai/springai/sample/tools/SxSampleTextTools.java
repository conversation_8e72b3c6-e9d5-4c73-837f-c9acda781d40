package io.sx.ai.springai.sample.tools;

import io.sx.ai.tools.logic.TextDecorationLogic;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

/**
 * Spring AI wrapper methods for text operations that use plain tool logic classes.
 * Provides @Tool annotations for Spring AI integration.
 */
@Component
public class SxSampleTextTools {
    
    private final TextDecorationLogic textDecorationLogic = new TextDecorationLogic();
    
    @Tool(description = "Add decorative borders around text using specified delimiter")
    public String decorateText(String text, String delimiter, String style) {
        return switch (style) {
            case "simple" -> textDecorationLogic.decorateSimple(text, delimiter);
            case "box" -> textDecorationLogic.decorateBox(text, delimiter);
            case "fancy" -> textDecorationLogic.decorateFancy(text, delimiter);
            default -> textDecorationLogic.decorateSimple(text, delimiter);
        };
    }
    
    @Tool(description = "Count words in the given text")
    public int countWords(String text) {
        return textDecorationLogic.countWords(text);
    }
    
    @Tool(description = "Count characters in the given text (excluding whitespace)")
    public int countCharacters(String text) {
        return textDecorationLogic.countCharacters(text);
    }
    
    @Tool(description = "Analyze text sentiment (basic implementation)")
    public String analyzeSentiment(String text) {
        // Basic sentiment analysis
        String lowerText = text.toLowerCase();
        if (lowerText.contains("good") || lowerText.contains("great") || lowerText.contains("excellent")) {
            return "positive";
        } else if (lowerText.contains("bad") || lowerText.contains("terrible") || lowerText.contains("awful")) {
            return "negative";
        } else {
            return "neutral";
        }
    }
} 