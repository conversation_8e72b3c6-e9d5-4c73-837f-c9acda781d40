package io.sx.ai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Structured request DTO for LLM interactions.
 * Provides consistent, parseable JSON structure for all LLM communications.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Structured LLM request with comprehensive context and metadata")
public class StructuredLLMRequest {
    
    /**
     * Unique identifier for this request
     */
    @NotBlank(message = "Request ID is required")
    @Schema(description = "Unique identifier for this request", example = "req-12345")
    private String requestId;
    
    /**
     * Type of analysis or task being performed
     */
    @NotBlank(message = "Task type is required")
    @Schema(description = "Type of analysis or task being performed", example = "ANALYZE_EVENT")
    private String taskType;
    
    /**
     * Specific scenario or context for the task
     */
    private String scenario;
    
    /**
     * Organization context
     */
    @NotNull(message = "Organization context is required")
    @Schema(description = "Organization context information")
    private OrganizationContext organizationContext;
    
    /**
     * User context (who triggered the request)
     */
    private UserContext userContext;
    
    /**
     * Ticket context (if applicable)
     */
    private TicketContext ticketContext;
    
    /**
     * Event context (what triggered this request)
     */
    private EventContext eventContext;
    
    /**
     * Historical context and related data
     */
    private HistoricalContext historicalContext;
    
    /**
     * Available tools and their capabilities
     */
    private List<ToolContext> availableTools;
    
    /**
     * Business rules and constraints
     */
    private BusinessRules businessRules;
    
    /**
     * Analysis goals and objectives
     */
    private List<String> analysisGoals;
    
    /**
     * Expected output format and structure
     */
    private OutputSchema expectedOutput;
    
    /**
     * Additional metadata
     */
    private Map<String, Object> metadata;
    
    /**
     * Timestamp when request was created
     */
    private Instant timestamp;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OrganizationContext {
        private Long organizationId;
        private String organizationName;
        private String organizationType; // SUPPORT, ACCOUNT
        private Long supportOrganizationId;
        private String timezone;
        private String businessHours;
        private String slaTier;
        private Map<String, Object> customFields;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class UserContext {
        private Long userId;
        private String username;
        private String userRole;
        private String userType;
        private Long teamId;
        private String teamName;
        private List<String> permissions;
        private Map<String, Object> preferences;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class TicketContext {
        private Long ticketId;
        private String ticketTitle;
        private String ticketDescription;
        private String ticketStatus;
        private String ticketPriority;
        private String ticketType;
        private String ticketCategory;
        private Long assigneeId;
        private String assigneeName;
        private String assignedTeam;
        private Long participantId;
        private String participantType;
        private String participantRole;
        private List<Map<String, Object>> similarTickets;
        private Map<String, Object> historicalData;
        private Map<String, Object> businessRules;
        private List<String> tags;
        private Map<String, Object> customFields;
        private List<Comment> comments;
        private List<Attachment> attachments;
        private Instant createdAt;
        private Instant updatedAt;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class EventContext {
        private String eventType;
        private String eventSubject;
        private String operation; // c, r, u, d
        private Long entityId;
        private String entityType;
        private Map<String, Object> eventData;
        private Instant eventTimestamp;
        private Instant timestamp;
        private String triggerSource;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class HistoricalContext {
        private List<SimilarTicket> similarTickets;
        private List<HistoricalEvent> recentEvents;
        private Map<String, Object> performanceMetrics;
        private List<String> commonPatterns;
        private Map<String, Object> trends;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ToolContext {
        private String toolId;
        private String toolName;
        private String toolCategory; // SYSTEM, BUSINESS, CRITICAL, ADMIN
        private String toolDescription;
        private String toolVersion;
        private String executionMode;
        private List<String> requiredPermissions;
        private Map<String, Object> parameters;
        private Boolean requiresApproval;
        private String approvalLevel;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class BusinessRules {
        private List<String> constraints;
        private Map<String, Object> policies;
        private List<String> complianceRequirements;
        private Map<String, Object> thresholds;
        private List<String> escalationRules;
        private List<String> priorities;
        private Map<String, String> slaRequirements;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OutputSchema {
        private String format; // JSON, XML, TEXT
        private String structure;
        private List<String> requiredFields;
        private Map<String, Object> fieldTypes;
        private String validationRules;
        private String expectedOutputFormat;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Comment {
        private Long commentId;
        private String content;
        private String authorName;
        private Long authorId;
        private String commentType; // PUBLIC, INTERNAL
        private Instant createdAt;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Attachment {
        private Long attachmentId;
        private String fileName;
        private String fileType;
        private Long fileSize;
        private String description;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SimilarTicket {
        private Long ticketId;
        private String title;
        private String status;
        private String resolution;
        private Double similarityScore;
        private List<String> commonTags;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class HistoricalEvent {
        private String eventType;
        private String description;
        private Instant timestamp;
        private String impact;
    }
} 