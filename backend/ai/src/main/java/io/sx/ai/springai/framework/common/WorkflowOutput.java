package io.sx.ai.springai.framework.common;

import java.util.Map;

/**
 * Generic output wrapper with Spring AI integration
 * Provides type-safe output handling for different workflow contexts
 */
public interface WorkflowOutput<T> {
    
    /**
     * Get the actual output value
     */
    T getValue();
    
    /**
     * Get the output type identifier
     */
    String getType();
    
    /**
     * Get metadata associated with this output
     */
    Map<String, Object> getMetadata();
    
    /**
     * Get confidence score for this output (0.0 to 1.0)
     */
    Double getConfidence();
    
    /**
     * Get the format of this output
     */
    String getFormat();
    
    /**
     * Get a human-readable description of this output
     */
    default String getDescription() {
        return String.format("%s output: %s (confidence: %.2f)", getType(), getValue(), getConfidence());
    }
} 