package io.sx.ai.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Spring AI Configuration
 * Provides different ChatClient configurations for different use cases
 */
@Slf4j
@Configuration
public class SpringAIConfig {

    /**
     * Primary ChatClient for general use
     * Uses default ChatModel with standard configuration
     */
    @Bean
    @Primary
    public ChatClient primaryChatClient(@Autowired ChatModel chatModel) {
        log.info("Creating primary ChatClient with default configuration");
        return ChatClient.builder(chatModel)
            .build();
    }

    /**
     * ChatClient for mathematical operations
     * Optimized for precise numerical calculations
     */
    @Bean
    @Qualifier("mathematicalChatClient")
    public ChatClient mathematicalChatClient(@Autowired ChatModel chatModel) {
        log.info("Creating mathematical ChatClient with precision-focused configuration");
        return ChatClient.builder(chatModel)
            .build();
    }

    /**
     * ChatClient for creative tasks
     * Optimized for creative writing and brainstorming
     */
    @Bean
    @Qualifier("creativeChatClient")
    public ChatClient creativeChatClient(@Autowired ChatModel chatModel) {
        log.info("Creating creative ChatClient with creativity-focused configuration");
        return ChatClient.builder(chatModel)
            .build();
    }

    /**
     * ChatClient for analytical tasks
     * Optimized for analysis and reasoning
     */
    @Bean
    @Qualifier("analyticalChatClient")
    public ChatClient analyticalChatClient(@Autowired ChatModel chatModel) {
        log.info("Creating analytical ChatClient with analysis-focused configuration");
        return ChatClient.builder(chatModel)
            .build();
    }

    /**
     * ChatClient for workflow execution
     * Optimized for structured workflow tasks
     */
    @Bean
    @Qualifier("workflowChatClient")
    public ChatClient workflowChatClient(@Autowired ChatModel chatModel) {
        log.info("Creating workflow ChatClient with workflow-focused configuration");
        return ChatClient.builder(chatModel)
            .build();
    }

    /**
     * ChatClient for function calling
     * Optimized for tool and function execution
     */
    @Bean
    @Qualifier("functionCallingChatClient")
    public ChatClient functionCallingChatClient(@Autowired ChatModel chatModel) {
        log.info("Creating function calling ChatClient with tool execution configuration");
        return ChatClient.builder(chatModel)
            .build();
    }

    /**
     * ChatClient for chain workflows
     * Optimized for multi-step chain operations
     */
    @Bean
    @Qualifier("chainWorkflowChatClient")
    public ChatClient chainWorkflowChatClient(@Autowired ChatModel chatModel) {
        log.info("Creating chain workflow ChatClient with chain execution configuration");
        return ChatClient.builder(chatModel)
            .build();
    }
} 