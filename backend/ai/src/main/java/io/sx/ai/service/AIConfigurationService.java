package io.sx.ai.service;

import io.sx.ai.model.AIConfiguration;

import java.util.List;
import java.util.Map;

/**
 * Service for managing AI configurations
 */
public interface AIConfigurationService {
    
    /**
     * Get configuration for a specific type and organization
     */
    Map<String, Object> getConfiguration(String configurationType, Long supportOrgId);
    
    /**
     * Save configuration for an organization
     */
    AIConfiguration saveConfiguration(AIConfiguration configuration);
    
    /**
     * Get all configurations for an organization
     */
    List<AIConfiguration> getConfigurationsByOrganization(Long supportOrgId);
    
    /**
     * Get configuration by ID
     */
    AIConfiguration getConfigurationById(Long id);
    
    /**
     * Delete configuration
     */
    void deleteConfiguration(Long id);
    
    /**
     * Check if configuration exists for organization
     */
    boolean hasConfiguration(String configurationType, Long supportOrgId);
    
    /**
     * Find configuration by type and organization
     */
    AIConfiguration findByTypeAndOrganization(Long organizationId, String configKey);
} 