package io.sx.ai.decision.strategies;

import io.sx.ai.decision.model.AIDecisionContext;
import io.sx.ai.model.AISuggestion;
import io.sx.ai.orchestration.TicketOrchestrator;

import java.util.List;
import java.util.Map;

/**
 * Interface for AI decision strategies
 */
public interface AIDecisionStrategy {
    
    /**
     * Get unique strategy identifier
     */
    String getStrategyId();
    
    /**
     * Get human-readable strategy name
     */
    String getStrategyName();
    
    /**
     * Check if strategy is applicable for the given context
     */
    boolean isApplicable(AIDecisionContext context);
    
    /**
     * Execute the strategy and return suggestions
     */
    List<AISuggestion> execute(AIDecisionContext context, Map<String, Object> config);
    
    /**
     * Execute ticket processing using this strategy
     * 
     * @param sessionId Unique session identifier
     * @param ticketId The ticket ID being processed
     * @param ticketData The ticket data
     * @param organizationId The organization ID
     * @param supportOrgId The support organization ID
     * @param userId The user ID
     * @return Processing result
     */
    TicketOrchestrator.TicketProcessingResult executeTicketProcessing(String sessionId, Long ticketId, 
                                                                     Map<String, Object> ticketData,
                                                                     Long organizationId, Long supportOrgId, Long userId);
} 