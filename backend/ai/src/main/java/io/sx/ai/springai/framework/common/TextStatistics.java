package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Statistics about a text, including word count, character count, and other metrics.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextStatistics {
    
    /**
     * Total number of words in the text
     */
    private Integer wordCount;
    
    /**
     * Total number of characters (excluding whitespace)
     */
    private Integer characterCount;
    
    /**
     * Total number of characters (including whitespace)
     */
    private Integer characterCountWithSpaces;
    
    /**
     * Number of sentences in the text
     */
    private Integer sentenceCount;
    
    /**
     * Number of paragraphs in the text
     */
    private Integer paragraphCount;
    
    /**
     * Average words per sentence
     */
    private Double averageWordsPerSentence;
    
    /**
     * Average characters per word
     */
    private Double averageCharactersPerWord;
    
    /**
     * Reading time estimate in minutes
     */
    private Double readingTimeMinutes;
    
    /**
     * Speaking time estimate in minutes
     */
    private Double speakingTimeMinutes;
    
    /**
     * Text complexity score (0.0 to 1.0)
     */
    private Double complexityScore;
    
    @Override
    public String toString() {
        return String.format("TextStatistics{words=%d, chars=%d, sentences=%d, complexity=%.2f}", 
            wordCount != null ? wordCount : 0, 
            characterCount != null ? characterCount : 0, 
            sentenceCount != null ? sentenceCount : 0,
            complexityScore != null ? complexityScore : 0.0);
    }
} 