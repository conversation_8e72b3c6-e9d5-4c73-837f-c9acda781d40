package io.sx.ai.controller;

import io.sx.ai.decision.engine.AIDecisionEngine;
import io.sx.ai.decision.model.AIDecisionResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * REST controller for AI decision framework
 * Provides endpoints for AI decision evaluation and processing
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/decision")
@RequiredArgsConstructor
public class AIDecisionController {

    private final AIDecisionEngine decisionEngine;
    
    /**
     * Evaluate AI decision for a given event and organization
     */
    @PostMapping("/evaluate")
    public ResponseEntity<AIDecisionResult> evaluateDecision(
            @RequestParam String triggerEvent,
            @RequestParam Long supportOrgId,
            @RequestBody Map<String, Object> eventData) {

        log.info("Evaluating AI decision for event: {}, org: {}", triggerEvent, supportOrgId);

        try {
            AIDecisionResult result = decisionEngine.evaluateDecision(triggerEvent, eventData, supportOrgId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error evaluating AI decision", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Health check endpoint for AI decision service
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = Map.of(
            "status", "healthy",
            "service", "ai-decision",
            "timestamp", java.time.LocalDateTime.now().toString(),
            "description", "AI Decision Evaluation Service"
        );
        return ResponseEntity.ok(health);
    }
}