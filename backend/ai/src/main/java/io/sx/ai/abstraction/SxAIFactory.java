package io.sx.ai.abstraction;

import io.sx.ai.springai.spring.SxSpringLLMClient;
import io.sx.ai.springai.spring.SxSpringPromptTemplate;
import io.sx.ai.springai.spring.SxSpringMessage;
import io.sx.ai.springai.spring.SxSpringPrompt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Factory for creating framework-agnostic AI components
 * Provides a unified interface for accessing AI capabilities
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SxAIFactory {
    
    private final SxSpringLLMClient llmClient;
    
    /**
     * Get the LLM client
     */
    public SxLLMClient getLLMClient() {
        return llmClient;
    }
    
    /**
     * Create a prompt template from a string
     */
    public SxPromptTemplate createPromptTemplate(String template) {
        return new SxSpringPromptTemplate(template);
    }
    
    /**
     * Create a system prompt template
     */
    public SxPromptTemplate createSystemPromptTemplate(String template) {
        return SxSpringPromptTemplate.createSystemTemplate(template);
    }
    
    /**
     * Create a user message
     */
    public SxMessage createUserMessage(String content) {
        return new SxSpringMessage(new org.springframework.ai.chat.messages.UserMessage(content));
    }
    
    /**
     * Create a system message
     */
    public SxMessage createSystemMessage(String content) {
        return new SxSpringMessage(new org.springframework.ai.chat.messages.SystemMessage(content));
    }
    
    /**
     * Create an assistant message
     */
    public SxMessage createAssistantMessage(String content) {
        return new SxSpringMessage(new org.springframework.ai.chat.messages.AssistantMessage(content));
    }
    
    /**
     * Create a prompt from messages
     */
    public SxPrompt createPrompt(List<SxMessage> messages) {
        // Convert SxMessages to Spring Messages
        java.util.List<org.springframework.ai.chat.messages.Message> springMessages = messages.stream()
            .map(msg -> ((SxSpringMessage) msg).getSpringMessage())
            .collect(java.util.stream.Collectors.toList());
        org.springframework.ai.chat.prompt.Prompt springPrompt = new org.springframework.ai.chat.prompt.Prompt(springMessages);
        return new SxSpringPrompt(springPrompt);
    }
    
    /**
     * Create a prompt from a template and variables
     */
    public SxPrompt createPrompt(String template, Map<String, Object> variables) {
        SxPromptTemplate promptTemplate = createPromptTemplate(template);
        return promptTemplate.createPrompt(variables);
    }
    
    /**
     * Generate a response for the given prompt
     */
    public SxLLMResponse<Object> generate(SxPrompt prompt) {
        return llmClient.generate(prompt);
    }
    
    /**
     * Generate a type-safe structured response
     */
    public <T> SxLLMResponse<T> generate(SxPrompt prompt, Class<T> outputType) {
        return llmClient.generate(prompt, outputType);
    }
    
    /**
     * Generate a structured response (convenience method)
     */
    public <T> T generateStructured(SxPrompt prompt, Class<T> outputType) {
        return llmClient.generateStructured(prompt, outputType);
    }
    
    /**
     * Generate a response with function calling
     */
    public SxLLMResponse generateWithFunctions(SxPrompt prompt, List<SxFunction> functions) {
        return llmClient.generateWithFunctions(prompt, functions);
    }
    
    /**
     * Check if the AI client is available
     */
    public boolean isAvailable() {
        return llmClient.isAvailable();
    }
} 