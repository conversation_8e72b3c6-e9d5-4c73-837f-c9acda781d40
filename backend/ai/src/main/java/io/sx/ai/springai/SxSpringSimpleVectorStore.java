package io.sx.ai.springai;

import io.sx.ai.abstraction.SxDocument;
import io.sx.ai.abstraction.SxVectorStore;
import io.sx.ai.springai.spring.SxSpringDocument;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.Embedding;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@ConditionalOnProperty(name = "spring.ai.custom.vector.store", havingValue = "simple")
public class SxSpringSimpleVectorStore implements SxVectorStore {

    private final SimpleVectorStore vectorStore;
    private final EmbeddingModel embeddingModel;

    public SxSpringSimpleVectorStore(EmbeddingModel embeddingModel) {
        this.embeddingModel = embeddingModel;
        this.vectorStore = SimpleVectorStore.builder(embeddingModel).build();
    }

    @Override
    public void addDocument(SxDocument document) {
        Document springDocument = new Document(document.getContent(), document.getMetadata());
        vectorStore.add(List.of(springDocument));
    }

    @Override
    public void addDocuments(List<SxDocument> documents) {
        List<Document> springDocuments = documents.stream()
            .map(doc -> new Document(doc.getContent(), doc.getMetadata()))
            .collect(Collectors.toList());
        vectorStore.add(springDocuments);
    }

    @Override
    public List<SxDocument> similaritySearch(String query, int maxResults) {
        List<Document> results = vectorStore.similaritySearch(
            SearchRequest.builder().query(query).topK(maxResults).build()
        );
        return results.stream()
            .map(SxSpringDocument::new)
            .map(d -> (SxDocument) d)
            .collect(Collectors.toList());
    }

    @Override
    public List<SxDocument> similaritySearch(String query, int maxResults, double scoreThreshold) {
        List<Document> results = vectorStore.similaritySearch(
            SearchRequest.builder().query(query)
                .topK(maxResults)
                .similarityThreshold(scoreThreshold)
                .build()
        );
        return results.stream()
            .map(
                doc -> new SxSpringDocument(new Document(doc.getId(), doc.getText(), doc.getMetadata())
                ))
            .map(d -> (SxDocument) d)
            .toList();
    }

    @Override
    public List<SxDocument> similaritySearch(List<Double> embeddings, int maxResults) {
        // SimpleVectorStore doesn't support direct embedding search
        // This would require a different vector store implementation
        // For now, return empty list
        return new ArrayList<>();
    }

    @Override
    public boolean deleteDocument(String documentId) {
        try {
            // SimpleVectorStore doesn't have a direct delete method
            // We would need to implement a custom solution or use a different vector store
            // For now, return false to indicate it's not implemented
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public SxDocument getDocument(String documentId) {
        try {
            // SimpleVectorStore doesn't have a direct get method
            // We would need to implement a custom solution or use a different vector store
            // For now, return null to indicate it's not implemented

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public boolean updateDocument(SxDocument document) {
        try {
            // For SimpleVectorStore, we would need to delete and re-add
            // Since delete is not implemented, we can't update either
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public long getDocumentCount() {
        try {
            // SimpleVectorStore doesn't expose document count
            // We would need to implement a custom solution
            return 0;
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public void clear() {
        try {
            // SimpleVectorStore doesn't have a clear method
            // We would need to implement a custom solution
        } catch (Exception e) {
            // Log error but don't throw
        }
    }

    @Override
    public Map<String, Object> getMetadata() {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("type", "SimpleVectorStore");
        metadata.put("implementation", "Spring AI SimpleVectorStore");
        metadata.put("embeddingModel", embeddingModel.getClass().getSimpleName());
        return metadata;
    }

    @Override
    public boolean isAvailable() {
        try {
            // Test if the embedding model is available
            embeddingModel.embed("test");
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public int getEmbeddingDimensions() {
        try {
            // Get embedding dimensions by testing with a sample text
            EmbeddingResponse response = embeddingModel.embedForResponse(List.of("test"));
            if (!response.getResults().isEmpty()) {
                return response.getResults().get(0).getOutput().length;
            }
            return 0;
        } catch (Exception e) {
            return 0;
        }
    }
}