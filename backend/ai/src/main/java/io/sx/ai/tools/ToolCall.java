package io.sx.ai.tools;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Abstract representation of a tool call/invocation
 * Provides a unified interface for all tool executions
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
public abstract class ToolCall {
    
    protected String toolName;
    protected String callId;
    protected Map<String, Object> parameters;
    protected Map<String, Object> metadata;
    protected LocalDateTime createdAt;
    protected String sessionId;
    protected Long organizationId;
    protected Long onBehalfOfId;
    
    /**
     * Execute the tool call
     * @return ToolCallResult containing success status and response data
     */
    public abstract ToolCallResult invoke();
    
    /**
     * Validate the tool call parameters
     * @return true if parameters are valid
     */
    public abstract boolean validate();
    
    /**
     * Get a human-readable description of what this tool call will do
     * @return Description string
     */
    public abstract String getDescription();
    
    /**
     * Get the tool schema for parameter validation
     * @return JSON schema string
     */
    public abstract String getSchema();
    
    /**
     * Create a new ToolCall instance with the given parameters
     * @param parameters Tool parameters
     * @return New ToolCall instance
     */
    public abstract ToolCall withParameters(Map<String, Object> parameters);
    
    /**
     * Result of a tool call execution
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ToolCallResult {
        private boolean success;
        private Map<String, Object> data;
        private String errorMessage;
        private String errorType;
        private LocalDateTime executedAt;
        private Long executionTimeMs;
        
        public static ToolCallResult success(Map<String, Object> data) {
            return ToolCallResult.builder()
                .success(true)
                .data(data)
                .executedAt(LocalDateTime.now())
                .build();
        }
        
        public static ToolCallResult error(String errorType, String errorMessage) {
            return ToolCallResult.builder()
                .success(false)
                .errorType(errorType)
                .errorMessage(errorMessage)
                .executedAt(LocalDateTime.now())
                .build();
        }
    }
} 