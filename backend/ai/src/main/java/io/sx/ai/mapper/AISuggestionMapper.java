package io.sx.ai.mapper;

import io.sx.ai.model.AISuggestion;
import io.sx.dto.AISuggestionDTO;
import io.sx.model.AISuggestionEnums;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between AISuggestion domain objects and DTOs
 */
@Component
public class AISuggestionMapper {
    
    /**
     * Convert domain object to DTO
     */
    public AISuggestionDTO toDTO(AISuggestion suggestion) {
        if (suggestion == null) {
            return null;
        }
        
        return AISuggestionDTO.builder()
                .id(suggestion.getId())
                .organizationId(suggestion.getOrganizationId())
                .createdAt(suggestion.getCreatedAt())
                .updatedAt(suggestion.getUpdatedAt())
                .createdById(suggestion.getCreatedById())
                .updatedById(suggestion.getUpdatedById())
                .createdBy(suggestion.getCreatedBy())
                .updatedBy(suggestion.getUpdatedBy())
                .onBehalfOfId(suggestion.getOnBehalfOfId())
                .suggestionId(suggestion.getSuggestionId())
                .sessionId(suggestion.getSessionId())
                .ticketId(suggestion.getTicketId())
                .thinkingLogId(suggestion.getThinkingLogId())
                .autonomyLevel(convertAutonomyLevel(suggestion.getAutonomyLevel()))
                .suggestionType(convertSuggestionType(suggestion.getSuggestionType()))
                .title(suggestion.getTitle())
                .description(suggestion.getDescription())
                .reasoning(suggestion.getReasoning())
                .confidenceScore(suggestion.getConfidenceScore())
                .priority(suggestion.getPriority())
                .toolName(suggestion.getToolName())
                .toolParameters(suggestion.getToolParameters())
                .status(convertSuggestionStatus(suggestion.getStatus()))
                .approvalRequired(suggestion.getApprovalRequired())
                .approvedBy(suggestion.getApprovedBy())
                .approvedAt(suggestion.getApprovedAt())
                .executedAt(suggestion.getExecutedAt())
                .errorMessage(suggestion.getErrorMessage())
                .metadata(suggestion.getMetadata())
                .build();
    }
    
    /**
     * Convert DTO to domain object
     */
    public AISuggestion toEntity(AISuggestionDTO dto) {
        if (dto == null) {
            return null;
        }
        
        return AISuggestion.builder()
                .id(dto.getId())
                .organizationId(dto.getOrganizationId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .createdById(dto.getCreatedById())
                .updatedById(dto.getUpdatedById())
                .createdBy(dto.getCreatedBy())
                .updatedBy(dto.getUpdatedBy())
                .onBehalfOfId(dto.getOnBehalfOfId())
                .suggestionId(dto.getSuggestionId())
                .sessionId(dto.getSessionId())
                .ticketId(dto.getTicketId())
                .thinkingLogId(dto.getThinkingLogId())
                .autonomyLevel(convertAutonomyLevel(dto.getAutonomyLevel()))
                .suggestionType(convertSuggestionType(dto.getSuggestionType()))
                .title(dto.getTitle())
                .description(dto.getDescription())
                .reasoning(dto.getReasoning())
                .confidenceScore(dto.getConfidenceScore())
                .priority(dto.getPriority())
                .toolName(dto.getToolName())
                .toolParameters(dto.getToolParameters())
                .status(convertSuggestionStatus(dto.getStatus()))
                .approvalRequired(dto.getApprovalRequired())
                .approvedBy(dto.getApprovedBy())
                .approvedAt(dto.getApprovedAt())
                .executedAt(dto.getExecutedAt())
                .errorMessage(dto.getErrorMessage())
                .metadata(dto.getMetadata())
                .build();
    }
    
    /**
     * Convert list of domain objects to DTOs
     */
    public List<AISuggestionDTO> toDTOList(List<AISuggestion> suggestions) {
        if (suggestions == null) {
            return null;
        }
        
        return suggestions.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Convert list of DTOs to domain objects
     */
    public List<AISuggestion> toEntityList(List<AISuggestionDTO> dtos) {
        if (dtos == null) {
            return null;
        }
        
        return dtos.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
    
    // Conversion methods for enums
    private AISuggestionEnums.AutonomyLevel convertAutonomyLevel(AISuggestion.AutonomyLevel level) {
        if (level == null) return null;
        return AISuggestionEnums.AutonomyLevel.valueOf(level.name());
    }
    
    private AISuggestion.AutonomyLevel convertAutonomyLevel(AISuggestionEnums.AutonomyLevel level) {
        if (level == null) return null;
        return AISuggestion.AutonomyLevel.valueOf(level.name());
    }
    
    private AISuggestionEnums.SuggestionType convertSuggestionType(AISuggestion.SuggestionType type) {
        if (type == null) return null;
        return AISuggestionEnums.SuggestionType.valueOf(type.name());
    }
    
    private AISuggestion.SuggestionType convertSuggestionType(AISuggestionEnums.SuggestionType type) {
        if (type == null) return null;
        return AISuggestion.SuggestionType.valueOf(type.name());
    }
    
    private AISuggestionEnums.SuggestionStatus convertSuggestionStatus(AISuggestion.SuggestionStatus status) {
        if (status == null) return null;
        return AISuggestionEnums.SuggestionStatus.valueOf(status.name());
    }
    
    private AISuggestion.SuggestionStatus convertSuggestionStatus(AISuggestionEnums.SuggestionStatus status) {
        if (status == null) return null;
        return AISuggestion.SuggestionStatus.valueOf(status.name());
    }
} 