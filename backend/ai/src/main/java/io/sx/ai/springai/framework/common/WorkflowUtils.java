package io.sx.ai.springai.framework.common;

import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Utility class for common workflow operations
 * Provides shared helper methods for workflow execution
 */
public class WorkflowUtils {
    
    private static final Pattern NUMERIC_PATTERN = Pattern.compile("-?\\d+(\\.\\d+)?");
    
    /**
     * Generate a unique session ID
     * @return Unique session identifier
     */
    public static String generateSessionId() {
        return "session_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
    
    /**
     * Load template from classpath resources
     * @param templatePath Path to the template file
     * @return Template content as string
     * @throws IOException if template cannot be loaded
     */
    public static String loadTemplate(String templatePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(templatePath);
        return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
    }
    
    /**
     * Extract numeric output from LLM response
     * @param response LLM response text
     * @return Extracted numeric value as string
     */
    public static String extractNumericOutput(String response) {
        if (response == null || response.trim().isEmpty()) {
            return "0";
        }
        
        // Try to match integers or decimals (including negative numbers)
        java.util.regex.Matcher matcher = NUMERIC_PATTERN.matcher(response);
        if (matcher.find()) {
            return matcher.group();
        }
        
        // If no number found, return the last word or "0"
        String[] words = response.trim().split("\\s+");
        return words.length > 0 ? words[words.length - 1] : "0";
    }
    
    /**
     * Extract input value from event data
     * @param eventData Event payload data
     * @return Extracted input value or null if not found
     */
    public static String extractInputFromEvent(Map<String, Object> eventData) {
        if (eventData == null) {
            return null;
        }
        
        // Try to extract input from various possible fields
        Object inputObj = eventData.get("input");
        if (inputObj != null) {
            return inputObj.toString();
        }
        
        inputObj = eventData.get("value");
        if (inputObj != null) {
            return inputObj.toString();
        }
        
        inputObj = eventData.get("data");
        if (inputObj != null) {
            return inputObj.toString();
        }
        
        // If no specific field found, try to use the first numeric value
        for (Object value : eventData.values()) {
            if (value != null && NUMERIC_PATTERN.matcher(value.toString()).matches()) {
                return value.toString();
            }
        }
        
        return null;
    }
    
    /**
     * Extract number from event data
     * @param eventData Event payload data
     * @return Extracted number
     * @throws IllegalArgumentException if no valid number found
     */
    public static double extractNumberFromEvent(Map<String, Object> eventData) {
        if (eventData == null) {
            throw new IllegalArgumentException("Event data cannot be null");
        }
        
        // Try to extract number from various possible fields
        Object numberObj = eventData.get("inputNumber");
        if (numberObj != null) {
            return ((Number) numberObj).doubleValue();
        }
        
        numberObj = eventData.get("number");
        if (numberObj != null) {
            return ((Number) numberObj).doubleValue();
        }
        
        numberObj = eventData.get("value");
        if (numberObj != null) {
            return ((Number) numberObj).doubleValue();
        }
        
        // If no specific field found, try to use the first numeric value
        for (Object value : eventData.values()) {
            if (value != null && NUMERIC_PATTERN.matcher(value.toString()).matches()) {
                return Double.parseDouble(value.toString());
            }
        }
        
        throw new IllegalArgumentException("No valid number found in event data");
    }
    
    /**
     * Extract numeric input from event data
     */
    public static Double extractNumericInputFromEvent(Map<String, Object> eventData) {
        if (eventData == null) {
            return null;
        }
        
        // Try different possible keys for numeric input
        Object input = eventData.get("input");
        if (input == null) {
            input = eventData.get("inputNumber");
        }
        if (input == null) {
            input = eventData.get("value");
        }
        if (input == null) {
            input = eventData.get("number");
        }
        
        if (input instanceof Number) {
            return ((Number) input).doubleValue();
        } else if (input instanceof String) {
            try {
                return Double.parseDouble((String) input);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        
        return null;
    }
    
    /**
     * Extract max iterations from event data
     * @param eventData Event payload data
     * @param defaultValue Default value if not found
     * @return Max iterations value
     */
    public static int extractMaxIterationsFromEvent(Map<String, Object> eventData, int defaultValue) {
        if (eventData == null) {
            return defaultValue;
        }
        
        Object maxIterationsObj = eventData.get("maxIterations");
        if (maxIterationsObj != null) {
            return ((Number) maxIterationsObj).intValue();
        }
        
        maxIterationsObj = eventData.get("maxSteps");
        if (maxIterationsObj != null) {
            return ((Number) maxIterationsObj).intValue();
        }
        
        return defaultValue;
    }
    
    /**
     * Calculate processing time in milliseconds
     * @param startTime Start time
     * @return Processing time in milliseconds
     */
    public static long calculateProcessingTime(LocalDateTime startTime) {
        if (startTime == null) {
            return 0;
        }
        return java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
    }
    
    /**
     * Validate that a string is not null or empty
     * @param value String to validate
     * @param fieldName Name of the field for error message
     * @throws IllegalArgumentException if validation fails
     */
    public static void validateNotNullOrEmpty(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException(fieldName + " cannot be null or empty");
        }
    }
    
    /**
     * Validate that a number is within a valid range
     * @param value Number to validate
     * @param min Minimum allowed value
     * @param max Maximum allowed value
     * @param fieldName Name of the field for error message
     * @throws IllegalArgumentException if validation fails
     */
    public static void validateNumberRange(double value, double min, double max, String fieldName) {
        if (value < min || value > max) {
            throw new IllegalArgumentException(fieldName + " must be between " + min + " and " + max);
        }
    }
    
    /**
     * Create a standardized error result with typed input
     * @param input Original typed input
     * @param errorMessage Error message
     * @param sessionId Session ID
     * @return Error result
     */
    public static <I, O> BaseWorkflowResult<I, O> createErrorResult(WorkflowInput<I> input, String errorMessage, String sessionId) {
        return BaseWorkflowResult.<I, O>builder()
            .originalInput(input)
            .success(false)
            .error(errorMessage)
            .sessionId(sessionId)
            .workflowType("ERROR")
            .startedAt(LocalDateTime.now())
            .completedAt(LocalDateTime.now())
            .build();
    }
    
    /**
     * Create a standardized success result with typed input and output
     * @param input Original typed input
     * @param finalOutput Final typed output
     * @param steps Executed steps
     * @param sessionId Session ID
     * @param workflowType Workflow type
     * @param startTime Start time
     * @return Success result
     */
    public static <I, O> BaseWorkflowResult<I, O> createSuccessResult(WorkflowInput<I> input, WorkflowOutput<O> finalOutput, 
                                                               java.util.List<BaseStepResult<I, O>> steps, 
                                                               String sessionId, String workflowType, 
                                                               LocalDateTime startTime) {
        return BaseWorkflowResult.<I, O>builder()
            .originalInput(input)
            .finalOutput(finalOutput)
            .steps(steps)
            .success(true)
            .sessionId(sessionId)
            .workflowType(workflowType)
            .startedAt(startTime)
            .completedAt(LocalDateTime.now())
            .processingTimeMs(calculateProcessingTime(startTime))
            .build();
    }
    
    /**
     * Create a standardized error result (backward compatibility)
     * @param input Original input string
     * @param errorMessage Error message
     * @param sessionId Session ID
     * @return Error result
     */
    public static BaseWorkflowResult<String, String> createErrorResult(String input, String errorMessage, String sessionId) {
        TextInput textInput = TextInput.of(input);
        return createErrorResult(textInput, errorMessage, sessionId);
    }
    
    /**
     * Create a standardized success result (backward compatibility)
     * @param input Original input string
     * @param finalOutput Final output string
     * @param steps Executed steps
     * @param sessionId Session ID
     * @param workflowType Workflow type
     * @param startTime Start time
     * @return Success result
     */
    public static BaseWorkflowResult<String, String> createSuccessResult(String input, String finalOutput, 
                                                               java.util.List<BaseStepResult<String, String>> steps, 
                                                               String sessionId, String workflowType, 
                                                               LocalDateTime startTime) {
        TextInput textInput = TextInput.of(input);
        TextOutput textOutput = TextOutput.of(finalOutput);
        return createSuccessResult(textInput, textOutput, steps, sessionId, workflowType, startTime);
    }
} 