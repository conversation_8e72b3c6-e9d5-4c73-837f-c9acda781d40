package io.sx.ai.config;

import lombok.Data;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
@EnableConfigurationProperties(PgVectorStoreConfigProperties.class)
@ConditionalOnProperty(name = "spring.ai.custom.vector.store", havingValue = "pgvector")
public class PgVectorStoreConfig {

    @Bean
    public PgVectorStore pgVectorStore(PgVectorStoreConfigProperties props, JdbcTemplate jdbcTemplate, EmbeddingModel embeddingModel) {
        PgVectorStore build = PgVectorStore.builder(jdbcTemplate, embeddingModel)
            .schemaName(props.getSchemaName())
            .vectorTableName(props.getTableName())
            .indexType(PgVectorStore.PgIndexType.valueOf(props.getIndexType()))
            .distanceType(PgVectorStore.PgDistanceType.valueOf(props.getDistanceType()))
            .dimensions(props.getDimensions())
            .maxDocumentBatchSize(props.getMaxDocumentBatchSize())
            .initializeSchema(props.isInitializeSchema())
            .vectorTableValidationsEnabled(props.isSchemaValidation())
            .build();
        return build;
    }
}

@ConfigurationProperties(prefix = "spring.ai.vectorstore.pgvector")
@Data
class PgVectorStoreConfigProperties {
    private String schemaName;
    private String tableName;
    private String indexType;
    private String distanceType;
    private int dimensions;
    private int maxDocumentBatchSize;
    private boolean initializeSchema;
    private boolean schemaValidation;
}