package io.sx.ai.springai.sample.dynamic.workflow.context;

import io.sx.ai.springai.framework.common.BaseWorkflowContext;
import io.sx.ai.springai.framework.common.NumericInput;
import io.sx.ai.springai.framework.common.NumericOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * Context for managing dynamic workflow state across multiple LLM interactions
 * Extends BaseWorkflowContext for numeric workflows with specific dynamic features
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkflowContext extends BaseWorkflowContext<Double, Double> {
    
    // Dynamic workflow specific parameters
    private double targetRangeMin;
    private double targetRangeMax;
    private int currentIteration;
    private double currentNumber;
    
    public WorkflowContext() {
        super();
    }
    
    public WorkflowContext(String sessionId, String workflowType, 
                          double targetRangeMin, double targetRangeMax,
                          int currentIteration, double currentNumber,
                          BaseWorkflowContext<Double, Double> baseContext) {
        // Call parent constructor with base context properties
        super(
            baseContext != null ? baseContext.getSessionId() : sessionId,
            baseContext != null ? baseContext.getWorkflowType() : workflowType,
            baseContext != null ? baseContext.getStartedAt() : LocalDateTime.now(),
            baseContext != null ? baseContext.getLastUpdatedAt() : LocalDateTime.now(),
            baseContext != null ? baseContext.getInput() : null,
            baseContext != null ? baseContext.getMaxSteps() : 0,
            baseContext != null ? baseContext.getParameters() : new HashMap<>(),
            baseContext != null ? baseContext.getCurrentStep() : 0,
            baseContext != null ? baseContext.getCurrentOutput() : null,
            baseContext != null ? baseContext.getFinalOutput() : null,
            baseContext != null ? baseContext.getStatus() : WorkflowStatus.PENDING,
            baseContext != null ? baseContext.getTerminationReason() : null,
            baseContext != null ? baseContext.getConversationHistory() : new ArrayList<>(),
            baseContext != null ? baseContext.getExecutedSteps() : new ArrayList<>(),
            baseContext != null ? baseContext.getFinalResult() : null,
            baseContext != null ? baseContext.getMetadata() : new HashMap<>(),
            baseContext != null ? baseContext.getProcessingTimeMs() : null,
            baseContext != null ? baseContext.getConfidenceScore() : null,
            baseContext != null ? baseContext.getOrganizationId() : null,
            baseContext != null ? baseContext.getOnBehalfOfId() : null,
            baseContext != null ? baseContext.getTriggerEvent() : null,
            baseContext != null ? baseContext.getTriggerEntityId() : null,
            baseContext != null ? baseContext.getTriggerEntityType() : null
        );
        
        // Set dynamic specific properties
        this.targetRangeMin = targetRangeMin;
        this.targetRangeMax = targetRangeMax;
        this.currentIteration = currentIteration;
        this.currentNumber = currentNumber;
    }
    
    /**
     * Initialize a new dynamic workflow context
     */
    public static WorkflowContext initialize(String sessionId, double inputNumber, int maxIterations) {
        BaseWorkflowContext<Double, Double> baseContext = BaseWorkflowContext.initialize(
            sessionId, 
            NumericInput.of(inputNumber), 
            maxIterations
        );
        baseContext.setWorkflowType("LLM_CONTROLLED_DYNAMIC_WORKFLOW");
        
        return new WorkflowContext(
            sessionId,
            "LLM_CONTROLLED_DYNAMIC_WORKFLOW",
            990.0,
            1000.0,
            0,
            inputNumber,
            baseContext
        );
    }
    
    /**
     * Check if the current number is in the target range
     */
    public boolean isInTargetRange() {
        return currentNumber >= targetRangeMin && currentNumber <= targetRangeMax;
    }
    
    /**
     * Check if max iterations have been reached
     */
    public boolean hasReachedMaxIterations() {
        return currentIteration >= getMaxSteps();
    }
    
    /**
     * Increment iteration and update current number
     */
    public void nextIteration(double newNumber) {
        currentIteration++;
        currentNumber = newNumber;
        setLastUpdatedAt(LocalDateTime.now());
        nextStep(NumericOutput.of(newNumber));
    }
    
    /**
     * Complete the workflow with final result
     */
    public void complete(double finalResult, String reason) {
        setFinalResult(finalResult);
        setTerminationReason(reason);
        setStatus(WorkflowStatus.COMPLETED);
        setLastUpdatedAt(LocalDateTime.now());
        complete(NumericOutput.of(finalResult), reason);
    }
    
    /**
     * Convert to Map for thinking_logs storage
     */
    public java.util.Map<String, Object> toThinkingProcessMap() {
        java.util.Map<String, Object> process = toMap();
        process.put("target_range_min", targetRangeMin);
        process.put("target_range_max", targetRangeMax);
        process.put("current_iteration", currentIteration);
        process.put("current_number", currentNumber);
        process.put("workflow_type", "LLM_CONTROLLED_DYNAMIC_WORKFLOW");
        return process;
    }
    
    /**
     * Create suggestions map for thinking_logs
     */
    public java.util.Map<String, Object> toSuggestionsMap() {
        java.util.Map<String, Object> suggestions = new java.util.HashMap<>();
        suggestions.put("final_result", getFinalResult());
        suggestions.put("termination_reason", getTerminationReason());
        suggestions.put("total_iterations", currentIteration);
        suggestions.put("success", getStatus() == WorkflowStatus.COMPLETED);
        suggestions.put("target_achieved", isInTargetRange());
        return suggestions;
    }

    // Convenience methods for backward compatibility


    public Double getFinalResult() {
        return super.getFinalResult() != null ? super.getFinalResult() : 0.0;
    }
} 