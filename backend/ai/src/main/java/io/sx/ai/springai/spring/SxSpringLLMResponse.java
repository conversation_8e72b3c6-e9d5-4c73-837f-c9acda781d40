package io.sx.ai.springai.spring;

import io.sx.ai.abstraction.SxLLMResponse;
import io.sx.ai.abstraction.SxMessage;
import lombok.Data;
import org.springframework.ai.chat.model.ChatResponse;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Spring AI implementation of SxLLMResponse
 * Wraps Spring AIs ChatResponse class
 */
@Data
public class SxSpringLLMResponse<T> implements SxLLMResponse<T> {
    
    private final ChatResponse springResponse;
    private final Class<T> defaultType;
    private final String content;
    private final List<SxMessage> messages;
    private final Map<String, Object> metadata;
    private final boolean successful;
    private final String error;
    private final String model;
    private final String finishReason;
    private final String id;
    private final Map<String, Object> usage;
    
    public SxSpringLLMResponse(ChatResponse springResponse, Class<T> defaultType) {
        this.springResponse = springResponse;
        this.defaultType = defaultType;
        
        // Extract content from the first result
        if (springResponse.getResult() != null && springResponse.getResult().getOutput() != null) {
            this.content = springResponse.getResult().getOutput().getText();
        } else {
            this.content = "";
        }
        
        // Convert Spring AI messages to SxMessages
        this.messages = new ArrayList<>();
        if (springResponse.getResult() != null && springResponse.getResult().getOutput() != null) {
            this.messages.add(new SxSpringMessage(springResponse.getResult().getOutput()));
        }
        
        this.metadata = new HashMap<>();
        this.successful = springResponse.getResult() != null;
        this.error = successful ? null : "No result in response";
        this.model = "spring-ai-model";
        this.finishReason = springResponse.getResult() != null && springResponse.getResult().getMetadata() != null ? 
            springResponse.getResult().getMetadata().get("finish_reason") != null ? 
                springResponse.getResult().getMetadata().get("finish_reason").toString() : "unknown" : "unknown";
        
        // Generate ID from response content
        this.id = String.valueOf((content + System.currentTimeMillis()).hashCode());
        
        // Extract usage information
        this.usage = new HashMap<>();
        if (springResponse.getMetadata() != null && springResponse.getMetadata().getUsage() != null) {
            var usageInfo = springResponse.getMetadata().getUsage();
            this.usage.put("prompt_tokens", usageInfo.getPromptTokens() != null ? usageInfo.getPromptTokens() : 0L);
            this.usage.put("completion_tokens", usageInfo.getCompletionTokens() != null ? usageInfo.getCompletionTokens() : 0L);
            this.usage.put("total_tokens", usageInfo.getTotalTokens() != null ? usageInfo.getTotalTokens() : 0L);
        }
        
        // Add Spring AI specific metadata
        this.metadata.put("spring_ai_response_type", "chat");
        this.metadata.put("has_result", springResponse.getResult() != null);
    }
    
    public SxSpringLLMResponse(ChatResponse springResponse, Class<T> defaultType, Class<?> outputType) {
        this(springResponse, defaultType);
        // Note: Structured output parsing would be implemented here
    }
    
    public static SxSpringLLMResponse createErrorResponse(String error) {
        return new SxSpringLLMResponse(null, null) {
            @Override
            public String getContent() {
                return "";
            }
            
            @Override
            public boolean isSuccessful() {
                return false;
            }
            
            @Override
            public String getError() {
                return error;
            }
        };
    }
    
    @Override
    public T getOutput() {
        return getStructuredOutput(defaultType);
    }

    @Override
    public <U> U getStructuredOutput(Class<U> outputType) {
        if (springResponse.getResult() != null) {
            try {
                // Spring AI doesn't have built-in structured output parsing
                // Use manual JSON parsing as fallback
                String content = springResponse.getResult().getOutput().getText();
                
                // Extract JSON from markdown code blocks if present
                content = extractJsonFromMarkdown(content);
                
                com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                try {
                    return mapper.readValue(content, outputType);
                } catch (Exception ex) {
                    throw new RuntimeException("Failed to parse structured output", ex);
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to get response content", e);
            }
        }
        throw new IllegalStateException("No result in response");
    }
    
    @Override
    public String getContent() {
        return springResponse.getResult() != null ? 
            springResponse.getResult().getOutput().getText() : "";
    }
    
    @Override
    public List<SxMessage> getMessages() {
        return new ArrayList<>(messages);
    }
    
    @Override
    public String getFirstMessageContent() {
        return messages.isEmpty() ? null : messages.get(0).getContent();
    }
    
    @Override
    public Map<String, Object> getMetadata() {
        return new HashMap<>(metadata);
    }
    
    @Override
    public Map<String, Object> getUsage() {
        return new HashMap<>(usage);
    }
    
    @Override
    public boolean isSuccessful() {
        return successful;
    }
    
    @Override
    public String getError() {
        return error;
    }
    
    @Override
    public String getModel() {
        return model;
    }
    
    @Override
    public String getFinishReason() {
        return finishReason;
    }
    
    @Override
    public String getId() {
        return id;
    }
    
    public long getPromptTokens() {
        Object value = usage.getOrDefault("prompt_tokens", 0L);
        return value instanceof Number ? ((Number) value).longValue() : 0L;
    }
    public long getCompletionTokens() {
        Object value = usage.getOrDefault("completion_tokens", 0L);
        return value instanceof Number ? ((Number) value).longValue() : 0L;
    }
    public long getTotalTokens() {
        Object value = usage.getOrDefault("total_tokens", 0L);
        return value instanceof Number ? ((Number) value).longValue() : 0L;
    }
    
    /**
     * Get the underlying Spring AI ChatResponse
     */
    public ChatResponse getSpringResponse() {
        return springResponse;
    }
    
    /**
     * Extract JSON from markdown code blocks if present
     */
    private String extractJsonFromMarkdown(String content) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }
        
        String trimmed = content.trim();
        
        // Look for JSON code blocks with ```json or ```
        String jsonPattern = "```(?:json)?\\s*\\n([\\s\\S]*?)\\n```";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(jsonPattern);
        java.util.regex.Matcher matcher = pattern.matcher(trimmed);
        
        if (matcher.find()) {
            // Extract the JSON content from the code block
            return matcher.group(1).trim();
        }
        
        // If no code block found, try to find JSON object directly
        // Look for the first { and last } to extract JSON
        int startBrace = trimmed.indexOf('{');
        int endBrace = trimmed.lastIndexOf('}');
        
        if (startBrace != -1 && endBrace != -1 && endBrace > startBrace) {
            return trimmed.substring(startBrace, endBrace + 1).trim();
        }
        
        // If no JSON found, return original content
        return trimmed;
    }
} 