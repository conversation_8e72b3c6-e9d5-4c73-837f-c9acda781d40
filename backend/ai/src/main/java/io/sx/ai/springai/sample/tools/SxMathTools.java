package io.sx.ai.springai.sample.tools;

import io.sx.ai.tools.logic.MathCalculationLogic;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

/**
 * Spring AI wrapper methods for mathematical operations that use plain tool logic classes.
 * Provides @Tool annotations for Spring AI integration.
 */
@Component
public class SxMathTools {
    
    private final MathCalculationLogic mathCalculationLogic = new MathCalculationLogic();
    
    @Tool(description = "Perform mathematical calculations with specified precision")
    public double calculateMath(String expression, int precision) {
        return mathCalculationLogic.evaluateExpression(expression, precision);
    }
    
    @Tool(description = "Calculate the sum of two numbers")
    public double add(double a, double b) {
        return a + b;
    }
    
    @Tool(description = "Calculate the difference between two numbers")
    public double subtract(double a, double b) {
        return a - b;
    }
    
    @Tool(description = "Calculate the product of two numbers")
    public double multiply(double a, double b) {
        return a * b;
    }
    
    @Tool(description = "Calculate the quotient of two numbers")
    public double divide(double a, double b) {
        if (b == 0) {
            throw new IllegalArgumentException("Division by zero is not allowed");
        }
        return a / b;
    }
    
    @Tool(description = "Calculate the power of a number")
    public double power(double base, double exponent) {
        return Math.pow(base, exponent);
    }
    
    @Tool(description = "Calculate the square root of a number")
    public double sqrt(double number) {
        if (number < 0) {
            throw new IllegalArgumentException("Cannot calculate square root of negative number");
        }
        return Math.sqrt(number);
    }
} 