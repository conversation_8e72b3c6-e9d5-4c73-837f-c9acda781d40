package io.sx.ai.springai.sample.dynamic.workflow;

import io.sx.ai.abstraction.PromptResolutionService;
import io.sx.ai.abstraction.ResolvedPrompt;
import io.sx.ai.abstraction.SxLLMClient;
import io.sx.ai.abstraction.SxLLMResponse;
import io.sx.ai.springai.spring.SxSpringLLMClient;
import io.sx.ai.model.ThinkingLog;
import io.sx.ai.repository.ThinkingLogRepository;
import io.sx.ai.springai.framework.common.BaseStepResult;
import io.sx.ai.springai.framework.common.BaseWorkflowContext;
import io.sx.ai.springai.framework.common.BaseWorkflowOrchestrator;
import io.sx.ai.springai.framework.common.BaseWorkflowResult;
import io.sx.ai.springai.framework.common.BaseWorkflowStep;
import io.sx.ai.springai.framework.common.LLMDecision;
import io.sx.ai.springai.framework.common.NumericInput;
import io.sx.ai.springai.framework.common.NumericOutput;
import io.sx.ai.springai.framework.common.WorkflowInput;
import io.sx.ai.springai.framework.common.WorkflowUtils;
import io.sx.ai.springai.framework.xstatic.workflow.WorkflowResult;
import io.sx.ai.springai.framework.xstatic.workflow.WorkflowStepResult;
import io.sx.ai.springai.sample.dynamic.workflow.context.WorkflowContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.memory.InMemoryChatMemoryRepository;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * App initiates workflow
 * LLM provides decisions
 * App controls execution flow and tool calls
 *
 * A simple mathematical workflow with LLM decision-making
 * The core difference from LLMAppHybridWorkflow here is that ChatMemory is used to maintain context
 * https://docs.spring.io/spring-ai/reference/api/chat-memory.html
 */
@Slf4j
@Service
public class LLMAppHybridWorkflowUsingChatMemory implements BaseWorkflowOrchestrator<Double, Double> {
    private final SxLLMClient llmClient;
    private final ThinkingLogRepository thinkingLogRepository;
    private final PromptResolutionService promptResolutionService;
    private final ChatModel chatModel;
    
    // Chat memory repository for maintaining conversation context
    private final ChatMemoryRepository chatMemoryRepository;
    
    // Template paths for externalized prompts
    private static final String SYSTEM_TEMPLATE_PATH = "templates/prompts/sample/llm-decision-system.st";
    private static final String USER_TEMPLATE_PATH = "templates/prompts/sample/llm-decision-user.st";
    
    // Chat memory window size (number of messages to keep in memory)
    private static final int CHAT_MEMORY_WINDOW_SIZE = 10;
    
    @Autowired
    public LLMAppHybridWorkflowUsingChatMemory(SxLLMClient llmClient, 
                                              ThinkingLogRepository thinkingLogRepository,
                                              PromptResolutionService promptResolutionService,
                                              ChatModel chatModel) {
        this.llmClient = llmClient;
        this.thinkingLogRepository = thinkingLogRepository;
        this.promptResolutionService = promptResolutionService;
        this.chatModel = chatModel;
        this.chatMemoryRepository = new InMemoryChatMemoryRepository();
    }
    
    /**
     * Execute the dynamic workflow
     */
    public WorkflowResult execute(double inputNumber, int maxIterations, Long organizationId, Long onBehalfOfId) {
        long startTime = System.currentTimeMillis();
        String sessionId = UUID.randomUUID().toString();
        
        log.info("Starting LLM-controlled workflow: session={}, input={}, maxIterations={}", 
                sessionId, inputNumber, maxIterations);
        
        try {
            // Initialize workflow context
            WorkflowContext context = WorkflowContext.initialize(sessionId, inputNumber, maxIterations);
            context.setOrganizationId(organizationId);
            context.setOnBehalfOfId(onBehalfOfId);
            context.setTriggerEvent("DYNAMIC_WORKFLOW_EXECUTION");
            context.setTriggerEntityType("WORKFLOW");
            context.setTriggerEntityId(0L); // No specific entity for this workflow
            
            // Create initial thinking log
            ThinkingLog thinkingLog = createInitialThinkingLog(context);
            
            // Execute workflow loop
            WorkflowResult result = executeWorkflowLoop(context, thinkingLog);
            
            // Calculate processing time
            long processingTime = System.currentTimeMillis() - startTime;
            context.setProcessingTimeMs(processingTime);
            
            // Complete thinking log
            completeThinkingLog(thinkingLog, context, result);
            
            // Clean up chat memory for this session
            cleanupChatMemory(sessionId);
            
            log.info("Workflow completed: session={}, result={}, iterations={}, time={}ms", 
                    sessionId, result.getFinalResult(), context.getCurrentIteration(), processingTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("Error in workflow execution: session={}", sessionId, e);
            throw new RuntimeException("Workflow execution failed", e);
        }
    }
    
    /**
     * Execute the main workflow loop
     */
    private WorkflowResult executeWorkflowLoop(WorkflowContext context, ThinkingLog thinkingLog) {
        try {
            // Initialize chat memory for this session
            ChatMemory chatMemory = getOrCreateChatMemory(context.getSessionId());
            
            // Load templates dynamically
            String systemTemplate = loadTemplate(SYSTEM_TEMPLATE_PATH);
            String userTemplate = loadTemplate(USER_TEMPLATE_PATH);
            
            // Add system message to chat memory if this is the first iteration
            if (context.getCurrentIteration() == 0) {
                Message systemMessage = new SystemMessage(systemTemplate);
                chatMemory.add(context.getSessionId(), systemMessage);
                log.debug("Added system message to chat memory for session: {}", context.getSessionId());
            }
            
            do {
                // Update context status
                context.setStatus(WorkflowContext.WorkflowStatus.ACTIVE);
                
                // Create user prompt for current iteration
                Map<String, Object> promptParams = Map.of(
                    "inputNumber", context.getCurrentNumber(),
                    "currentIteration", context.getCurrentIteration(),
                    "maxIterations", context.getMaxSteps()
                );
                
                // Create user message using template
                PromptTemplate template = new PromptTemplate(userTemplate);
                Prompt userPromptPrompt = template.create(promptParams);
                String userPromptText = userPromptPrompt.toString();
                
                // Create user message and add to chat memory
                Message userMessage = new UserMessage(userPromptText);
                chatMemory.add(context.getSessionId(), userMessage);
                
                // Calculate step number for this iteration (should be sequential: 1, 2, 3, ...)
                int stepNumber = context.getCurrentIteration() + 1;
                
                // Create workflow step with proper naming
                String stepName = "iteration-" + stepNumber;
                WorkflowStepResult step = WorkflowStepResult.create(
                    stepName,
                    stepNumber, // Use sequential step number
                    WorkflowStepResult.StepType.LLM_ANALYSIS
                );
                step.setInputNumber(context.getCurrentNumber());
                step.setLlmPrompt(userPromptText);
                
                // Execute LLM with chat memory
                LLMDecision decision = executeLLMWithChatMemory(context, chatMemory, step);
                
                // ALWAYS execute range_checker first for deterministic decision making
                // method modifies step :)
                executeToolsSequentially(context, new String[]{"range_checker"}, step);
                
                // Get the range checker result from the step
                Map<String, Object> toolResults = step.getToolResults();
                if (toolResults != null && toolResults.containsKey("range_checker")) {
                    Map<String, Object> rangeResult = (Map<String, Object>) toolResults.get("range_checker");
                    String position = (String) rangeResult.get("position");
                    
                    log.info("Range checker result: {} for input {}", position, context.getCurrentNumber());
                    
                    // Make decision based on range checker result
                    if ("WITHIN".equals(position)) {
                        // Terminate if within range
                        context.complete(context.getCurrentNumber(), "Target range reached");
                        step.setOutcome(context.getCurrentNumber(), "Terminate - within target range", true); // Success: target achieved
                        step.setStepType(BaseWorkflowStep.StepType.TERMINATION);
                    } else {
                        // Continue with appropriate mathematical tool based on position
                        String[] nextTools = "BELOW".equals(position) ? 
                            new String[]{"quadruple_number"} : new String[]{"halve_number"};
                        
                        log.info("Executing {} for position {}", nextTools[0], position);
                        
                        // Execute the mathematical tool
                        double mathResult = executeToolsSequentially(context, nextTools, step);
                        context.nextIteration(mathResult);
                        step.setOutcome(mathResult, "Continue with " + nextTools[0], true); // Success: tool executed successfully
                    }
                } else {
                    // Fallback: use LLM decision if range checker fails
                    log.warn("Range checker failed, falling back to LLM decision");
                    if (decision.isContinue()) {
                        double result = executeToolsSequentially(context, decision.getToolsToExecute(), step);
                        context.nextIteration(result);
                        step.setOutcome(result, "Continue to next iteration", true); // Success: tool executed successfully
                    } else {
                        context.complete(decision.getFinalResult(), decision.getTerminationReason());
                        step.setOutcome(decision.getFinalResult(), "Terminate workflow", true); // Success: workflow completed
                        step.setStepType(BaseWorkflowStep.StepType.TERMINATION);
                    }
                }
                
                // Add step to context and update thinking log
                context.addStep(step);
                updateThinkingLog(thinkingLog, context);
                
                log.info("Iteration {} completed: decision={}, result={}", 
                    context.getCurrentIteration(), decision.getDescription(), context.getCurrentNumber());
                
            } while (shouldContinue(context));
            
            // Create final result
            return WorkflowResult.workflowResultBuilder()
                    .sessionId(context.getSessionId())
                    .originalInput(NumericInput.of(context.getInputValue()))
                    .finalOutput(NumericOutput.of(context.getFinalResult()))
                    .totalIterations(context.getCurrentIteration())
                    .status(context.getTerminationReason())
                    .executedSteps((List<WorkflowStepResult>) (List<?>) context.getExecutedSteps())
                    .processingTimeMs(context.getProcessingTimeMs())
                    .success(context.getStatus() == WorkflowContext.WorkflowStatus.COMPLETED)
                    .error(context.getTerminationReason())
                    .workflowType("LLM_CONTROLLED_DYNAMIC_WORKFLOW")
                    .build();
                    
        } catch (IOException e) {
            log.error("Error loading templates: {}", e.getMessage(), e);
            throw new RuntimeException("Template loading failed", e);
        }
    }
    
    /**
     * Get or create chat memory for a session
     */
    private ChatMemory getOrCreateChatMemory(String sessionId) {
        return MessageWindowChatMemory.builder()
            .chatMemoryRepository(chatMemoryRepository)
            .maxMessages(CHAT_MEMORY_WINDOW_SIZE)
            .build();
    }
    
    /**
     * Clean up chat memory for a session
     */
    private void cleanupChatMemory(String sessionId) {
        // ChatMemoryRepository doesn't have a delete method, so we'll just log
        log.debug("Chat memory cleanup requested for session: {}", sessionId);
    }
    
    /**
     * Execute LLM with chat memory using Spring AI ChatModel
     */
    private LLMDecision executeLLMWithChatMemory(WorkflowContext context, ChatMemory chatMemory, WorkflowStepResult step) {
        try {
            // Get messages from chat memory (ChatMemory interface doesn't have getMessages(String))
            List<Message> messages = chatMemory.get(context.getSessionId());
            
            log.debug("Chat memory contains {} messages for session: {}", 
                messages.size(), context.getSessionId());
            
            // Generate response using ChatModel
            Prompt prompt = new Prompt(messages);
            ChatResponse response = chatModel.call(prompt);
            AssistantMessage assistantMessage = response.getResult().getOutput();
            
            // Add assistant message to chat memory using session ID as conversation ID
            chatMemory.add(context.getSessionId(), assistantMessage);
            
            // Extract response content
            String responseContent = assistantMessage.getText();
            log.debug("LLM response: {}", responseContent);
            
            // Parse the response into LLMDecision
            LLMDecision decision = parseLLMResponse(responseContent, step);
            
            return decision;
            
        } catch (Exception e) {
            log.error("Error in LLM execution with chat memory: {}", e.getMessage(), e);
            throw new RuntimeException("LLM execution with chat memory failed", e);
        }
    }
    
    /**
     * Load template from classpath resources
     */
    private String loadTemplate(String templatePath) throws IOException {
        return WorkflowUtils.loadTemplate(templatePath);
    }
    
    /**
     * Parse LLM response to extract decision
     */
    private LLMDecision parseLLMResponse(String response, WorkflowStepResult step) {
        // For now, use simple parsing. In production, use structured output
        String lowerResponse = response.toLowerCase();
        
        // Check for explicit decision keywords
        boolean shouldTerminate = lowerResponse.contains("terminate") && !lowerResponse.contains("not terminate") && !lowerResponse.contains("should not terminate");
        boolean shouldContinue = lowerResponse.contains("continue") && !lowerResponse.contains("not continue");
        
        // If neither explicit keyword is found, fall back to context-based detection
        if (!shouldTerminate && !shouldContinue) {
            shouldTerminate = (lowerResponse.contains("stop") && !lowerResponse.contains("not stop")) ||
                             lowerResponse.contains("final result") ||
                             lowerResponse.contains("target range reached") ||
                             lowerResponse.contains("max iterations reached");
        }
        
        if (shouldTerminate) {
            // Extract final result from response
            double finalResult = extractNumberFromResponse(response);
            String reason = extractReasonFromResponse(response);
            
            step.setLLMAnalysis("TERMINATE", response, 0.9);
            
            return LLMDecision.terminate(response, 0.9, finalResult, reason);
            
        } else {
            // Continue with tools - determine which tool to use
            String[] toolsToExecute = determineToolsToExecute(response);
            step.setLLMAnalysis("CONTINUE", response, 0.8);
            
            return LLMDecision.continueWithTools(response, 0.8, toolsToExecute);
        }
    }
    
    /**
     * Execute tools sequentially (simplified without function calling)
     */
    private double executeToolsSequentially(WorkflowContext context, String[] tools, WorkflowStepResult step) {
        long toolStartTime = System.currentTimeMillis();
        Map<String, Object> toolResults = new HashMap<>();
        double currentNumber = context.getCurrentNumber();
        
        for (String toolName : tools) {
            try {
                Map<String, Object> resultMap = new HashMap<>();
                
                // Execute tool logic directly
                if ("halve_number".equals(toolName)) {
                    double result = currentNumber / 2.0;
                    resultMap.put("input", currentNumber);
                    resultMap.put("operation", "halve");
                    resultMap.put("result", result);
                    resultMap.put("success", true);
                    currentNumber = result;
                    
                } else if ("quadruple_number".equals(toolName)) {
                    double result = currentNumber * 4.0;
                    resultMap.put("input", currentNumber);
                    resultMap.put("operation", "quadruple");
                    resultMap.put("result", result);
                    resultMap.put("success", true);
                    currentNumber = result;
                    
                } else if ("range_checker".equals(toolName)) {
                    // Execute range checker tool
                    Map<String, Object> rangeResult = executeRangeChecker(currentNumber, 990.0, 1000.0);
                    resultMap.putAll(rangeResult);
                    resultMap.put("success", true);
                    // Don't update currentNumber for range checker
                }
                
                toolResults.put(toolName, resultMap);
                log.info("Tool {} executed: input={}, output={}", toolName, context.getCurrentNumber(), 
                    "range_checker".equals(toolName) ? resultMap.get("position") : currentNumber);
                
            } catch (Exception e) {
                log.error("Error executing tool {}: {}", toolName, e.getMessage(), e);
                toolResults.put(toolName, Map.of("error", e.getMessage(), "success", false));
            }
        }
        
        long toolExecutionTime = System.currentTimeMillis() - toolStartTime;
        step.setToolResults(Arrays.asList(tools), toolResults, toolExecutionTime);
        
        return currentNumber;
    }
    
    /**
     * Execute range checker tool
     */
    private Map<String, Object> executeRangeChecker(double number, double min, double max) {
        String position;
        String reasoning;
        
        if (number < min) {
            position = "BELOW";
            reasoning = String.format("Number %.1f is below the minimum value %.1f", number, min);
        } else if (number > max) {
            position = "ABOVE";
            reasoning = String.format("Number %.1f is above the maximum value %.1f", number, max);
        } else {
            position = "WITHIN";
            reasoning = String.format("Number %.1f is within the range [%.1f, %.1f]", number, min, max);
        }
        
        return Map.of(
            "position", position,
            "number", number,
            "min", min,
            "max", max,
            "reasoning", reasoning,
            "inRange", position.equals("WITHIN")
        );
    }
    
    /**
     * Check if workflow should continue
     */
    private boolean shouldContinue(WorkflowContext context) {
        // Check termination conditions
        if (context.isInTargetRange()) {
            context.complete(context.getCurrentNumber(), "Target range reached");
            return false;
        }
        
        if (context.hasReachedMaxIterations()) {
            context.complete(-1.0, "Max iterations reached");
            return false;
        }
        
        return context.getStatus() == WorkflowContext.WorkflowStatus.ACTIVE;
    }
    
    /**
     * Create initial thinking log
     */
    private ThinkingLog createInitialThinkingLog(WorkflowContext context) {
        ThinkingLog thinkingLog = ThinkingLog.builder()
                .sessionId(context.getSessionId())
                .triggerEvent(context.getTriggerEvent())
                .triggerEntityId(context.getTriggerEntityId())
                .triggerEntityType(context.getTriggerEntityType())
                .thinkingStrategy("LLM_CONTROLLED_DYNAMIC_WORKFLOW")
                .sessionStatus("ACTIVE")
                .organizationId(context.getOrganizationId())
                .onBehalfOfId(context.getOnBehalfOfId())
                .thinkingProcess(context.toThinkingProcessMap())
                .build();
        
        return thinkingLogRepository.save(thinkingLog);
    }
    
    /**
     * Update thinking log during execution
     */
    private void updateThinkingLog(ThinkingLog thinkingLog, WorkflowContext context) {
        thinkingLog.setThinkingProcess(context.toThinkingProcessMap());
        thinkingLogRepository.save(thinkingLog);
    }
    
    /**
     * Complete thinking log
     */
    private void completeThinkingLog(ThinkingLog thinkingLog, WorkflowContext context, WorkflowResult result) {
        thinkingLog.setSessionStatus("COMPLETED");
        thinkingLog.setThinkingProcess(context.toThinkingProcessMap());
        thinkingLog.setSuggestions(context.toSuggestionsMap());
        if (context.getConfidenceScore() != null) {
            thinkingLog.setConfidenceScore(new java.math.BigDecimal(context.getConfidenceScore()));
        }
        thinkingLog.setProcessingTimeMs(context.getProcessingTimeMs());
        
        thinkingLogRepository.save(thinkingLog);
    }
    
    /**
     * Extract number from response (simple implementation)
     */
    private double extractNumberFromResponse(String response) {
        // Simple regex to extract numbers
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("-?\\d+(\\.\\d+)?");
        java.util.regex.Matcher matcher = pattern.matcher(response);
        if (matcher.find()) {
            return Double.parseDouble(matcher.group());
        }
        return 0.0;
    }
    
    /**
     * Extract reason from response (simple implementation)
     */
    private String extractReasonFromResponse(String response) {
        if (response.toLowerCase().contains("target range")) {
            return "Target range reached";
        } else if (response.toLowerCase().contains("max iteration")) {
            return "Max iterations reached";
        }
        return "LLM decision";
    }
    
    /**
     * Determine which tools to execute based on LLM response
     */
    private String[] determineToolsToExecute(String response) {
        String lowerResponse = response.toLowerCase();
        
        // Check for specific tool mentions
        if (lowerResponse.contains("range_checker") || lowerResponse.contains("check range") || lowerResponse.contains("range check")) {
            return new String[]{"range_checker"};
        } else if (lowerResponse.contains("halve") || lowerResponse.contains("divide") || lowerResponse.contains("half")) {
            return new String[]{"halve_number"};
        } else if (lowerResponse.contains("quadruple") || lowerResponse.contains("multiply") || lowerResponse.contains("4")) {
            return new String[]{"quadruple_number"};
        } else {
            // Default: try range checker first, then mathematical tools
            return new String[]{"range_checker"};
        }
    }
    
    // BaseWorkflowOrchestrator interface implementations
    
    @Override
    public BaseWorkflowResult<Double, Double> executeWorkflow(WorkflowInput<Double> input, String sessionId, Long organizationId, Long onBehalfOfId) {
        try {
            // Convert WorkflowInput to double and execute
            double inputNumber = input.getValue();
            WorkflowResult result = execute(inputNumber, 4, organizationId, onBehalfOfId);
            
            // Convert WorkflowResult to BaseWorkflowResult
            List<BaseStepResult<Double, Double>> baseSteps = new ArrayList<>();
            for (BaseStepResult<Double, Double> step : result.getSteps()) {
                // Convert WorkflowStepResult to BaseStepResult
                WorkflowStepResult workflowStep = (WorkflowStepResult) step;
                BaseStepResult<Double, Double> baseStep = BaseStepResult.success(
                    workflowStep.getIteration(),
                    workflowStep.getStepId(),
                    workflowStep.getInputNumber(),
                    workflowStep.getOutputNumber(),
                    workflowStep.getLlmDecision()
                );
                baseSteps.add(baseStep);
            }
            
            return BaseWorkflowResult.success(
                input,
                result.getFinalOutput(),
                baseSteps,
                sessionId,
                "LLM_CONTROLLED_DYNAMIC_WORKFLOW"
            );
            
        } catch (Exception e) {
            log.error("Error executing workflow: {}", e.getMessage(), e);
            return BaseWorkflowResult.error(input, "Workflow execution failed: " + e.getMessage());
        }
    }
    
    @Override
    public BaseWorkflowResult<Double, Double> executeWorkflowFromEvent(Map<String, Object> eventData, String sessionId, Long organizationId, Long onBehalfOfId) {
        try {
            // Extract input from event data
            Double inputNumber = WorkflowUtils.extractNumericInputFromEvent(eventData);
            if (inputNumber == null) {
                return BaseWorkflowResult.error("No numeric input found in event data");
            }
            
            // Execute workflow
            WorkflowInput<Double> input = NumericInput.of(inputNumber);
            return executeWorkflow(input, sessionId, organizationId, onBehalfOfId);
            
        } catch (Exception e) {
            log.error("Error executing workflow from event: {}", e.getMessage(), e);
            return BaseWorkflowResult.error("Workflow execution from event failed: " + e.getMessage());
        }
    }
    
    @Override
    public BaseStepResult<Double, Double> executeWorkflowStep(String sessionId, String stepId, Map<String, Object> context, Long organizationId, Long onBehalfOfId) {
        // For dynamic workflow, we don't support individual step execution
        // This method is not applicable for LLM-controlled workflows
        return BaseStepResult.error(0, stepId, 0.0, "Individual step execution not supported for LLM-controlled workflows");
    }
    
    @Override
    public BaseWorkflowContext<Double, Double> getWorkflowStatus(String sessionId) {
        // For dynamic workflow, we don't maintain persistent state between calls
        NumericInput unknownInput = NumericInput.of(0.0);
        return BaseWorkflowContext.initialize(sessionId, unknownInput, 4);
    }
    
    @Override
    public Map<String, String> getAvailableWorkflowTypes() {
        return Map.of(
            "LLM_CONTROLLED_DYNAMIC", "LLM-controlled dynamic workflow with mathematical tools"
        );
    }
    
    @Override
    public boolean validateWorkflowParameters(String workflowType, Map<String, Object> parameters) {
        if (!"LLM_CONTROLLED_DYNAMIC".equals(workflowType)) {
            return false;
        }
        
        Object input = parameters.get("input");
        if (input == null) {
            return false;
        }
        
        try {
            Double.parseDouble(input.toString());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    @Override
    public Map<String, Object> getWorkflowStats(Long organizationId) {
        return Map.of(
            "workflow_type", "LLM_CONTROLLED_DYNAMIC",
            "total_executions", 0, // Would be tracked in a real implementation
            "success_rate", 1.0,
            "average_execution_time_ms", 2000,
            "max_iterations", 4,
            "target_range", "990-1000"
        );
    }
    
    @Override
    public boolean cancelWorkflow(String sessionId, String reason) {
        // Dynamic workflow executes quickly and doesn't support cancellation
        return false;
    }
    
    @Override
    public List<BaseWorkflowResult<Double, Double>> getWorkflowHistory(Long organizationId, int limit) {
        // Would return actual history in a real implementation
        return new ArrayList<>();
    }
} 