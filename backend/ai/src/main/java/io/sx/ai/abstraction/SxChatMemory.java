package io.sx.ai.abstraction;

import java.util.List;
import java.util.Map;

/**
 * Framework-agnostic chat memory interface
 * Abstracts chat memory operations across different AI frameworks
 */
public interface SxChatMemory {
    
    /**
     * Add a message to the chat memory
     */
    void addMessage(SxMessage message);
    
    /**
     * Get all messages in the chat memory
     */
    List<SxMessage> getMessages();
    
    /**
     * Get messages up to a maximum count
     */
    List<SxMessage> getMessages(int maxCount);
    
    /**
     * Clear all messages from the chat memory
     */
    void clear();
    
    /**
     * Get the number of messages in the chat memory
     */
    int getMessageCount();
    
    /**
     * Check if the chat memory is empty
     */
    boolean isEmpty();
    
    /**
     * Get the maximum number of messages this memory can hold
     */
    int getMaxMessages();
    
    /**
     * Get metadata associated with this chat memory
     */
    Map<String, Object> getMetadata();
    
    /**
     * Get the session ID for this chat memory
     */
    String getSessionId();
    
    /**
     * Set the session ID for this chat memory
     */
    void setSessionId(String sessionId);
    
    /**
     * Get the underlying framework-specific memory object
     * This allows access to framework-specific features when needed
     */
    Object getFrameworkMemory();
} 