package io.sx.ai.abstraction;

import java.util.List;
import java.util.Map;

/**
 * Framework-agnostic document interface
 * Abstracts document representation across different AI frameworks
 */
public interface SxDocument {
    
    /**
     * Get the document ID
     */
    String getId();
    
    /**
     * Get the document content
     */
    String getContent();
    
    /**
     * Get the document embeddings
     */
    List<Double> getEmbeddings();
    
    /**
     * Get the document metadata
     */
    Map<String, Object> getMetadata();
    
    /**
     * Get the document score (for search results)
     */
    Double getScore();
    
    /**
     * Set the document score
     */
    void setScore(Double score);
    
    /**
     * Get the document type
     */
    String getType();
    
    /**
     * Get the document source
     */
    String getSource();
    
    /**
     * Get the document creation timestamp
     */
    Long getCreatedAt();
    
    /**
     * Get the document last modified timestamp
     */
    Long getModifiedAt();
    
    /**
     * Check if the document has embeddings
     */
    boolean hasEmbeddings();
    
    /**
     * Get a specific metadata value
     */
    Object getMetadataValue(String key);
    
    /**
     * Set a metadata value
     */
    void setMetadataValue(String key, Object value);
} 