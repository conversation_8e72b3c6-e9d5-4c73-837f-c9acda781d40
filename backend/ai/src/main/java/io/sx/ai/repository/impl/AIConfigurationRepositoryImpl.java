package io.sx.ai.repository.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sx.ai.model.AIConfiguration;
import io.sx.ai.repository.AIConfigurationRepository;
import io.sx.repository.impl.BaseRepositoryImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

/**
 * JDBC implementation of AI configuration repository
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AIConfigurationRepositoryImpl extends BaseRepositoryImpl<AIConfiguration> 
    implements AIConfigurationRepository {
    
    private final JdbcTemplate jdbcTemplate;
    private final ObjectMapper objectMapper;
    
    @Override
    public AIConfiguration findByTypeAndOrganization(Long organizationId, String configKey) {
        log.info("Finding AI configuration by org: {}, configKey: {}", organizationId, configKey);
        String sql = """
            SELECT id, organization_id, config_key, config_value, configuration_type, support_organization_id, configuration_data, is_active,
                   created_at, updated_at, created_by_id, updated_by_id, created_by, on_behalf_of_id
            FROM ai.configurations 
            WHERE organization_id = ? AND config_key = ? AND is_active = true
            """;
        try {
            List<AIConfiguration> results = jdbcTemplate.query(sql, new AIConfigurationRowMapper(), organizationId, configKey);
            log.info("Query returned {} results for org: {}, configKey: {}", results.size(), organizationId, configKey);
            if (!results.isEmpty()) {
                log.info("Found existing configuration with ID: {}", results.get(0).getId());
            }
            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error finding AI configuration by org and configKey", e);
            return null;
        }
    }
    
    @Override
    public List<AIConfiguration> findByOrganization(Long supportOrganizationId) {
        log.debug("Finding AI configurations by organization: {}", supportOrganizationId);
        
        String sql = """
            SELECT id, organization_id, config_key, config_value, configuration_type, support_organization_id, configuration_data, is_active,
                   created_at, updated_at, created_by_id, updated_by_id, created_by, on_behalf_of_id
            FROM ai.configurations 
            WHERE support_organization_id = ? AND is_active = true
            ORDER BY configuration_type
            """;
        
        try {
            return jdbcTemplate.query(sql, new AIConfigurationRowMapper(), supportOrganizationId);
        } catch (Exception e) {
            log.error("Error finding AI configurations by organization", e);
            return List.of();
        }
    }
    
    @Override
    public Optional<AIConfiguration> findById(Long id) {
        log.debug("Finding AI configuration by ID: {}", id);
        
        String sql = """
            SELECT id, organization_id, config_key, config_value, configuration_type, support_organization_id, configuration_data, is_active,
                   created_at, updated_at, created_by_id, updated_by_id, created_by, on_behalf_of_id
            FROM ai.configurations 
            WHERE id = ?
            """;
        
        try {
            List<AIConfiguration> results = jdbcTemplate.query(sql, new AIConfigurationRowMapper(), id);
            return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
        } catch (Exception e) {
            log.error("Error finding AI configuration by ID", e);
            return Optional.empty();
        }
    }
    
    @Override
    public AIConfiguration save(AIConfiguration configuration) {
        log.debug("Saving AI configuration: {}", configuration.getConfigurationType());
        
        if (configuration.getId() == null) {
            return insert(configuration);
        } else {
            update(configuration);
            return configuration;
        }
    }
    
    private AIConfiguration insert(AIConfiguration configuration) {
        String sql = """
            INSERT INTO ai.configurations (
                organization_id, config_key, config_value, configuration_type, support_organization_id, configuration_data, is_active,
                created_at, updated_at, created_by_id, updated_by_id, created_by, on_behalf_of_id
            ) VALUES (?, ?, ?::jsonb, ?, ?, ?::jsonb, ?, NOW(), NOW(), ?, ?, ?, ?)
            RETURNING id, organization_id, config_key, config_value, configuration_type, support_organization_id, configuration_data, is_active,
                      created_at, updated_at, created_by_id, updated_by_id, created_by, on_behalf_of_id
            """;

        try {
            log.info("Inserting AI configuration with organizationId: {}, configurationType: {}, supportOrganizationId: {}", 
                configuration.getOrganizationId(), configuration.getConfigurationType(), configuration.getSupportOrganizationId());
            return jdbcTemplate.queryForObject(sql, new AIConfigurationRowMapper(),
                configuration.getOrganizationId(),
                configuration.getConfigurationType(), // Use configurationType as config_key
                objectMapper.writeValueAsString(configuration.getConfigurationData()), // Use configuration_data as config_value
                configuration.getConfigurationType(),
                configuration.getSupportOrganizationId(),
                objectMapper.writeValueAsString(configuration.getConfigurationData()),
                configuration.getIsActive(),
                configuration.getCreatedById(),
                configuration.getUpdatedById(),
                configuration.getCreatedBy(),
                configuration.getOnBehalfOfId()
            );
        } catch (Exception e) {
            log.error("Error inserting AI configuration", e);
            throw new RuntimeException("Failed to insert AI configuration", e);
        }
    }
    
    @Override
    public Long update(AIConfiguration configuration) {
        String sql = """
            UPDATE ai.configurations SET
                configuration_type = ?, support_organization_id = ?, configuration_data = ?::jsonb, is_active = ?,
                updated_at = NOW(), updated_by_id = ?, on_behalf_of_id = ?
            WHERE id = ?
            """;
        
        try {
            int rowsAffected = jdbcTemplate.update(sql,
                configuration.getConfigurationType(),
                configuration.getSupportOrganizationId(),
                objectMapper.writeValueAsString(configuration.getConfigurationData()),
                configuration.getIsActive(),
                configuration.getUpdatedById(),
                configuration.getOnBehalfOfId(),
                configuration.getId()
            );
            
            if (rowsAffected > 0) {
                return configuration.getId();
            } else {
                throw new RuntimeException("No rows affected when updating AI configuration");
            }
        } catch (Exception e) {
            log.error("Error updating AI configuration", e);
            throw new RuntimeException("Failed to update AI configuration", e);
        }
    }
    
    @Override
    public List<AIConfiguration> findAll(Long organizationId) {
        log.debug("Finding AI configurations by organization: {}", organizationId);
        
        String sql = """
            SELECT id, organization_id, configuration_type, support_organization_id, configuration_data, is_active,
                   created_at, updated_at, created_by_id, updated_by_id, created_by, on_behalf_of_id
            FROM ai.configurations 
            WHERE support_organization_id = ? AND is_active = true
            ORDER BY configuration_type
            """;
        
        try {
            return jdbcTemplate.query(sql, new AIConfigurationRowMapper(), organizationId);
        } catch (Exception e) {
            log.error("Error finding AI configurations by organization", e);
            return List.of();
        }
    }
    
    @Override
    public void deleteById(Long id) {
        log.debug("Deleting AI configuration: {}", id);
        
        String sql = "DELETE FROM ai.configurations WHERE id = ?";
        
        try {
            jdbcTemplate.update(sql, id);
        } catch (Exception e) {
            log.error("Error deleting AI configuration", e);
            throw new RuntimeException("Failed to delete AI configuration", e);
        }
    }
    
    /**
     * Row mapper for AI configuration
     */
    private class AIConfigurationRowMapper implements RowMapper<AIConfiguration> {
        
        @Override
        public AIConfiguration mapRow(ResultSet rs, int rowNum) throws SQLException {
            try {
                JsonNode configurationData = objectMapper.readTree(rs.getString("configuration_data"));
                
                return AIConfiguration.builder()
                    .id(rs.getLong("id"))
                    .organizationId(rs.getObject("organization_id", Long.class))
                    .configurationType(rs.getString("configuration_type"))
                    .supportOrganizationId(rs.getObject("support_organization_id", Long.class))
                    .configurationData(configurationData)
                    .isActive(rs.getBoolean("is_active"))
                    .createdAt(rs.getTimestamp("created_at").toLocalDateTime())
                    .updatedAt(rs.getTimestamp("updated_at").toLocalDateTime())
                    .createdById(rs.getObject("created_by_id", Long.class))
                    .updatedById(rs.getObject("updated_by_id", Long.class))
                    .createdBy(rs.getString("created_by"))
                    .onBehalfOfId(rs.getObject("on_behalf_of_id", Long.class))
                    .build();
            } catch (Exception e) {
                log.error("Error mapping AI configuration row", e);
                throw new SQLException("Failed to map AI configuration row", e);
            }
        }
    }
} 