package io.sx.ai.repository.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sx.ai.model.ToolExecution;
import io.sx.ai.repository.ToolExecutionRepository;
import io.sx.repository.impl.BaseRepositoryImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * JDBC implementation of ToolExecutionRepository
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ToolExecutionRepositoryImpl extends BaseRepositoryImpl<ToolExecution> implements ToolExecutionRepository {
    
    private final JdbcTemplate jdbcTemplate;
    private final ObjectMapper objectMapper;
    
    @Override
    public List<ToolExecution> findBySuggestionId(Long suggestionId) {
        String sql = """
            SELECT * FROM ai.tool_executions 
            WHERE suggestion_id = ? 
            ORDER BY attempt_number ASC
            """;
        return jdbcTemplate.query(sql, new ToolExecutionRowMapper(), suggestionId);
    }
    
    @Override
    public List<ToolExecution> findBySuggestionIdAndToolName(Long suggestionId, String toolName) {
        String sql = """
            SELECT * FROM ai.tool_executions 
            WHERE suggestion_id = ? AND tool_name = ? 
            ORDER BY attempt_number ASC
            """;
        return jdbcTemplate.query(sql, new ToolExecutionRowMapper(), suggestionId, toolName);
    }
    
    @Override
    public Optional<ToolExecution> findLatestBySuggestionIdAndToolName(Long suggestionId, String toolName) {
        String sql = """
            SELECT * FROM ai.tool_executions 
            WHERE suggestion_id = ? AND tool_name = ? 
            ORDER BY attempt_number DESC 
            LIMIT 1
            """;
        List<ToolExecution> results = jdbcTemplate.query(sql, new ToolExecutionRowMapper(), suggestionId, toolName);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }
    
    @Override
    public List<ToolExecution> findByExecutionStatus(ToolExecution.ExecutionStatus status) {
        String sql = """
            SELECT * FROM ai.tool_executions 
            WHERE execution_status = ? 
            ORDER BY execution_timestamp ASC
            """;
        return jdbcTemplate.query(sql, new ToolExecutionRowMapper(), status.name());
    }
    
    @Override
    public List<ToolExecution> findByScheduledAtBefore(Instant before) {
        String sql = """
            SELECT * FROM ai.tool_executions 
            WHERE scheduled_at IS NOT NULL AND scheduled_at <= ? 
            ORDER BY scheduled_at ASC
            """;
        return jdbcTemplate.query(sql, new ToolExecutionRowMapper(), before);
    }
    
    @Override
    public List<ToolExecution> findReadyForRetry(Instant now) {
        String sql = """
            SELECT * FROM ai.tool_executions 
            WHERE next_attempt_at IS NOT NULL AND next_attempt_at <= ? 
            ORDER BY next_attempt_at ASC
            """;
        return jdbcTemplate.query(sql, new ToolExecutionRowMapper(), now);
    }
    
    @Override
    public long countSuccessfulBySuggestionId(Long suggestionId) {
        String sql = "SELECT COUNT(*) FROM ai.tool_executions WHERE suggestion_id = ? AND success = true";
        return jdbcTemplate.queryForObject(sql, Long.class, suggestionId);
    }
    
    @Override
    public long countFailedBySuggestionId(Long suggestionId) {
        String sql = "SELECT COUNT(*) FROM ai.tool_executions WHERE suggestion_id = ? AND success = false";
        return jdbcTemplate.queryForObject(sql, Long.class, suggestionId);
    }
    
    @Override
    public ToolExecution save(ToolExecution toolExecution) {
        if (toolExecution.getId() == null) {
            return insert(toolExecution);
        } else {
            update(toolExecution);
            return toolExecution;
        }
    }
    
    private ToolExecution insert(ToolExecution toolExecution) {
        String sql = """
            INSERT INTO ai.tool_executions (
                session_id, suggestion_id, tool_name, parameters, execution_timestamp,
                execution_time_ms, success, result, error_message, confidence_score,
                attempt_number, max_attempts, execution_status, next_attempt_at,
                failure_reason, tool_config_hash, depends_on_tool_execution_id,
                execution_order, rollback_required, rollback_executed, rollback_result,
                scheduled_at, execution_priority, execution_context, state_snapshot,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING *
            """;
        
        try {
            return jdbcTemplate.queryForObject(sql, new ToolExecutionRowMapper(),
                toolExecution.getSessionId(),
                toolExecution.getSuggestionId(),
                toolExecution.getToolName(),
                objectMapper.writeValueAsString(toolExecution.getParameters()),
                toolExecution.getExecutionTimestamp(),
                toolExecution.getExecutionTimeMs(),
                toolExecution.getSuccess(),
                toolExecution.getResult() != null ? objectMapper.writeValueAsString(toolExecution.getResult()) : null,
                toolExecution.getErrorMessage(),
                toolExecution.getConfidenceScore(),
                toolExecution.getAttemptNumber(),
                toolExecution.getMaxAttempts(),
                toolExecution.getExecutionStatus().name(),
                toolExecution.getNextAttemptAt(),
                toolExecution.getFailureReason(),
                toolExecution.getToolConfigHash(),
                toolExecution.getDependsOnToolExecutionId(),
                toolExecution.getExecutionOrder(),
                toolExecution.getRollbackRequired(),
                toolExecution.getRollbackExecuted(),
                toolExecution.getRollbackResult() != null ? objectMapper.writeValueAsString(toolExecution.getRollbackResult()) : null,
                toolExecution.getScheduledAt(),
                toolExecution.getExecutionPriority(),
                toolExecution.getExecutionContext() != null ? objectMapper.writeValueAsString(toolExecution.getExecutionContext()) : null,
                toolExecution.getStateSnapshot() != null ? objectMapper.writeValueAsString(toolExecution.getStateSnapshot()) : null,
                Instant.now(),
                Instant.now()
            );
        } catch (JsonProcessingException e) {
            log.error("Error serializing tool execution data", e);
            throw new RuntimeException("Failed to serialize tool execution data", e);
        }
    }
    
    @Override
    public Long update(ToolExecution toolExecution) {
        String sql = """
            UPDATE ai.tool_executions SET
                session_id = ?, suggestion_id = ?, tool_name = ?, parameters = ?, execution_timestamp = ?,
                execution_time_ms = ?, success = ?, result = ?, error_message = ?, confidence_score = ?,
                attempt_number = ?, max_attempts = ?, execution_status = ?, next_attempt_at = ?,
                failure_reason = ?, tool_config_hash = ?, depends_on_tool_execution_id = ?,
                execution_order = ?, rollback_required = ?, rollback_executed = ?, rollback_result = ?,
                scheduled_at = ?, execution_priority = ?, execution_context = ?, state_snapshot = ?,
                updated_at = ?
            WHERE id = ?
            """;
        
        try {
            jdbcTemplate.update(sql,
                toolExecution.getSessionId(),
                toolExecution.getSuggestionId(),
                toolExecution.getToolName(),
                objectMapper.writeValueAsString(toolExecution.getParameters()),
                toolExecution.getExecutionTimestamp(),
                toolExecution.getExecutionTimeMs(),
                toolExecution.getSuccess(),
                toolExecution.getResult() != null ? objectMapper.writeValueAsString(toolExecution.getResult()) : null,
                toolExecution.getErrorMessage(),
                toolExecution.getConfidenceScore(),
                toolExecution.getAttemptNumber(),
                toolExecution.getMaxAttempts(),
                toolExecution.getExecutionStatus().name(),
                toolExecution.getNextAttemptAt(),
                toolExecution.getFailureReason(),
                toolExecution.getToolConfigHash(),
                toolExecution.getDependsOnToolExecutionId(),
                toolExecution.getExecutionOrder(),
                toolExecution.getRollbackRequired(),
                toolExecution.getRollbackExecuted(),
                toolExecution.getRollbackResult() != null ? objectMapper.writeValueAsString(toolExecution.getRollbackResult()) : null,
                toolExecution.getScheduledAt(),
                toolExecution.getExecutionPriority(),
                toolExecution.getExecutionContext() != null ? objectMapper.writeValueAsString(toolExecution.getExecutionContext()) : null,
                toolExecution.getStateSnapshot() != null ? objectMapper.writeValueAsString(toolExecution.getStateSnapshot()) : null,
                Instant.now(),
                toolExecution.getId()
            );
            
            return toolExecution.getId();
        } catch (JsonProcessingException e) {
            log.error("Error serializing tool execution data", e);
            throw new RuntimeException("Failed to serialize tool execution data", e);
        }
    }
    
    @Override
    public Optional<ToolExecution> findById(Long id) {
        String sql = "SELECT * FROM ai.tool_executions WHERE id = ?";
        List<ToolExecution> results = jdbcTemplate.query(sql, new ToolExecutionRowMapper(), id);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }
    
    @Override
    public List<ToolExecution> findAll(Long organizationId) {
        String sql = "SELECT * FROM ai.tool_executions ORDER BY created_at DESC";
        return jdbcTemplate.query(sql, new ToolExecutionRowMapper());
    }
    
    @Override
    public void deleteById(Long id) {
        String sql = "DELETE FROM ai.tool_executions WHERE id = ?";
        jdbcTemplate.update(sql, id);
    }
    
    /**
     * Row mapper for ToolExecution
     */
    private class ToolExecutionRowMapper implements RowMapper<ToolExecution> {
        @Override
        public ToolExecution mapRow(ResultSet rs, int rowNum) throws SQLException {
            try {
                return ToolExecution.builder()
                    .id(rs.getLong("id"))
                    .sessionId(rs.getString("session_id"))
                    .suggestionId(rs.getLong("suggestion_id"))
                    .toolName(rs.getString("tool_name"))
                    .parameters(parseJson(rs.getString("parameters")))
                    .executionTimestamp(rs.getTimestamp("execution_timestamp") != null ? rs.getTimestamp("execution_timestamp").toInstant() : null)
                    .executionTimeMs(rs.getLong("execution_time_ms"))
                    .success(rs.getBoolean("success"))
                    .result(parseJson(rs.getString("result")))
                    .errorMessage(rs.getString("error_message"))
                    .confidenceScore(rs.getDouble("confidence_score"))
                    .attemptNumber(rs.getInt("attempt_number"))
                    .maxAttempts(rs.getInt("max_attempts"))
                    .executionStatus(ToolExecution.ExecutionStatus.valueOf(rs.getString("execution_status")))
                    .nextAttemptAt(rs.getTimestamp("next_attempt_at") != null ? rs.getTimestamp("next_attempt_at").toInstant() : null)
                    .failureReason(rs.getString("failure_reason"))
                    .toolConfigHash(rs.getString("tool_config_hash"))
                    .dependsOnToolExecutionId(rs.getLong("depends_on_tool_execution_id"))
                    .executionOrder(rs.getInt("execution_order"))
                    .rollbackRequired(rs.getBoolean("rollback_required"))
                    .rollbackExecuted(rs.getBoolean("rollback_executed"))
                    .rollbackResult(parseJson(rs.getString("rollback_result")))
                    .scheduledAt(rs.getTimestamp("scheduled_at") != null ? rs.getTimestamp("scheduled_at").toInstant() : null)
                    .executionPriority(rs.getInt("execution_priority"))
                    .executionContext(parseJson(rs.getString("execution_context")))
                    .stateSnapshot(parseJson(rs.getString("state_snapshot")))
                    .createdAt(rs.getTimestamp("created_at") != null ? rs.getTimestamp("created_at").toLocalDateTime() : null)
                    .updatedAt(rs.getTimestamp("updated_at") != null ? rs.getTimestamp("updated_at").toLocalDateTime() : null)
                    .build();
            } catch (Exception e) {
                log.error("Error mapping tool execution row", e);
                throw new RuntimeException("Failed to map tool execution row", e);
            }
        }
        
        private Map<String, Object> parseJson(String json) {
            if (json == null || json.trim().isEmpty()) {
                return null;
            }
            try {
                return objectMapper.readValue(json, Map.class);
            } catch (JsonProcessingException e) {
                log.error("Error parsing JSON: {}", json, e);
                return null;
            }
        }
    }
} 