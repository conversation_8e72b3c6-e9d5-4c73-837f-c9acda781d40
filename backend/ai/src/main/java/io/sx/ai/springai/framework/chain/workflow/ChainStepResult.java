package io.sx.ai.springai.framework.chain.workflow;

import io.sx.ai.springai.framework.common.BaseStepResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Represents the result of executing a chain step
 * Extends BaseStepResult for consistency with the workflow architecture
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ChainStepResult<I, O> extends BaseStepResult<I, O> {
    
    // Chain-specific fields
    private String prompt;
    private String operation;
    private String chainId;
    private int chainPosition;
    
    /**
     * Create a successful chain step result
     */
    public static <I, O> ChainStepResult<I, O> success(int stepNumber, String stepName, I input, O output, 
                                                      String operation, String prompt) {
        return ChainStepResult.<I, O>builder()
            .stepNumber(stepNumber)
            .stepName(stepName)
            .input(input)
            .output(output)
            .operation(operation)
            .prompt(prompt)
            .success(true)
            .build();
    }
    
    /**
     * Create an error chain step result
     */
    public static <I, O> ChainStepResult<I, O> error(int stepNumber, String stepName, I input, String errorMessage) {
        return ChainStepResult.<I, O>builder()
            .stepNumber(stepNumber)
            .stepName(stepName)
            .input(input)
            .errorMessage(errorMessage)
            .success(false)
            .build();
    }
} 