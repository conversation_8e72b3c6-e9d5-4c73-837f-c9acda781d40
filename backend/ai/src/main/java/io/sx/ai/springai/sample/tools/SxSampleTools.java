package io.sx.ai.springai.sample.tools;

import io.sx.ai.tools.logic.TextDecorationLogic;
import io.sx.ai.tools.logic.MathCalculationLogic;
import io.sx.ai.tools.logic.DateTimeLogic;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.time.ZoneId;

/**
 * Spring AI wrapper methods that use plain tool logic classes.
 * Provides @Tool annotations for Spring AI integration.
 */
@Component
public class SxSampleTools {
    
    private final TextDecorationLogic textDecorationLogic = new TextDecorationLogic();
    private final MathCalculationLogic mathCalculationLogic = new MathCalculationLogic();
    private final DateTimeLogic dateTimeLogic = new DateTimeLogic();
    
    @Tool(description = "Add decorative borders around text using specified delimiter")
    String decorateText(String text, String delimiter, String style) {
        
        return switch (style) {
            case "simple" -> textDecorationLogic.decorateSimple(text, delimiter);
            case "box" -> textDecorationLogic.decorateBox(text, delimiter);
            case "fancy" -> textDecorationLogic.decorateFancy(text, delimiter);
            default -> textDecorationLogic.decorateSimple(text, delimiter);
        };
    }
    
    @Tool(description = "Count words in the given text")
    int countWords(String text) {
        
        return textDecorationLogic.countWords(text);
    }
    
    @Tool(description = "Count characters in the given text (excluding whitespace)")
    int countCharacters(String text) {
        
        return textDecorationLogic.countCharacters(text);
    }
    
    @Tool(description = "Perform mathematical calculations")
    double calculateMath(String expression, int precision) {
        
        return mathCalculationLogic.evaluateExpression(expression, precision);
    }
    
    @Tool(description = "Calculate percentage of a value relative to total")
    double calculatePercentage(double value, double total) {
        
        return mathCalculationLogic.calculatePercentage(value, total);
    }
    
    @Tool(description = "Round a number to specified decimal places")
    double roundNumber(double number, int decimalPlaces) {
        
        return mathCalculationLogic.roundNumber(number, decimalPlaces);
    }
    
    @Tool(description = "Calculate the absolute value")
    double absoluteValue(double number) {
        
        return mathCalculationLogic.absoluteValue(number);
    }
    
    @Tool(description = "Calculate the square root")
    double squareRoot(double number) {
        
        return mathCalculationLogic.squareRoot(number);
    }
    
    @Tool(description = "Calculate power (base^exponent)")
    double power(double base, double exponent) {
        
        return mathCalculationLogic.power(base, exponent);
    }
    
    @Tool(description = "Get the current date and time in the user's timezone")
    String getCurrentDateTime() {
        return dateTimeLogic.getCurrentDateTime();
    }
    
    @Tool(description = "Format a datetime string using a pattern")
    String formatDateTime(String dateTimeString, String pattern) {
        
        return dateTimeLogic.formatDateTime(dateTimeString, pattern);
    }
    
    @Tool(description = "Calculate the difference between two dates in days")
    long calculateDateDifference(String startDate, String endDate) {
        
        return dateTimeLogic.calculateDateDifference(startDate, endDate);
    }
    
    @Tool(description = "Calculate the difference between two dates in hours")
    long calculateDateDifferenceHours(String startDate, String endDate) {
        
        return dateTimeLogic.calculateDateDifferenceHours(startDate, endDate);
    }
    
    @Tool(description = "Add days to a datetime")
    String addDays(String dateTimeString, long days) {
        
        return dateTimeLogic.addDays(dateTimeString, days);
    }
    
    @Tool(description = "Check if a datetime string is valid")
    boolean isValidDateTime(String dateTimeString) {
        
        return dateTimeLogic.isValidDateTime(dateTimeString);
    }
    
    @Tool(description = "Get the day of week for a datetime")
    String getDayOfWeek(String dateTimeString) {
        
        return dateTimeLogic.getDayOfWeek(dateTimeString);
    }
} 