package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Base result of executing a workflow step with type safety
 * Provides common structure for step execution results across different workflow types
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseStepResult<I, O> {
    
    /**
     * The step number in the sequence
     */
    private int stepNumber;
    
    /**
     * The name of the step
     */
    private String stepName;
    
    /**
     * The input value for this step
     */
    private I input;
    
    /**
     * The output value from this step
     */
    private O output;
    
    /**
     * Description of the operation performed
     */
    private String operation;
    
    /**
     * The actual prompt sent to the LLM (if applicable)
     */
    private String prompt;
    
    /**
     * Whether this step was successful
     */
    private boolean success;
    
    /**
     * Error message if the step failed
     */
    private String errorMessage;
    
    /**
     * Timestamp when the step was executed
     */
    private LocalDateTime executedAt;
    
    /**
     * Execution time in milliseconds
     */
    private Long executionTimeMs;
    
    /**
     * Step type for categorization
     */
    private BaseWorkflowStep.StepType stepType;
    
    /**
     * Additional metadata for the step
     */
    private Map<String, Object> metadata;
    
    /**
     * Create a successful result
     */
    public static <I, O> BaseStepResult<I, O> success(int stepNumber, String stepName, I input, O output, String operation) {
        BaseStepResult<I, O> result = new BaseStepResult<>();
        result.setStepNumber(stepNumber);
        result.setStepName(stepName);
        result.setInput(input);
        result.setOutput(output);
        result.setOperation(operation);
        result.setSuccess(true);
        result.setExecutedAt(LocalDateTime.now());
        return result;
    }
    
    /**
     * Create an error result
     */
    public static <I, O> BaseStepResult<I, O> error(int stepNumber, String stepName, I input, String errorMessage) {
        BaseStepResult<I, O> result = new BaseStepResult<>();
        result.setStepNumber(stepNumber);
        result.setStepName(stepName);
        result.setInput(input);
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setExecutedAt(LocalDateTime.now());
        return result;
    }
    
    /**
     * Get a human-readable description of the step result
     */
    public String getDescription() {
        if (success) {
            return String.format("Step %d (%s): %s -> %s", stepNumber, stepName, input, output);
        } else {
            return String.format("Step %d (%s): Failed - %s", stepNumber, stepName, errorMessage);
        }
    }
    
    /**
     * Get the input value as string (for backward compatibility)
     */
    public String getInputAsString() {
        return input != null ? input.toString() : null;
    }
    
    /**
     * Get the output value as string (for backward compatibility)
     */
    public String getOutputAsString() {
        return output != null ? output.toString() : null;
    }

    // Getter and Setter methods
    public int getStepNumber() { return stepNumber; }
    public void setStepNumber(int stepNumber) { this.stepNumber = stepNumber; }
    
    public String getStepName() { return stepName; }
    public void setStepName(String stepName) { this.stepName = stepName; }
    
    public I getInput() { return input; }
    public void setInput(I input) { this.input = input; }
    
    public O getOutput() { return output; }
    public void setOutput(O output) { this.output = output; }
    
    public String getOperation() { return operation; }
    public void setOperation(String operation) { this.operation = operation; }
    
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public LocalDateTime getExecutedAt() { return executedAt; }
    public void setExecutedAt(LocalDateTime executedAt) { this.executedAt = executedAt; }
    
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    
    public Object getMetadata(String key) { return metadata.get(key); }
    public void setMetadata(String key, Object value) { this.metadata.put(key, value); }
} 