package io.sx.ai.springai.framework.xstatic.workflow;

import io.sx.ai.springai.framework.common.BaseWorkflowResult;
import io.sx.ai.springai.framework.common.BaseStepResult;
import io.sx.ai.springai.framework.common.WorkflowInput;
import io.sx.ai.springai.framework.common.WorkflowOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Result of the dynamic workflow execution, extends BaseWorkflowResult<Double, Double>
 */
@Data
@EqualsAndHashCode(callSuper=false)
@NoArgsConstructor
public class WorkflowResult extends BaseWorkflowResult<Double, Double> {
    // Dynamic-specific fields
    private int totalIterations;
    private String status;

    public WorkflowResult(WorkflowInput<Double> originalInput, WorkflowOutput<Double> finalOutput, List<WorkflowStepResult> executedSteps, boolean success, String error, String sessionId, String workflowType, int totalIterations, String status, Long processingTimeMs) {
        super(originalInput, finalOutput, (List) executedSteps, success, error, sessionId, workflowType, LocalDateTime.now(), LocalDateTime.now(), processingTimeMs, null);
        this.totalIterations = totalIterations;
        this.status = status;
    }

    // Static builder method
    public static WorkflowResultBuilder workflowResultBuilder() {
        return new WorkflowResultBuilder();
    }

    public static class WorkflowResultBuilder {
        private WorkflowInput<Double> originalInput;
        private WorkflowOutput<Double> finalOutput;
        private List<WorkflowStepResult> executedSteps;
        private boolean success;
        private String error;
        private String sessionId;
        private String workflowType;
        private int totalIterations;
        private String status;
        private Long processingTimeMs;

        public WorkflowResultBuilder originalInput(WorkflowInput<Double> originalInput) {
            this.originalInput = originalInput;
            return this;
        }

        public WorkflowResultBuilder finalOutput(WorkflowOutput<Double> finalOutput) {
            this.finalOutput = finalOutput;
            return this;
        }

        public WorkflowResultBuilder executedSteps(List<WorkflowStepResult> executedSteps) {
            this.executedSteps = executedSteps;
            return this;
        }

        public WorkflowResultBuilder success(boolean success) {
            this.success = success;
            return this;
        }

        public WorkflowResultBuilder error(String error) {
            this.error = error;
            return this;
        }

        public WorkflowResultBuilder sessionId(String sessionId) {
            this.sessionId = sessionId;
            return this;
        }

        public WorkflowResultBuilder workflowType(String workflowType) {
            this.workflowType = workflowType;
            return this;
        }

        public WorkflowResultBuilder totalIterations(int totalIterations) {
            this.totalIterations = totalIterations;
            return this;
        }

        public WorkflowResultBuilder status(String status) {
            this.status = status;
            return this;
        }

        public WorkflowResultBuilder processingTimeMs(Long processingTimeMs) {
            this.processingTimeMs = processingTimeMs;
            return this;
        }

        public WorkflowResult build() {
            return new WorkflowResult(originalInput, finalOutput, executedSteps, success, error, sessionId, workflowType, totalIterations, status, processingTimeMs);
        }
    }

    /**
     * Get a human-readable summary of the workflow execution
     */
    public String getSummary() {
        return String.format(
            "Workflow %s: Input=%.2f, Result=%.2f, Iterations=%d, Time=%dms, Success=%s, Reason=%s",
            getSessionId(), getInputValue(), getOutputValue(), totalIterations, getProcessingTimeMs(), isSuccess(), getError()
        );
    }

    /**
     * Get a detailed description of each step
     */
    public String getDetailedDescription() {
        StringBuilder description = new StringBuilder();
        description.append("Workflow Execution Details:\n");
        description.append("Session ID: ").append(getSessionId()).append("\n");
        description.append("Workflow Type: ").append(getWorkflowType()).append("\n");
        description.append("Input: ").append(getInputValue()).append("\n");
        description.append("Final Output: ").append(getOutputValue()).append("\n");
        description.append("Total Steps: ").append(getStepCount()).append("\n");
        description.append("Total Iterations: ").append(totalIterations).append("\n");
        description.append("Status: ").append(status).append("\n");
        description.append("Processing Time: ").append(getProcessingTimeMs()).append("ms\n");
        description.append("Success: ").append(isSuccess()).append("\n");
        if (getError() != null) {
            description.append("Error: ").append(getError()).append("\n");
        }
        if (getSteps() != null) {
            description.append("\nExecution Steps:\n");
            for (int i = 0; i < getSteps().size(); i++) {
                BaseStepResult<Double, Double> step = getSteps().get(i);
                description.append(i + 1).append(". ").append(step.getDescription()).append("\n");
            }
        }
        return description.toString();
    }

    /**
     * Check if the workflow achieved the target range
     */
    public boolean isTargetAchieved() {
        return getOutputValue() >= 990.0 && getOutputValue() <= 1000.0;
    }

    /**
     * Check if the workflow terminated due to max iterations
     */
    public boolean isMaxIterationsReached() {
        return "Max iterations reached".equals(getError());
    }

    /**
     * Get the number of successful tool executions
     */
    public long getSuccessfulToolExecutions() {
        if (getSteps() == null) {
            return 0;
        }
        
        return getSteps().stream()
                .filter(step -> {
                    WorkflowStepResult workflowStep = (WorkflowStepResult) step;
                    return workflowStep.getToolResults() != null && !workflowStep.getToolResults().isEmpty();
                })
                .count();
    }

    /**
     * Get the average confidence score across all steps
     */
    public double getAverageConfidence() {
        if (getSteps() == null || getSteps().isEmpty()) {
            return 0.0;
        }
        
        return getSteps().stream()
                .filter(step -> {
                    WorkflowStepResult workflowStep = (WorkflowStepResult) step;
                    return workflowStep.getConfidenceScore() != null;
                })
                .mapToDouble(step -> {
                    WorkflowStepResult workflowStep = (WorkflowStepResult) step;
                    return workflowStep.getConfidenceScore();
                })
                .average()
                .orElse(0.0);
    }

    // Convenience methods for backward compatibility
    public Double getInputValue() {
        return getOriginalInput() != null ? getOriginalInput().getValue() : 0.0;
    }

    public Double getOutputValue() {
        return getFinalOutput() != null ? (Double) getFinalOutput().getValue() : 0.0;
    }

    public double getInputNumber() {
        return getInputValue();
    }
} 