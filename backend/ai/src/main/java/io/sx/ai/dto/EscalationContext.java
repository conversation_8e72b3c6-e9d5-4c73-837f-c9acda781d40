package io.sx.ai.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * Context data for escalation analysis
 * Contains all the deterministic factors used in decision making
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EscalationContext {
    
    private SLAViolationRisk slaViolationRisk;
    private CustomerContext customerContext;
    private TeamAvailability teamAvailability;
    private BusinessImpactAssessment businessImpact;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SLAViolationRisk {
        private boolean isViolationRisk;
        private java.time.Duration timeToViolation;
        private double riskPercentage;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerContext {
        private double satisfactionScore;
        private int daysUntilRenewal;
        private double totalSpend;
        private double contractValue;
        private boolean isLeaving;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TeamAvailability {
        private int availableAgents;
        private int totalAgents;
        private java.time.Duration averageResponseTime;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessImpactAssessment {
        private double impactScore;
        private double revenueAtRisk;
        private double reputationRisk;
    }
} 