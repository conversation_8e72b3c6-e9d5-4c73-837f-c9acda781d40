package io.sx.ai.springai.sample.dynamic.workflow;

import io.sx.ai.abstraction.PromptResolutionService;
import io.sx.ai.abstraction.ResolvedPrompt;
import io.sx.ai.abstraction.SxLLMClient;
import io.sx.ai.abstraction.SxLLMResponse;
import io.sx.ai.abstraction.SxChatMemory;
import io.sx.ai.abstraction.SxMessage;
import io.sx.ai.abstraction.SxPrompt;
import io.sx.ai.springai.spring.SxSpringChatMemory;
import io.sx.ai.springai.framework.common.BaseWorkflowOrchestrator;
import io.sx.ai.springai.framework.common.BaseWorkflowResult;
import io.sx.ai.springai.framework.common.BaseStepResult;
import io.sx.ai.springai.framework.common.TextInput;
import io.sx.ai.springai.framework.common.WorkflowInput;
import io.sx.ai.springai.framework.common.AnalysisOutput;
import io.sx.ai.springai.framework.common.TextAnalysisResult;
import io.sx.ai.springai.framework.common.TextStatistics;
import io.sx.ai.springai.framework.common.DateTimeInfo;
import io.sx.ai.springai.spring.SxSpringLLMClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * LLM-Controlled Workflow that showcases tool calling capabilities.
 * This workflow demonstrates how the LLM can intelligently use tools
 * to enhance its analysis and decision-making capabilities.
 */
@Component
public class LLMControlledWorkflow implements BaseWorkflowOrchestrator<String, TextAnalysisResult> {
    
    private static final Logger log = LoggerFactory.getLogger(LLMControlledWorkflow.class);
    
    private final SxLLMClient llmClient;
    private final PromptResolutionService promptResolutionService;
    
    @Autowired
    public LLMControlledWorkflow(SxLLMClient llmClient, PromptResolutionService promptResolutionService) {
        this.llmClient = llmClient;
        this.promptResolutionService = promptResolutionService;
    }
    
    @Override
    public BaseWorkflowResult<String, TextAnalysisResult> executeWorkflow(WorkflowInput<String> input, String sessionId, Long organizationId, Long onBehalfOfId) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("Starting LLM-Controlled Workflow for session: {}", sessionId);
        
        try {
            // Create chat memory for conversation context
            SxChatMemory chatMemory = SxSpringChatMemory.createDefault(sessionId);
            
            // Step 1: Initial analysis with sample tools
            TextAnalysisResult initialAnalysis = executeInitialAnalysis(input.getValue(), chatMemory);
            
            // Step 2: Mathematical analysis with math tools
            TextAnalysisResult mathAnalysis = executeMathematicalAnalysis(input.getValue(), chatMemory);
            
            // Step 3: DateTime analysis with datetime tools
            TextAnalysisResult dateTimeAnalysis = executeDateTimeAnalysis(input.getValue(), chatMemory);
            
            // Step 4: Final comprehensive analysis with all tools
            TextAnalysisResult finalAnalysis = executeFinalAnalysis(input.getValue(), chatMemory);
            
            // Combine all results
            TextAnalysisResult combinedResult = combineResults(initialAnalysis, mathAnalysis, dateTimeAnalysis, finalAnalysis);
            
            return BaseWorkflowResult.success(input, AnalysisOutput.of(combinedResult), 
                createStepResults(initialAnalysis, mathAnalysis, dateTimeAnalysis, finalAnalysis), 
                sessionId, "LLM_CONTROLLED_WORKFLOW");
                
        } catch (Exception e) {
            log.error("Error in LLM-Controlled Workflow: {}", e.getMessage(), e);
            return BaseWorkflowResult.error(input, e.getMessage());
        }
    }
    
    /**
     * Execute initial analysis using sample tools
     */
    private TextAnalysisResult executeInitialAnalysis(String text, SxChatMemory chatMemory) {
        log.debug("Executing initial text analysis with sample tools");
        log.debug("Text to analyze: '{}'", text);
        
        String userPrompt = String.format("""
            %s
            
            Return this exact JSON structure with your analysis values:
            {"sentiment":"neutral","themes":["general"],"statistics":{"wordCount":0,"characterCount":0,"sentenceCount":0},"dateTimeInfo":{"hasDates":false,"hasTimes":false,"extractedDates":[]},"comprehensiveAnalysis":"analysis","confidenceScore":0.85,"toolsUsed":["sample_tools"],"warnings":[]}
            """, text);
        
        ResolvedPrompt prompt = promptResolutionService.createSimplePrompt(userPrompt, 
            SxPrompt.PromptType.CHAT, SxMessage.PromptRole.USER);
        
        // Use sample tools for general analysis
        SxSpringLLMClient springLLMClient = (SxSpringLLMClient) llmClient;
        // Spring AI automatically discovers @Tool annotated methods in Spring components
        // We can also pass explicit tool instances for additional tools
        
        // Create a structured output prompt that explicitly requests JSON format
        String structuredPrompt = createStructuredAnalysisPrompt(text);
        SxPrompt structuredSxPrompt = springLLMClient.createPrompt(List.of(
            springLLMClient.createSystemMessage("You are an AI assistant that analyzes text and returns structured JSON responses."),
            springLLMClient.createUserMessage(structuredPrompt)
        ));
        
        // Convert SxPrompt to ResolvedPrompt
        ResolvedPrompt resolvedPrompt = promptResolutionService.createSimplePrompt(structuredPrompt, 
            SxPrompt.PromptType.CHAT, SxMessage.PromptRole.USER);
        
        SxLLMResponse<TextAnalysisResult> response = springLLMClient.generateWithMemoryAndTools(
            resolvedPrompt, TextAnalysisResult.class, chatMemory);
        
        // Add to chat memory
        chatMemory.addMessage(springLLMClient.createUserMessage(userPrompt));
        chatMemory.addMessage(springLLMClient.createAssistantMessage(response.getOutput().toString()));
        
        return response.getOutput();
    }
    
    /**
     * Execute mathematical analysis using math tools
     */
    private TextAnalysisResult executeMathematicalAnalysis(String text, SxChatMemory chatMemory) {
        log.debug("Executing mathematical analysis with math tools");
        log.debug("Text to analyze: '{}'", text);
        
        String userPrompt = String.format("""
            %s
            
            Return this exact JSON structure with your analysis values:
            {"sentiment":"neutral","themes":["mathematical"],"statistics":{"wordCount":0,"characterCount":0,"sentenceCount":0},"dateTimeInfo":{"hasDates":false,"hasTimes":false,"extractedDates":[]},"comprehensiveAnalysis":"analysis","confidenceScore":0.9,"toolsUsed":["math_tools"],"warnings":[]}
            """, text);
        
        ResolvedPrompt prompt = promptResolutionService.createSimplePrompt(userPrompt, 
            SxPrompt.PromptType.CHAT, SxMessage.PromptRole.USER);
        
        // Use math tools specifically
        SxSpringLLMClient springLLMClient = (SxSpringLLMClient) llmClient;
        // Spring AI automatically discovers @Tool annotated methods in Spring components
        
        // Create a structured output prompt for mathematical analysis
        String structuredPrompt = createMathematicalAnalysisPrompt(text);
        SxPrompt structuredSxPrompt = springLLMClient.createPrompt(List.of(
            springLLMClient.createSystemMessage("You are an AI assistant that performs mathematical analysis and returns structured JSON responses."),
            springLLMClient.createUserMessage(structuredPrompt)
        ));
        
        // Convert SxPrompt to ResolvedPrompt
        ResolvedPrompt resolvedPrompt = promptResolutionService.createSimplePrompt(structuredPrompt, 
            SxPrompt.PromptType.CHAT, SxMessage.PromptRole.USER);
        
        SxLLMResponse<TextAnalysisResult> response = springLLMClient.generateWithMemoryAndTools(
            resolvedPrompt, TextAnalysisResult.class, chatMemory);
        
        // Add to chat memory
        chatMemory.addMessage(llmClient.createUserMessage(userPrompt));
        chatMemory.addMessage(llmClient.createAssistantMessage(response.getOutput().toString()));
        
        return response.getOutput();
    }
    
    /**
     * Execute datetime analysis using datetime tools
     */
    private TextAnalysisResult executeDateTimeAnalysis(String text, SxChatMemory chatMemory) {
        log.debug("Executing datetime analysis with datetime tools");
        log.debug("Text to analyze: '{}'", text);
        
        String userPrompt = String.format("""
            %s
            
            Return this exact JSON structure with your analysis values:
            {"sentiment":"neutral","themes":["datetime"],"statistics":{"wordCount":0,"characterCount":0,"sentenceCount":0},"dateTimeInfo":{"hasDates":false,"hasTimes":false,"extractedDates":[]},"comprehensiveAnalysis":"analysis","confidenceScore":0.85,"toolsUsed":["datetime_tools"],"warnings":[]}
            """, text);
        
        ResolvedPrompt prompt = promptResolutionService.createSimplePrompt(userPrompt, 
            SxPrompt.PromptType.CHAT, SxMessage.PromptRole.USER);
        
        // Use datetime tools specifically
        SxSpringLLMClient springLLMClient = (SxSpringLLMClient) llmClient;
        // Spring AI automatically discovers @Tool annotated methods in Spring components
        
        // Create a structured output prompt for datetime analysis
        String structuredPrompt = createDateTimeAnalysisPrompt(text);
        SxPrompt structuredSxPrompt = springLLMClient.createPrompt(List.of(
            springLLMClient.createSystemMessage("You are an AI assistant that performs datetime analysis and returns structured JSON responses."),
            springLLMClient.createUserMessage(structuredPrompt)
        ));
        
        // Convert SxPrompt to ResolvedPrompt
        ResolvedPrompt resolvedPrompt = promptResolutionService.createSimplePrompt(structuredPrompt, 
            SxPrompt.PromptType.CHAT, SxMessage.PromptRole.USER);
        
        SxLLMResponse<TextAnalysisResult> response = springLLMClient.generateWithMemoryAndTools(
            resolvedPrompt, TextAnalysisResult.class, chatMemory);
        
        // Add to chat memory
        chatMemory.addMessage(llmClient.createUserMessage(userPrompt));
        chatMemory.addMessage(llmClient.createAssistantMessage(response.getOutput().toString()));
        
        return response.getOutput();
    }
    
    /**
     * Execute final comprehensive analysis with all tools
     */
    private TextAnalysisResult executeFinalAnalysis(String text, SxChatMemory chatMemory) {
        log.debug("Executing final comprehensive analysis with all tools");
        log.debug("Text to analyze: '{}'", text);
        if (text == null || text.trim().isEmpty()) {
            log.error("Text is null or empty in final analysis step");
            throw new IllegalArgumentException("Text cannot be null or empty for final analysis");
        }
        
        // Use all tools: sample + math + datetime
        SxSpringLLMClient springLLMClient = (SxSpringLLMClient) llmClient;
        // Spring AI automatically discovers @Tool annotated methods in Spring components
        
        // Create a structured output prompt for final comprehensive analysis
        String structuredPrompt = createFinalAnalysisPrompt(text);
        SxPrompt structuredSxPrompt = springLLMClient.createPrompt(List.of(
            springLLMClient.createSystemMessage("You are an AI assistant that performs comprehensive analysis and returns structured JSON responses."),
            springLLMClient.createUserMessage(structuredPrompt)
        ));
        
        // Convert SxPrompt to ResolvedPrompt
        ResolvedPrompt resolvedPrompt = promptResolutionService.createSimplePrompt(structuredPrompt, 
            SxPrompt.PromptType.CHAT, SxMessage.PromptRole.USER);
        
        SxLLMResponse<TextAnalysisResult> response = springLLMClient.generateWithMemoryAndTools(
            resolvedPrompt, TextAnalysisResult.class, chatMemory);
        
        // Add to chat memory
        chatMemory.addMessage(llmClient.createUserMessage(structuredPrompt));
        chatMemory.addMessage(llmClient.createAssistantMessage(response.getOutput().toString()));
        
        return response.getOutput();
    }
    
    /**
     * Combine all analysis results into a comprehensive result
     */
    private TextAnalysisResult combineResults(TextAnalysisResult... results) {
        log.debug("Combining {} analysis results", results.length);
        
        // Extract key metrics from all results
        String sentiment = extractSentiment(results);
        List<String> themes = extractThemes(results);
        TextStatistics statistics = extractStatistics(results);
        DateTimeInfo dateTimeInfo = extractDateTimeInfo(results);
        String comprehensiveAnalysis = extractComprehensiveAnalysis(results);
        
        return TextAnalysisResult.builder()
            .sentiment(sentiment)
            .themes(themes)
            .statistics(statistics)
            .dateTimeInfo(dateTimeInfo)
            .comprehensiveAnalysis(comprehensiveAnalysis)
            .analysisTimestamp(LocalDateTime.now().toString())
            .build();
    }
    
    /**
     * Create step results for the workflow
     */
    private List<BaseStepResult<String, TextAnalysisResult>> createStepResults(TextAnalysisResult... results) {
        List<BaseStepResult<String, TextAnalysisResult>> steps = new ArrayList<>();
        
        String[] stepNames = {"Initial Analysis", "Mathematical Analysis", "DateTime Analysis", "Final Analysis"};
        
        for (int i = 0; i < results.length; i++) {
            steps.add(BaseStepResult.success(i + 1, stepNames[i], 
                "", results[i], stepNames[i]));
        }
        
        return steps;
    }
    
    // Helper methods for result combination
    private String extractSentiment(TextAnalysisResult... results) {
        // Combine sentiment analysis from all results
        return "Mixed"; // Simplified for now
    }
    
    private List<String> extractThemes(TextAnalysisResult... results) {
        // Combine themes from all results
        List<String> allThemes = new ArrayList<>();
        for (TextAnalysisResult result : results) {
            if (result.getThemes() != null) {
                allThemes.addAll(result.getThemes());
            }
        }
        return allThemes;
    }
    
    private TextStatistics extractStatistics(TextAnalysisResult... results) {
        // Combine statistics from all results
        return TextStatistics.builder()
            .wordCount(0)
            .characterCount(0)
            .sentenceCount(0)
            .build();
    }
    
    private DateTimeInfo extractDateTimeInfo(TextAnalysisResult... results) {
        // Combine datetime info from all results
        return DateTimeInfo.builder()
            .hasDates(false)
            .hasTimes(false)
            .extractedDates(new ArrayList<>())
            .build();
    }
    
    private String extractComprehensiveAnalysis(TextAnalysisResult... results) {
        // Combine comprehensive analysis from all results
        StringBuilder analysis = new StringBuilder();
        for (TextAnalysisResult result : results) {
            if (result.getComprehensiveAnalysis() != null) {
                analysis.append(result.getComprehensiveAnalysis()).append("\n\n");
            }
        }
        return analysis.toString().trim();
    }
    
    // BaseWorkflowOrchestrator interface implementations
    
    @Override
    public BaseWorkflowResult<String, TextAnalysisResult> executeWorkflowFromEvent(java.util.Map<String, Object> eventData, String sessionId, Long organizationId, Long onBehalfOfId) {
        String inputValue = extractInputFromEvent(eventData);
        if (inputValue == null) {
            TextInput nullInput = TextInput.of("");
            return BaseWorkflowResult.error(nullInput, "Could not extract input from event data");
        }
        TextInput input = TextInput.of(inputValue);
        return executeWorkflow(input, sessionId, organizationId, onBehalfOfId);
    }
    
    @Override
    public BaseStepResult<String, TextAnalysisResult> executeWorkflowStep(String sessionId, String stepId, java.util.Map<String, Object> context, Long organizationId, Long onBehalfOfId) {
        // For LLM-controlled workflow, we execute the specific step
        String inputValue = (String) context.get("input");
        if (inputValue == null) {
            return BaseStepResult.error(0, stepId, "", "Input not found in context");
        }
        
        // Execute the step based on stepId
        TextInput input = TextInput.of(inputValue);
        SxChatMemory chatMemory = SxSpringChatMemory.createDefault(sessionId);
        
        TextAnalysisResult result = switch (stepId) {
            case "initial_analysis" -> executeInitialAnalysis(inputValue, chatMemory);
            case "mathematical_analysis" -> executeMathematicalAnalysis(inputValue, chatMemory);
            case "datetime_analysis" -> executeDateTimeAnalysis(inputValue, chatMemory);
            case "final_analysis" -> executeFinalAnalysis(inputValue, chatMemory);
            default -> throw new IllegalArgumentException("Unknown step: " + stepId);
        };
        
        return BaseStepResult.success(1, stepId, inputValue, result, "Execute " + stepId);
    }
    
    @Override
    public io.sx.ai.springai.framework.common.BaseWorkflowContext<String, TextAnalysisResult> getWorkflowStatus(String sessionId) {
        // For LLM-controlled workflow, we don't maintain persistent state
        TextInput unknownInput = TextInput.of("");
        return io.sx.ai.springai.framework.common.BaseWorkflowContext.initialize(sessionId, unknownInput, 4);
    }
    
    @Override
    public java.util.Map<String, String> getAvailableWorkflowTypes() {
        return java.util.Map.of(
            "LLM_CONTROLLED_WORKFLOW", "LLM-Controlled Workflow with tool calling capabilities"
        );
    }
    
    @Override
    public boolean validateWorkflowParameters(String workflowType, java.util.Map<String, Object> parameters) {
        if (!"LLM_CONTROLLED_WORKFLOW".equals(workflowType)) {
            return false;
        }
        
        Object input = parameters.get("input");
        return input != null && !input.toString().trim().isEmpty();
    }
    
    @Override
    public java.util.Map<String, Object> getWorkflowStats(Long organizationId) {
        return java.util.Map.of(
            "workflow_type", "LLM_CONTROLLED_WORKFLOW",
            "total_executions", 0, // Would be tracked in a real implementation
            "success_rate", 1.0,
            "average_execution_time_ms", 2000
        );
    }
    
    @Override
    public boolean cancelWorkflow(String sessionId, String reason) {
        // LLM-controlled workflow executes quickly and doesn't support cancellation
        return false;
    }
    
    @Override
    public List<BaseWorkflowResult<String, TextAnalysisResult>> getWorkflowHistory(Long organizationId, int limit) {
        // Would return actual history in a real implementation
        return new ArrayList<>();
    }
    
    private String extractInputFromEvent(java.util.Map<String, Object> eventData) {
        Object input = eventData.get("input");
        return input != null ? input.toString() : null;
    }
    
    /**
     * Create a structured analysis prompt that explicitly requests JSON format
     */
    private String createStructuredAnalysisPrompt(String textToAnalyze) {
        return String.format("""
            Analyze the following text and return a JSON response with this exact structure:
            
            {
              "comprehensiveAnalysis": "Detailed analysis of the text",
              "sentiment": "positive|negative|neutral",
              "confidenceScore": 0.95,
              "themes": ["theme1", "theme2"],
              "statistics": {"wordCount": 5, "characterCount": 20, "sentenceCount": 1},
              "dateTimeInfo": {"hasDates": false, "hasTimes": false, "extractedDates": []},
              "toolsUsed": ["sample_tools"],
              "warnings": []
            }
            
            TEXT TO ANALYZE: "%s"
            
            CRITICAL: Return ONLY valid JSON. Do NOT add any text before or after the JSON. 
            Do NOT wrap in markdown code blocks. Return pure JSON only.
            """, textToAnalyze);
    }
    
    /**
     * Create a mathematical analysis prompt that explicitly requests JSON format
     */
    private String createMathematicalAnalysisPrompt(String textToAnalyze) {
        return String.format("""
            Analyze the following text for mathematical content and return a JSON response with this exact structure:
            
            {
              "comprehensiveAnalysis": "Mathematical analysis of the text",
              "sentiment": "positive|negative|neutral",
              "confidenceScore": 0.95,
              "themes": ["mathematical", "calculation"],
              "statistics": {"wordCount": 5, "characterCount": 20, "sentenceCount": 1},
              "dateTimeInfo": {"hasDates": false, "hasTimes": false, "extractedDates": []},
              "toolsUsed": ["math_tools"],
              "warnings": []
            }
            
            TEXT TO ANALYZE: "%s"
            
            CRITICAL: Return ONLY valid JSON. Do NOT add any text before or after the JSON. 
            Do NOT wrap in markdown code blocks. Return pure JSON only.
            """, textToAnalyze);
    }
    
    /**
     * Create a datetime analysis prompt that explicitly requests JSON format
     */
    private String createDateTimeAnalysisPrompt(String textToAnalyze) {
        return String.format("""
            Analyze the following text for datetime content and return a JSON response with this exact structure:
            
            {
              "comprehensiveAnalysis": "DateTime analysis of the text",
              "sentiment": "positive|negative|neutral",
              "confidenceScore": 0.95,
              "themes": ["datetime", "temporal"],
              "statistics": {"wordCount": 5, "characterCount": 20, "sentenceCount": 1},
              "dateTimeInfo": {"hasDates": false, "hasTimes": false, "extractedDates": []},
              "toolsUsed": ["datetime_tools"],
              "warnings": []
            }
            
            TEXT TO ANALYZE: "%s"
            
            CRITICAL: Return ONLY valid JSON. Do NOT add any text before or after the JSON. 
            Do NOT wrap in markdown code blocks. Return pure JSON only.
            """, textToAnalyze);
    }
    
    /**
     * Create a final comprehensive analysis prompt that explicitly requests JSON format
     */
    private String createFinalAnalysisPrompt(String textToAnalyze) {
        return String.format("""
            Perform a comprehensive analysis of the following text and return a JSON response with this exact structure:
            
            {
              "comprehensiveAnalysis": "Comprehensive analysis combining all aspects of the text",
              "sentiment": "positive|negative|neutral",
              "confidenceScore": 0.95,
              "themes": ["comprehensive", "overall"],
              "statistics": {"wordCount": 5, "characterCount": 20, "sentenceCount": 1},
              "dateTimeInfo": {"hasDates": false, "hasTimes": false, "extractedDates": []},
              "toolsUsed": ["all_tools"],
              "warnings": []
            }
            
            TEXT TO ANALYZE: "%s"
            
            CRITICAL: Return ONLY valid JSON. Do NOT add any text before or after the JSON. 
            Do NOT wrap in markdown code blocks. Return pure JSON only.
            """, textToAnalyze);
    }
} 