package io.sx.ai.decision.rules;

import io.sx.ai.decision.conditions.AIConditionEvaluator;
import io.sx.ai.decision.model.AIDecisionContext;
import io.sx.ai.decision.model.AIDecisionResult;
import io.sx.ai.decision.strategies.AIDecisionStrategyFactory;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Base implementation for AI decision rules
 */
@RequiredArgsConstructor
public abstract class BaseAIDecisionRule implements AIDecisionRule {
    
    protected final AIConditionEvaluator conditionEvaluator;
    protected final AIDecisionStrategyFactory strategyFactory;
    
    @Override
    public AIDecisionResult evaluate(AIDecisionContext context) {
        // 1. Evaluate conditions
        if (!evaluateConditions(context)) {
            return AIDecisionResult.noAI();
        }
        
        // 2. Determine AI strategy
        String strategy = determineStrategy(context);
        
        // 3. Get strategy configuration
        var strategyConfig = getStrategyConfig(strategy, context);
        
        // 4. Build decision result
        return buildDecisionResult(strategy, strategyConfig, context);
    }
    
    /**
     * Evaluate all conditions for this rule
     */
    protected abstract boolean evaluateConditions(AIDecisionContext context);
    
    /**
     * Determine which AI strategy to use
     */
    protected abstract String determineStrategy(AIDecisionContext context);
    
    /**
     * Get configuration for the selected strategy
     */
    protected abstract Map<String, Object> getStrategyConfig(String strategy, AIDecisionContext context);
    
    /**
     * Get reasoning for the decision
     */
    protected abstract String getReasoning(AIDecisionContext context);
    
    /**
     * Build the final decision result
     */
    protected AIDecisionResult buildDecisionResult(String strategy, Map<String, Object> strategyConfig, AIDecisionContext context) {
        // Get confidence threshold from strategy config
        Double confidenceThreshold = strategyConfig != null && strategyConfig.containsKey("confidence_threshold") 
            ? (Double) strategyConfig.get("confidence_threshold") 
            : 0.7;
        
        // Build result based on strategy
        AIDecisionResult.AIDecisionResultBuilder builder = AIDecisionResult.builder()
            .shouldApplyAI(true)
            .aiStrategy(strategy)
            .strategyConfig(strategyConfig)
            .reasoning(getReasoning(context))
            .ruleId(getRuleId())
            .confidenceThreshold(confidenceThreshold);
        
        // Set AI methods based on strategy
        switch (strategy) {
            case "enhanced":
                builder.aiMethods(List.of("vector", "llm", "rules"));
                break;
            case "app_only":
                builder.aiMethods(List.of("vector", "rules"));
                break;
            case "hybrid":
                builder.aiMethods(List.of("vector", "llm"));
                break;
            case "llm_only":
                builder.aiMethods(List.of("llm"));
                break;
            default:
                builder.aiMethods(List.of("vector", "llm"));
                break;
        }
        
        return builder.build();
    }
} 