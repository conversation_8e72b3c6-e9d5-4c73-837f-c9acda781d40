package io.sx.ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentConfig {
    
    private String agentId;
    private Long organizationId;
    private Boolean enabled;
    private String model;
    private Double temperature;
    private Integer maxTokens;
    private Map<String, Object> parameters;
    private Map<String, Object> thresholds;
} 