package io.sx.ai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class AIAnalysisRequest {
    
    @JsonProperty("event_type")
    private String eventType;
    
    @JsonProperty("event_data")
    private Map<String, Object> eventData;
    
    @JsonProperty("organization_id")
    private Long organizationId;
    
    @JsonProperty("on_behalf_of_id")
    private Long onBehalfOfId;
    
    @JsonProperty("context")
    private AnalysisContext context;
    
    @JsonProperty("available_actions")
    private List<AvailableAction> availableActions;
    
    @JsonProperty("analysis_goals")
    private List<String> analysisGoals;
    
    @JsonProperty("expected_output_format")
    private String expectedOutputFormat;
    
    @Data
    @Builder
    public static class AnalysisContext {
        @JsonProperty("ticket_id")
        private Long ticketId;
        
        @JsonProperty("participant_id")
        private Long participantId;
        
        @JsonProperty("participant_type")
        private String participantType;
        
        @JsonProperty("participant_role")
        private String participantRole;
        
        @JsonProperty("ticket_status")
        private String ticketStatus;
        
        @JsonProperty("ticket_priority")
        private String ticketPriority;
        
        @JsonProperty("ticket_category")
        private String ticketCategory;
        
        @JsonProperty("assigned_team")
        private String assignedTeam;
        
        @JsonProperty("similar_tickets")
        private List<Map<String, Object>> similarTickets;
        
        @JsonProperty("historical_data")
        private Map<String, Object> historicalData;
        
        @JsonProperty("business_rules")
        private Map<String, Object> businessRules;
        
        @JsonProperty("general_info_type")
        private String generalInfoType;
        
        @JsonProperty("general_info_value")
        private String generalInfoValue;
    }
    
    @Data
    @Builder
    public static class AvailableAction {
        @JsonProperty("action_id")
        private String actionId;
        
        @JsonProperty("action_name")
        private String actionName;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("category")
        private String category; // ANALYSIS, CLASSIFICATION, ASSIGNMENT, NOTIFICATION, etc.
        
        @JsonProperty("required_tool")
        private String requiredTool;
        
        @JsonProperty("parameters")
        private Map<String, Object> parameters;
        
        @JsonProperty("conditions")
        private List<String> conditions;
        
        @JsonProperty("priority")
        private Integer priority;
    }
} 