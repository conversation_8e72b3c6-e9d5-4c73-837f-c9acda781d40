package io.sx.ai.dto;

import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Standardized ticket data structure for consistent AI processing
 * This ensures all ticket information follows the same schema regardless of context
 */
@Data
@Builder
public class StandardizedTicketData {
    
    // Core ticket information
    private Long ticketId;
    private String title;
    private String description;
    private String status;
    private String priority;
    private String category;
    private String subcategory;
    
    // Timestamps
    private Instant createdAt;
    private Instant updatedAt;
    private Instant resolvedAt;
    private Instant closedAt;
    
    // Organization and ownership
    private Long organizationId;
    private Long supportOrganizationId;
    private Long createdById;
    private String createdByEmail;
    private String createdBy;
    private Long updatedById;
    private String updatedByEmail;
    private String updatedBy;
    private Long onBehalfOfId;
    private Long assignedToId;
    private String assignedToEmail;
    private String assignedTo;
    private Long teamId;
    private String teamName;
    
    // Ticket details
    private String source;
    private String channel;
    private String language;
    private String timezone;
    private String externalId;
    private String customerEmail;
    private String customerName;
    private String customerPhone;
    private Long customerId;
    
    // Metrics and timing
    private Long firstResponseTimeMs;
    private Long resolutionTimeMs;
    private Long totalTimeMs;
    private Integer responseCount;
    private Integer escalationCount;
    private Integer reassignmentCount;
    private Double satisfactionScore;
    
    // Content and attachments
    private List<TicketComment> comments;
    private List<TicketParticipant> participants;
    private List<TicketAttachment> attachments;
    private List<TicketHistory> history;
    
    // Tags and labels
    private List<String> tags;
    private List<String> labels;
    private Map<String, Object> customFields;
    
    // AI and analysis data
    private TicketAnalysis analysis;
    private TicketPredictions predictions;
    
    // Context-specific metadata
    private Map<String, Object> metadata;
    
    /**
     * Ticket comment structure
     */
    @Data
    @Builder
    public static class TicketComment {
        private Long commentId;
        private String content;
        private String authorEmail;
        private Long authorId;
        private String authorName;
        private String commentType; // PUBLIC, INTERNAL, SYSTEM
        private Instant createdAt;
        private Instant updatedAt;
        private Boolean isInternal;
        private String visibility; // PUBLIC, INTERNAL, PRIVATE
    }
    
    /**
     * Ticket participant structure
     */
    @Data
    @Builder
    public static class TicketParticipant {
        private Long participantId;
        private Long userId;
        private String userEmail;
        private String userName;
        private String role; // ASSIGNEE, WATCHER, REQUESTER, etc.
        private String participantType; // USER, TEAM, GROUP
        private Instant addedAt;
        private Instant removedAt;
        private Boolean isActive;
    }
    
    /**
     * Ticket attachment structure
     */
    @Data
    @Builder
    public static class TicketAttachment {
        private Long attachmentId;
        private String fileName;
        private String fileType; // TEXT, IMAGE, VIDEO, AUDIO, DOCUMENT, etc.
        private String mimeType;
        private Long fileSize;
        private String url;
        private String uploadedBy;
        private String uploadedByEmail;
        private Long uploadedById;
        private Instant uploadedAt;
        private Boolean isPublic;
    }
    
    /**
     * Ticket history structure
     */
    @Data
    @Builder
    public static class TicketHistory {
        private Long historyId;
        private String action;
        private String field;
        private String oldValue;
        private String newValue;
        private String changedBy;
        private String changedByEmail;
        private Long changedById;
        private Instant changedAt;
        private String details;
    }
    
    /**
     * AI analysis data
     */
    @Data
    @Builder
    public static class TicketAnalysis {
        private String sentiment;
        private Double sentimentScore;
        private String urgency;
        private Double urgencyScore;
        private String complexity;
        private Double complexityScore;
        private List<String> detectedIssues;
        private List<String> suggestedActions;
        private String aiSummary;
        private Map<String, Object> analysisMetadata;
    }
    
    /**
     * AI predictions
     */
    @Data
    @Builder
    public static class TicketPredictions {
        private Instant predictedResolutionTime;
        private Double resolutionProbability;
        private String predictedCategory;
        private String predictedPriority;
        private Double escalationProbability;
        private Double customerSatisfactionPrediction;
        private Map<String, Object> predictionMetadata;
    }
    
    /**
     * Builder method for creating standardized ticket data with metadata
     */
    public static StandardizedTicketDataBuilder builder() {
        return new StandardizedTicketDataBuilder();
    }
    
    /**
     * Add metadata to the ticket
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new java.util.HashMap<>();
        }
        metadata.put(key, value);
    }
    
    /**
     * Get metadata value
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
    
    /**
     * Add similarity score for context-specific metadata
     */
    public void setSimilarityScore(Double score) {
        addMetadata("similarity_score", score);
    }
    
    /**
     * Get similarity score from metadata
     */
    public Double getSimilarityScore() {
        Object score = getMetadata("similarity_score");
        return score instanceof Double ? (Double) score : null;
    }
} 