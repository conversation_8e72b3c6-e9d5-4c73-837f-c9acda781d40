package io.sx.ai.orchestration;

import io.sx.ai.decision.engine.AIDecisionEngine;
import io.sx.ai.decision.model.AIDecisionContext;
import io.sx.ai.decision.model.AIDecisionResult;
import io.sx.ai.decision.strategies.AIDecisionStrategy;
import io.sx.ai.decision.strategies.AIDecisionStrategyFactory;
import io.sx.ai.model.TicketEventType;
import io.sx.ai.service.AIConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * Orchestrator for actual ticket processing workflows.
 * Integrates with the AI Decision Framework to determine when and how to apply AI methods.
 * This orchestrator handles production ticket processing, not sample workflows.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TicketOrchestrator {

    private final AIDecisionEngine aiDecisionEngine;
    private final AIDecisionStrategyFactory strategyFactory;
    private final AIConfigurationService aiConfigurationService;

    /**
     * Process a ticket creation event through the AI workflow
     * 
     * @param ticketId The ID of the created ticket
     * @param ticketData The ticket data including description, priority, etc.
     * @param organizationId The organization ID
     * @param supportOrgId The support organization ID
     * @param userId The user ID who created the ticket
     * @return Processing result with AI suggestions and execution status
     */
    public TicketProcessingResult processTicketCreation(Long ticketId, Map<String, Object> ticketData, 
                                                       Long organizationId, Long supportOrgId, Long userId) {
        String sessionId = generateSessionId(ticketId);
        log.info("TicketOrchestrator: Processing ticket creation. Session: {}, Ticket: {}, Org: {}", 
                sessionId, ticketId, organizationId);

        try {
            // Step 1: Evaluate AI decision using the Decision Framework
            AIDecisionResult decision = evaluateAIDecision(ticketData, supportOrgId, sessionId);
            
            if (!decision.getShouldApplyAI()) {
                log.info("TicketOrchestrator: AI not recommended for ticket {}. Reason: {}", 
                        ticketId, decision.getReasoning());
                return TicketProcessingResult.noAIAction(sessionId, ticketId, decision.getReasoning());
            }

            // Step 2: Execute the recommended AI strategy
            AIDecisionStrategy strategy = strategyFactory.getStrategy(decision.getAiStrategy());
            if (strategy == null) {
                log.warn("TicketOrchestrator: Strategy '{}' not found for ticket {}", 
                        decision.getAiStrategy(), ticketId);
                return TicketProcessingResult.error(sessionId, ticketId, 
                        "AI strategy not found: " + decision.getAiStrategy());
            }

            // Step 3: Execute the strategy
            TicketProcessingResult result = strategy.executeTicketProcessing(
                    sessionId, ticketId, ticketData, organizationId, supportOrgId, userId);

            log.info("TicketOrchestrator: Ticket processing completed. Session: {}, Ticket: {}, Strategy: {}", 
                    sessionId, ticketId, decision.getAiStrategy());

            return result;

        } catch (Exception e) {
            log.error("TicketOrchestrator: Error processing ticket {}. Session: {}", ticketId, sessionId, e);
            return TicketProcessingResult.error(sessionId, ticketId, "Processing failed: " + e.getMessage());
        }
    }

    /**
     * Process a ticket update event through the AI workflow
     */
    public TicketProcessingResult processTicketUpdate(Long ticketId, Map<String, Object> ticketData,
                                                     Long organizationId, Long supportOrgId, Long userId) {
        String sessionId = generateSessionId(ticketId);
        log.info("TicketOrchestrator: Processing ticket update. Session: {}, Ticket: {}, Org: {}", 
                sessionId, ticketId, organizationId);

        // For now, use the same logic as ticket creation
        // In the future, this could have different decision rules
        return processTicketCreation(ticketId, ticketData, organizationId, supportOrgId, userId);
    }

    /**
     * Evaluate AI decision using the Decision Framework
     */
    private AIDecisionResult evaluateAIDecision(Map<String, Object> ticketData, Long supportOrgId, String sessionId) {
        log.debug("TicketOrchestrator: Evaluating AI decision for session: {}", sessionId);
        
        try {
            // Build decision context from ticket data
            AIDecisionContext context = AIDecisionContext.builder()
                    .triggerEvent(TicketEventType.TICKET_CREATED.getValue())
                    .entityId(extractTicketId(ticketData))
                    .organizationId(extractOrganizationId(ticketData))
                    .entityData(ticketData)
                    .timestamp(LocalDateTime.now())
                    .build();

            // Evaluate decision using the AI Decision Engine
            return aiDecisionEngine.evaluateDecision(TicketEventType.TICKET_CREATED.getValue(), ticketData, supportOrgId);

        } catch (Exception e) {
            log.error("TicketOrchestrator: Error evaluating AI decision for session: {}", sessionId, e);
            // Return a conservative decision (no AI) on error
            return AIDecisionResult.builder()
                    .shouldApplyAI(false)
                    .aiStrategy("none")
                    .confidenceThreshold(0.0)
                    .reasoning("Error evaluating AI decision: " + e.getMessage())
                    .build();
        }
    }

    /**
     * Generate a unique session ID for the ticket processing
     */
    private String generateSessionId(Long ticketId) {
        return "ticket-" + ticketId + "-" + UUID.randomUUID().toString().substring(0, 8);
    }

    // Helper methods to extract data from ticketData map
    private Long extractTicketId(Map<String, Object> ticketData) {
        Object id = ticketData.get("id");
        return id instanceof Number ? ((Number) id).longValue() : null;
    }

    private Long extractOrganizationId(Map<String, Object> ticketData) {
        Object orgId = ticketData.get("organizationId");
        return orgId instanceof Number ? ((Number) orgId).longValue() : null;
    }

    /**
     * Result class for ticket processing operations
     */
    public static class TicketProcessingResult {
        private final String sessionId;
        private final Long ticketId;
        private final boolean success;
        private final String strategy;
        private final String reasoning;
        private final Map<String, Object> suggestions;
        private final String error;

        private TicketProcessingResult(String sessionId, Long ticketId, boolean success, 
                                     String strategy, String reasoning, 
                                     Map<String, Object> suggestions, String error) {
            this.sessionId = sessionId;
            this.ticketId = ticketId;
            this.success = success;
            this.strategy = strategy;
            this.reasoning = reasoning;
            this.suggestions = suggestions;
            this.error = error;
        }

        public static TicketProcessingResult success(String sessionId, Long ticketId, 
                                                   String strategy, String reasoning, 
                                                   Map<String, Object> suggestions) {
            return new TicketProcessingResult(sessionId, ticketId, true, strategy, reasoning, suggestions, null);
        }

        public static TicketProcessingResult noAIAction(String sessionId, Long ticketId, String reasoning) {
            return new TicketProcessingResult(sessionId, ticketId, true, "none", reasoning, Map.of(), null);
        }

        public static TicketProcessingResult error(String sessionId, Long ticketId, String error) {
            return new TicketProcessingResult(sessionId, ticketId, false, null, null, Map.of(), error);
        }

        // Getters
        public String getSessionId() { return sessionId; }
        public Long getTicketId() { return ticketId; }
        public boolean isSuccess() { return success; }
        public String getStrategy() { return strategy; }
        public String getReasoning() { return reasoning; }
        public Map<String, Object> getSuggestions() { return suggestions; }
        public String getError() { return error; }
    }
} 