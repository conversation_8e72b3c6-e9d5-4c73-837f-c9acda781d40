package io.sx.ai.decision.engine;

import io.sx.ai.decision.model.AIDecisionContext;
import io.sx.ai.decision.model.AIDecisionResult;
import io.sx.ai.decision.rules.AIDecisionRule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Engine for evaluating AI decisions based on rules
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AIDecisionEngine {
    
    private final List<AIDecisionRule> decisionRules;
    private final AIDecisionContextBuilder contextBuilder;
    
    /**
     * Evaluate AI decision for a trigger event
     */
    public AIDecisionResult evaluateDecision(String triggerEvent, Map<String, Object> eventData, Long supportOrgId) {
        log.debug("Evaluating AI decision for event: {}, org: {}", triggerEvent, supportOrgId);
        
        try {
            // 1. Build decision context
            AIDecisionContext context = contextBuilder.buildContext(triggerEvent, eventData, supportOrgId);
            
            // 2. Find applicable rules
            List<AIDecisionRule> applicableRules = findApplicableRules(triggerEvent, context);
            
            log.debug("Found {} applicable rules for event: {}", applicableRules.size(), triggerEvent);
            
            // 3. Evaluate each rule in priority order
            for (AIDecisionRule rule : applicableRules) {
                log.debug("Evaluating rule: {}", rule.getRuleId());
                
                AIDecisionResult result = rule.evaluate(context);
                
                if (result.getShouldApplyAI()) {
                    log.info("AI decision made by rule {}: strategy={}, reasoning={}", 
                        rule.getRuleId(), result.getAiStrategy(), result.getReasoning());
                    return result;
                }
            }
            
            // 4. Default: no AI
            log.debug("No AI decision made, defaulting to no AI");
            return AIDecisionResult.noAI();
            
        } catch (Exception e) {
            log.error("Error evaluating AI decision for event: {}", triggerEvent, e);
            return AIDecisionResult.noAI();
        }
    }
    
    /**
     * Find applicable rules for the trigger event and context
     */
    private List<AIDecisionRule> findApplicableRules(String triggerEvent, AIDecisionContext context) {
        return decisionRules.stream()
            .filter(rule -> rule.isApplicable(triggerEvent, context))
            .sorted(Comparator.comparing(AIDecisionRule::getPriority).reversed())
            .collect(Collectors.toList());
    }
} 