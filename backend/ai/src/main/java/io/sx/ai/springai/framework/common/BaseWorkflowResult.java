package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

/**
 * Base result of executing a workflow with type safety
 * Provides common structure for workflow execution results across different workflow types
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseWorkflowResult<I, O> {
    
    /**
     * Original typed input to the workflow
     */
    private WorkflowInput<I> originalInput;
    
    /**
     * Final typed output after all steps are completed
     */
    private WorkflowOutput<O> finalOutput;
    
    /**
     * List of all steps executed in the workflow
     */
    private List<BaseStepResult<I, O>> steps;
    
    /**
     * Whether the entire workflow executed successfully
     */
    private boolean success;
    
    /**
     * Error message if the workflow failed
     */
    private String error;
    
    /**
     * Workflow session ID
     */
    private String sessionId;
    
    /**
     * Workflow type/category
     */
    private String workflowType;
    
    /**
     * Timestamp when workflow started
     */
    private LocalDateTime startedAt;
    
    /**
     * Timestamp when workflow completed
     */
    private LocalDateTime completedAt;
    
    /**
     * Total processing time in milliseconds
     */
    private Long processingTimeMs;
    
    /**
     * Additional metadata for the workflow
     */
    private Map<String, Object> metadata;
    
    /**
     * Builder pattern for creating workflow results
     */
    public static <I, O> Builder<I, O> builder() {
        return new Builder<>();
    }
    
    public static class Builder<I, O> {
        private WorkflowInput<I> originalInput;
        private WorkflowOutput<O> finalOutput;
        private List<BaseStepResult<I, O>> steps;
        private boolean success;
        private String error;
        private String sessionId;
        private String workflowType;
        private LocalDateTime startedAt;
        private LocalDateTime completedAt;
        private Long processingTimeMs;
        private Map<String, Object> metadata;
        
        public Builder<I, O> originalInput(WorkflowInput<I> originalInput) {
            this.originalInput = originalInput;
            return this;
        }
        
        public Builder<I, O> finalOutput(WorkflowOutput<O> finalOutput) {
            this.finalOutput = finalOutput;
            return this;
        }
        
        public Builder<I, O> steps(List<BaseStepResult<I, O>> steps) {
            this.steps = steps;
            return this;
        }
        
        public Builder<I, O> success(boolean success) {
            this.success = success;
            return this;
        }
        
        public Builder<I, O> error(String error) {
            this.error = error;
            return this;
        }
        
        public Builder<I, O> sessionId(String sessionId) {
            this.sessionId = sessionId;
            return this;
        }
        
        public Builder<I, O> workflowType(String workflowType) {
            this.workflowType = workflowType;
            return this;
        }
        
        public Builder<I, O> startedAt(LocalDateTime startedAt) {
            this.startedAt = startedAt;
            return this;
        }
        
        public Builder<I, O> completedAt(LocalDateTime completedAt) {
            this.completedAt = completedAt;
            return this;
        }
        
        public Builder<I, O> processingTimeMs(Long processingTimeMs) {
            this.processingTimeMs = processingTimeMs;
            return this;
        }
        
        public Builder<I, O> metadata(Map<String, Object> metadata) {
            this.metadata = metadata;
            return this;
        }
        
        public BaseWorkflowResult<I, O> build() {
            return new BaseWorkflowResult<>(originalInput, finalOutput, steps, success, error, 
                                          sessionId, workflowType, startedAt, completedAt, 
                                          processingTimeMs, metadata);
        }
    }
    
    // Static factory methods
    public static <I, O> BaseWorkflowResult<I, O> success(WorkflowInput<I> originalInput, WorkflowOutput<O> finalOutput, 
                                                         List<BaseStepResult<I, O>> steps, String sessionId, String workflowType) {
        BaseWorkflowResult<I, O> result = new BaseWorkflowResult<>();
        result.setOriginalInput(originalInput);
        result.setFinalOutput(finalOutput);
        result.setSteps(steps);
        result.setSuccess(true);
        result.setSessionId(sessionId);
        result.setWorkflowType(workflowType);
        result.setStartedAt(LocalDateTime.now());
        result.setCompletedAt(LocalDateTime.now());
        return result;
    }

    public static <I, O> BaseWorkflowResult<I, O> error(String errorMessage) {
        BaseWorkflowResult<I, O> result = new BaseWorkflowResult<>();
        result.setSteps(new ArrayList<>());
        result.setSuccess(false);
        result.setError(errorMessage);
        result.setStartedAt(LocalDateTime.now());
        result.setCompletedAt(LocalDateTime.now());
        return result;
    }

    public static <I, O> BaseWorkflowResult<I, O> error(WorkflowInput<I> originalInput, String errorMessage) {
        BaseWorkflowResult<I, O> result = new BaseWorkflowResult<>();
        result.setOriginalInput(originalInput);
        result.setSteps(new ArrayList<>());
        result.setSuccess(false);
        result.setError(errorMessage);
        result.setStartedAt(LocalDateTime.now());
        result.setCompletedAt(LocalDateTime.now());
        return result;
    }

    // Getter and Setter methods
    public WorkflowInput<I> getOriginalInput() { return originalInput; }
    public void setOriginalInput(WorkflowInput<I> originalInput) { this.originalInput = originalInput; }
    
    public WorkflowOutput<O> getFinalOutput() { return finalOutput; }
    public void setFinalOutput(WorkflowOutput<O> finalOutput) { this.finalOutput = finalOutput; }
    
    public List<BaseStepResult<I, O>> getSteps() { return steps; }
    public void setSteps(List<BaseStepResult<I, O>> steps) { this.steps = steps; }
    
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getError() { return error; }
    public void setError(String error) { this.error = error; }
    
    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    
    public String getWorkflowType() { return workflowType; }
    public void setWorkflowType(String workflowType) { this.workflowType = workflowType; }
    
    public LocalDateTime getStartedAt() { return startedAt; }
    public void setStartedAt(LocalDateTime startedAt) { this.startedAt = startedAt; }
    
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    
    public Long getProcessingTimeMs() { return processingTimeMs; }
    public void setProcessingTimeMs(Long processingTimeMs) { this.processingTimeMs = processingTimeMs; }
    
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    
    public Object getMetadata(String key) { return metadata.get(key); }
    public void setMetadata(String key, Object value) { this.metadata.put(key, value); }

    // Convenience methods
    public I getInputValue() {
        return originalInput != null ? originalInput.getValue() : null;
    }
    
    public O getOutputValue() {
        return finalOutput != null ? finalOutput.getValue() : null;
    }
    
    public O getFinalResult() {
        return getOutputValue();
    }

    /**
     * Get the number of steps in the workflow
     */
    public int getStepCount() {
        return steps != null ? steps.size() : 0;
    }
    
    /**
     * Get the first step output
     */
    public String getFirstStepOutput() {
        return steps != null && !steps.isEmpty() ? steps.get(0).getOutputAsString() : null;
    }
    
    /**
     * Get the last step output
     */
    public String getLastStepOutput() {
        return steps != null && !steps.isEmpty() ? steps.get(steps.size() - 1).getOutputAsString() : null;
    }
    
    /**
     * Get the number of successful steps
     */
    public long getSuccessfulStepCount() {
        if (steps == null) {
            return 0;
        }
        return steps.stream().filter(BaseStepResult::isSuccess).count();
    }
    
    /**
     * Get the number of failed steps
     */
    public long getFailedStepCount() {
        if (steps == null) {
            return 0;
        }
        return steps.stream().filter(step -> !step.isSuccess()).count();
    }
    
    /**
     * Get a human-readable summary of the workflow execution
     */
    public String getSummary() {
        return String.format(
            "Workflow %s: Input=%s, Output=%s, Steps=%d, Success=%s, Time=%dms",
            workflowType, getInputValue(), getOutputValue(), getStepCount(), success, processingTimeMs
        );
    }
    
    /**
     * Get a detailed description of each step
     */
    public String getDetailedDescription() {
        StringBuilder description = new StringBuilder();
        description.append("Workflow Execution Details:\n");
        description.append("Session ID: ").append(sessionId).append("\n");
        description.append("Workflow Type: ").append(workflowType).append("\n");
        description.append("Input: ").append(getInputValue()).append("\n");
        description.append("Final Output: ").append(getOutputValue()).append("\n");
        description.append("Total Steps: ").append(getStepCount()).append("\n");
        description.append("Successful Steps: ").append(getSuccessfulStepCount()).append("\n");
        description.append("Failed Steps: ").append(getFailedStepCount()).append("\n");
        description.append("Processing Time: ").append(processingTimeMs).append("ms\n");
        description.append("Success: ").append(success).append("\n");
        
        if (error != null) {
            description.append("Error: ").append(error).append("\n");
        }
        
        if (steps != null) {
            description.append("\nExecution Steps:\n");
            for (int i = 0; i < steps.size(); i++) {
                BaseStepResult<I, O> step = steps.get(i);
                description.append(i + 1).append(". ").append(step.getDescription()).append("\n");
            }
        }
        
        return description.toString();
    }
} 