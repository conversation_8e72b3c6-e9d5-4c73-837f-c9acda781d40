package io.sx.ai.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Structured LLM Response DTO for Spring AI mapping
 * Provides a reliable alternative to JSON parsing for LLM responses
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StructuredLLMResponse {
    
    @JsonProperty("result")
    private AnalysisResult result;
    
    @JsonProperty("metadata")
    private ResponseMetadata metadata;
    
    @JsonProperty("providerData")
    private ProviderData providerData;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AnalysisResult {
        @JsonProperty("decisions")
        private List<Decision> decisions;
        
        @JsonProperty("reasoning")
        private String reasoning;
        
        @JsonProperty("nextSteps")
        private List<NextStep> nextSteps;
        
        @JsonProperty("sessionId")
        private String sessionId;
        
        @JsonProperty("toolCalls")
        private List<ToolCall> toolCalls;
        
        @JsonProperty("analysisId")
        private String analysisId;
        
        @JsonProperty("confidenceScore")
        private Double confidenceScore;
        
        @JsonProperty("recommendedActions")
        private List<RecommendedAction> recommendedActions;
        
        @JsonProperty("informationRequests")
        private List<InformationRequest> informationRequests;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Decision {
        @JsonProperty("action")
        private String action;
        
        @JsonProperty("reasoning")
        private String reasoning;
        
        @JsonProperty("confidence")
        private Double confidence;
        
        @JsonProperty("parameters")
        private Map<String, Object> parameters;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonDeserialize(using = NextStep.NextStepDeserializer.class)
    public static class NextStep {
        @JsonProperty("stepId")
        private String stepId;
        
        @JsonProperty("stepName")
        private String stepName;
        
        @JsonProperty("stepType")
        private String stepType;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("estimatedDuration")
        private String estimatedDuration;
        
        // Constructor to handle string values from LLM
        public NextStep(String description) {
            this.description = description;
            this.stepName = description;
        }
        
        // Static factory method for string conversion
        public static NextStep fromString(String stepDescription) {
            return new NextStep(stepDescription);
        }
        
        // Custom deserializer to handle both string and object formats
        public static class NextStepDeserializer extends JsonDeserializer<NextStep> {
            @Override
            public NextStep deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                if (p.getCurrentToken() == JsonToken.VALUE_STRING) {
                    // Handle string format: "Execute suggested tool calls"
                    String stepDescription = p.getValueAsString();
                    return NextStep.fromString(stepDescription);
                } else if (p.getCurrentToken() == JsonToken.START_OBJECT) {
                    // Handle object format: {"stepName": "...", "description": "..."}
                    return ctxt.readValue(p, NextStep.class);
                } else {
                    throw ctxt.wrongTokenException(p, NextStep.class, JsonToken.VALUE_STRING, 
                        "Expected string or object for NextStep");
                }
            }
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ToolCall {
        @JsonProperty("toolName")
        private String toolName;
        
        @JsonProperty("parameters")
        private Map<String, Object> parameters;
        
        @JsonProperty("reasoning")
        private String reasoning;
        
        @JsonProperty("purpose")
        private String purpose;
        
        @JsonProperty("expectedOutcome")
        private String expectedOutcome;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonDeserialize(using = RecommendedAction.RecommendedActionDeserializer.class)
    public static class RecommendedAction {
        @JsonProperty("actionId")
        private String actionId;
        
        @JsonProperty("actionName")
        private String actionName;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("reasoning")
        private String reasoning;
        
        @JsonProperty("priority")
        private Integer priority;
        
        @JsonProperty("estimatedImpact")
        private String estimatedImpact;
        
        // Constructor to handle string values from LLM
        public RecommendedAction(String description) {
            this.description = description;
            this.actionName = description;
        }
        
        // Static factory method for string conversion
        public static RecommendedAction fromString(String actionDescription) {
            return new RecommendedAction(actionDescription);
        }
        
        // Custom deserializer to handle both string and object formats
        public static class RecommendedActionDeserializer extends JsonDeserializer<RecommendedAction> {
            @Override
            public RecommendedAction deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                if (p.getCurrentToken() == JsonToken.VALUE_STRING) {
                    // Handle string format: "Assign to senior agent"
                    String actionDescription = p.getValueAsString();
                    return RecommendedAction.fromString(actionDescription);
                } else if (p.getCurrentToken() == JsonToken.START_OBJECT) {
                    // Handle object format: {"actionName": "...", "description": "..."}
                    return ctxt.readValue(p, RecommendedAction.class);
                } else {
                    throw ctxt.wrongTokenException(p, RecommendedAction.class, JsonToken.VALUE_STRING, 
                        "Expected string or object for RecommendedAction");
                }
            }
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonDeserialize(using = InformationRequest.InformationRequestDeserializer.class)
    public static class InformationRequest {
        @JsonProperty("requestId")
        private String requestId;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("informationType")
        private String informationType;
        
        @JsonProperty("parameters")
        private Map<String, Object> parameters;
        
        @JsonProperty("priority")
        private Integer priority;
        
        @JsonProperty("requiredForDecision")
        private Boolean requiredForDecision;
        
        // Constructor to handle string values from LLM
        public InformationRequest(String description) {
            this.description = description;
            this.informationType = "GENERAL";
        }
        
        // Static factory method for string conversion
        public static InformationRequest fromString(String requestDescription) {
            return new InformationRequest(requestDescription);
        }
        
        // Custom deserializer to handle both string and object formats
        public static class InformationRequestDeserializer extends JsonDeserializer<InformationRequest> {
            @Override
            public InformationRequest deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                if (p.getCurrentToken() == JsonToken.VALUE_STRING) {
                    // Handle string format: "Check team availability"
                    String requestDescription = p.getValueAsString();
                    return InformationRequest.fromString(requestDescription);
                } else if (p.getCurrentToken() == JsonToken.START_OBJECT) {
                    // Handle object format: {"description": "...", "informationType": "..."}
                    return ctxt.readValue(p, InformationRequest.class);
                } else {
                    throw ctxt.wrongTokenException(p, InformationRequest.class, JsonToken.VALUE_STRING, 
                        "Expected string or object for InformationRequest");
                }
            }
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResponseMetadata {
        @JsonProperty("status")
        private String status;
        
        @JsonProperty("provider")
        private String provider;
        
        @JsonProperty("modelUsed")
        private String modelUsed;
        
        @JsonProperty("requestId")
        private String requestId;
        
        @JsonProperty("timestamp")
        private String timestamp;
        
        @JsonProperty("responseTimeMs")
        private Long responseTimeMs;
        
        @JsonProperty("message")
        private String message;
        
        @JsonProperty("promptTokens")
        private Integer promptTokens;
        
        @JsonProperty("completionTokens")
        private Integer completionTokens;
        
        @JsonProperty("totalTokens")
        private Integer totalTokens;
        
        @JsonProperty("finishReason")
        private String finishReason;
        
        @JsonProperty("additionalInfo")
        private Map<String, Object> additionalInfo;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProviderData {
        @JsonProperty("modelInformation")
        private String modelInformation;
        
        @JsonProperty("toolSuggestions")
        private Map<String, Object> toolSuggestions;
        
        @JsonProperty("chainOfThought")
        private Map<String, Object> chainOfThought;
    }
} 