package io.sx.ai.consumer;

import io.nats.client.Connection;
import io.nats.client.ConsumerContext;
import io.nats.client.JetStream;
import io.nats.client.JetStreamApiException;
import io.nats.client.JetStreamManagement;
import io.nats.client.StreamContext;
import io.nats.client.api.AckPolicy;
import io.nats.client.api.ConsumerConfiguration;
import io.nats.client.api.DeliverPolicy;
import io.nats.client.api.ReplayPolicy;
import io.nats.client.api.StreamConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
@RequiredArgsConstructor
public class NatsConnectionManagerImpl implements NatsConnectionManager {
    private final Connection natsConnection;

    @Value("${nats.jetstream.audit.stream.name:sx-audit-stream}")
    private String streamName;

    @Value("${nats.jetstream.audit.stream.subjects:sx.audit.events.>}")
    private String streamSubjects;

    @Value("${nats.jetstream.audit.consumer.name:ai-consumer}")
    private String consumerName;

    @Value("${nats.jetstream.audit.consumer.durable:true}")
    private boolean durable;

    @Value("${nats.jetstream.audit.consumer.ack-policy:Explicit}")
    private String ackPolicy;

    @Value("${nats.jetstream.audit.consumer.deliver-policy:All}")
    private String deliverPolicy;

    @Value("${nats.jetstream.audit.consumer.replay-policy:Instant}")
    private String replayPolicy;

    @Value("${nats.consumer.max-reconnect-attempts:5}")
    private int maxReconnectAttempts;

    @Value("${nats.consumer.reconnect-wait-ms:1000}")
    private long reconnectWaitMs;

    private JetStream jetStream;

    @Override
    public void setupJetStream() {
        try {
            jetStream = natsConnection.jetStream();
            createJetStreamIfNotExists();
            log.info("JetStream setup completed for stream: {}", streamName);
        } catch (Exception e) {
            log.error("Error setting up JetStream: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to setup JetStream", e);
        }
    }

    @Override
    public ConsumerContext createConsumer() {
        try {
            ConsumerConfiguration consumerConfig = ConsumerConfiguration.builder()
                    .durable(durable ? consumerName : null)
                    .ackPolicy(AckPolicy.valueOf(ackPolicy))
                    .deliverPolicy(DeliverPolicy.valueOf(deliverPolicy))
                    .replayPolicy(ReplayPolicy.valueOf(replayPolicy))
                    .deliverGroup(consumerName)
                    .build();

            StreamContext streamContext = jetStream.getStreamContext(streamName);
            ConsumerContext consumerContext = streamContext.createOrUpdateConsumer(consumerConfig);
            log.info("Created AI consumer subscription for stream: {}", streamName);
            return consumerContext;
        } catch (Exception e) {
            log.error("Error creating consumer: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create consumer", e);
        }
    }

    @Override
    public void handleConnectionError() {
        int attempts = 0;
        while (attempts < maxReconnectAttempts) {
            try {
                log.info("Attempting to reconnect to NATS (attempt {}/{})", attempts + 1, maxReconnectAttempts);
                Thread.sleep(reconnectWaitMs);
                setupJetStream();
                log.info("Successfully reconnected to NATS");
                return;
            } catch (Exception e) {
                attempts++;
                log.error("Reconnection attempt {} failed: {}", attempts, e.getMessage());
            }
        }
        log.error("Failed to reconnect after {} attempts", maxReconnectAttempts);
    }

    @Override
    public JetStream getJetStream() {
        return jetStream;
    }

    private void createJetStreamIfNotExists() throws IOException, JetStreamApiException {
        try {
            StreamConfiguration streamConfig = StreamConfiguration.builder()
                    .name(streamName)
                    .subjects(streamSubjects)
                    .build();
            JetStreamManagement jsm = natsConnection.jetStreamManagement();
            jsm.addStream(streamConfig);
            log.info("Created AI stream: {}", streamName);
        } catch (JetStreamApiException e) {
            if (e.getErrorCode() == 400) {
                log.info("AI stream {} already exists", streamName);
            } else {
                throw e;
            }
        }
    }
} 