package io.sx.ai.springai.spring;

import io.sx.ai.abstraction.SxMessage;
import io.sx.ai.abstraction.SxPrompt;
import lombok.Data;
import org.springframework.ai.chat.prompt.Prompt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Spring AI implementation of SxPrompt
 * Wraps Spring AIs Prompt class
 */
@Data
public class SxSpringPrompt implements SxPrompt {
    
    private final Prompt springPrompt;
    private final List<SxMessage> messages;
    private final Map<String, Object> metadata;
    private final PromptType type;
    
    public SxSpringPrompt(Prompt springPrompt) {
        this.springPrompt = springPrompt;
        this.messages = new ArrayList<>();
        this.metadata = new HashMap<>();
        this.type = PromptType.CHAT;
        
        // Add all instructions as SxSpringMessage
        if (springPrompt.getInstructions() != null) {
            this.messages.addAll(springPrompt.getInstructions().stream()
                .map(SxSpringMessage::new)
                .collect(Collectors.toList()));
        }
        
        // Add Spring AI specific metadata
        this.metadata.put("spring_ai_prompt_type", "conversation");
        this.metadata.put("message_count", this.messages.size());
    }
    
    @Override
    public List<SxMessage> getMessages() {
        return new ArrayList<>(messages);
    }
    
    @Override
    public String getText() {
        // Combine all message contents
        return messages.stream()
            .map(SxMessage::getContent)
            .filter(content -> content != null && !content.trim().isEmpty())
            .collect(Collectors.joining("\n"));
    }
    
    @Override
    public Map<String, Object> getMetadata() {
        return new HashMap<>(metadata);
    }
    
    @Override
    public SxPrompt addMessage(SxMessage message) {
        this.messages.add(message);
        this.metadata.put("message_count", this.messages.size());
        return this;
    }
    
    @Override
    public SxPrompt addMessages(List<SxMessage> messages) {
        this.messages.addAll(messages);
        this.metadata.put("message_count", this.messages.size());
        return this;
    }
    
    @Override
    public PromptType getType() {
        return type;
    }
    
    public boolean isEmpty() {
        return messages.isEmpty();
    }
    
    public int getMessageCount() {
        return messages.size();
    }
    
    /**
     * Get the underlying Spring AI Prompt
     */
    public Prompt getSpringPrompt() {
        return springPrompt;
    }
    
    /**
     * Create a simple prompt from text
     */
    public static SxSpringPrompt fromText(String text) {
        Prompt springPrompt = new Prompt(text);
        return new SxSpringPrompt(springPrompt);
    }
    
    /**
     * Create a prompt from messages
     */
    public static SxSpringPrompt fromMessages(List<SxMessage> messages) {
        List<org.springframework.ai.chat.messages.Message> springMessages = messages.stream()
            .map(msg -> {
                if (msg instanceof SxSpringMessage) {
                    return ((SxSpringMessage) msg).getSpringMessage();
                }
                // Convert generic SxMessage to Spring Message
                return new org.springframework.ai.chat.messages.UserMessage(msg.getContent());
            })
            .collect(Collectors.toList());
        
        Prompt springPrompt = new Prompt(springMessages);
        return new SxSpringPrompt(springPrompt);
    }
} 