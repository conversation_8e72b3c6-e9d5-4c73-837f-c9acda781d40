package io.sx.ai.abstraction;

import java.util.List;
import java.util.Map;

/**
 * Interface for resolved prompts that have been processed and are ready for LLM consumption.
 * All prompts must be resolved before being sent to the LLM to ensure proper variable substitution.
 */
public interface ResolvedPrompt {
    
    /**
     * Get the fully resolved text content of the prompt
     */
    String getResolvedText();
    
    /**
     * Get the resolved messages in this prompt
     */
    List<SxMessage> getResolvedMessages();
    
    /**
     * Get the original variables used for resolution
     */
    Map<String, Object> getOriginalVariables();
    
    /**
     * Get the prompt type (CHAT, COMPLETION, STRUCTURED_OUTPUT, etc.)
     */
    SxPrompt.PromptType getPromptType();
    
    /**
     * Get the primary message role (SYSTEM, USER, ASSISTANT)
     * This represents the role of the main message in this resolved prompt
     */
    SxMessage.PromptRole getMessageRole();
    
    /**
     * Check if the prompt is fully resolved (no unresolved variables)
     */
    boolean isFullyResolved();
    
    /**
     * Get the original template source for debugging purposes
     */
    String getTemplateSource();
    
    /**
     * Get metadata associated with this resolved prompt
     */
    Map<String, Object> getMetadata();

    /**
     * Get a human-readable description of this resolved prompt
     */
    String getDescription();
} 