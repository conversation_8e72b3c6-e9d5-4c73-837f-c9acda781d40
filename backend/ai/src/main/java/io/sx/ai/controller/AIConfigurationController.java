package io.sx.ai.controller;

import io.sx.ai.service.AIConfigurationService;
import io.sx.ai.model.AIConfiguration;
import io.sx.ai.model.AISuggestion;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * REST controller for AI configuration management
 * Provides endpoints for managing AI configurations, autonomy settings, and capabilities
 * for support organization administrators
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/config")
@RequiredArgsConstructor
public class AIConfigurationController {

    private final AIConfigurationService aiConfigurationService;
    private final ObjectMapper objectMapper;

    /**
     * Get AI configuration for organization
     */
    @GetMapping("/{supportOrgId}")
    public ResponseEntity<Map<String, Object>> getConfiguration(@PathVariable Long supportOrgId) {
        log.info("Getting AI configuration for org: {}", supportOrgId);
        
        try {
            Map<String, Object> config = aiConfigurationService.getConfiguration("classification", supportOrgId);
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            log.error("Error getting AI configuration", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Update AI configuration for organization
     */
    @PutMapping("/{supportOrgId}")
    public ResponseEntity<AIConfiguration> updateConfiguration(
            @PathVariable Long supportOrgId,
            @RequestBody Map<String, Object> config) {

        log.info("Updating AI configuration for org: {}", supportOrgId);

        try {
            // Create AI configuration object
            AIConfiguration aiConfig = AIConfiguration.builder()
                .organizationId(supportOrgId)
                .configurationType("general")
                .supportOrganizationId(supportOrgId)
                .configurationData(objectMapper.valueToTree(config))
                .isActive(true)
                .build();

            AIConfiguration savedConfig = aiConfigurationService.saveConfiguration(aiConfig);
            return ResponseEntity.ok(savedConfig);
        } catch (Exception e) {
            log.error("Error updating AI configuration", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get all AI configurations for an organization
     */
    @GetMapping("/all/{supportOrgId}")
    public ResponseEntity<List<AIConfiguration>> getAllConfigurations(@PathVariable Long supportOrgId) {
        log.info("Getting all AI configurations for org: {}", supportOrgId);

        try {
            List<AIConfiguration> configurations = aiConfigurationService.getConfigurationsByOrganization(supportOrgId);
            return ResponseEntity.ok(configurations);
        } catch (Exception e) {
            log.error("Error getting AI configurations", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get autonomy configuration for an organization (includes capabilities)
     */
    @GetMapping("/autonomy/{organizationId}")
    public ResponseEntity<Map<String, Object>> getAutonomyConfiguration(@PathVariable Long organizationId) {
        log.info("Getting autonomy configuration for organization: {}", organizationId);

        try {
            Map<String, Object> autonomyConfig = aiConfigurationService.getConfiguration("autonomy", organizationId);
            Map<String, Object> capabilitiesConfig = aiConfigurationService.getConfiguration("capabilities", organizationId);

            // If no autonomy configuration exists, return default configuration
            if (autonomyConfig.isEmpty()) {
                autonomyConfig = createDefaultAutonomyConfiguration(organizationId);
            }

            // If no capabilities configuration exists, return default configuration
            if (capabilitiesConfig.isEmpty()) {
                capabilitiesConfig = createDefaultCapabilitiesConfiguration();
            }

            // Combine autonomy and capabilities into a single response
            Map<String, Object> combinedConfig = new HashMap<>(autonomyConfig);
            combinedConfig.put("capabilities", capabilitiesConfig);

            return ResponseEntity.ok(combinedConfig);
        } catch (Exception e) {
            log.error("Error getting autonomy configuration", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Update autonomy configuration for an organization
     */
    @PutMapping("/autonomy/{organizationId}")
    public ResponseEntity<AIConfiguration> updateAutonomyConfiguration(
            @PathVariable Long organizationId,
            @RequestBody Map<String, Object> autonomyConfig) {

        log.info("Updating autonomy configuration for organization: {}", organizationId);

        try {
            // Check if configuration already exists
            AIConfiguration existingConfig = aiConfigurationService.findByTypeAndOrganization(organizationId, "autonomy");
            
            AIConfiguration aiConfig;
            if (existingConfig != null) {
                // Update existing configuration
                aiConfig = AIConfiguration.builder()
                    .id(existingConfig.getId()) // Set ID to trigger update instead of insert
                    .organizationId(organizationId)
                    .configurationType("autonomy")
                    .supportOrganizationId(organizationId)
                    .configurationData(objectMapper.valueToTree(autonomyConfig))
                    .isActive(true)
                    .createdAt(existingConfig.getCreatedAt())
                    .createdById(existingConfig.getCreatedById())
                    .createdBy(existingConfig.getCreatedBy())
                    .build();
            } else {
                // Create new configuration
                aiConfig = AIConfiguration.builder()
                    .organizationId(organizationId)
                    .configurationType("autonomy")
                    .supportOrganizationId(organizationId)
                    .configurationData(objectMapper.valueToTree(autonomyConfig))
                    .isActive(true)
                    .build();
            }
            
            log.debug("Created AIConfiguration with organizationId: {}, supportOrganizationId: {}, id: {}", 
                aiConfig.getOrganizationId(), aiConfig.getSupportOrganizationId(), aiConfig.getId());

            AIConfiguration savedConfig = aiConfigurationService.saveConfiguration(aiConfig);
            return ResponseEntity.ok(savedConfig);
        } catch (Exception e) {
            log.error("Error updating autonomy configuration", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get AI capabilities configuration for an organization
     */
    @GetMapping("/capabilities/{organizationId}")
    public ResponseEntity<Map<String, Object>> getCapabilitiesConfiguration(@PathVariable Long organizationId) {
        log.info("Getting AI capabilities configuration for organization: {}", organizationId);

        try {
            Map<String, Object> capabilitiesConfig = aiConfigurationService.getConfiguration("capabilities", organizationId);

            // If no configuration exists, return default configuration
            if (capabilitiesConfig.isEmpty()) {
                capabilitiesConfig = createDefaultCapabilitiesConfiguration();
            }

            return ResponseEntity.ok(capabilitiesConfig);
        } catch (Exception e) {
            log.error("Error getting capabilities configuration", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Update AI capabilities configuration for an organization
     */
    @PutMapping("/capabilities/{organizationId}")
    public ResponseEntity<AIConfiguration> updateCapabilitiesConfiguration(
            @PathVariable Long organizationId,
            @RequestBody Map<String, Object> capabilitiesConfig) {

        log.info("Updating AI capabilities configuration for organization: {}", organizationId);

        try {
            AIConfiguration aiConfig = AIConfiguration.builder()
                .organizationId(organizationId)
                .configurationType("capabilities")
                .supportOrganizationId(organizationId)
                .configurationData(objectMapper.valueToTree(capabilitiesConfig))
                .isActive(true)
                .build();

            AIConfiguration savedConfig = aiConfigurationService.saveConfiguration(aiConfig);
            return ResponseEntity.ok(savedConfig);
        } catch (Exception e) {
            log.error("Error updating capabilities configuration", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get available suggestion types and autonomy levels
     */
    @GetMapping("/suggestion-types")
    public ResponseEntity<Map<String, Object>> getSuggestionTypes() {
        log.info("Getting available suggestion types and autonomy levels");

        try {
            Map<String, Object> response = new HashMap<>();

            // Get all suggestion types
            List<String> suggestionTypes = Arrays.stream(AISuggestion.SuggestionType.values())
                .map(Enum::name)
                .toList();

            // Get all autonomy levels
            List<String> autonomyLevels = Arrays.stream(AISuggestion.AutonomyLevel.values())
                .map(Enum::name)
                .toList();

            response.put("suggestionTypes", suggestionTypes);
            response.put("autonomyLevels", autonomyLevels);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting suggestion types", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Health check endpoint for AI configuration service
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = Map.of(
            "status", "healthy",
            "service", "ai-configuration",
            "timestamp", java.time.LocalDateTime.now().toString(),
            "description", "AI Configuration Management Service"
        );
        return ResponseEntity.ok(health);
    }

    // Helper methods

    private Map<String, Object> createDefaultAutonomyConfiguration(Long organizationId) {
        Map<String, Object> config = new HashMap<>();
        config.put("organizationId", organizationId);
        config.put("enabled", true);
        config.put("description", "Default autonomy configuration");

                    // Default autonomy levels for each suggestion type
            Map<String, String> suggestionTypeAutonomy = new HashMap<>();
            suggestionTypeAutonomy.put("ASSIGNMENT", "HITL");
            suggestionTypeAutonomy.put("PRIORITY_ADJUSTMENT", "HITL");
            suggestionTypeAutonomy.put("ESCALATION", "ADVISOR");
            suggestionTypeAutonomy.put("NOTIFICATION", "FULL_AUTONOMY");
            suggestionTypeAutonomy.put("STATUS_UPDATE", "HITL");
            suggestionTypeAutonomy.put("COMMENT_ADDITION", "FULL_AUTONOMY");
            suggestionTypeAutonomy.put("SLA_ALERT", "HITL");
            suggestionTypeAutonomy.put("PROCESS_IMPROVEMENT", "ADVISOR");
            suggestionTypeAutonomy.put("CUSTOMER_COMMUNICATION", "HITL");
            suggestionTypeAutonomy.put("RESOURCE_ALLOCATION", "ADVISOR");
            suggestionTypeAutonomy.put("CLASSIFICATION", "HITL");
            // New capabilities with AI_DISABLED as default for some
            suggestionTypeAutonomy.put("TICKET_CLASSIFICATION", "AI_DISABLED");
            suggestionTypeAutonomy.put("PRIORITY_ASSESSMENT", "AI_DISABLED");
            suggestionTypeAutonomy.put("AUTO_ASSIGNMENT", "AI_DISABLED");
            suggestionTypeAutonomy.put("RESPONSE_GENERATION", "AI_DISABLED");
            suggestionTypeAutonomy.put("SENTIMENT_ANALYSIS", "AI_DISABLED");
            suggestionTypeAutonomy.put("ESCALATION_PREDICTION", "AI_DISABLED");
            suggestionTypeAutonomy.put("KNOWLEDGE_BASE_SEARCH", "AI_DISABLED");
            suggestionTypeAutonomy.put("WORKFLOW_AUTOMATION", "AI_DISABLED");
            suggestionTypeAutonomy.put("QUALITY_ASSURANCE", "AI_DISABLED");
            suggestionTypeAutonomy.put("PERFORMANCE_ANALYTICS", "AI_DISABLED");

        config.put("suggestionTypeAutonomy", suggestionTypeAutonomy);

        // Default confidence thresholds
        Map<String, Double> confidenceThresholds = new HashMap<>();
        confidenceThresholds.put("fullAutonomy", 0.9);
        confidenceThresholds.put("hitl", 0.7);
        confidenceThresholds.put("advisor", 0.5);

        config.put("confidenceThresholds", confidenceThresholds);

        return config;
    }

    private Map<String, Object> createDefaultCapabilitiesConfiguration() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", true);
        config.put("ticketClassification", true);
        config.put("priorityAssessment", true);
        config.put("autoAssignment", true);
        config.put("responseGeneration", true);
        config.put("sentimentAnalysis", true);
        config.put("escalationPrediction", true);
        config.put("knowledgeBaseSearch", true);
        config.put("workflowAutomation", true);
        config.put("qualityAssurance", true);
        config.put("performanceAnalytics", true);

        return config;
    }
} 