package io.sx.ai.springai.framework.common;

import lombok.Data;

/**
 * Structured output for LLM decisions in the dynamic workflow
 * Used with Spring AI's structured output capabilities
 */
@Data
public class LLMDecision {
    
    /**
     * The decision made by the LLM
     */
    private DecisionType decision;
    
    /**
     * Detailed reasoning for the decision
     */
    private String reasoning;
    
    /**
     * Confidence score (0.0 to 1.0)
     */
    private Double confidence;
    
    /**
     * Tools to execute (if decision is CONTINUE)
     */
    private String[] toolsToExecute;
    
    /**
     * Final result (if decision is TERMINATE)
     */
    private Double finalResult;
    
    /**
     * Reason for termination (if decision is TERMINATE)
     */
    private String terminationReason;
    
    public enum DecisionType {
        CONTINUE,    // Continue with tool execution
        TERMINATE    // Terminate workflow
    }
    
    /**
     * Create a continue decision
     */
    public static LLMDecision continueWithTools(String reasoning, Double confidence, String... tools) {
        LLMDecision decision = new LLMDecision();
        decision.setDecision(DecisionType.CONTINUE);
        decision.setReasoning(reasoning);
        decision.setConfidence(confidence);
        decision.setToolsToExecute(tools);
        return decision;
    }
    
    /**
     * Create a terminate decision
     */
    public static LLMDecision terminate(String reasoning, Double confidence, Double finalResult, String terminationReason) {
        LLMDecision decision = new LLMDecision();
        decision.setDecision(DecisionType.TERMINATE);
        decision.setReasoning(reasoning);
        decision.setConfidence(confidence);
        decision.setFinalResult(finalResult);
        decision.setTerminationReason(terminationReason);
        return decision;
    }
    
    /**
     * Check if the decision is to continue
     */
    public boolean isContinue() {
        return decision == DecisionType.CONTINUE;
    }
    
    /**
     * Check if the decision is to terminate
     */
    public boolean isTerminate() {
        return decision == DecisionType.TERMINATE;
    }
    
    /**
     * Get a human-readable description of the decision
     */
    public String getDescription() {
        if (isContinue()) {
            return String.format("Continue with tools: %s (confidence: %.2f)", 
                String.join(", ", toolsToExecute), confidence);
        } else {
            return String.format("Terminate: %s (final result: %s, confidence: %.2f)", 
                terminationReason, finalResult, confidence);
        }
    }
} 