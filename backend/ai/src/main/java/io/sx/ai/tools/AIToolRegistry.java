package io.sx.ai.tools;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Registry for all AI tools with approval management based on autonomy mode
 * Manages tool discovery, categorization, and approval requirements
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AIToolRegistry {
    
    private final List<AITool> allTools;
    private final Map<String, AITool> toolById = new ConcurrentHashMap<>();
    private final Map<ToolCategory, List<AITool>> toolsByCategory = new ConcurrentHashMap<>();
    
    /**
     * Initialize the tool registry
     */
    public void initialize() {
        log.info("Initializing AI Tool Registry with {} tools", allTools.size());
        
        // Index tools by ID and category
        for (AITool tool : allTools) {
            toolById.put(tool.getToolId(), tool);
            
            ToolCategory category = tool.getToolCategory();
            toolsByCategory.computeIfAbsent(category, k -> new java.util.ArrayList<>()).add(tool);
            
            log.debug("Registered tool: {} ({}) in category: {}", 
                     tool.getToolName(), tool.getToolId(), category.getCategoryId());
        }
        
        // Log summary by category
        for (Map.Entry<ToolCategory, List<AITool>> entry : toolsByCategory.entrySet()) {
            log.info("Category {}: {} tools", entry.getKey().getCategoryId(), entry.getValue().size());
        }
    }
    
    /**
     * Get all available tools
     */
    public List<AITool> getAllTools() {
        return allTools;
    }
    
    /**
     * Get tool by ID
     */
    public Optional<AITool> getToolById(String toolId) {
        return Optional.ofNullable(toolById.get(toolId));
    }
    
    /**
     * Get tools by category
     */
    public List<AITool> getToolsByCategory(ToolCategory category) {
        return toolsByCategory.getOrDefault(category, List.of());
    }
    
    /**
     * Get tools that require approval in the given autonomy mode
     */
    public List<AITool> getToolsRequiringApproval(ToolCategory.AIAutonomyMode autonomyMode) {
        return allTools.stream()
                .filter(tool -> tool.requiresApproval(autonomyMode))
                .collect(Collectors.toList());
    }
    
    /**
     * Get tools that can be executed without approval in the given autonomy mode
     */
    public List<AITool> getToolsNotRequiringApproval(ToolCategory.AIAutonomyMode autonomyMode) {
        return allTools.stream()
                .filter(tool -> !tool.requiresApproval(autonomyMode))
                .collect(Collectors.toList());
    }
    
    /**
     * Get system tools (information gathering, analysis)
     */
    public List<AITool> getSystemTools() {
        return getToolsByCategory(ToolCategory.SYSTEM_TOOLS);
    }
    
    /**
     * Get business tools (ticket operations, assignments)
     */
    public List<AITool> getBusinessTools() {
        return getToolsByCategory(ToolCategory.BUSINESS_TOOLS);
    }
    
    /**
     * Get critical tools (high-impact operations)
     */
    public List<AITool> getCriticalTools() {
        return getToolsByCategory(ToolCategory.CRITICAL_TOOLS);
    }
    
    /**
     * Check if a tool requires approval in the given autonomy mode
     */
    public boolean requiresApproval(String toolId, ToolCategory.AIAutonomyMode autonomyMode) {
        return getToolById(toolId)
                .map(tool -> tool.requiresApproval(autonomyMode))
                .orElse(true); // Default to requiring approval if tool not found
    }
    
    /**
     * Get MCP tool schemas for all tools
     */
    public Map<String, AITool.ToolSchema> getAllToolSchemas() {
        return allTools.stream()
                .collect(Collectors.toMap(
                    AITool::getToolId,
                    AITool::getToolSchema
                ));
    }
    
    /**
     * Get MCP tool schemas for tools that don't require approval in the given mode
     */
    public Map<String, AITool.ToolSchema> getAvailableToolSchemas(ToolCategory.AIAutonomyMode autonomyMode) {
        return getToolsNotRequiringApproval(autonomyMode).stream()
                .collect(Collectors.toMap(
                    AITool::getToolId,
                    AITool::getToolSchema
                ));
    }
    
    /**
     * Execute a tool with approval checking
     */
    public AITool.ToolResult executeTool(String toolId, Map<String, Object> parameters, 
                                       AITool.ToolContext context) {
        Optional<AITool> toolOpt = getToolById(toolId);
        if (toolOpt.isEmpty()) {
            return AITool.ToolResult.error("Tool not found: " + toolId);
        }
        
        AITool tool = toolOpt.get();
        
        // Check if tool requires approval in current mode
        if (tool.requiresApproval(context.getAutonomyMode())) {
            return AITool.ToolResult.error(
                "Tool " + tool.getToolName() + " requires approval in " + 
                context.getAutonomyMode().getModeId() + " mode"
            );
        }
        
        // Check if tool can be executed in current context
        if (!tool.canExecute(context)) {
            return AITool.ToolResult.error(
                "Tool " + tool.getToolName() + " cannot be executed in current context"
            );
        }
        
        // Execute the tool
        log.info("Executing tool: {} ({}) in {} mode", 
                tool.getToolName(), toolId, context.getAutonomyMode().getModeId());
        
        return tool.execute(parameters, context);
    }
    
    /**
     * Get tool execution statistics
     */
    public ToolRegistryStats getStats() {
        Map<ToolCategory, Integer> toolsPerCategory = toolsByCategory.entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().size()
                ));
        
        return new ToolRegistryStats(
            allTools.size(),
            toolsPerCategory,
            toolsByCategory.keySet().size()
        );
    }
    
    /**
     * Tool registry statistics
     */
    public static class ToolRegistryStats {
        private final int totalTools;
        private final Map<ToolCategory, Integer> toolsPerCategory;
        private final int totalCategories;
        
        public ToolRegistryStats(int totalTools, Map<ToolCategory, Integer> toolsPerCategory, int totalCategories) {
            this.totalTools = totalTools;
            this.toolsPerCategory = toolsPerCategory;
            this.totalCategories = totalCategories;
        }
        
        // Getters
        public int getTotalTools() { return totalTools; }
        public Map<ToolCategory, Integer> getToolsPerCategory() { return toolsPerCategory; }
        public int getTotalCategories() { return totalCategories; }
    }
} 