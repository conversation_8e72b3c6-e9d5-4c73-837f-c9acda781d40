package io.sx.ai.service.impl;

import io.sx.ai.service.CustomerImportanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Implementation of CustomerImportanceService
 * Analyzes customer importance based on various business factors
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerImportanceServiceImpl implements CustomerImportanceService {

    @Override
    public Double getCustomerImportance(Long organizationId, Long supportOrgId) {
        log.debug("Getting customer importance for org: {}, support org: {}", organizationId, supportOrgId);
        
        try {
            // For now, use mock data based on organization ID
            // In production, this would analyze:
            // - Contract value
            // - Revenue contribution
            // - Strategic importance
            // - Historical relationship
            // - Current satisfaction scores
            
            if (organizationId == null) {
                return 5.0; // Default medium importance
            }
            
            // Mock importance scores based on org ID
            // In real implementation, this would query customer data
            if (organizationId == 6L) {
                return 8.5; // High importance customer
            } else if (organizationId % 3 == 0) {
                return 7.0; // Above average importance
            } else if (organizationId % 2 == 0) {
                return 6.0; // Average importance
            } else {
                return 4.5; // Below average importance
            }
            
        } catch (Exception e) {
            log.error("Error getting customer importance for org: {}", organizationId, e);
            return 5.0; // Default to medium importance on error
        }
    }

    @Override
    public Map<String, Object> getCustomerImportanceAnalysis(Long organizationId, Long supportOrgId) {
        log.debug("Getting detailed customer importance analysis for org: {}", organizationId);
        
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            Double importanceScore = getCustomerImportance(organizationId, supportOrgId);
            
            analysis.put("importance_score", importanceScore);
            analysis.put("importance_level", getImportanceLevel(importanceScore));
            analysis.put("customer_tier", getCustomerTier(importanceScore));
            analysis.put("priority_adjustment", getPriorityAdjustment(importanceScore));
            analysis.put("sla_multiplier", getSLAMultiplier(importanceScore));
            analysis.put("escalation_threshold", getEscalationThreshold(importanceScore));
            
            // Mock additional factors
            analysis.put("contract_value", getMockContractValue(organizationId));
            analysis.put("revenue_contribution", getMockRevenueContribution(organizationId));
            analysis.put("strategic_importance", getMockStrategicImportance(organizationId));
            analysis.put("relationship_duration_months", getMockRelationshipDuration(organizationId));
            analysis.put("satisfaction_score", getMockSatisfactionScore(organizationId));
            
        } catch (Exception e) {
            log.error("Error getting customer importance analysis for org: {}", organizationId, e);
            analysis.put("error", "Failed to analyze customer importance: " + e.getMessage());
        }
        
        return analysis;
    }

    /**
     * Helper methods for importance analysis
     */
    private String getImportanceLevel(Double score) {
        if (score >= 8.0) return "CRITICAL";
        if (score >= 6.0) return "HIGH";
        if (score >= 4.0) return "MEDIUM";
        return "LOW";
    }

    private String getCustomerTier(Double score) {
        if (score >= 8.0) return "PREMIUM";
        if (score >= 6.0) return "STANDARD";
        if (score >= 4.0) return "BASIC";
        return "STARTER";
    }

    private String getPriorityAdjustment(Double score) {
        if (score >= 8.0) return "INCREASE";
        if (score >= 6.0) return "MAINTAIN";
        return "DECREASE";
    }

    private Double getSLAMultiplier(Double score) {
        if (score >= 8.0) return 0.5; // 50% faster SLA
        if (score >= 6.0) return 0.75; // 25% faster SLA
        return 1.0; // Standard SLA
    }

    private Integer getEscalationThreshold(Double score) {
        if (score >= 8.0) return 2; // Escalate after 2 hours
        if (score >= 6.0) return 4; // Escalate after 4 hours
        return 8; // Escalate after 8 hours
    }

    /**
     * Mock data methods (replace with real data in production)
     */
    private String getMockContractValue(Long organizationId) {
        if (organizationId == 6L) return "$500,000";
        if (organizationId % 3 == 0) return "$250,000";
        if (organizationId % 2 == 0) return "$100,000";
        return "$50,000";
    }

    private String getMockRevenueContribution(Long organizationId) {
        if (organizationId == 6L) return "15%";
        if (organizationId % 3 == 0) return "8%";
        if (organizationId % 2 == 0) return "3%";
        return "1%";
    }

    private String getMockStrategicImportance(Long organizationId) {
        if (organizationId == 6L) return "Reference Customer";
        if (organizationId % 3 == 0) return "Growth Potential";
        if (organizationId % 2 == 0) return "Stable";
        return "Standard";
    }

    private Integer getMockRelationshipDuration(Long organizationId) {
        if (organizationId == 6L) return 24;
        if (organizationId % 3 == 0) return 18;
        if (organizationId % 2 == 0) return 12;
        return 6;
    }

    private Double getMockSatisfactionScore(Long organizationId) {
        if (organizationId == 6L) return 9.2;
        if (organizationId % 3 == 0) return 8.5;
        if (organizationId % 2 == 0) return 7.8;
        return 7.0;
    }
} 