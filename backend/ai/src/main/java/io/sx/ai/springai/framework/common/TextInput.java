package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Text input wrapper for String values with Spring AI integration
 * Supports text analysis, sentiment analysis, and content processing
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextInput implements WorkflowInput<String> {
    
    /**
     * The text value
     */
    private String value;
    
    /**
     * Language code (e.g., "en", "es", "fr")
     */
    private String language;
    
    /**
     * Character encoding (e.g., "UTF-8", "ISO-8859-1")
     */
    private String encoding;
    
    /**
     * Content type (e.g., "plain", "markdown", "html", "json")
     */
    private String contentType;
    
    /**
     * Additional metadata
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    @Override
    public String getType() {
        return "TEXT";
    }
    
    @Override
    public Map<String, Object> getMetadata() {
        Map<String, Object> enhancedMetadata = new HashMap<>(metadata);
        enhancedMetadata.put("language", language);
        enhancedMetadata.put("encoding", encoding);
        enhancedMetadata.put("contentType", contentType);
        return enhancedMetadata;
    }
    
    @Override
    public Prompt createPrompt(String template, Map<String, Object> variables) {
        Map<String, Object> enhancedVariables = new HashMap<>(variables);
        enhancedVariables.put("textValue", value);
        enhancedVariables.put("language", language);
        enhancedVariables.put("encoding", encoding);
        enhancedVariables.put("contentType", contentType);
        enhancedVariables.put("inputValue", value);
        enhancedVariables.put("inputType", getType());
        enhancedVariables.putAll(metadata);
        
        PromptTemplate promptTemplate = new PromptTemplate(template);
        return promptTemplate.create(enhancedVariables);
    }
    
    /**
     * Create a structured prompt for text analysis
     */
    public Prompt createTextAnalysisPrompt() {
        String template = """
            Analyze the following text and provide structured analysis:
            Text: {textValue}
            Language: {language}
            Content Type: {contentType}
            
            Return the analysis as a JSON object with:
            - analysis: detailed text analysis
            - sentiment: overall sentiment (positive/negative/neutral)
            - confidence: confidence score (0.0 to 1.0)
            - keywords: list of key terms found
            - wordCount: total number of words
            - language: detected language if different from specified
            """;
        
        Map<String, Object> variables = Map.of(
            "textValue", value,
            "language", language != null ? language : "en",
            "contentType", contentType != null ? contentType : "plain"
        );
        
        return createPrompt(template, variables);
    }
    
    /**
     * Create a structured prompt for sentiment analysis
     */
    public Prompt createSentimentAnalysisPrompt() {
        String template = """
            Analyze the sentiment of the following text:
            Text: {textValue}
            Language: {language}
            
            Return the sentiment analysis as a JSON object with:
            - sentiment: overall sentiment (positive/negative/neutral)
            - confidence: confidence score (0.0 to 1.0)
            - explanation: brief explanation of the sentiment
            - intensity: sentiment intensity (low/medium/high)
            - emotions: list of detected emotions
            """;
        
        Map<String, Object> variables = Map.of(
            "textValue", value,
            "language", language != null ? language : "en"
        );
        
        return createPrompt(template, variables);
    }
    
    /**
     * Create a structured prompt for summary generation
     */
    public Prompt createSummaryPrompt(int maxWords) {
        String template = """
            Generate a concise summary of the following text:
            Text: {textValue}
            Language: {language}
            Maximum words: {maxWords}
            
            Return the summary as a JSON object with:
            - summary: the generated summary
            - wordCount: actual number of words in summary
            - confidence: confidence score (0.0 to 1.0)
            - keyPoints: list of main points covered
            """;
        
        Map<String, Object> variables = Map.of(
            "textValue", value,
            "language", language != null ? language : "en",
            "maxWords", maxWords
        );
        
        return createPrompt(template, variables);
    }
    
    /**
     * Static factory method for creating text inputs
     */
    public static TextInput of(String value) {
        return TextInput.builder()
            .value(value)
            .language("en")
            .encoding("UTF-8")
            .contentType("plain")
            .build();
    }
    
    /**
     * Static factory method for creating text inputs with language
     */
    public static TextInput of(String value, String language) {
        return TextInput.builder()
            .value(value)
            .language(language)
            .encoding("UTF-8")
            .contentType("plain")
            .build();
    }
    
    /**
     * Static factory method for creating markdown inputs
     */
    public static TextInput markdown(String value) {
        return TextInput.builder()
            .value(value)
            .language("en")
            .encoding("UTF-8")
            .contentType("markdown")
            .build();
    }
    
    /**
     * Static factory method for creating JSON inputs
     */
    public static TextInput json(String value) {
        return TextInput.builder()
            .value(value)
            .language("en")
            .encoding("UTF-8")
            .contentType("json")
            .build();
    }
} 