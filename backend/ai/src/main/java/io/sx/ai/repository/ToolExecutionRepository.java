package io.sx.ai.repository;

import io.sx.ai.model.ToolExecution;
import io.sx.repository.BaseRepository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for ToolExecution operations
 */
public interface ToolExecutionRepository extends BaseRepository<ToolExecution> {
    
    /**
     * Find all tool executions for a suggestion
     */
    List<ToolExecution> findBySuggestionId(Long suggestionId);
    
    /**
     * Find all tool executions for a specific tool within a suggestion
     */
    List<ToolExecution> findBySuggestionIdAndToolName(Long suggestionId, String toolName);
    
    /**
     * Find the latest tool execution for a specific tool within a suggestion
     */
    Optional<ToolExecution> findLatestBySuggestionIdAndToolName(Long suggestionId, String toolName);
    
    /**
     * Find all pending tool executions
     */
    List<ToolExecution> findByExecutionStatus(ToolExecution.ExecutionStatus status);
    
    /**
     * Find all tool executions scheduled for execution before a given time
     */
    List<ToolExecution> findByScheduledAtBefore(java.time.Instant before);
    
    /**
     * Find all tool executions ready for retry (next_attempt_at <= now)
     */
    List<ToolExecution> findReadyForRetry(java.time.Instant now);
    
    /**
     * Count successful tool executions for a suggestion
     */
    long countSuccessfulBySuggestionId(Long suggestionId);
    
    /**
     * Count failed tool executions for a suggestion
     */
    long countFailedBySuggestionId(Long suggestionId);
} 