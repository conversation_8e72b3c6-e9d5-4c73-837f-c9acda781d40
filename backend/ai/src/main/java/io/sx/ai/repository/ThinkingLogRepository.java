package io.sx.ai.repository;

import io.sx.ai.model.ThinkingLog;
import io.sx.repository.BaseRepository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for ThinkingLog entities
 */
public interface ThinkingLogRepository extends BaseRepository<ThinkingLog> {
    
    /**
     * Find thinking log by session ID
     */
    Optional<ThinkingLog> findBySessionId(String sessionId);
    
    /**
     * Find thinking logs by trigger entity
     */
    List<ThinkingLog> findByTriggerEntityTypeAndTriggerEntityId(String triggerEntityType, Long triggerEntityId);
    
    /**
     * Find thinking logs by status
     */
    List<ThinkingLog> findBySessionStatus(String sessionStatus);
    
    /**
     * Find thinking logs by strategy
     */
    List<ThinkingLog> findByThinkingStrategy(String thinkingStrategy);
    
    /**
     * Find active thinking logs
     */
    List<ThinkingLog> findBySessionStatusOrderByCreatedAtDesc(String sessionStatus);
} 