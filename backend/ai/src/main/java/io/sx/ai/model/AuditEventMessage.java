package io.sx.ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Represents an audit event message for tracking AI operations and decisions
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditEventMessage {
    
    private String eventId;
    private String eventType;
    private String source;
    private String target;
    private String action;
    private String status;
    private String description;
    private Map<String, Object> metadata;
    private LocalDateTime timestamp;
    private String userId;
    private Long organizationId;
    private Long supportOrganizationId;
    private String correlationId;
    private String sessionId;
    
    /**
     * Create a simple audit event message
     */
    public static AuditEventMessage simple(String eventType, String action, String description) {
        return AuditEventMessage.builder()
            .eventType(eventType)
            .action(action)
            .description(description)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    /**
     * Create an audit event message for AI operations
     */
    public static AuditEventMessage aiOperation(String action, String description, Long organizationId) {
        return AuditEventMessage.builder()
            .eventType("AI_OPERATION")
            .action(action)
            .description(description)
            .organizationId(organizationId)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    /**
     * Create an audit event message for ticket operations
     */
    public static AuditEventMessage ticketOperation(String action, String description, Long ticketId, Long organizationId) {
        return AuditEventMessage.builder()
            .eventType("TICKET_OPERATION")
            .action(action)
            .description(description)
            .target("TICKET:" + ticketId)
            .organizationId(organizationId)
            .timestamp(LocalDateTime.now())
            .build();
    }
} 