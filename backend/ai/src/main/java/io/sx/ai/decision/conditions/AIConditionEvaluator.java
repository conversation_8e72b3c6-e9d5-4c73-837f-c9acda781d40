package io.sx.ai.decision.conditions;

import io.sx.ai.decision.model.AIDecisionContext;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Evaluates conditions for AI decision rules
 */
@Component
public class AIConditionEvaluator {
    
    /**
     * Evaluate boolean condition from configuration
     */
    public boolean evaluateBoolean(String condition, Map<String, Object> context) {
        return (Boolean) context.getOrDefault(condition, false);
    }
    
    /**
     * Evaluate integer condition from configuration
     */
    public int evaluateInteger(String condition, Map<String, Object> context, int defaultValue) {
        Object value = context.get(condition);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }
    
    /**
     * Evaluate string condition from configuration
     */
    public String evaluateString(String condition, Map<String, Object> context, String defaultValue) {
        Object value = context.get(condition);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * Evaluate double condition from configuration
     */
    public double evaluateDouble(String condition, Map<String, Object> context, double defaultValue) {
        Object value = context.get(condition);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }
    
    /**
     * Evaluate complex boolean conditions
     */
    public boolean evaluateBoolean(String condition, AIDecisionContext context) {
        switch (condition) {
            case "ai.classification.has_sufficient_content":
                return hasSufficientContent(context);
            case "ai.classification.is_high_priority":
                return isHighPriority(context);
            case "ai.classification.is_important_customer":
                return isImportantCustomer(context);
            default:
                return false;
        }
    }
    
    /**
     * Check if ticket has sufficient content for classification
     */
    private boolean hasSufficientContent(AIDecisionContext context) {
        String description = extractDescription(context);
        if (description == null) {
            return false;
        }
        
        int minLength = evaluateInteger("min_description_length", 
            context.getAiConfiguration(), 10);
        
        return description.length() >= minLength;
    }
    
    /**
     * Check if ticket is high priority
     */
    private boolean isHighPriority(AIDecisionContext context) {
        String priority = extractPriority(context);
        return "HIGH".equals(priority) || "CRITICAL".equals(priority);
    }
    
    /**
     * Check if customer is important
     */
    private boolean isImportantCustomer(AIDecisionContext context) {
        Double importance = extractCustomerImportance(context);
        return importance != null && importance >= 7.0;
    }
    
    /**
     * Extract description from context
     */
    private String extractDescription(AIDecisionContext context) {
        if (context.getEntityData() != null) {
            Object description = context.getEntityData().get("description");
            return description != null ? description.toString() : null;
        }
        return null;
    }
    
    /**
     * Extract priority from context
     */
    private String extractPriority(AIDecisionContext context) {
        if (context.getEntityData() != null) {
            Object priority = context.getEntityData().get("priority");
            return priority != null ? priority.toString() : null;
        }
        return null;
    }
    
    /**
     * Extract customer importance from context
     */
    private Double extractCustomerImportance(AIDecisionContext context) {
        if (context.getEntityData() != null) {
            Object importance = context.getEntityData().get("customerImportance");
            if (importance instanceof Number) {
                return ((Number) importance).doubleValue();
            }
        }
        return null;
    }
} 