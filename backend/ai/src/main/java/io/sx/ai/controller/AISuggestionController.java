package io.sx.ai.controller;

import io.sx.ai.service.AISuggestionService;
import io.sx.ai.decision.strategies.EnhancedAIStrategy;
import io.sx.dto.AISuggestionDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for AI suggestions
 */
@Slf4j
@RestController
@RequestMapping("/api/suggestions")
@RequiredArgsConstructor
public class AISuggestionController {
    
    private final AISuggestionService aiSuggestionService;
    private final EnhancedAIStrategy enhancedAIStrategy;
    
    /**
     * Get all AI suggestions for the current organization
     */
    @GetMapping
    public ResponseEntity<List<AISuggestionDTO>> getSuggestions() {
        log.info("Getting all AI suggestions");
        
        try {
            List<AISuggestionDTO> suggestions = aiSuggestionService.findAll();
            return ResponseEntity.ok(suggestions);
        } catch (Exception e) {
            log.error("Error getting AI suggestions", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get AI suggestions for a specific ticket
     */
    @GetMapping("/ticket/{ticketId}")
    public ResponseEntity<List<AISuggestionDTO>> getSuggestionsForTicket(@PathVariable Long ticketId) {
        log.info("Getting AI suggestions for ticket: {}", ticketId);
        
        try {
            List<AISuggestionDTO> suggestions = aiSuggestionService.getSuggestionsForTicket(ticketId);
            return ResponseEntity.ok(suggestions);
        } catch (Exception e) {
            log.error("Error getting AI suggestions for ticket: {}", ticketId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get AI suggestions by status
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<List<AISuggestionDTO>> getSuggestionsByStatus(@PathVariable String status) {
        log.info("Getting AI suggestions by status: {}", status);
        
        try {
            List<AISuggestionDTO> suggestions = aiSuggestionService.getSuggestionsByStatus(status);
            return ResponseEntity.ok(suggestions);
        } catch (IllegalArgumentException e) {
            log.error("Invalid status: {}", status);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Error getting AI suggestions by status: {}", status, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get AI suggestions by type
     */
    @GetMapping("/type/{type}")
    public ResponseEntity<List<AISuggestionDTO>> getSuggestionsByType(@PathVariable String type) {
        log.info("Getting AI suggestions by type: {}", type);
        
        try {
            List<AISuggestionDTO> suggestions = aiSuggestionService.getSuggestionsByType(type);
            return ResponseEntity.ok(suggestions);
        } catch (IllegalArgumentException e) {
            log.error("Invalid type: {}", type);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Error getting AI suggestions by type: {}", type, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get a specific AI suggestion by ID
     */
    @GetMapping("/{suggestionId}")
    public ResponseEntity<AISuggestionDTO> getSuggestionById(@PathVariable String suggestionId) {
        log.info("Getting AI suggestion by ID: {}", suggestionId);
        
        try {
            AISuggestionDTO suggestion = aiSuggestionService.getBySuggestionId(suggestionId);
            return ResponseEntity.ok(suggestion);
        } catch (IllegalArgumentException e) {
            log.error("Suggestion not found: {}", suggestionId);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error getting AI suggestion: {}", suggestionId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Accept an AI suggestion
     */
    @PostMapping("/{suggestionId}/accept")
    public ResponseEntity<AISuggestionDTO> acceptSuggestion(
            @PathVariable String suggestionId,
            @RequestBody(required = false) Map<String, Object> request) {
        
        log.info("Accepting AI suggestion: {}", suggestionId);
        
        try {
            // For now, use a default user ID. In a real implementation, this would come from authentication
            Long userId = 1L;
            AISuggestionDTO suggestion = aiSuggestionService.acceptSuggestion(suggestionId, userId);
            return ResponseEntity.ok(suggestion);
        } catch (IllegalArgumentException e) {
            log.error("Suggestion not found: {}", suggestionId);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error accepting AI suggestion: {}", suggestionId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Reject an AI suggestion
     */
    @PostMapping("/{suggestionId}/reject")
    public ResponseEntity<AISuggestionDTO> rejectSuggestion(
            @PathVariable String suggestionId,
            @RequestBody(required = false) Map<String, Object> request) {
        
        log.info("Rejecting AI suggestion: {}", suggestionId);
        
        try {
            // For now, use a default user ID. In a real implementation, this would come from authentication
            Long userId = 1L;
            String reason = null;
            if (request != null && request.containsKey("reason")) {
                reason = (String) request.get("reason");
            }
            
            AISuggestionDTO suggestion = aiSuggestionService.rejectSuggestion(suggestionId, userId, reason);
            return ResponseEntity.ok(suggestion);
        } catch (IllegalArgumentException e) {
            log.error("Suggestion not found: {}", suggestionId);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error rejecting AI suggestion: {}", suggestionId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Execute an AI suggestion
     */
    @PostMapping("/{suggestionId}/execute")
    public ResponseEntity<AISuggestionDTO> executeSuggestion(@PathVariable String suggestionId) {
        log.info("Executing AI suggestion: {}", suggestionId);
        
        try {
            AISuggestionDTO suggestion = aiSuggestionService.executeSuggestion(suggestionId);
            return ResponseEntity.ok(suggestion);
        } catch (IllegalArgumentException e) {
            log.error("Suggestion not found: {}", suggestionId);
            return ResponseEntity.notFound().build();
        } catch (IllegalStateException e) {
            log.error("Suggestion cannot be executed: {}", suggestionId);
            return ResponseEntity.badRequest().body(null);
        } catch (Exception e) {
            log.error("Error executing AI suggestion: {}", suggestionId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Create a new AI suggestion
     */
    @PostMapping
    public ResponseEntity<AISuggestionDTO> createSuggestion(@RequestBody AISuggestionDTO suggestionDTO) {
        log.info("Creating new AI suggestion");
        
        try {
            AISuggestionDTO createdSuggestion = aiSuggestionService.save(suggestionDTO);
            return ResponseEntity.ok(createdSuggestion);
        } catch (Exception e) {
            log.error("Error creating AI suggestion", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Update an AI suggestion
     */
    @PutMapping("/{suggestionId}")
    public ResponseEntity<AISuggestionDTO> updateSuggestion(
            @PathVariable String suggestionId,
            @RequestBody AISuggestionDTO suggestionDTO) {
        
        log.info("Updating AI suggestion: {}", suggestionId);
        
        try {
            // Set the suggestion ID from path
            suggestionDTO.setSuggestionId(suggestionId);
            AISuggestionDTO updatedSuggestion = aiSuggestionService.update(suggestionDTO);
            return ResponseEntity.ok(updatedSuggestion);
        } catch (IllegalArgumentException e) {
            log.error("Suggestion not found: {}", suggestionId);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error updating AI suggestion: {}", suggestionId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Delete an AI suggestion
     */
    @DeleteMapping("/{suggestionId}")
    public ResponseEntity<Void> deleteSuggestion(@PathVariable String suggestionId) {
        log.info("Deleting AI suggestion: {}", suggestionId);
        
        try {
            // First get the suggestion to find its internal ID
            AISuggestionDTO suggestion = aiSuggestionService.getBySuggestionId(suggestionId);
            aiSuggestionService.delete(suggestion.getId());
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            log.error("Suggestion not found: {}", suggestionId);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error deleting AI suggestion: {}", suggestionId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
} 