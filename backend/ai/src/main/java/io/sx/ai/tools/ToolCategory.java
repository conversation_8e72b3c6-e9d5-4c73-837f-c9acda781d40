package io.sx.ai.tools;

import java.util.Set;

/**
 * Enhanced tool categorization with configurable autonomy levels
 * Supports asymmetric categories and user-configurable approval requirements
 */
public enum ToolCategory {
    
    /**
     * SYSTEM_TOOLS - Information gathering and analysis tools
     * - No direct business impact
     * - Can be executed in parallel
     * - Default: No approval required
     * - Configurable: User can require approval for sensitive analysis
     */
    SYSTEM_TOOLS("system_tools", "Information gathering and analysis tools", 
                 ApprovalRequirement.NEVER, true, Set.of()),
    
    /**
     * READONLY_TOOLS - Data retrieval tools (can be any category)
     * - Read-only operations on existing data
     * - Can be executed in parallel
     * - Default: No approval required
     * - Configurable: User can require approval for sensitive data access
     */
    READONLY_TOOLS("readonly_tools", "Data retrieval and query tools", 
                   ApprovalRequirement.NEVER, true, Set.of()),
    
    /**
     * ANALYSIS_TOOLS - AI analysis and classification tools
     * - May update analysis metadata but not core business data
     * - Can be executed in parallel
     * - Default: No approval required
     * - Configurable: User can require approval for classification changes
     */
    ANALYSIS_TOOLS("analysis_tools", "AI analysis and classification tools", 
                   ApprovalRequirement.NEVER, true, Set.of()),
    
    /**
     * BUSINESS_TOOLS - Core business operation tools
     * - Direct impact on tickets, users, assignments
     * - Sequential execution (business state changes)
     * - Default: Approval required in HYBRID/LOW modes
     * - Configurable: User can set approval levels per operation type
     */
    BUSINESS_TOOLS("business_tools", "Core business operation tools", 
                   ApprovalRequirement.CONFIGURABLE, false, 
                   Set.of("ticket_assignment", "priority_change", "escalation")),
    
    /**
     * CRITICAL_BUSINESS_TOOLS - High-impact business operations
     * - Significant business impact (escalations, major changes)
     * - Sequential execution
     * - Default: Always require approval
     * - Configurable: User can set approval levels but with warnings
     */
    CRITICAL_BUSINESS_TOOLS("critical_business_tools", "High-impact business operations", 
                           ApprovalRequirement.ALWAYS, false,
                           Set.of("ticket_closure", "org_transfer", "major_priority_change")),
    
    /**
     * CRITICAL_TOOLS - Alias for CRITICAL_BUSINESS_TOOLS for backward compatibility
     */
    CRITICAL_TOOLS("critical_tools", "Critical business operations (alias)", 
                   ApprovalRequirement.ALWAYS, false,
                   Set.of("ticket_closure", "org_transfer", "major_priority_change")),
    
    /**
     * ADMIN_TOOLS - Administrative and configuration tools
     * - System configuration and administrative tasks
     * - Sequential execution
     * - Default: Never (unless admin and read-only)
     * - Configurable: User can enable/disable based on admin privileges
     */
    ADMIN_TOOLS("admin_tools", "Administrative and configuration tools", 
                ApprovalRequirement.NEVER, false,
                Set.of("user_management", "permission_changes", "system_config")),
    
    /**
     * CRITICAL_ADMIN_TOOLS - High-impact administrative operations
     * - System-wide changes requiring careful consideration
     * - Sequential execution
     * - Default: Always require approval with admin privileges
     * - Configurable: User can set approval levels but with strict validation
     */
    CRITICAL_ADMIN_TOOLS("critical_admin_tools", "High-impact administrative operations", 
                        ApprovalRequirement.ALWAYS, false,
                        Set.of("system_shutdown", "data_deletion", "security_changes"));
    
    private final String categoryId;
    private final String description;
    private final ApprovalRequirement defaultApprovalRequirement;
    private final boolean canExecuteInParallel;
    private final Set<String> operationTypes;
    
    ToolCategory(String categoryId, String description, 
                ApprovalRequirement defaultApprovalRequirement,
                boolean canExecuteInParallel, Set<String> operationTypes) {
        this.categoryId = categoryId;
        this.description = description;
        this.defaultApprovalRequirement = defaultApprovalRequirement;
        this.canExecuteInParallel = canExecuteInParallel;
        this.operationTypes = operationTypes;
    }
    
    public String getCategoryId() {
        return categoryId;
    }
    
    public String getDescription() {
        return description;
    }
    
    public ApprovalRequirement getDefaultApprovalRequirement() {
        return defaultApprovalRequirement;
    }
    
    public boolean canExecuteInParallel() {
        return canExecuteInParallel;
    }
    
    public Set<String> getOperationTypes() {
        return operationTypes;
    }
    
    /**
     * Check if this tool category requires approval based on autonomy configuration
     */
    public boolean requiresApproval(AIAutonomyMode mode, AutonomyConfiguration config) {
        ApprovalRequirement requirement = config.getApprovalRequirement(this, mode);
        
        switch (requirement) {
            case NEVER:
                return false;
            case ALWAYS:
                return true;
            case CONFIGURABLE:
                return config.isApprovalRequired(this, mode);
            default:
                return defaultApprovalRequirement == ApprovalRequirement.ALWAYS;
        }
    }
    
    /**
     * Check if this tool category requires approval in a specific mode (simplified version)
     */
    public boolean requiresApprovalInMode(AIAutonomyMode mode) {
        switch (mode) {
            case FULL_MCP:
                return defaultApprovalRequirement == ApprovalRequirement.ALWAYS;
            case HYBRID:
                return defaultApprovalRequirement != ApprovalRequirement.NEVER;
            case LOW_AUTONOMY:
                return defaultApprovalRequirement != ApprovalRequirement.NEVER;
            default:
                return defaultApprovalRequirement == ApprovalRequirement.ALWAYS;
        }
    }
    
    /**
     * Approval requirement types
     */
    public enum ApprovalRequirement {
        NEVER("never", "Never requires approval"),
        ALWAYS("always", "Always requires approval"),
        CONFIGURABLE("configurable", "User-configurable approval requirement");
        
        private final String requirementId;
        private final String description;
        
        ApprovalRequirement(String requirementId, String description) {
            this.requirementId = requirementId;
            this.description = description;
        }
        
        public String getRequirementId() {
            return requirementId;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * AI autonomy modes with enhanced configuration support
     */
    public enum AIAutonomyMode {
        FULL_MCP("full_mcp", "Full autonomous operation"),
        HYBRID("hybrid", "Hybrid human-AI operation"),
        LOW_AUTONOMY("low_autonomy", "Minimal autonomous operation");
        
        private final String modeId;
        private final String description;
        
        AIAutonomyMode(String modeId, String description) {
            this.modeId = modeId;
            this.description = description;
        }
        
        public String getModeId() {
            return modeId;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * Autonomy configuration for user-customizable approval requirements
     */
    public static class AutonomyConfiguration {
        private final Long organizationId;
        private final Long configuredByUserId;
        private final java.util.Map<ToolCategory, java.util.Map<AIAutonomyMode, Boolean>> approvalSettings;
        private final java.util.Map<String, Boolean> operationTypeSettings;
        
        public AutonomyConfiguration(Long organizationId, Long configuredByUserId) {
            this.organizationId = organizationId;
            this.configuredByUserId = configuredByUserId;
            this.approvalSettings = new java.util.HashMap<>();
            this.operationTypeSettings = new java.util.HashMap<>();
        }
        
        public void setApprovalRequired(ToolCategory category, AIAutonomyMode mode, boolean required) {
            approvalSettings.computeIfAbsent(category, k -> new java.util.HashMap<>()).put(mode, required);
        }
        
        public boolean isApprovalRequired(ToolCategory category, AIAutonomyMode mode) {
            return approvalSettings
                .getOrDefault(category, new java.util.HashMap<>())
                .getOrDefault(mode, category.getDefaultApprovalRequirement() == ApprovalRequirement.ALWAYS);
        }
        
        public ApprovalRequirement getApprovalRequirement(ToolCategory category, AIAutonomyMode mode) {
            if (approvalSettings.containsKey(category) && 
                approvalSettings.get(category).containsKey(mode)) {
                return approvalSettings.get(category).get(mode) ? 
                    ApprovalRequirement.ALWAYS : ApprovalRequirement.NEVER;
            }
            return category.getDefaultApprovalRequirement();
        }
        
        public void setOperationTypeApproval(String operationType, boolean required) {
            operationTypeSettings.put(operationType, required);
        }
        
        public boolean isOperationTypeApprovalRequired(String operationType) {
            return operationTypeSettings.getOrDefault(operationType, false);
        }
        
        // Getters
        public Long getOrganizationId() { return organizationId; }
        public Long getConfiguredByUserId() { return configuredByUserId; }
    }
} 