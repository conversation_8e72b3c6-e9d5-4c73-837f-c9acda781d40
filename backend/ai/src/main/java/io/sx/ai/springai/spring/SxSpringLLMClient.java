package io.sx.ai.springai.spring;

import io.sx.ai.abstraction.ResolvedPrompt;
import io.sx.ai.abstraction.SxFunction;
import io.sx.ai.abstraction.SxLLMClient;
import io.sx.ai.abstraction.SxLLMResponse;
import io.sx.ai.abstraction.SxPrompt;
import io.sx.ai.abstraction.SxPromptTemplate;
import io.sx.ai.abstraction.SxMessage;
import io.sx.ai.abstraction.SxVectorStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.chat.memory.ChatMemory;
import io.sx.ai.abstraction.SxChatMemory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * Spring AI implementation of SxLLMClient
 * Wraps Spring AI's ChatModel class
 */
@Slf4j
@Component
public class SxSpringLLMClient implements SxLLMClient {

    private final ChatModel chatModel;

    @Autowired
    public SxSpringLLMClient(@Qualifier("primaryChatClient") ChatClient primaryChatClient,
                             ChatModel chatModel) {
        this.chatModel = chatModel;
    }

    @Override
    public <T> SxLLMResponse<T> generate(SxPrompt prompt, Class<T> outputType) {
        throw new UnsupportedOperationException("Use generate(ResolvedPrompt, Class<T>) instead");
    }

    @Override
    public SxLLMResponse<Object> generate(SxPrompt prompt) {
        throw new UnsupportedOperationException("Use generate(ResolvedPrompt) instead");
    }

    @Override
    /**
     * Generate response using a resolved prompt
     */
    public <T> SxLLMResponse<T> generate(ResolvedPrompt resolvedPrompt, Class<T> outputType) {
        if (resolvedPrompt == null) {
            throw new IllegalArgumentException("ResolvedPrompt cannot be null");
        }

        // Convert resolved prompt to Spring AI Prompt
        List<Message> springMessages = resolvedPrompt.getResolvedMessages().stream()
            .map(this::convertToSpringMessage)
            .collect(Collectors.toList());
        Prompt springPrompt = new Prompt(springMessages);

        // Use primary ChatClient for general use
        ChatClient.CallResponseSpec response = ChatClient.builder(this.chatModel)
            .build()
            .prompt(springPrompt).call();
        return new SxSpringLLMResponse<>(response.chatResponse(), outputType);
    }

    /**
     * Generate response using a resolved prompt
     */
    public SxLLMResponse<Object> generate(ResolvedPrompt resolvedPrompt) {
        return generate(resolvedPrompt, Object.class);
    }

    /**
     * Generate response using a resolved prompt with chat memory
     */
    public <T> SxLLMResponse<T> generateWithMemory(ResolvedPrompt resolvedPrompt, Class<T> outputType, SxChatMemory chatMemory) {
        if (resolvedPrompt == null) {
            throw new IllegalArgumentException("ResolvedPrompt cannot be null");
        }

        if (chatMemory == null) {
            throw new IllegalArgumentException("SxChatMemory cannot be null");
        }

        // Get the underlying Spring AI ChatMemory
        Object frameworkMemory = chatMemory.getFrameworkMemory();
        if (!(frameworkMemory instanceof ChatMemory)) {
            throw new IllegalArgumentException("SxChatMemory must wrap a Spring AI ChatMemory instance");
        }
        ChatMemory springChatMemory = (ChatMemory) frameworkMemory;

        // Convert resolved prompt to Spring AI Prompt
        List<Message> springMessages = resolvedPrompt.getResolvedMessages().stream()
            .map(this::convertToSpringMessage)
            .collect(Collectors.toList());
        Prompt springPrompt = new Prompt(springMessages);

        // Use ChatClient with memory - add messages from memory to the prompt
        List<Message> allMessages = new ArrayList<>();

        // Add messages from chat memory
        List<org.springframework.ai.chat.messages.Message> memoryMessages = springChatMemory.get(chatMemory.getSessionId());
        allMessages.addAll(memoryMessages);

        // Add current prompt messages
        allMessages.addAll(springMessages);

        // Create new prompt with all messages
        Prompt combinedPrompt = new Prompt(allMessages);

        // Use ChatClient
        ChatClient.CallResponseSpec response = ChatClient.builder(chatModel)
            .build()
            .prompt(combinedPrompt)
            .call();

        return new SxSpringLLMResponse<>(response.chatResponse(), outputType);
    }

    /**
     * Generate response using a resolved prompt with chat memory (Object output)
     */
    public SxLLMResponse<Object> generateWithMemory(ResolvedPrompt resolvedPrompt, SxChatMemory chatMemory) {
        return generateWithMemory(resolvedPrompt, Object.class, chatMemory);
    }

    /**
     * Generate response using a resolved prompt with tool callbacks
     * Note: Spring AI's tool calling is not yet fully implemented.
     * This method currently just uses the primary ChatClient without tool configuration.
     * For actual tool calling, use FunctionCallingService or implement manual tool parsing.
     */
    public <T> SxLLMResponse<T> generateWithTools(ResolvedPrompt resolvedPrompt, Class<T> outputType, Object... toolCallbacks) {
        if (resolvedPrompt == null) {
            throw new IllegalArgumentException("ResolvedPrompt cannot be null");
        }

        // Convert resolved prompt to Spring AI Prompt
        List<Message> springMessages = resolvedPrompt.getResolvedMessages().stream()
            .map(this::convertToSpringMessage)
            .collect(Collectors.toList());
        Prompt springPrompt = new Prompt(springMessages);

        // Use primary ChatClient with hybrid tool approach:
        // 1. Auto-discover Spring beans with @Tool annotations via .defaultTools()
        // 2. Add explicit tool instances via .toolCallbacks() for dynamic tools
        var builder = ChatClient.builder(this.chatModel);
        
        // Enable auto-discovery of @Tool annotated Spring beans
        builder.defaultTools();
        
        // Add explicit tools if provided
        if (toolCallbacks != null && toolCallbacks.length > 0) {
            builder.defaultTools(toolCallbacks);
        }
        
        var requestSpec = builder.build().prompt(springPrompt);

        return new SxSpringLLMResponse<>(requestSpec.call().chatResponse(), outputType);
    }

    /**
     * Generate response using a resolved prompt with tool callbacks (Object output)
     */
    public SxLLMResponse<Object> generateWithTools(ResolvedPrompt resolvedPrompt, List<?> toolCallbacks) {
        return generateWithTools(resolvedPrompt, Object.class, toolCallbacks);
    }

    /**
     * Generate response using a resolved prompt with both memory and tool callbacks
     * Note: Spring AI's tool calling is not yet fully implemented.
     * This method currently just uses memory without tool configuration.
     * For actual tool calling, use FunctionCallingService or implement manual tool parsing.
     */
    public <T> SxLLMResponse<T> generateWithMemoryAndTools(ResolvedPrompt resolvedPrompt, Class<T> outputType,
                                                           SxChatMemory chatMemory, Object... toolCallbacks) {
        if (resolvedPrompt == null) {
            throw new IllegalArgumentException("ResolvedPrompt cannot be null");
        }

        if (chatMemory == null) {
            throw new IllegalArgumentException("SxChatMemory cannot be null");
        }

        // Get the underlying Spring AI ChatMemory
        Object frameworkMemory = chatMemory.getFrameworkMemory();
        if (!(frameworkMemory instanceof ChatMemory)) {
            throw new IllegalArgumentException("SxChatMemory must wrap a Spring AI ChatMemory instance");
        }
        ChatMemory springChatMemory = (ChatMemory) frameworkMemory;

        // Convert resolved prompt to Spring AI Prompt
        List<Message> springMessages = resolvedPrompt.getResolvedMessages().stream()
            .map(this::convertToSpringMessage)
            .collect(Collectors.toList());
        Prompt springPrompt = new Prompt(springMessages);

        // Use ChatClient with memory and tools - add messages from memory to the prompt
        List<Message> allMessages = new ArrayList<>();

        // Add messages from chat memory
        List<org.springframework.ai.chat.messages.Message> memoryMessages = springChatMemory.get(chatMemory.getSessionId());
        allMessages.addAll(memoryMessages);

        // Add current prompt messages
        allMessages.addAll(springMessages);

        // Create new prompt with all messages
        Prompt combinedPrompt = new Prompt(allMessages);

        // Use primary ChatClient with hybrid tool approach:
        // 1. Auto-discover Spring beans with @Tool annotations via .defaultTools()
        // 2. Add explicit tool instances via .toolCallbacks() for dynamic tools
        var builder = ChatClient.builder(this.chatModel);
        
        // Enable auto-discovery of @Tool annotated Spring beans
        builder.defaultTools();
        
        // Add explicit tools if provided
        if (toolCallbacks != null && toolCallbacks.length > 0) {
            builder.defaultTools(toolCallbacks);
        }
        
        var requestSpec = builder.build().prompt(combinedPrompt);
        return new SxSpringLLMResponse<>(requestSpec.call().chatResponse(), outputType);
    }

    /**
     * Generate response using a resolved prompt with both memory and tool callbacks (Object output)
     */
    public SxLLMResponse<Object> generateWithMemoryAndTools(ResolvedPrompt resolvedPrompt,
                                                            SxChatMemory chatMemory, List<?> toolCallbacks) {
        return generateWithMemoryAndTools(resolvedPrompt, Object.class, chatMemory, toolCallbacks);
    }

    @Override
    public <T> T generateStructured(SxPrompt prompt, Class<T> outputType) {
        return generate(prompt, outputType).getOutput();
    }

    @Override
    public SxLLMResponse<Object> generateWithFunctions(SxPrompt prompt, List<SxFunction> functions) {
        // TODO: Implement function calling when Spring AI supports it
        log.warn("Function calling not yet implemented, falling back to regular generation");
        return generate(prompt);
    }

    @Override
    public SxLLMResponse<Object> generateWithContext(SxPrompt prompt, SxVectorStore vectorStore, String query) {
        // TODO: Implement vector store context when Spring AI supports it
        log.warn("Vector store context not yet implemented, falling back to regular generation");
        return generate(prompt);
    }

    @Override
    public SxLLMResponse<Object> generateWithMemory(SxPrompt prompt, SxChatMemory chatMemory) {
        throw new UnsupportedOperationException("Use generateWithMemory(ResolvedPrompt, SxChatMemory) instead");
    }

    @Override
    public <T> SxLLMResponse<T> generateWithMemory(SxPrompt prompt, Class<T> outputType, SxChatMemory chatMemory) {
        throw new UnsupportedOperationException("Use generateWithMemory(ResolvedPrompt, Class<T>, SxChatMemory) instead");
    }

    // Factory methods

    @Override
    public SxMessage createSystemMessage(String content) {
        return new SxSpringMessage(new SystemMessage(content));
    }

    @Override
    public SxMessage createUserMessage(String content) {
        return new SxSpringMessage(new UserMessage(content));
    }

    @Override
    public SxMessage createAssistantMessage(String content) {
        return new SxSpringMessage(new AssistantMessage(content));
    }

    @Override
    public SxPromptTemplate createPromptTemplate(String template) {
        return new SxSpringPromptTemplate(new PromptTemplate(template));
    }

    @Override
    public SxPrompt createPrompt(List<SxMessage> messages) {
        List<Message> springMessages = messages.stream()
            .map(this::convertToSpringMessage)
            .collect(Collectors.toList());
        Prompt springPrompt = new Prompt(springMessages);
        return new SxSpringPrompt(springPrompt);
    }

    @Override
    public List<SxMessage> convertToSxMessages(List<?> springMessages) {
        return springMessages.stream()
            .map(msg -> new SxSpringMessage((Message) msg))
            .collect(Collectors.toList());
    }

    @Override
    public String getModelName() {
        return "spring-ai-chat-model";
    }

    @Override
    public Map<String, Object> getModelConfig() {
        return Map.of("framework", "spring-ai");
    }

    @Override
    public boolean isAvailable() {
        try {
            // Simple availability check
            return chatModel != null;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Map<String, Object> getUsageStats() {
        return Map.of("framework", "spring-ai", "model", getModelName());
    }

    // Helper methods

    private Prompt convertToSpringPrompt(SxPrompt sxPrompt) {
        if (sxPrompt instanceof SxSpringPrompt) {
            return ((SxSpringPrompt) sxPrompt).getSpringPrompt();
        }

        // Convert SxMessages to Spring Messages
        List<Message> springMessages = sxPrompt.getMessages().stream()
            .map(this::convertToSpringMessage)
            .collect(Collectors.toList());

        return new Prompt(springMessages);
    }

    private Message convertToSpringMessage(SxMessage sxMessage) {
        if (sxMessage instanceof SxSpringMessage) {
            return ((SxSpringMessage) sxMessage).getSpringMessage();
        }

        // Create appropriate Spring message based on type
        switch (sxMessage.getType()) {
            case SYSTEM:
                return new SystemMessage(sxMessage.getContent());
            case USER:
                return new UserMessage(sxMessage.getContent());
            case ASSISTANT:
                return new AssistantMessage(sxMessage.getContent());
            default:
                return new UserMessage(sxMessage.getContent());
        }
    }
} 