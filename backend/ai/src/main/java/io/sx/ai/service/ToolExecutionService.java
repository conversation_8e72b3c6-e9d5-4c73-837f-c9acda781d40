package io.sx.ai.service;

import io.sx.ai.model.AISuggestion;
import io.sx.ai.tools.AITool;
import io.sx.ai.tools.AIToolRegistry;
import io.sx.ai.tools.AITool.ToolContext;
import io.sx.ai.tools.AITool.ToolResult;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Service for executing AI tools with comprehensive failure handling and retry logic
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ToolExecutionService {
    
    private final AIToolRegistry toolRegistry;
    
    /**
     * Execute a single tool with retry logic
     */
    public ToolExecutionResult executeToolWithRetry(String toolName, Map<String, Object> parameters, 
                                                   ToolContext context, RetryConfig retryConfig) {
        List<ToolAttempt> attempts = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        
        for (int attempt = 1; attempt <= retryConfig.getMaxAttempts(); attempt++) {
            long attemptStartTime = System.currentTimeMillis();
            
            try {
                log.info("Executing tool {} (attempt {}/{})", toolName, attempt, retryConfig.getMaxAttempts());
                
                ToolResult result = toolRegistry.executeTool(toolName, parameters, context);
                long attemptDuration = System.currentTimeMillis() - attemptStartTime;
                
                ToolAttempt toolAttempt = ToolAttempt.builder()
                    .attemptNumber(attempt)
                    .timestamp(new Date())
                    .success(result.isSuccess())
                    .result(result.getData())
                    .errorMessage(result.getErrorMessage())
                    .executionDurationMs(attemptDuration)
                    .build();
                
                attempts.add(toolAttempt);
                
                if (result.isSuccess()) {
                    // Success - return immediately
                    long totalDuration = System.currentTimeMillis() - startTime;
                    return ToolExecutionResult.builder()
                        .success(true)
                        .attempts(attempts)
                        .totalAttempts(attempt)
                        .finalSuccess(true)
                        .totalDurationMs(totalDuration)
                        .retryStrategy(retryConfig.getStrategy().name())
                        .build();
                } else {
                    // Check if we should retry
                    if (!shouldRetry(result.getErrorMessage(), retryConfig, attempt)) {
                        break;
                    }
                    
                    // Wait before retry
                    if (attempt < retryConfig.getMaxAttempts()) {
                        long delay = calculateRetryDelay(retryConfig, attempt);
                        log.info("Tool {} failed, retrying in {} ms", toolName, delay);
                        try {
                            Thread.sleep(delay);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
                
            } catch (Exception e) {
                long attemptDuration = System.currentTimeMillis() - attemptStartTime;
                log.error("Exception executing tool {} (attempt {})", toolName, attempt, e);
                
                ToolAttempt toolAttempt = ToolAttempt.builder()
                    .attemptNumber(attempt)
                    .timestamp(new Date())
                    .success(false)
                    .errorMessage(e.getMessage())
                    .executionDurationMs(attemptDuration)
                    .build();
                
                attempts.add(toolAttempt);
                
                if (!shouldRetry(e.getMessage(), retryConfig, attempt)) {
                    break;
                }
            }
        }
        
        // All attempts failed
        long totalDuration = System.currentTimeMillis() - startTime;
        return ToolExecutionResult.builder()
            .success(false)
            .attempts(attempts)
            .totalAttempts(attempts.size())
            .finalSuccess(false)
            .totalDurationMs(totalDuration)
            .retryStrategy(retryConfig.getStrategy().name())
            .build();
    }
    
    /**
     * Execute multiple tools for a single suggestion
     */
    public MultiToolExecutionResult executeMultipleTools(List<ToolExecutionRequest> toolRequests, 
                                                        ToolContext context, RetryConfig retryConfig) {
        List<ToolExecutionResult> results = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        
        // Execute tools in parallel if configured
        if (retryConfig.isParallelExecution()) {
            List<CompletableFuture<ToolExecutionResult>> futures = new ArrayList<>();
            
            for (ToolExecutionRequest request : toolRequests) {
                CompletableFuture<ToolExecutionResult> future = CompletableFuture.supplyAsync(() ->
                    executeToolWithRetry(request.getToolName(), request.getParameters(), context, retryConfig)
                );
                futures.add(future);
            }
            
            // Wait for all to complete
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            for (CompletableFuture<ToolExecutionResult> future : futures) {
                try {
                    results.add(future.get());
                } catch (Exception e) {
                    log.error("Error getting tool execution result", e);
                    results.add(ToolExecutionResult.builder()
                        .success(false)
                        .errorMessage("Failed to get result: " + e.getMessage())
                        .build());
                }
            }
        } else {
            // Execute tools sequentially
            for (ToolExecutionRequest request : toolRequests) {
                ToolExecutionResult result = executeToolWithRetry(
                    request.getToolName(), request.getParameters(), context, retryConfig);
                results.add(result);
            }
        }
        
        long totalDuration = System.currentTimeMillis() - startTime;
        
        // Calculate overall status
        int successfulTools = (int) results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum();
        int failedTools = results.size() - successfulTools;
        
        MultiToolExecutionResult.OverallStatus overallStatus;
        if (successfulTools == results.size()) {
            overallStatus = MultiToolExecutionResult.OverallStatus.OVERALL_SUCCESS;
        } else if (successfulTools > 0) {
            overallStatus = MultiToolExecutionResult.OverallStatus.PARTIAL_SUCCESS;
        } else {
            overallStatus = MultiToolExecutionResult.OverallStatus.OVERALL_FAILED;
        }
        
        return MultiToolExecutionResult.builder()
            .toolResults(results)
            .totalTools(results.size())
            .successfulTools(successfulTools)
            .failedTools(failedTools)
            .overallStatus(overallStatus)
            .totalDurationMs(totalDuration)
            .build();
    }
    
    /**
     * Determine if a failure should be retried
     */
    private boolean shouldRetry(String errorMessage, RetryConfig retryConfig, int attempt) {
        if (attempt >= retryConfig.getMaxAttempts()) {
            return false;
        }
        
        // Check if error is in skip retry conditions
        for (String skipCondition : retryConfig.getSkipRetryConditions()) {
            if (errorMessage != null && errorMessage.toLowerCase().contains(skipCondition.toLowerCase())) {
                return false;
            }
        }
        
        // Check if error is in retry conditions (if specified)
        if (!retryConfig.getRetryConditions().isEmpty()) {
            boolean shouldRetry = false;
            for (String retryCondition : retryConfig.getRetryConditions()) {
                if (errorMessage != null && errorMessage.toLowerCase().contains(retryCondition.toLowerCase())) {
                    shouldRetry = true;
                    break;
                }
            }
            return shouldRetry;
        }
        
        // Default: retry all errors unless in skip conditions
        return true;
    }
    
    /**
     * Calculate retry delay based on strategy
     */
    private long calculateRetryDelay(RetryConfig retryConfig, int attempt) {
        switch (retryConfig.getStrategy()) {
            case FIXED:
                return retryConfig.getRetryDelayMs();
            case EXPONENTIAL_BACKOFF:
                return retryConfig.getRetryDelayMs() * (long) Math.pow(retryConfig.getBackoffMultiplier(), attempt - 1);
            case LINEAR:
                return retryConfig.getRetryDelayMs() * attempt;
            default:
                return retryConfig.getRetryDelayMs();
        }
    }
    
    // Data classes for tool execution
    @lombok.Data
    @lombok.Builder
    public static class ToolExecutionRequest {
        private String toolName;
        private Map<String, Object> parameters;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class ToolAttempt {
        private int attemptNumber;
        private Date timestamp;
        private boolean success;
        private Object result;
        private String errorMessage;
        private long executionDurationMs;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class ToolExecutionResult {
        private boolean success;
        private List<ToolAttempt> attempts;
        private int totalAttempts;
        private boolean finalSuccess;
        private long totalDurationMs;
        private String retryStrategy;
        private String errorMessage;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class MultiToolExecutionResult {
        private List<ToolExecutionResult> toolResults;
        private int totalTools;
        private int successfulTools;
        private int failedTools;
        private OverallStatus overallStatus;
        private long totalDurationMs;
        
        public enum OverallStatus {
            OVERALL_SUCCESS,
            PARTIAL_SUCCESS,
            OVERALL_FAILED
        }
    }
    
    @lombok.Data
    @lombok.Builder
    public static class RetryConfig {
        @Builder.Default
        private int maxAttempts = 3;
        @Builder.Default
        private long retryDelayMs = 1000;
        @Builder.Default
        private double backoffMultiplier = 2.0;
        @Builder.Default
        private RetryStrategy strategy = RetryStrategy.EXPONENTIAL_BACKOFF;
        @Builder.Default
        private List<String> retryConditions = Arrays.asList("RATE_LIMIT", "NETWORK_ERROR", "TIMEOUT");
        @Builder.Default
        private List<String> skipRetryConditions = Arrays.asList("AUTHENTICATION_ERROR", "INVALID_PARAMETERS");
        @Builder.Default
        private boolean parallelExecution = false;
        
        public enum RetryStrategy {
            FIXED,
            LINEAR,
            EXPONENTIAL_BACKOFF
        }
    }
} 