package io.sx.ai.repository;

import io.sx.repository.BaseRepository;
import io.sx.ai.model.AIConfiguration;

import java.util.List;
import java.util.Optional;

/**
 * Repository for AI configuration operations
 */
public interface AIConfigurationRepository extends BaseRepository<AIConfiguration> {
    
    /**
     * Find configuration by type and organization
     */
    AIConfiguration findByTypeAndOrganization(Long organizationId, String configKey);
    
    /**
     * Find all configurations for an organization
     */
    List<AIConfiguration> findByOrganization(Long supportOrganizationId);
    
    /**
     * Find configuration by ID
     */
    Optional<AIConfiguration> findById(Long id);
    
    /**
     * Delete configuration by ID
     */
    void deleteById(Long id);
} 