package io.sx.ai.assignment;

import io.sx.ai.model.AISuggestion;
import io.sx.ai.service.TicketCategorizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Dedicated agent for ticket assignment
 * Focuses specifically on team/agent assignment based on classification and availability
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TicketAssignmentAgent {

    private final TicketCategorizationService ticketCategorizationService;

    /**
     * Process ticket assignment and generate assignment suggestions
     * 
     * @param ticketId The ticket ID to assign
     * @param organizationId The organization ID
     * @param supportOrgId The support organization ID
     * @return List of assignment suggestions
     */
    public List<AISuggestion> processTicketAssignment(Long ticketId, Long organizationId, Long supportOrgId) {
        log.debug("Processing ticket assignment for ticket: {}", ticketId);
        
        List<AISuggestion> suggestions = new ArrayList<>();
        
        try {
            // Get classification analysis to inform assignment
            Map<String, Object> classificationAnalysis = ticketCategorizationService.getClassificationAnalysis(
                ticketId, organizationId, supportOrgId);
            
            if (classificationAnalysis != null) {
                // Extract assignment recommendations from classification
                String recommendedTeam = (String) classificationAnalysis.get("recommended_team");
                String category = (String) classificationAnalysis.get("suggested_category");
                String complexity = (String) classificationAnalysis.get("complexity_level");
                
                // Create assignment suggestion
                AISuggestion assignmentSuggestion = AISuggestion.builder()
                    .suggestionId("assignment-" + ticketId + "-" + System.currentTimeMillis())
                    .sessionId("session-" + ticketId)
                    .ticketId(ticketId)
                    .autonomyLevel(AISuggestion.AutonomyLevel.HITL) // Human approval required for assignments
                    .suggestionType(AISuggestion.SuggestionType.ASSIGNMENT)
                    .title("Assign Ticket to Team")
                    .description("Assign to team: " + recommendedTeam + 
                               " | Category: " + category + 
                               " | Complexity: " + complexity)
                    .reasoning("Based on ticket classification and team expertise: " + 
                             classificationAnalysis.get("assignment_recommendation"))
                    .confidenceScore(BigDecimal.valueOf(0.75)) // Moderate confidence for assignments
                    .priority(2) // Medium priority for assignments
                    .status(AISuggestion.SuggestionStatus.PENDING)
                    .approvalRequired(true)
                    .organizationId(organizationId)
                    .supportOrganizationId(supportOrgId)
                    .createdBy("ai-system")
                    .updatedBy("ai-system")
                    .metadata(Map.of(
                        "recommended_team", recommendedTeam,
                        "category", category,
                        "complexity", complexity,
                        "skill_requirements", classificationAnalysis.get("skill_requirements"),
                        "experience_level", classificationAnalysis.get("experience_level"),
                        "workload_consideration", "balanced"
                    ))
                    .build();
                
                suggestions.add(assignmentSuggestion);
                
                log.debug("Generated assignment suggestion for ticket {}: {}", 
                         ticketId, recommendedTeam);
            }
            
        } catch (Exception e) {
            log.warn("Error processing ticket assignment for ticket: {}", ticketId, e);
        }
        
        return suggestions;
    }

    /**
     * Get assignment analysis without generating suggestions
     * 
     * @param ticketId The ticket ID to analyze
     * @param organizationId The organization ID
     * @param supportOrgId The support organization ID
     * @return Assignment analysis map
     */
    public Map<String, Object> getAssignmentAnalysis(Long ticketId, Long organizationId, Long supportOrgId) {
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            Map<String, Object> classificationAnalysis = ticketCategorizationService.getClassificationAnalysis(
                ticketId, organizationId, supportOrgId);
            
            if (classificationAnalysis != null) {
                analysis.put("recommended_team", classificationAnalysis.get("recommended_team"));
                analysis.put("skill_requirements", classificationAnalysis.get("skill_requirements"));
                analysis.put("experience_level", classificationAnalysis.get("experience_level"));
                analysis.put("workload_consideration", "balanced");
                analysis.put("assignment_reasoning", classificationAnalysis.get("assignment_recommendation"));
            }
            
        } catch (Exception e) {
            log.warn("Error getting assignment analysis for ticket: {}", ticketId, e);
        }
        
        return analysis;
    }
} 