package io.sx.ai.springai.framework.common;

import java.util.Map;

/**
 * Factory utility for creating typed workflow inputs
 * Provides convenience methods and backward compatibility
 */
public class WorkflowInputFactory {
    
    /**
     * Create a numeric input from a Double value
     */
    public static NumericInput createNumericInput(Double value) {
        return NumericInput.of(value);
    }
    
    /**
     * Create a numeric input from a Double value with unit
     */
    public static NumericInput createNumericInput(Double value, String unit) {
        return NumericInput.of(value, unit);
    }
    
    /**
     * Create a numeric input from a Double value with unit and format
     */
    public static NumericInput createNumericInput(Double value, String unit, String format) {
        return NumericInput.builder()
            .value(value)
            .unit(unit)
            .format(format)
            .build();
    }
    
    /**
     * Create a text input from a String value
     */
    public static TextInput createTextInput(String value) {
        return TextInput.of(value);
    }
    
    /**
     * Create a text input from a String value with language
     */
    public static TextInput createTextInput(String value, String language) {
        return TextInput.of(value, language);
    }
    
    /**
     * Create a text input from a String value with language and content type
     */
    public static TextInput createTextInput(String value, String language, String contentType) {
        return TextInput.builder()
            .value(value)
            .language(language)
            .encoding("UTF-8")
            .contentType(contentType)
            .build();
    }
    
    /**
     * Create a percentage input
     */
    public static NumericInput createPercentageInput(Double value) {
        return NumericInput.percentage(value);
    }
    
    /**
     * Create a currency input
     */
    public static NumericInput createCurrencyInput(Double value, String currencyCode) {
        return NumericInput.currency(value, currencyCode);
    }
    
    /**
     * Create a markdown input
     */
    public static TextInput createMarkdownInput(String value) {
        return TextInput.markdown(value);
    }
    
    /**
     * Create a JSON input
     */
    public static TextInput createJsonInput(String value) {
        return TextInput.json(value);
    }
    
    /**
     * Convenience method for backward compatibility - convert String to NumericInput
     * Attempts to parse the string as a Double
     */
    public static NumericInput fromString(String value) {
        try {
            return NumericInput.of(Double.parseDouble(value));
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid numeric value: " + value);
        }
    }
    
    /**
     * Convenience method for backward compatibility - convert String to NumericInput with unit
     */
    public static NumericInput fromString(String value, String unit) {
        try {
            return NumericInput.of(Double.parseDouble(value), unit);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid numeric value: " + value);
        }
    }
    
    /**
     * Create input from a generic object with type detection
     */
    public static WorkflowInput<?> fromObject(Object value) {
        if (value instanceof Number) {
            return NumericInput.of(((Number) value).doubleValue());
        } else if (value instanceof String) {
            return TextInput.of((String) value);
        } else {
            throw new IllegalArgumentException("Unsupported input type: " + value.getClass().getSimpleName());
        }
    }
    
    /**
     * Create input from a map with type information
     */
    public static WorkflowInput<?> fromMap(Map<String, Object> inputMap) {
        String type = (String) inputMap.get("type");
        Object value = inputMap.get("value");
        
        if ("NUMERIC".equals(type)) {
            Double numericValue = value instanceof Number ? ((Number) value).doubleValue() : Double.parseDouble(value.toString());
            String unit = (String) inputMap.get("unit");
            String format = (String) inputMap.get("format");
            return createNumericInput(numericValue, unit, format);
        } else if ("TEXT".equals(type)) {
            String textValue = value.toString();
            String language = (String) inputMap.get("language");
            String contentType = (String) inputMap.get("contentType");
            return createTextInput(textValue, language, contentType);
        } else {
            throw new IllegalArgumentException("Unknown input type: " + type);
        }
    }
} 