package io.sx.ai.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamAvailability {
    private Long organizationId;
    private Integer availableAgents;
    private Integer totalAgents;
    private Double averageWorkload; // percentage
    private Integer seniorAgentsAvailable;
    private Integer estimatedWaitTime; // minutes
} 