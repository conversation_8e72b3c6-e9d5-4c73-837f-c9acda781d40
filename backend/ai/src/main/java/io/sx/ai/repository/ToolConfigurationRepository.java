package io.sx.ai.repository;

import io.sx.ai.model.ToolConfiguration;
import io.sx.repository.BaseRepository;

import java.util.Optional;

/**
 * Repository interface for ToolConfiguration operations
 */
public interface ToolConfigurationRepository extends BaseRepository<ToolConfiguration> {
    
    /**
     * Find configuration by tool name
     */
    Optional<ToolConfiguration> findByToolName(String toolName);
    
    /**
     * Check if configuration exists for tool
     */
    boolean existsByToolName(String toolName);
} 