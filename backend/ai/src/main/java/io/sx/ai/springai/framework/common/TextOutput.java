package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Text output wrapper for String values with Spring AI integration
 * Supports text analysis, sentiment analysis, and content processing
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextOutput implements WorkflowOutput<String> {
    
    /**
     * The text value
     */
    private String value;
    
    /**
     * Language code (e.g., "en", "es", "fr")
     */
    private String language;
    
    /**
     * Format specification (e.g., "plain", "markdown", "json")
     */
    private String format;
    
    /**
     * Confidence score (0.0 to 1.0)
     */
    private Double confidence;
    
    /**
     * Additional metadata
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    @Override
    public String getType() {
        return "TEXT";
    }
    
    @Override
    public Map<String, Object> getMetadata() {
        Map<String, Object> enhancedMetadata = new HashMap<>(metadata);
        enhancedMetadata.put("language", language);
        enhancedMetadata.put("format", format);
        return enhancedMetadata;
    }
    
    /**
     * Static factory method for creating text outputs
     */
    public static TextOutput of(String value) {
        return TextOutput.builder()
            .value(value)
            .language("en")
            .format("plain")
            .confidence(1.0)
            .build();
    }
    
    /**
     * Static factory method for creating text outputs with language
     */
    public static TextOutput of(String value, String language) {
        return TextOutput.builder()
            .value(value)
            .language(language)
            .format("plain")
            .confidence(1.0)
            .build();
    }
    
    /**
     * Static factory method for creating markdown outputs
     */
    public static TextOutput markdown(String value) {
        return TextOutput.builder()
            .value(value)
            .language("en")
            .format("markdown")
            .confidence(1.0)
            .build();
    }
    
    /**
     * Static factory method for creating JSON outputs
     */
    public static TextOutput json(String value) {
        return TextOutput.builder()
            .value(value)
            .language("en")
            .format("json")
            .confidence(1.0)
            .build();
    }
    
    /**
     * Static factory method for creating outputs with confidence
     */
    public static TextOutput of(String value, String language, Double confidence) {
        return TextOutput.builder()
            .value(value)
            .language(language)
            .format("plain")
            .confidence(confidence)
            .build();
    }
    
    /**
     * Static factory method for creating analysis outputs
     */
    public static TextOutput analysis(String value, Double confidence) {
        return TextOutput.builder()
            .value(value)
            .language("en")
            .format("analysis")
            .confidence(confidence)
            .build();
    }
} 