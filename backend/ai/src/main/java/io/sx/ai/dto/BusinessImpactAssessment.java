package io.sx.ai.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessImpactAssessment {
    private Long ticketId;
    private String impactLevel;
    private Integer affectedUsers;
    private Double revenueImpact;
    private String brandRisk;
    private String complianceRisk;
    private String recommendedPriority;
} 