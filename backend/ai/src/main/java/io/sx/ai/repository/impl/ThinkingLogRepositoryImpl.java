package io.sx.ai.repository.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sx.ai.model.ThinkingLog;
import io.sx.ai.repository.ThinkingLogRepository;
import io.sx.base.rowmapper.BaseRowMapper;
import io.sx.repository.NullHandlingResultSet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * JDBC-based repository implementation for ThinkingLog
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ThinkingLogRepositoryImpl implements ThinkingLogRepository {
    
    private final JdbcTemplate jdbcTemplate;
    private final ObjectMapper objectMapper;
    
    private final RowMapper<ThinkingLog> rowMapper = new BaseRowMapper<ThinkingLog>() {
        @Override
        protected ThinkingLog mapRowInternal(NullHandlingResultSet rs, int rowNum) throws SQLException {
            ThinkingLog thinkingLog = ThinkingLog.builder()
                    .sessionId(rs.getString("session_id"))
                    .triggerEvent(rs.getString("trigger_event"))
                    .triggerEntityId(rs.getLong("trigger_entity_id"))
                    .triggerEntityType(rs.getString("trigger_entity_type"))
                    .thinkingStrategy(rs.getString("thinking_strategy"))
                    .sessionStatus(rs.getString("session_status"))
                    .confidenceScore(rs.getBigDecimal("confidence_score"))
                    .processingTimeMs(rs.getLong("processing_time_ms"))
                    .build();
            
            // Set base fields
            thinkingLog.setId(rs.getLong("id"));
            thinkingLog.setOrganizationId(rs.getLong("organization_id"));
            thinkingLog.setSupportOrganizationId(rs.getLong("support_organization_id"));
            thinkingLog.setCreatedAt(rs.getLocalDateTime("created_at"));
            thinkingLog.setUpdatedAt(rs.getLocalDateTime("updated_at"));
            thinkingLog.setCreatedById(rs.getLong("created_by_id"));
            thinkingLog.setUpdatedById(rs.getLong("updated_by_id"));
            thinkingLog.setCreatedBy(rs.getString("created_by"));
            thinkingLog.setUpdatedBy(rs.getString("updated_by"));
            thinkingLog.setOnBehalfOfId(rs.getLong("on_behalf_of_id"));
            
            // Parse JSON fields
            try {
                String thinkingProcessJson = rs.getString("thinking_process");
                if (thinkingProcessJson != null) {
                    Map<String, Object> thinkingProcess = objectMapper.readValue(thinkingProcessJson, new TypeReference<Map<String, Object>>() {});
                    thinkingLog.setThinkingProcess(thinkingProcess);
                }
                
                String suggestionsJson = rs.getString("suggestions");
                if (suggestionsJson != null) {
                    Map<String, Object> suggestions = objectMapper.readValue(suggestionsJson, new TypeReference<Map<String, Object>>() {});
                    thinkingLog.setSuggestions(suggestions);
                }
            } catch (Exception e) {
                log.warn("Error parsing JSON fields for thinking log {}: {}", rs.getLong("id"), e.getMessage());
            }
            
            return thinkingLog;
        }
    };
    
    @Override
    public ThinkingLog save(ThinkingLog entity) {
        if (entity.getId() == null) {
            return insert(entity);
        } else {
            update(entity);
            return entity;
        }
    }
    
    private ThinkingLog insert(ThinkingLog entity) {
        String sql = """
            INSERT INTO ai.thinking_logs (
                session_id, trigger_event, trigger_entity_id, trigger_entity_type, 
                thinking_strategy, thinking_process, suggestions, session_status, 
                confidence_score, processing_time_ms, organization_id, support_organization_id,
                created_by_id, updated_by_id, created_by, updated_by, on_behalf_of_id
            ) VALUES (?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING id
            """;
        
        try {
            Long id = jdbcTemplate.queryForObject(sql, Long.class,
                entity.getSessionId(),
                entity.getTriggerEvent(),
                entity.getTriggerEntityId(),
                entity.getTriggerEntityType(),
                entity.getThinkingStrategy(),
                entity.getThinkingProcess() != null ? objectMapper.writeValueAsString(entity.getThinkingProcess()) : null,
                entity.getSuggestions() != null ? objectMapper.writeValueAsString(entity.getSuggestions()) : null,
                entity.getSessionStatus(),
                entity.getConfidenceScore(),
                entity.getProcessingTimeMs(),
                entity.getOrganizationId(),
                entity.getSupportOrganizationId(),
                entity.getCreatedById(),
                entity.getUpdatedById(),
                entity.getCreatedBy(),
                entity.getUpdatedBy(),
                entity.getOnBehalfOfId()
            );
            
            entity.setId(id);
            return entity;
            
        } catch (Exception e) {
            log.error("Error inserting thinking log: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to insert thinking log", e);
        }
    }
    
    @Override
    public Long update(ThinkingLog entity) {
        String sql = """
            UPDATE ai.thinking_logs SET 
                thinking_strategy = ?, thinking_process = ?::jsonb, suggestions = ?::jsonb, 
                session_status = ?, confidence_score = ?, processing_time_ms = ?,
                updated_by_id = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """;
        
        try {
            String thinkingProcessJson = null;
            String suggestionsJson = null;
            
            try {
                thinkingProcessJson = entity.getThinkingProcess() != null ? objectMapper.writeValueAsString(entity.getThinkingProcess()) : null;
                suggestionsJson = entity.getSuggestions() != null ? objectMapper.writeValueAsString(entity.getSuggestions()) : null;
            } catch (Exception jsonEx) {
                throw new RuntimeException("Failed to serialize JSON fields", jsonEx);
            }
            
            int rowsAffected = jdbcTemplate.update(sql,
                entity.getThinkingStrategy(),
                thinkingProcessJson,
                suggestionsJson,
                entity.getSessionStatus(),
                entity.getConfidenceScore(),
                entity.getProcessingTimeMs(),
                entity.getUpdatedById(),
                entity.getUpdatedBy(),
                entity.getId()
            );
            
            return rowsAffected > 0 ? entity.getId() : null;
            
        } catch (Exception e) {
            log.error("Error updating thinking log {}: {}", entity.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to update thinking log", e);
        }
    }
    
    @Override
    public Optional<ThinkingLog> findById(Long id) {
        String sql = "SELECT * FROM ai.thinking_logs WHERE id = ?";
        
        try {
            ThinkingLog thinkingLog = jdbcTemplate.queryForObject(sql, rowMapper, id);
            return Optional.ofNullable(thinkingLog);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public void deleteById(Long id) {
        String sql = "DELETE FROM ai.thinking_logs WHERE id = ?";
        jdbcTemplate.update(sql, id);
    }
    
    @Override
    public List<ThinkingLog> findAll(Long organizationId) {
        String sql = "SELECT * FROM ai.thinking_logs WHERE organization_id = ? ORDER BY created_at DESC";
        return jdbcTemplate.query(sql, rowMapper, organizationId);
    }
    
    @Override
    public Optional<ThinkingLog> findBySessionId(String sessionId) {
        String sql = "SELECT * FROM ai.thinking_logs WHERE session_id = ?";
        
        try {
            ThinkingLog thinkingLog = jdbcTemplate.queryForObject(sql, rowMapper, sessionId);
            return Optional.ofNullable(thinkingLog);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<ThinkingLog> findByTriggerEntityTypeAndTriggerEntityId(String triggerEntityType, Long triggerEntityId) {
        String sql = "SELECT * FROM ai.thinking_logs WHERE trigger_entity_type = ? AND trigger_entity_id = ? ORDER BY created_at DESC";
        return jdbcTemplate.query(sql, rowMapper, triggerEntityType, triggerEntityId);
    }
    
    @Override
    public List<ThinkingLog> findBySessionStatus(String sessionStatus) {
        String sql = "SELECT * FROM ai.thinking_logs WHERE session_status = ? ORDER BY created_at DESC";
        return jdbcTemplate.query(sql, rowMapper, sessionStatus);
    }
    
    @Override
    public List<ThinkingLog> findByThinkingStrategy(String thinkingStrategy) {
        String sql = "SELECT * FROM ai.thinking_logs WHERE thinking_strategy = ? ORDER BY created_at DESC";
        return jdbcTemplate.query(sql, rowMapper, thinkingStrategy);
    }
    
    @Override
    public List<ThinkingLog> findBySessionStatusOrderByCreatedAtDesc(String sessionStatus) {
        String sql = "SELECT * FROM ai.thinking_logs WHERE session_status = ? ORDER BY created_at DESC";
        return jdbcTemplate.query(sql, rowMapper, sessionStatus);
    }
} 