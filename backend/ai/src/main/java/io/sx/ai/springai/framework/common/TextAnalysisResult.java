package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Result of text analysis performed by LLM-Controlled Workflow.
 * Contains comprehensive analysis information including sentiment, themes, statistics, and datetime information.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextAnalysisResult {
    
    /**
     * Sentiment analysis result (positive, negative, neutral, mixed)
     */
    private String sentiment;
    
    /**
     * Key themes identified in the text
     */
    private List<String> themes;
    
    /**
     * Text statistics (word count, character count, etc.)
     */
    private TextStatistics statistics;
    
    /**
     * DateTime information extracted from the text
     */
    private DateTimeInfo dateTimeInfo;
    
    /**
     * Comprehensive analysis summary
     */
    private String comprehensiveAnalysis;
    
    /**
     * Timestamp when the analysis was performed
     */
    private String analysisTimestamp;
    
    /**
     * Confidence score for the analysis (0.0 to 1.0)
     */
    private Double confidenceScore;
    
    /**
     * Tools used during the analysis
     */
    private List<String> toolsUsed;
    
    /**
     * Any errors or warnings encountered during analysis
     */
    private List<String> warnings;
    
    @Override
    public String toString() {
        return String.format("TextAnalysisResult{sentiment='%s', themes=%s, statistics=%s, confidence=%.2f}", 
            sentiment, themes, statistics, confidenceScore != null ? confidenceScore : 0.0);
    }
} 