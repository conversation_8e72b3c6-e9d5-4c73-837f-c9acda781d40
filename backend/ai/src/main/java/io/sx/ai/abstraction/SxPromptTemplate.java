package io.sx.ai.abstraction;

import java.util.Map;

/**
 * Framework-agnostic prompt template interface
 * Abstracts template rendering across different AI frameworks
 */
public interface SxPromptTemplate {
    
    /**
     * Render the template with the given variables
     */
    String render(Map<String, Object> variables);
    
    /**
     * Create a prompt from this template with variables
     */
    SxPrompt createPrompt(Map<String, Object> variables);
    
    /**
     * Create a message from this template with variables
     */
    SxMessage createMessage(Map<String, Object> variables);
    
    /**
     * Get the template text
     */
    String getTemplate();
    
    /**
     * Get the template variables
     */
    Map<String, Object> getVariables();
    
    /**
     * Validate that all required variables are provided
     */
    boolean validateVariables(Map<String, Object> variables);
    
    /**
     * Get the list of required variables for this template
     */
    java.util.List<String> getRequiredVariables();
    
    /**
     * Get the list of optional variables for this template
     */
    java.util.List<String> getOptionalVariables();
} 