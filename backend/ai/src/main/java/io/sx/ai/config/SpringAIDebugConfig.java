package io.sx.ai.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import jakarta.annotation.PostConstruct;

/**
 * Debug configuration to log Spring AI properties
 */
@Configuration
public class SpringAIDebugConfig {

    @Autowired
    private Environment env;

    @PostConstruct
    public void logSpringAIProperties() {
        System.out.println("=== SPRING AI CONFIGURATION DEBUG ===");
        
        // Log all Spring AI related properties
        String[] springAIProperties = {
            "spring.ai.ollama.base-url",
            "spring.ai.ollama.chat.options.model",
            "spring.ai.ollama.chat.options.temperature",
            "spring.ai.ollama.chat.options.max-tokens",
            "spring.ai.ollama.chat.options.timeout",
            "spring.ai.ollama.client.timeout",
            "spring.ai.ollama.client.connect-timeout",
            "spring.ai.ollama.client.read-timeout",
            "spring.ai.ollama.client.connection-timeout",
            "spring.ai.ollama.client.response-timeout"
        };

        for (String property : springAIProperties) {
            String value = env.getProperty(property);
            System.out.println(property + " = " + (value != null ? value : "NOT SET"));
        }

        // Log HTTP client properties
        String[] httpClientProperties = {
            "spring.webflux.client.timeout",
            "spring.webflux.client.connect-timeout",
            "spring.webflux.client.read-timeout"
        };

        System.out.println("--- HTTP Client Properties ---");
        for (String property : httpClientProperties) {
            String value = env.getProperty(property);
            System.out.println(property + " = " + (value != null ? value : "NOT SET"));
        }

        System.out.println("================================");
    }
} 