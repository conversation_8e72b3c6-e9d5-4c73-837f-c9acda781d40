package io.sx.ai.springai.spring;

import io.sx.ai.abstraction.SxChatMemory;
import io.sx.ai.abstraction.SxMessage;
import lombok.Data;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.memory.InMemoryChatMemoryRepository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Spring AI implementation of SxChatMemory
 * Wraps Spring AI's ChatMemory class
 */
@Data
public class SxSpringChatMemory implements SxChatMemory {
    
    private final ChatMemory springChatMemory;
    private final String sessionId;
    private final Map<String, Object> metadata;
    
    public SxSpringChatMemory(ChatMemory springChatMemory, String sessionId) {
        this.springChatMemory = springChatMemory;
        this.sessionId = sessionId;
        this.metadata = new HashMap<>();
        this.metadata.put("framework", "spring-ai");
        this.metadata.put("session_id", sessionId);
    }
    
    /**
     * Create a new SxSpringChatMemory with default configuration
     */
    public static SxSpringChatMemory createDefault(String sessionId) {
        ChatMemoryRepository repository = new InMemoryChatMemoryRepository();
        ChatMemory springChatMemory = MessageWindowChatMemory.builder()
            .chatMemoryRepository(repository)
            .maxMessages(10)
            .build();
        return new SxSpringChatMemory(springChatMemory, sessionId);
    }
    
    /**
     * Create a new SxSpringChatMemory with custom configuration
     */
    public static SxSpringChatMemory createWithConfig(String sessionId, ChatMemoryRepository repository, int maxMessages) {
        ChatMemory springChatMemory = MessageWindowChatMemory.builder()
            .chatMemoryRepository(repository)
            .maxMessages(maxMessages)
            .build();
        return new SxSpringChatMemory(springChatMemory, sessionId);
    }
    
    @Override
    public void addMessage(SxMessage message) {
        // Convert SxMessage to Spring AI Message
        org.springframework.ai.chat.messages.Message springMessage = convertToSpringMessage(message);
        springChatMemory.add(sessionId, springMessage);
    }
    
    @Override
    public List<SxMessage> getMessages() {
        List<org.springframework.ai.chat.messages.Message> springMessages = springChatMemory.get(sessionId);
        return springMessages.stream()
            .map(this::convertToSxMessage)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<SxMessage> getMessages(int maxCount) {
        List<SxMessage> allMessages = getMessages();
        int count = Math.min(maxCount, allMessages.size());
        return allMessages.subList(0, count);
    }
    
    @Override
    public void clear() {
        // Spring AI ChatMemory doesn't have a clear method, so we'll create a new one
        // This is a limitation of the current Spring AI implementation
        metadata.put("cleared_at", System.currentTimeMillis());
    }
    
    @Override
    public int getMessageCount() {
        return getMessages().size();
    }
    
    @Override
    public boolean isEmpty() {
        return getMessageCount() == 0;
    }
    
    @Override
    public int getMaxMessages() {
        // Try to get max messages from the underlying Spring AI memory
        if (springChatMemory instanceof MessageWindowChatMemory) {
            // This is a bit of a hack since Spring AI doesn't expose this directly
            return 10; // Default value
        }
        return 10; // Default fallback
    }
    
    @Override
    public Map<String, Object> getMetadata() {
        return new HashMap<>(metadata);
    }
    
    @Override
    public String getSessionId() {
        return sessionId;
    }
    
    @Override
    public void setSessionId(String sessionId) {
        // Note: This doesn't actually change the session ID in Spring AI
        // It just updates our local reference
        metadata.put("session_id", sessionId);
    }
    
    @Override
    public Object getFrameworkMemory() {
        return springChatMemory;
    }
    
    // Helper methods for conversion
    
    private org.springframework.ai.chat.messages.Message convertToSpringMessage(SxMessage sxMessage) {
        if (sxMessage instanceof SxSpringMessage) {
            return ((SxSpringMessage) sxMessage).getSpringMessage();
        }
        
        // Create appropriate Spring message based on type
        switch (sxMessage.getType()) {
            case SYSTEM:
                return new org.springframework.ai.chat.messages.SystemMessage(sxMessage.getContent());
            case USER:
                return new org.springframework.ai.chat.messages.UserMessage(sxMessage.getContent());
            case ASSISTANT:
                return new org.springframework.ai.chat.messages.AssistantMessage(sxMessage.getContent());
            default:
                return new org.springframework.ai.chat.messages.UserMessage(sxMessage.getContent());
        }
    }
    
    private SxMessage convertToSxMessage(org.springframework.ai.chat.messages.Message springMessage) {
        return new SxSpringMessage(springMessage);
    }
} 