package io.sx.ai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class AIAuditLog {
    
    @JsonProperty("audit_id")
    private String auditId;
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("event_type")
    private String eventType;
    
    @JsonProperty("execution_mode")
    private String executionMode; // HYBRID, FULL_MCP
    
    @JsonProperty("organization_id")
    private Long organizationId;
    
    @JsonProperty("on_behalf_of_id")
    private Long onBehalfOfId;
    
    @JsonProperty("timestamp")
    private LocalDateTime timestamp;
    
    @JsonProperty("analysis_request")
    private AIAnalysisRequest analysisRequest;
    
    @JsonProperty("analysis_response")
    private AIAnalysisResponse analysisResponse;
    
    @JsonProperty("tool_executions")
    private List<ToolExecution> toolExecutions;
    
    @JsonProperty("workflow_steps")
    private List<WorkflowStep> workflowSteps;
    
    @JsonProperty("performance_metrics")
    private PerformanceMetrics performanceMetrics;
    
    @JsonProperty("decision_tree")
    private DecisionTree decisionTree;
    
    @JsonProperty("metadata")
    private Map<String, Object> metadata;
    
    @Data
    @Builder
    public static class ToolExecution {
        @JsonProperty("tool_name")
        private String toolName;
        
        @JsonProperty("parameters")
        private Map<String, Object> parameters;
        
        @JsonProperty("execution_timestamp")
        private LocalDateTime executionTimestamp;
        
        @JsonProperty("execution_duration_ms")
        private Long executionDurationMs;
        
        @JsonProperty("success")
        private Boolean success;
        
        @JsonProperty("result")
        private Map<String, Object> result;
        
        @JsonProperty("error_message")
        private String errorMessage;
        
        @JsonProperty("llm_reasoning")
        private String llmReasoning;
        
        @JsonProperty("confidence_score")
        private Double confidenceScore;
    }
    
    @Data
    @Builder
    public static class WorkflowStep {
        @JsonProperty("step_id")
        private String stepId;
        
        @JsonProperty("step_type")
        private String stepType;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("start_timestamp")
        private LocalDateTime startTimestamp;
        
        @JsonProperty("end_timestamp")
        private LocalDateTime endTimestamp;
        
        @JsonProperty("duration_ms")
        private Long durationMs;
        
        @JsonProperty("success")
        private Boolean success;
        
        @JsonProperty("parallel_execution")
        private Boolean parallelExecution;
        
        @JsonProperty("dependencies")
        private List<String> dependencies;
        
        @JsonProperty("output")
        private Map<String, Object> output;
    }
    
    @Data
    @Builder
    public static class PerformanceMetrics {
        @JsonProperty("total_analysis_time_ms")
        private Long totalAnalysisTimeMs;
        
        @JsonProperty("llm_response_time_ms")
        private Long llmResponseTimeMs;
        
        @JsonProperty("tool_execution_time_ms")
        private Long toolExecutionTimeMs;
        
        @JsonProperty("workflow_execution_time_ms")
        private Long workflowExecutionTimeMs;
        
        @JsonProperty("total_tokens_used")
        private Integer totalTokensUsed;
        
        @JsonProperty("llm_provider")
        private String llmProvider;
        
        @JsonProperty("model_version")
        private String modelVersion;
        
        @JsonProperty("cost_estimate")
        private Double costEstimate;
        
        @JsonProperty("memory_usage_mb")
        private Double memoryUsageMb;
        
        @JsonProperty("cpu_usage_percent")
        private Double cpuUsagePercent;
    }
    
    @Data
    @Builder
    public static class DecisionTree {
        @JsonProperty("root_decision")
        private DecisionNode rootDecision;
        
        @JsonProperty("total_decisions")
        private Integer totalDecisions;
        
        @JsonProperty("decision_path")
        private List<String> decisionPath;
        
        @JsonProperty("confidence_progression")
        private List<Double> confidenceProgression;
        
        @JsonProperty("reasoning_chain")
        private List<String> reasoningChain;
    }
    
    @Data
    @Builder
    public static class DecisionNode {
        @JsonProperty("decision_id")
        private String decisionId;
        
        @JsonProperty("decision_type")
        private String decisionType;
        
        @JsonProperty("question")
        private String question;
        
        @JsonProperty("answer")
        private String answer;
        
        @JsonProperty("confidence")
        private Double confidence;
        
        @JsonProperty("reasoning")
        private String reasoning;
        
        @JsonProperty("children")
        private List<DecisionNode> children;
        
        @JsonProperty("action_taken")
        private String actionTaken;
        
        @JsonProperty("tool_called")
        private String toolCalled;
    }
} 