package io.sx.ai.springai.spring;

import io.sx.ai.abstraction.SxMessage;
import io.sx.ai.abstraction.SxPrompt;
import io.sx.ai.abstraction.SxPromptTemplate;
import lombok.Data;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Spring AI implementation of SxPromptTemplate
 * Wraps Spring AI's PromptTemplate class
 */
@Data
public class SxSpringPromptTemplate implements SxPromptTemplate {
    
    private final PromptTemplate springTemplate;
    private final String template;
    private final Map<String, Object> variables;
    private final List<String> requiredVariables;
    private final List<String> optionalVariables;
    
    public SxSpringPromptTemplate(String template) {
        this.springTemplate = new PromptTemplate(template);
        this.template = template;
        this.variables = new HashMap<>();
        this.requiredVariables = extractRequiredVariables(template);
        this.optionalVariables = new ArrayList<>();
    }
    
    public SxSpringPromptTemplate(PromptTemplate springTemplate) {
        this.springTemplate = springTemplate;
        this.template = springTemplate.getTemplate();
        this.variables = new HashMap<>();
        this.requiredVariables = extractRequiredVariables(template);
        this.optionalVariables = new ArrayList<>();
    }
    
    @Override
    public String render(Map<String, Object> variables) {
        return springTemplate.render(variables);
    }
    
    @Override
    public SxPrompt createPrompt(Map<String, Object> variables) {
        org.springframework.ai.chat.prompt.Prompt springPrompt = springTemplate.create(variables);
        return new SxSpringPrompt(springPrompt);
    }
    
    @Override
    public SxMessage createMessage(Map<String, Object> variables) {
        String renderedContent = render(variables);
        return new SxSpringMessage(new org.springframework.ai.chat.messages.UserMessage(renderedContent));
    }
    
    @Override
    public String getTemplate() {
        return template;
    }
    
    @Override
    public Map<String, Object> getVariables() {
        return new HashMap<>(variables);
    }
    
    @Override
    public boolean validateVariables(Map<String, Object> variables) {
        return requiredVariables.stream().allMatch(variables::containsKey);
    }
    
    @Override
    public List<String> getRequiredVariables() {
        return new ArrayList<>(requiredVariables);
    }
    
    @Override
    public List<String> getOptionalVariables() {
        return new ArrayList<>(optionalVariables);
    }
    
    /**
     * Extract required variables from template using regex
     */
    private List<String> extractRequiredVariables(String template) {
        List<String> variables = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(template);
        
        while (matcher.find()) {
            String variable = matcher.group(1);
            if (!variable.contains(":")) { // Simple variable without format specifier
                variables.add(variable);
            } else { // Variable with format specifier like {name:format}
                variables.add(variable.split(":")[0]);
            }
        }
        
        return variables;
    }
    
    /**
     * Create a system prompt template
     */
    public static SxSpringPromptTemplate createSystemTemplate(String template) {
        SystemPromptTemplate systemTemplate = new SystemPromptTemplate(template);
        return new SxSpringPromptTemplate(systemTemplate);
    }
    
    /**
     * Get the underlying Spring AI PromptTemplate
     */
    public PromptTemplate getSpringTemplate() {
        return springTemplate;
    }
} 