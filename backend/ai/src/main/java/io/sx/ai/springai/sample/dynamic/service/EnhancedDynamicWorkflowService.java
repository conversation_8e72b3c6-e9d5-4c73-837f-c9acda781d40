package io.sx.ai.springai.sample.dynamic.service;

import io.sx.ai.springai.FunctionCallingService;
import io.sx.ai.springai.FunctionCallingService.WorkflowResult;
import io.sx.ai.springai.FunctionCallingService.ToolCall;
import io.sx.ai.tools.AITool.ToolContext;
import io.sx.ai.tools.ToolCategory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Enhanced service for executing dynamic workflows using Spring AI function calling.
 * Integrates with the new tool registry and history management system.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EnhancedDynamicWorkflowService {

    private final FunctionCallingService functionCallingService;
    
    @Value("classpath:templates/prompts/arithmetic/function-calling-system.st")
    private Resource systemPromptResource;
    
    @Value("classpath:templates/prompts/arithmetic/function-calling-user.st")
    private Resource userPromptResource;

    /**
     * Execute a dynamic workflow with function calling capabilities.
     * 
     * @param inputNumber The input number to process
     * @param minValue The minimum value of the target range
     * @param maxValue The maximum value of the target range
     * @param maxIterations Maximum number of tool call iterations
     * @param organizationId The organization ID
     * @param onBehalfOfId The user ID on behalf of whom the operation is performed
     * @return The workflow execution result
     */
    public EnhancedWorkflowResult executeWorkflow(double inputNumber, double minValue, double maxValue, 
                                                 int maxIterations, Long organizationId, Long onBehalfOfId) {
        String sessionId = UUID.randomUUID().toString();
        log.info("Executing enhanced dynamic workflow: input={}, range=[{}, {}], maxIterations={}, orgId={}, sessionId={}", 
                inputNumber, minValue, maxValue, maxIterations, organizationId, sessionId);
        
        try {
            // Validate parameters
            validateParameters(inputNumber, minValue, maxValue, maxIterations);
            
            // Create tool context
            ToolContext context = ToolContext.builder()
                    .organizationId(organizationId)
                    .onBehalfOfId(onBehalfOfId)
                    .autonomyMode(ToolCategory.AIAutonomyMode.FULL_MCP)
                    .sessionId(sessionId)
                    .build();
            
            // Load prompts
            String systemPrompt = loadPrompt(systemPromptResource);
            String userPromptTemplate = loadPrompt(userPromptResource);
            
            // Create user prompt with parameters
            PromptTemplate template = new PromptTemplate(userPromptTemplate);
            Prompt userPromptPrompt = template.create(Map.of(
                "inputNumber", inputNumber,
                "minValue", minValue,
                "maxValue", maxValue
            ));
            String userPrompt = userPromptPrompt.toString();
            
            // Execute workflow with function calling
            WorkflowResult result = functionCallingService.executeWorkflow(
                systemPrompt, userPrompt, context, maxIterations
            );
            
            // Create enhanced result
            return EnhancedWorkflowResult.builder()
                    .success(result.isSuccess())
                    .inputNumber(inputNumber)
                    .minValue(minValue)
                    .maxValue(maxValue)
                    .finalResult(result.isSuccess() ? (Double) result.getData() : null)
                    .errorMessage(result.getErrorMessage())
                    .toolCalls(result.getToolCalls())
                    .sessionId(sessionId)
                    .organizationId(organizationId)
                    .onBehalfOfId(onBehalfOfId)
                    .build();
                    
        } catch (Exception e) {
            log.error("Error executing enhanced dynamic workflow: input={}", inputNumber, e);
            return EnhancedWorkflowResult.builder()
                    .success(false)
                    .inputNumber(inputNumber)
                    .minValue(minValue)
                    .maxValue(maxValue)
                    .errorMessage("Enhanced workflow execution failed: " + e.getMessage())
                    .sessionId(sessionId)
                    .organizationId(organizationId)
                    .onBehalfOfId(onBehalfOfId)
                    .build();
        }
    }

    /**
     * Execute a dynamic workflow with default parameters.
     */
    public EnhancedWorkflowResult executeWorkflow(double inputNumber, double minValue, double maxValue, 
                                                 Long organizationId, Long onBehalfOfId) {
        return executeWorkflow(inputNumber, minValue, maxValue, 10, organizationId, onBehalfOfId);
    }

    /**
     * Validate workflow parameters.
     */
    public void validateParameters(double inputNumber, double minValue, double maxValue, int maxIterations) {
        if (Double.isNaN(inputNumber) || Double.isInfinite(inputNumber)) {
            throw new IllegalArgumentException("Input number must be a finite number");
        }
        if (Double.isNaN(minValue) || Double.isInfinite(minValue)) {
            throw new IllegalArgumentException("Min value must be a finite number");
        }
        if (Double.isNaN(maxValue) || Double.isInfinite(maxValue)) {
            throw new IllegalArgumentException("Max value must be a finite number");
        }
        if (minValue >= maxValue) {
            throw new IllegalArgumentException("Min value must be less than max value");
        }
        if (maxIterations <= 0 || maxIterations > 20) {
            throw new IllegalArgumentException("Max iterations must be between 1 and 20");
        }
        
        log.debug("Enhanced workflow parameters validated: input={}, range=[{}, {}], maxIterations={}", 
                inputNumber, minValue, maxValue, maxIterations);
    }

    /**
     * Load prompt from resource.
     */
    private String loadPrompt(Resource resource) throws IOException {
        return resource.getContentAsString(StandardCharsets.UTF_8);
    }

    /**
     * Enhanced workflow result with additional metadata.
     * 
     * TODO: Add workflow history support in future scope
     * - WorkflowHistory and WorkflowHistoryService classes need to be implemented
     * - History tracking for audit and debugging purposes
     * - Session-based history management
     */
    public static class EnhancedWorkflowResult {
        private final boolean success;
        private final double inputNumber;
        private final double minValue;
        private final double maxValue;
        private final Double finalResult;
        private final String errorMessage;
        private final List<ToolCall> toolCalls;
        private final String sessionId;
        private final Long organizationId;
        private final Long onBehalfOfId;
        
        private EnhancedWorkflowResult(Builder builder) {
            this.success = builder.success;
            this.inputNumber = builder.inputNumber;
            this.minValue = builder.minValue;
            this.maxValue = builder.maxValue;
            this.finalResult = builder.finalResult;
            this.errorMessage = builder.errorMessage;
            this.toolCalls = builder.toolCalls;
            this.sessionId = builder.sessionId;
            this.organizationId = builder.organizationId;
            this.onBehalfOfId = builder.onBehalfOfId;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public double getInputNumber() { return inputNumber; }
        public double getMinValue() { return minValue; }
        public double getMaxValue() { return maxValue; }
        public Double getFinalResult() { return finalResult; }
        public String getErrorMessage() { return errorMessage; }
        public List<ToolCall> getToolCalls() { return toolCalls; }
        public String getSessionId() { return sessionId; }
        public Long getOrganizationId() { return organizationId; }
        public Long getOnBehalfOfId() { return onBehalfOfId; }
        // TODO: Implement history getter when WorkflowHistory is available
        // public List<WorkflowHistory.HistoryEntry> getHistory() { return history; }
        
        public static class Builder {
            private boolean success;
            private double inputNumber;
            private double minValue;
            private double maxValue;
            private Double finalResult;
            private String errorMessage;
            private List<ToolCall> toolCalls;
            private String sessionId;
            private Long organizationId;
            private Long onBehalfOfId;
            // TODO: Add history field when WorkflowHistory is implemented
            // private List<WorkflowHistory.HistoryEntry> history;
            
            public Builder success(boolean success) {
                this.success = success;
                return this;
            }
            
            public Builder inputNumber(double inputNumber) {
                this.inputNumber = inputNumber;
                return this;
            }
            
            public Builder minValue(double minValue) {
                this.minValue = minValue;
                return this;
            }
            
            public Builder maxValue(double maxValue) {
                this.maxValue = maxValue;
                return this;
            }
            
            public Builder finalResult(Double finalResult) {
                this.finalResult = finalResult;
                return this;
            }
            
            public Builder errorMessage(String errorMessage) {
                this.errorMessage = errorMessage;
                return this;
            }
            
            public Builder toolCalls(List<ToolCall> toolCalls) {
                this.toolCalls = toolCalls;
                return this;
            }
            
            public Builder sessionId(String sessionId) {
                this.sessionId = sessionId;
                return this;
            }
            
            public Builder organizationId(Long organizationId) {
                this.organizationId = organizationId;
                return this;
            }
            
            public Builder onBehalfOfId(Long onBehalfOfId) {
                this.onBehalfOfId = onBehalfOfId;
                return this;
            }
            
            // TODO: Implement history builder method when WorkflowHistory is available
            // public Builder history(List<WorkflowHistory.HistoryEntry> history) {
            //     this.history = history;
            //     return this;
            // }
            
            public EnhancedWorkflowResult build() {
                return new EnhancedWorkflowResult(this);
            }
        }
    }
} 