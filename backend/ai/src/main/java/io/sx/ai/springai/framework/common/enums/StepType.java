package io.sx.ai.springai.framework.common.enums;

/**
 * Enumeration of workflow step types.
 * Defines the different categories of steps that can be executed in a workflow.
 */
public enum StepType {
    /**
     * Data transformation step - modifies input data
     */
    DATA_TRANSFORMATION,
    
    /**
     * Analysis step - analyzes data without modifying it
     */
    ANALYSIS,
    
    /**
     * Generation step - generates new content or data
     */
    GENERATION,
    
    /**
     * LLM analysis step - uses language model for analysis
     */
    LLM_ANALYSIS,
    
    /**
     * Termination step - ends the workflow
     */
    TERMINATION,
    
    /**
     * Decision step - makes decisions about workflow flow
     */
    DECISION,
    
    /**
     * Mathematical operation step - performs mathematical calculations
     */
    MATHEMATICAL_OPERATION,
    
    /**
     * Text processing step - processes text data
     */
    TEXT_PROCESSING,
    
    /**
     * Validation step - validates data or results
     */
    VALIDATION,
    
    /**
     * Custom step - user-defined step type
     */
    CUSTOM
} 