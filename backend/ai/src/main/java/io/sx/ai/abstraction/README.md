# SX AI Framework-Agnostic Abstraction Layer

## Overview

This package provides a framework-agnostic abstraction layer for AI capabilities, allowing the codebase to switch between different AI frameworks (Spring AI, LangChain4j, etc.) without changing business logic.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                     │
│  (Workflows, Services, Controllers)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                SX AI Abstraction Layer                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ SxLLMClient │ │ SxPrompt    │ │ SxFunction  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │SxVectorStore│ │SxDocument   │ │SxMessage    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Framework Implementations                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Spring AI    │ │LangChain4j  │ │Custom       │           │
│  │Implementation│ │Implementation│ │Implementation│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## Core Interfaces

### SxLLMClient
Framework-agnostic interface for LLM interactions:
- `generate(SxPrompt)` - Generate response for prompt
- `generateStructured(SxPrompt, Class<T>)` - Generate structured output
- `generateWithFunctions(SxPrompt, List<SxFunction>)` - Function calling
- `generateEmbeddings(String)` - Generate embeddings

### SxPrompt
Framework-agnostic prompt representation:
- `getMessages()` - Get prompt messages
- `getText()` - Get rendered text
- `addMessage(SxMessage)` - Add message to prompt

### SxMessage
Framework-agnostic message representation:
- `getContent()` - Get message content
- `getType()` - Get message type (SYSTEM, USER, ASSISTANT)
- `getRole()` - Get message role

### SxPromptTemplate
Framework-agnostic template rendering:
- `render(Map<String, Object>)` - Render template with variables
- `createPrompt(Map<String, Object>)` - Create prompt from template
- `validateVariables(Map<String, Object>)` - Validate required variables

### SxFunction
Framework-agnostic function definition:
- `getName()` - Function name
- `getDescription()` - Function description
- `execute(Map<String, Object>)` - Execute function with arguments

### SxVectorStore
Framework-agnostic vector store operations:
- `addDocument(SxDocument)` - Add document to store
- `similaritySearch(String, int)` - Search for similar documents
- `getDocumentCount()` - Get total document count

### SxDocument
Framework-agnostic document representation:
- `getId()` - Document ID
- `getContent()` - Document content
- `getEmbeddings()` - Document embeddings

## Spring AI Implementation

The `spring` package contains Spring AI implementations of all interfaces:

### SxSpringLLMClient
Wraps Spring AI's `ChatModel`:
```java
@Component
public class SxSpringLLMClient implements SxLLMClient {
    private final ChatModel chatModel;
    
    @Override
    public SxLLMResponse generate(SxPrompt prompt) {
        SxSpringPrompt springPrompt = (SxSpringPrompt) prompt;
        ChatResponse response = chatModel.call(springPrompt.getSpringPrompt());
        return new SxSpringLLMResponse(response);
    }
}
```

### SxSpringPrompt
Wraps Spring AI's `Prompt`:
```java
public class SxSpringPrompt implements SxPrompt {
    private final Prompt springPrompt;
    
    public SxSpringPrompt(Prompt springPrompt) {
        this.springPrompt = springPrompt;
        // Convert Spring AI messages to SxMessages
    }
}
```

### SxSpringMessage
Wraps Spring AI's `Message` classes:
```java
public class SxSpringMessage implements SxMessage {
    private final Message springMessage;
    
    public SxSpringMessage(Message springMessage) {
        this.springMessage = springMessage;
        // Determine type based on Spring AI message type
    }
}
```

## Usage Examples

### Basic LLM Interaction
```java
@Service
public class MyService {
    private final SxAIFactory aiFactory;
    
    public String analyzeText(String text) {
        SxPrompt prompt = aiFactory.createPrompt(
            "Analyze the following text: {text}",
            Map.of("text", text)
        );
        
        SxLLMResponse response = aiFactory.generate(prompt);
        return response.getContent();
    }
}
```

### Structured Output
```java
public record AnalysisResult(String sentiment, Double confidence) {}

public AnalysisResult analyzeSentiment(String text) {
    SxPrompt prompt = aiFactory.createPrompt(
        "Analyze sentiment of: {text}. Return JSON with sentiment and confidence.",
        Map.of("text", text)
    );
    
    SxLLMResponse response = aiFactory.generateStructured(prompt, AnalysisResult.class);
    return response.getStructuredOutput(AnalysisResult.class);
}
```

### Function Calling
```java
@Component
public class MathFunctions {
    @SxFunction(description = "Halves the input number")
    public double halve(@Parameter(description = "Number to halve") double input) {
        return input / 2.0;
    }
}

public double processWithFunctions(double input) {
    SxPrompt prompt = aiFactory.createPrompt("Process the number: " + input);
    List<SxFunction> functions = List.of(halveFunction);
    
    SxLLMResponse response = aiFactory.generateWithFunctions(prompt, functions);
    return response.getStructuredOutput(Double.class);
}
```

### Vector Store Operations
```java
@Service
public class DocumentService {
    private final SxVectorStore vectorStore;
    
    public void addDocument(String content, String id) {
        SxDocument document = new SxDocument(id, content);
        vectorStore.addDocument(document);
    }
    
    public List<SxDocument> findSimilar(String query, int maxResults) {
        return vectorStore.similaritySearch(query, maxResults);
    }
}
```

## Benefits

### Framework Independence
- Switch between Spring AI, LangChain4j, or custom implementations
- No changes to business logic when switching frameworks
- Consistent API across different frameworks

### Testability
- Easy to mock AI components for testing
- Framework-specific implementations can be tested independently
- Business logic can be tested without AI dependencies

### Maintainability
- Clear separation of concerns
- Framework-specific code isolated in implementation packages
- Easy to add new framework support

### Flexibility
- Use different frameworks for different use cases
- Gradual migration between frameworks
- Framework-specific optimizations when needed

## Migration Guide

### From Direct Spring AI Usage
```java
// Before: Direct Spring AI usage
@Autowired
private ChatModel chatModel;

public String process(String input) {
    Prompt prompt = new Prompt(new UserMessage(input));
    ChatResponse response = chatModel.call(prompt);
    return response.getResult().getOutput().getText();
}

// After: Framework-agnostic usage
@Autowired
private SxAIFactory aiFactory;

public String process(String input) {
    SxPrompt prompt = aiFactory.createUserMessage(input);
    SxLLMResponse response = aiFactory.generate(prompt);
    return response.getContent();
}
```

### From Manual JSON Parsing
```java
// Before: Manual JSON parsing
private LLMDecision parseLLMResponse(String response) {
    // Manual parsing logic
    String lowerResponse = response.toLowerCase();
    boolean shouldTerminate = lowerResponse.contains("terminate");
    // ... more parsing
}

// After: Structured output
@StructuredOutput
public record LLMDecision(
    String action,
    String reasoning,
    Double confidence
) {}

public LLMDecision getDecision(String input) {
    SxPrompt prompt = aiFactory.createPrompt("Analyze: " + input);
    SxLLMResponse response = aiFactory.generateStructured(prompt, LLMDecision.class);
    return response.getStructuredOutput(LLMDecision.class);
}
```

## Future Enhancements

### LangChain4j Implementation
- Create `langchain4j` package with LangChain4j implementations
- Support for LangChain4j's chain and agent patterns
- Integration with LangChain4j's tool ecosystem

### Custom Implementation
- Create `custom` package for custom AI implementations
- Support for direct API calls to various LLM providers
- Custom embedding and vector store implementations

### Advanced Features
- Streaming support for long-running operations
- Batch processing capabilities
- Caching and optimization features
- Advanced prompt engineering tools 