package io.sx.ai.dto;

import lombok.Builder;
import lombok.Data;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * AI's escalation decision with reasoning and recommendations
 */
@Data
@Builder
public class AIEscalationDecision {
    
    /**
     * Whether escalation is recommended
     */
    private boolean shouldEscalate;
    
    /**
     * Confidence level (0.0 to 1.0)
     */
    private double confidence;
    
    /**
     * Primary reason for escalation
     */
    private String primaryReason;
    
    /**
     * Detailed reasoning from AI analysis
     */
    private String detailedReasoning;
    
    /**
     * Risk factors identified
     */
    private List<RiskFactor> riskFactors;
    
    /**
     * Recommended actions
     */
    private List<RecommendedAction> recommendedActions;
    
    /**
     * Time sensitivity of the decision
     */
    private Duration timeSensitivity;
    
    /**
     * Whether this overrides basic SLA logic
     */
    private boolean overridesBasicLogic;
    
    /**
     * Customer impact assessment
     */
    private CustomerImpact customerImpact;
    
    /**
     * Business impact assessment
     */
    private BusinessImpact businessImpact;
    
    // Additional useful fields from LLM responses
    /**
     * Summary of the escalation analysis
     */
    private String summary;
    
    /**
     * Key insights from the analysis
     */
    private List<String> keyInsights;
    
    /**
     * Alternative approaches considered
     */
    private List<String> alternativeApproaches;
    
    /**
     * Decision rationale with detailed explanation
     */
    private String decisionRationale;
    
    /**
     * Confidence breakdown by factor
     */
    private Map<String, Double> confidenceBreakdown;
    
    /**
     * Factors contributing to uncertainty
     */
    private String uncertaintyFactors;
    
    /**
     * Summary of recommendations
     */
    private String recommendationsSummary;
    
    /**
     * Contextual data used in decision making
     */
    private Map<String, Object> contextualData;
    
    /**
     * Risk assessment details
     */
    private String riskAssessment;
    
    /**
     * Urgency level classification
     */
    private String urgencyLevel;
    
    /**
     * Impact analysis details
     */
    private String impactAnalysis;
    
    /**
     * Decision tree or decision path taken
     */
    private String decisionTree;
    
    /**
     * Trade-offs considered
     */
    private String tradeOffs;
    
    /**
     * Long-term implications
     */
    private String longTermImplications;
    
    /**
     * Quality metrics for the decision
     */
    private Map<String, Object> qualityMetrics;
    
    /**
     * Performance metrics for the analysis
     */
    private Map<String, Object> performanceMetrics;
    
    @Data
    @Builder
    public static class RiskFactor {
        private String factor;
        private double severity; // 0.0 to 1.0
        private String description;
        
        // Additional useful risk factor fields
        private String category;
        private String mitigation;
        private String probability;
        private String impact;
        private String timeframe;
        private List<String> dependencies;
        private String monitoring;
        private String escalationTriggers;
    }
    
    @Data
    @Builder
    public static class RecommendedAction {
        private String action;
        private String reasoning;
        private double priority; // 0.0 to 1.0
        private Duration urgency;
        
        // Additional useful action fields
        private String actionType;
        private String successCriteria;
        private String failureHandling;
        private String resourceRequirements;
        private String timeline;
        private List<String> stakeholders;
        private String rollbackPlan;
        private String dependencies;
        private String constraints;
        private String riskLevel;
        private String impact;
        private String alternatives;
    }
    
    @Data
    @Builder
    public static class CustomerImpact {
        private double satisfactionRisk; // 0.0 to 1.0
        private boolean renewalAtRisk;
        private String impactDescription;
        
        // Additional useful customer impact fields
        private String customerSegment;
        private String impactType;
        private String severity;
        private String timeframe;
        private String mitigation;
        private String monitoring;
        private String escalationTriggers;
        private String historicalContext;
        private String competitiveRisk;
    }
    
    @Data
    @Builder
    public static class BusinessImpact {
        private double revenueRisk; // 0.0 to 1.0
        private double reputationRisk; // 0.0 to 1.0
        private String impactDescription;
        
        // Additional useful business impact fields
        private String impactCategory;
        private String severity;
        private String timeframe;
        private String mitigation;
        private String monitoring;
        private String escalationTriggers;
        private String historicalContext;
        private String competitiveRisk;
        private String regulatoryRisk;
        private String operationalRisk;
    }
} 