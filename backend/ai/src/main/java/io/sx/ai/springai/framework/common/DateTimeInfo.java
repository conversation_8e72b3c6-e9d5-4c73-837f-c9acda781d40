package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DateTime information extracted from text analysis.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DateTimeInfo {
    
    /**
     * Whether the text contains any dates
     */
    private Boolean hasDates;
    
    /**
     * Whether the text contains any times
     */
    private Boolean hasTimes;
    
    /**
     * List of dates extracted from the text
     */
    private List<String> extractedDates;
    
    /**
     * List of times extracted from the text
     */
    private List<String> extractedTimes;
    
    /**
     * List of datetime ranges found in the text
     */
    private List<DateTimeRange> dateTimeRanges;
    
    /**
     * Current datetime when analysis was performed
     */
    private String currentDateTime;
    
    /**
     * Timezone information if mentioned in the text
     */
    private String timezone;
    
    /**
     * Whether the text contains relative time references (today, tomorrow, etc.)
     */
    private Boolean hasRelativeTimeReferences;
    
    /**
     * List of relative time references found
     */
    private List<String> relativeTimeReferences;
    
    @Override
    public String toString() {
        return String.format("DateTimeInfo{hasDates=%s, hasTimes=%s, dates=%s, times=%s}", 
            hasDates, hasTimes, 
            extractedDates != null ? extractedDates.size() : 0,
            extractedTimes != null ? extractedTimes.size() : 0);
    }
    
    /**
     * Represents a datetime range with start and end times
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateTimeRange {
        private String startDateTime;
        private String endDateTime;
        private String description;
        private Long durationInMinutes;
    }
} 