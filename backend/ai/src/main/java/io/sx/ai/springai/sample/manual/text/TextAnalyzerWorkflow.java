package io.sx.ai.springai.sample.manual.text;

import io.sx.ai.springai.framework.common.BaseStepResult;
import io.sx.ai.springai.framework.common.BaseWorkflowContext;
import io.sx.ai.springai.framework.common.BaseWorkflowOrchestrator;
import io.sx.ai.springai.framework.common.BaseWorkflowResult;
import io.sx.ai.springai.framework.common.BaseWorkflowStep;
import io.sx.ai.springai.framework.common.TextInput;
import io.sx.ai.springai.framework.common.TextOutput;
import io.sx.ai.springai.framework.common.WorkflowInput;
import io.sx.ai.springai.framework.common.WorkflowOutput;
import io.sx.ai.springai.framework.common.WorkflowUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.sx.ai.abstraction.SxLLMClient;
import io.sx.ai.abstraction.SxPrompt;
import io.sx.ai.abstraction.SxAIFactory;
import io.sx.ai.abstraction.SxMessage;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import io.sx.ai.abstraction.SxLLMResponse;

/**
 * Example workflow demonstrating how to use the common classes
 * This workflow performs a simple text analysis using Spring AI
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TextAnalyzerWorkflow implements BaseWorkflowOrchestrator<String, String> {
    
    private final SxLLMClient llmClient;
    private final SxAIFactory aiFactory;
    
    // Example workflow steps
    private final TextAnalysisStep textAnalysisStep = new TextAnalysisStep();
    private final SentimentAnalysisStep sentimentAnalysisStep = new SentimentAnalysisStep();
    private final SummaryGenerationStep summaryGenerationStep = new SummaryGenerationStep();
    
    @Override
    public BaseWorkflowResult<String, String> executeWorkflow(WorkflowInput<String> input, String sessionId, Long organizationId, Long onBehalfOfId) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("Starting example workflow with input: {}, sessionId: {}", input.getValue(), sessionId);
        
        try {
            List<BaseStepResult<String, String>> steps = new ArrayList<>();
            
            // Step 1: Text Analysis
            BaseStepResult<String, String> step1 = textAnalysisStep.execute(input, null);
            steps.add(step1);
            
            // Step 2: Sentiment Analysis (using output from step 1)
            WorkflowInput<String> step2Input = TextInput.of(step1.getOutputAsString());
            BaseStepResult<String, String> step2 = sentimentAnalysisStep.execute(step2Input, step1);
            steps.add(step2);
            
            // Step 3: Summary Generation (using output from step 2)
            WorkflowInput<String> step3Input = TextInput.of(step2.getOutputAsString());
            BaseStepResult<String, String> step3 = summaryGenerationStep.execute(step3Input, step2);
            steps.add(step3);
            
            // Create final output
            TextOutput finalOutput = TextOutput.of(step3.getOutputAsString());
            
            return WorkflowUtils.createSuccessResult(
                input,
                finalOutput,
                steps,
                sessionId,
                "TEXT_ANALYSIS_WORKFLOW",
                startTime
            );
            
        } catch (Exception e) {
            log.error("Error executing example workflow: {}", e.getMessage(), e);
            return WorkflowUtils.createErrorResult(input, e.getMessage(), sessionId);
        }
    }
    
    @Override
    public BaseWorkflowResult<String, String> executeWorkflowFromEvent(Map<String, Object> eventData, String sessionId, Long organizationId, Long onBehalfOfId) {
        String inputValue = WorkflowUtils.extractInputFromEvent(eventData);
        if (inputValue == null) {
            TextInput nullInput = TextInput.of("null");
            return WorkflowUtils.createErrorResult(nullInput, "Could not extract input from event data", sessionId);
        }
        TextInput input = TextInput.of(inputValue);
        return executeWorkflow(input, sessionId, organizationId, onBehalfOfId);
    }
    
    @Override
    public BaseStepResult<String, String> executeWorkflowStep(String sessionId, String stepId, Map<String, Object> context, Long organizationId, Long onBehalfOfId) {
        // For this example, we execute the specific step
        String inputValue = (String) context.get("input");
        if (inputValue == null) {
            return BaseStepResult.error(0, stepId, "null", "Input not found in context");
        }
        TextInput input = TextInput.of(inputValue);
        
        // Execute the appropriate step based on stepId
        switch (stepId) {
            case "text_analysis":
                return textAnalysisStep.execute(input, null);
            case "sentiment_analysis":
                return sentimentAnalysisStep.execute(input, null);
            case "summary_generation":
                return summaryGenerationStep.execute(input, null);
            default:
                return BaseStepResult.error(0, stepId, inputValue, "Unknown step: " + stepId);
        }
    }
    
    @Override
    public BaseWorkflowContext<String, String> getWorkflowStatus(String sessionId) {
        TextInput unknownInput = TextInput.of("unknown");
        return BaseWorkflowContext.initialize(sessionId, unknownInput, 3);
    }
    
    @Override
    public Map<String, String> getAvailableWorkflowTypes() {
        return Map.of(
            "TEXT_ANALYSIS_WORKFLOW", "Text analysis workflow with sentiment and summary generation"
        );
    }
    
    @Override
    public boolean validateWorkflowParameters(String workflowType, Map<String, Object> parameters) {
        if (!"TEXT_ANALYSIS_WORKFLOW".equals(workflowType)) {
            return false;
        }
        
        Object input = parameters.get("input");
        return input != null && !input.toString().trim().isEmpty();
    }
    
    @Override
    public Map<String, Object> getWorkflowStats(Long organizationId) {
        return Map.of(
            "workflow_type", "TEXT_ANALYSIS_WORKFLOW",
            "total_executions", 0,
            "success_rate", 1.0,
            "average_execution_time_ms", 1500
        );
    }
    
    @Override
    public boolean cancelWorkflow(String sessionId, String reason) {
        return false;
    }
    
    @Override
    public List<BaseWorkflowResult<String, String>> getWorkflowHistory(Long organizationId, int limit) {
        return new ArrayList<>();
    }
    
    /**
     * Example workflow step: Text Analysis
     */
    private class TextAnalysisStep implements BaseWorkflowStep<String, String> {
        
        @Override
        public BaseStepResult<String, String> execute(WorkflowInput<String> input, BaseStepResult<?, ?> previousStep) {
            try {
                // Simple text analysis using abstraction
                SxMessage systemMessage = aiFactory.createSystemMessage("You are a text analysis expert.");
                SxMessage userMessage = aiFactory.createUserMessage("Analyze the following text and provide insights: " + input.getValue());
                SxPrompt prompt = aiFactory.createPrompt(java.util.List.of(systemMessage, userMessage));
                SxLLMResponse response = llmClient.generate(prompt);
                String responseText = response.getContent();
                
                return BaseStepResult.success(
                    1,
                    "Text Analysis",
                    input.getValue(),
                    responseText,
                    "text_analysis"
                );
            } catch (Exception e) {
                return BaseStepResult.error(1, "Text Analysis", input.getValue(), "Error in text analysis: " + e.getMessage());
            }
        }

        @Override
        public WorkflowOutput<String> processInput(WorkflowInput<String> input, BaseStepResult<?, ?> previousStep) {
            try {
                SxMessage systemMessage = aiFactory.createSystemMessage("You are a text analysis expert.");
                SxMessage userMessage = aiFactory.createUserMessage("Analyze the following text and provide insights: " + input.getValue());
                SxPrompt prompt = aiFactory.createPrompt(java.util.List.of(systemMessage, userMessage));
                SxLLMResponse response = llmClient.generate(prompt);
                String responseText = response.getContent();
                return TextOutput.of(responseText);
            } catch (Exception e) {
                return null;
            }
        }

        @Override
        public String getStepName() {
            return "Text Analysis";
        }

        @Override
        public String getStepDescription() {
            return "Analyzes the input text and provides insights";
        }

        @Override
        public boolean isFinalStep() {
            return false;
        }

        @Override
        public BaseWorkflowStep.StepType getStepType() {
            return BaseWorkflowStep.StepType.TEXT_PROCESSING;
        }
    }

    private class SentimentAnalysisStep implements BaseWorkflowStep<String, String> {
        
        @Override
        public BaseStepResult<String, String> execute(WorkflowInput<String> input, BaseStepResult<?, ?> previousStep) {
            try {
                // Sentiment analysis using abstraction
                SxMessage systemMessage = aiFactory.createSystemMessage("You are a sentiment analysis expert. Provide JSON responses.");
                SxMessage userMessage = aiFactory.createUserMessage("Analyze the sentiment of the following text: " + input.getValue() +
                        "\n\nProvide a JSON response with:\n" +
                        "- sentiment: positive, negative, or neutral\n" +
                        "- confidence: 0.0 to 1.0\n" +
                        "- reasoning: brief explanation");
                SxPrompt prompt = aiFactory.createPrompt(java.util.List.of(systemMessage, userMessage));
                SxLLMResponse response = llmClient.generate(prompt);
                String responseText = response.getContent();
                
                return BaseStepResult.success(
                    2,
                    "Sentiment Analysis",
                    input.getValue(),
                    responseText,
                    "sentiment_analysis"
                );
            } catch (Exception e) {
                return BaseStepResult.error(2, "Sentiment Analysis", input.getValue(), "Error in sentiment analysis: " + e.getMessage());
            }
        }

        @Override
        public WorkflowOutput<String> processInput(WorkflowInput<String> input, BaseStepResult<?, ?> previousStep) {
            try {
                SxMessage systemMessage = aiFactory.createSystemMessage("You are a sentiment analysis expert. Provide JSON responses.");
                SxMessage userMessage = aiFactory.createUserMessage("Analyze the sentiment of the following text: " + input.getValue() +
                        "\n\nProvide a JSON response with:\n" +
                        "- sentiment: positive, negative, or neutral\n" +
                        "- confidence: 0.0 to 1.0\n" +
                        "- reasoning: brief explanation");
                SxPrompt prompt = aiFactory.createPrompt(java.util.List.of(systemMessage, userMessage));
                SxLLMResponse response = llmClient.generate(prompt);
                String responseText = response.getContent();
                return TextOutput.of(responseText);
            } catch (Exception e) {
                return null;
            }
        }

        @Override
        public String getStepName() {
            return "Sentiment Analysis";
        }

        @Override
        public String getStepDescription() {
            return "Analyzes the sentiment of the input text";
        }

        @Override
        public boolean isFinalStep() {
            return false;
        }

        @Override
        public BaseWorkflowStep.StepType getStepType() {
            return BaseWorkflowStep.StepType.TEXT_PROCESSING;
        }
    }

    private class SummaryGenerationStep implements BaseWorkflowStep<String, String> {
        
        @Override
        public BaseStepResult<String, String> execute(WorkflowInput<String> input, BaseStepResult<?, ?> previousStep) {
            try {
                // Summary generation using abstraction
                SxMessage systemMessage = aiFactory.createSystemMessage("You are a summarization expert.");
                SxMessage userMessage = aiFactory.createUserMessage("Generate a concise summary of the following text: " + input.getValue());
                SxPrompt prompt = aiFactory.createPrompt(java.util.List.of(systemMessage, userMessage));
                SxLLMResponse response = llmClient.generate(prompt);
                String responseText = response.getContent();
                
                return BaseStepResult.success(
                    3,
                    "Summary Generation",
                    input.getValue(),
                    responseText,
                    "summary_generation"
                );
            } catch (Exception e) {
                return BaseStepResult.error(3, "Summary Generation", input.getValue(), "Error in summary generation: " + e.getMessage());
            }
        }

        @Override
        public WorkflowOutput<String> processInput(WorkflowInput<String> input, BaseStepResult<?, ?> previousStep) {
            try {
                SxMessage systemMessage = aiFactory.createSystemMessage("You are a summarization expert.");
                SxMessage userMessage = aiFactory.createUserMessage("Generate a concise summary of the following text: " + input.getValue());
                SxPrompt prompt = aiFactory.createPrompt(java.util.List.of(systemMessage, userMessage));
                SxLLMResponse response = llmClient.generate(prompt);
                String responseText = response.getContent();
                return TextOutput.of(responseText);
            } catch (Exception e) {
                return null;
            }
        }

        @Override
        public String getStepName() {
            return "Summary Generation";
        }

        @Override
        public String getStepDescription() {
            return "Generates a concise summary of the input text";
        }

        @Override
        public boolean isFinalStep() {
            return true;
        }

        @Override
        public BaseWorkflowStep.StepType getStepType() {
            return BaseWorkflowStep.StepType.TEXT_PROCESSING;
        }
    }
} 