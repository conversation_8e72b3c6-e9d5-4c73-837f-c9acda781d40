package io.sx.ai.classification;

import io.sx.ai.model.AISuggestion;
import io.sx.ai.service.TicketCategorizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Dedicated agent for ticket classification
 * Focuses specifically on classifying tickets to known labels
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TicketClassificationAgent {

    private final TicketCategorizationService ticketCategorizationService;

    /**
     * Process ticket classification and generate classification suggestions
     * 
     * @param ticketId The ticket ID to classify
     * @param organizationId The organization ID for context
     * @param supportOrgId The support organization ID
     * @return List of classification suggestions
     */
    public List<AISuggestion> processTicketClassification(Long ticketId, Long organizationId, Long supportOrgId) {
        log.debug("Processing classification for ticket: {}", ticketId);
        
        List<AISuggestion> suggestions = new ArrayList<>();
        
        try {
            // Get classification from service (now uses LLM)
            Map<String, Object> classification = ticketCategorizationService.categorize(
                ticketId, organizationId, supportOrgId);
            
            if (classification != null && classification.get("confidence") != null) {
                // Create classification suggestion with only the fields that exist
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("category", classification.get("category"));
                metadata.put("confidence", classification.get("confidence"));
                metadata.put("reasoning", classification.get("reasoning"));
                
                AISuggestion classificationSuggestion = AISuggestion.builder()
                    .suggestionId("classification-" + ticketId + "-" + System.currentTimeMillis())
                    .sessionId("session-" + ticketId)
                    .ticketId(ticketId)
                    .autonomyLevel(AISuggestion.AutonomyLevel.ADVISOR)
                    .suggestionType(AISuggestion.SuggestionType.CLASSIFICATION)
                    .title("Ticket Classification: " + classification.get("category"))
                    .description("Apply classification labels to ticket")
                    .confidenceScore(BigDecimal.valueOf((Double) classification.get("confidence")))
                    .status(AISuggestion.SuggestionStatus.PENDING)
                    .organizationId(organizationId)
                    .supportOrganizationId(supportOrgId)
                    .createdBy("ai-system")
                    .updatedBy("ai-system")
                    .metadata(metadata)
                    .build();
                
                suggestions.add(classificationSuggestion);
                
                log.debug("Created classification suggestion for ticket {}: {}", 
                    ticketId, classification.get("category"));
            } else {
                log.warn("No classification data returned for ticket: {}", ticketId);
            }
            
        } catch (Exception e) {
            log.error("Error processing classification for ticket: {}", ticketId, e);
            
            // Create error suggestion
            Map<String, Object> errorMetadata = new HashMap<>();
            errorMetadata.put("error", e.getMessage());
            
            AISuggestion errorSuggestion = AISuggestion.builder()
                .suggestionId("classification-error-" + ticketId + "-" + System.currentTimeMillis())
                .sessionId("session-" + ticketId)
                .ticketId(ticketId)
                .autonomyLevel(AISuggestion.AutonomyLevel.ADVISOR)
                .suggestionType(AISuggestion.SuggestionType.CLASSIFICATION)
                .title("Classification Error")
                .description("Failed to classify ticket: " + e.getMessage())
                .confidenceScore(BigDecimal.ZERO)
                .status(AISuggestion.SuggestionStatus.FAILED)
                .organizationId(organizationId)
                .supportOrganizationId(supportOrgId)
                .createdBy("ai-system")
                .updatedBy("ai-system")
                .metadata(errorMetadata)
                .build();
            
            suggestions.add(errorSuggestion);
        }
        
        return suggestions;
    }

    /**
     * Get detailed classification analysis
     * 
     * @param ticketId The ticket ID to analyze
     * @param organizationId The organization ID for context
     * @param supportOrgId The support organization ID
     * @return Classification analysis with recommendations
     */
    public Map<String, Object> getClassificationAnalysis(Long ticketId, Long organizationId, Long supportOrgId) {
        log.debug("Getting classification analysis for ticket: {}", ticketId);
        
        try {
            return ticketCategorizationService.getClassificationAnalysis(ticketId, organizationId, supportOrgId);
        } catch (Exception e) {
            log.error("Error getting classification analysis for ticket: {}", ticketId, e);
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "Failed to analyze classification: " + e.getMessage());
            return errorMap;
        }
    }
} 