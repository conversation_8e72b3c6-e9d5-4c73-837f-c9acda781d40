package io.sx.ai.springai.spring;

import io.sx.ai.abstraction.SxDocument;
import lombok.Data;
import org.springframework.ai.document.Document;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Spring AI implementation of SxDocument
 * Wraps Spring AI's Document class
 */
@Data
public class SxSpringDocument implements SxDocument {

    private final Document springDocument;
    private Double score;
    private String id;
    private String content;
    private List<Double> embeddings;
    private Map<String, Object> metadata;
    private String type;
    private String source;
    private Long createdAt;
    private Long modifiedAt;

    public SxSpringDocument(Document springDocument) {
        this.springDocument = springDocument;
        this.id = springDocument.getId();
        this.content = springDocument.getText();
        this.metadata = springDocument.getMetadata();
//        this.embeddings = springDocument.getEmbedding(); // TODO check why this is neeeded
        this.score = springDocument.getScore(); // Will be set by search results
    }

    /**
     * Create SxSpringDocument from Spring AI Document
     */
    public static SxSpringDocument fromSpringDocument(Document springDocument) {
        return new SxSpringDocument(springDocument);
    }

    /**
     * Create Spring AI Document from SxDocument
     */
    public static Document toSpringDocument(SxDocument sxDocument) {
        return new Document(sxDocument.getContent(), sxDocument.getMetadata());
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getContent() {
        return content;
    }

    @Override
    public List<Double> getEmbeddings() {
        return embeddings;
    }

    @Override
    public Map<String, Object> getMetadata() {
        return metadata != null ? metadata : new HashMap<>();
    }

    @Override
    public Double getScore() {
        return score;
    }

    @Override
    public void setScore(Double score) {
        this.score = score;
    }

    @Override
    public String getType() {
        if (type != null) {
            return type;
        }
        return (String) getMetadataValue("type");
    }

    @Override
    public String getSource() {
        if (source != null) {
            return source;
        }
        return (String) getMetadataValue("source");
    }

    @Override
    public Long getCreatedAt() {
        if (createdAt != null) {
            return createdAt;
        }
        Object value = getMetadataValue("created_at");
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return null;
    }

    @Override
    public Long getModifiedAt() {
        if (modifiedAt != null) {
            return modifiedAt;
        }
        Object value = getMetadataValue("modified_at");
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return null;
    }

    @Override
    public boolean hasEmbeddings() {
        return embeddings != null && !embeddings.isEmpty();
    }

    @Override
    public Object getMetadataValue(String key) {
        return metadata != null ? metadata.get(key) : null;
    }

    @Override
    public void setMetadataValue(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }

    /**
     * Get the underlying Spring AI Document
     */
    public Document getSpringDocument() {
        return springDocument;
    }
}
