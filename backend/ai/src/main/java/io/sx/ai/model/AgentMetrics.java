package io.sx.ai.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentMetrics {
    
    private String agentId;
    private Long organizationId;
    private Integer totalSuggestions;
    private Integer acceptedSuggestions;
    private Integer rejectedSuggestions;
    private Double acceptanceRate;
    private Double averageConfidence;
    private Double averageResponseTime;
    private LocalDateTime lastActivity;
    private Map<String, Object> additionalMetrics;
} 