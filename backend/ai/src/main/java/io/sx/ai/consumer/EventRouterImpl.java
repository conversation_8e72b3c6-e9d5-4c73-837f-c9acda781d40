package io.sx.ai.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class EventRouterImpl implements EventRouter {
    private final List<MessageHandler> messageHandlers;

    @Override
    public void route(String eventType, JsonNode eventData, String subject) {
        for (MessageHandler handler : messageHandlers) {
            if (handler.canHandle(eventType)) {
                try {
                    handler.handle(eventData, subject);
                    return;
                } catch (Exception e) {
                    log.error("Error handling event type {} with handler {}: {}", eventType, handler.getClass().getSimpleName(), e.getMessage(), e);
                    throw new RuntimeException("Failed to handle event: " + eventType, e);
                }
            }
        }
        log.debug("No handler found for event type: {}", eventType);
    }
} 