package io.sx.ai.springai.framework.chain.workflow;

import io.sx.ai.springai.framework.common.BaseWorkflowResult;
import io.sx.ai.springai.framework.common.BaseStepResult;
import io.sx.ai.springai.framework.common.WorkflowInput;
import io.sx.ai.springai.framework.common.WorkflowOutput;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Represents the complete result of a chain workflow execution
 * Now uses List<BaseStepResult<I, O>> for steps
 */
@NoArgsConstructor
public class ChainWorkflowResult<I, O> extends BaseWorkflowResult<I, O> {
    
    public ChainWorkflowResult(WorkflowInput<I> originalInput, WorkflowOutput<O> finalOutput,
                              List<BaseStepResult<I, O>> steps, boolean success, String error,
                              String sessionId, String workflowType) {
        super(originalInput, finalOutput, steps, success, error, sessionId, workflowType, 
              LocalDateTime.now(), LocalDateTime.now(), null, null);
    }
    
    // Static factory method with different name to avoid conflicts
    public static <I, O> ChainWorkflowResultBuilder<I, O> chainBuilder() {
        return new ChainWorkflowResultBuilder<>();
    }
    
    public static class ChainWorkflowResultBuilder<I, O> {
        private WorkflowInput<I> originalInput;
        private WorkflowOutput<O> finalOutput;
        private List<BaseStepResult<I, O>> steps;
        private boolean success;
        private String error;
        private String sessionId;
        private String workflowType;
        
        public ChainWorkflowResultBuilder<I, O> originalInput(WorkflowInput<I> originalInput) {
            this.originalInput = originalInput;
            return this;
        }
        
        public ChainWorkflowResultBuilder<I, O> finalOutput(WorkflowOutput<O> finalOutput) {
            this.finalOutput = finalOutput;
            return this;
        }
        
        public ChainWorkflowResultBuilder<I, O> steps(List<BaseStepResult<I, O>> steps) {
            this.steps = steps;
            return this;
        }
        
        public ChainWorkflowResultBuilder<I, O> success(boolean success) {
            this.success = success;
            return this;
        }
        
        public ChainWorkflowResultBuilder<I, O> error(String error) {
            this.error = error;
            return this;
        }
        
        public ChainWorkflowResultBuilder<I, O> sessionId(String sessionId) {
            this.sessionId = sessionId;
            return this;
        }
        
        public ChainWorkflowResultBuilder<I, O> workflowType(String workflowType) {
            this.workflowType = workflowType;
            return this;
        }
        
        public ChainWorkflowResult<I, O> build() {
            return new ChainWorkflowResult<>(originalInput, finalOutput, steps, success, error, sessionId, workflowType);
        }
    }

    // Convenience methods for backward compatibility
    public List<BaseStepResult<I, O>> getExecutedSteps() {
        return getSteps();
    }

    public void setExecutedSteps(List<BaseStepResult<I, O>> executedSteps) {
        setSteps(executedSteps);
    }

    public String getWorkflowType() {
        return super.getWorkflowType();
    }

    public void setWorkflowType(String workflowType) {
        super.setWorkflowType(workflowType);
    }
} 