package io.sx.ai.abstraction;

import io.sx.ai.springai.spring.SpringResolvedPrompt;
import io.sx.ai.springai.spring.SxSpringMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for resolving prompts and preparing them for LLM consumption.
 * Ensures all prompts are properly resolved before being sent to the LLM.
 */
@Slf4j
@Service
public class PromptResolutionService {
    
    /**
     * Resolve a system prompt
     */
    public ResolvedPrompt resolveSystemPrompt(String template, Map<String, Object> variables) {
        log.debug("Resolving system prompt with {} variables", variables != null ? variables.size() : 0);
        ResolvedPromptValidator.validateRequiredVariables(template, variables);
        ResolvedPrompt resolvedPrompt = SpringResolvedPrompt.systemPrompt(template, variables);
        validateResolvedPromptForLLM(resolvedPrompt);
        log.debug("System prompt resolved successfully: {}", resolvedPrompt.getDescription());
        return resolvedPrompt;
    }
    
    /**
     * Resolve a user prompt
     */
    public ResolvedPrompt resolveUserPrompt(String template, Map<String, Object> variables) {
        log.debug("Resolving user prompt with {} variables", variables != null ? variables.size() : 0);
        ResolvedPromptValidator.validateRequiredVariables(template, variables);
        ResolvedPrompt resolvedPrompt = SpringResolvedPrompt.userPrompt(template, variables);
        validateResolvedPromptForLLM(resolvedPrompt);
        log.debug("User prompt resolved successfully: {}", resolvedPrompt.getDescription());
        return resolvedPrompt;
    }
    
    /**
     * Resolve a prompt with specified prompt type and message role
     */
    public ResolvedPrompt resolvePrompt(String template, Map<String, Object> variables, SxPrompt.PromptType promptType, SxMessage.PromptRole messageRole) {
        log.debug("Resolving prompt with {} variables, promptType: {}, messageRole: {}", 
            variables != null ? variables.size() : 0, promptType, messageRole);
        ResolvedPromptValidator.validateRequiredVariables(template, variables);
        ResolvedPrompt resolvedPrompt = SpringResolvedPrompt.fromTemplate(template, variables, promptType, messageRole);
        validateResolvedPromptForLLM(resolvedPrompt);
        log.debug("Prompt resolved successfully: {}", resolvedPrompt.getDescription());
        return resolvedPrompt;
    }
    
    /**
     * Resolve a prompt with specified prompt type (defaults to USER message role)
     */
    public ResolvedPrompt resolvePrompt(String template, Map<String, Object> variables, SxPrompt.PromptType promptType) {
        return resolvePrompt(template, variables, promptType, SxMessage.PromptRole.USER);
    }
    
    /**
     * Validate a resolved prompt for LLM consumption
     */
    public void validateForLLM(ResolvedPrompt prompt) {
        if (prompt == null) {
            throw new IllegalArgumentException("Prompt cannot be null");
        }
        validateResolvedPromptForLLM(prompt);
        log.debug("Prompt validated for LLM consumption: {}", prompt.getDescription());
    }
    
    /**
     * Validate that a resolved prompt is ready for LLM consumption
     */
    private void validateResolvedPromptForLLM(ResolvedPrompt prompt) {
        if (prompt == null) {
            throw new IllegalArgumentException("Prompt cannot be null");
        }
        
        if (prompt.getResolvedText() == null || prompt.getResolvedText().trim().isEmpty()) {
            throw new IllegalArgumentException("Resolved prompt text cannot be empty");
        }
        
        if (!ResolvedPromptValidator.isValidForLLM(prompt.getResolvedText())) {
            throw new IllegalStateException(
                String.format("Resolved prompt is not valid for LLM consumption. Template: %s, Resolved: %s", 
                    prompt.getTemplateSource(), prompt.getResolvedText()));
        }
    }
    
    /**
     * Combine multiple resolved prompts into a single prompt
     */
    public ResolvedPrompt combinePrompts(List<ResolvedPrompt> prompts) {
        if (prompts == null || prompts.isEmpty()) {
            throw new IllegalArgumentException("Cannot combine empty or null prompt list");
        }
        
        if (prompts.size() == 1) {
            return prompts.get(0);
        }
        
        // Combine all resolved texts
        StringBuilder combinedText = new StringBuilder();
        Map<String, Object> combinedVariables = new HashMap<>();
        List<SxMessage> combinedMessages = new ArrayList<>();
        
        for (ResolvedPrompt prompt : prompts) {
            if (prompt.getResolvedText() != null && !prompt.getResolvedText().trim().isEmpty()) {
                if (combinedText.length() > 0) {
                    combinedText.append("\n\n");
                }
                combinedText.append(prompt.getResolvedText());
            }
            
            // Merge variables
            if (prompt.getOriginalVariables() != null) {
                combinedVariables.putAll(prompt.getOriginalVariables());
            }
            
            // Merge messages
            if (prompt.getResolvedMessages() != null) {
                combinedMessages.addAll(prompt.getResolvedMessages());
            }
        }
        
        // Use the prompt type and message role from the first prompt
        ResolvedPrompt firstPrompt = prompts.get(0);
        
        return SpringResolvedPrompt.builder()
            .resolvedText(combinedText.toString())
            .resolvedMessages(combinedMessages)
            .originalVariables(combinedVariables)
            .promptType(firstPrompt.getPromptType())
            .messageRole(firstPrompt.getMessageRole())
            .templateSource("combined_prompts")
            .fullyResolved(true)
            .build();
    }
    
    /**
     * Create a resolved prompt from a simple text string
     */
    public ResolvedPrompt createSimplePrompt(String text, SxPrompt.PromptType promptType, SxMessage.PromptRole messageRole) {
        return SpringResolvedPrompt.builder()
            .resolvedText(text)
            .resolvedMessages(createSimpleMessage(text, messageRole))
            .originalVariables(new HashMap<>())
            .promptType(promptType)
            .messageRole(messageRole)
            .templateSource("simple_text")
            .fullyResolved(true)
            .build();
    }
    
    /**
     * Create a simple message from text
     */
    private List<SxMessage> createSimpleMessage(String text, SxMessage.PromptRole messageRole) {
        List<SxMessage> messages = new ArrayList<>();
        
        switch (messageRole) {
            case SYSTEM:
                messages.add(new SxSpringMessage(
                    new org.springframework.ai.chat.messages.SystemMessage(text)));
                break;
            case USER:
                messages.add(new SxSpringMessage(
                    new org.springframework.ai.chat.messages.UserMessage(text)));
                break;
            case ASSISTANT:
                messages.add(new SxSpringMessage(
                    new org.springframework.ai.chat.messages.AssistantMessage(text)));
                break;
            default:
                messages.add(new SxSpringMessage(
                    new org.springframework.ai.chat.messages.UserMessage(text)));
                break;
        }
        
        return messages;
    }
} 