package io.sx.ai.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.nats.client.ConsumerContext;
import io.nats.client.FetchConsumer;
import io.nats.client.Message;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Component
@RequiredArgsConstructor
public class AINatsConsumer {
    private final NatsConnectionManager natsConnectionManager;
    private final EventRouter eventRouter;
    private final ObjectMapper objectMapper;

    @Value("${nats.jetstream.audit.stream.name:sx-audit-stream}")
    private String streamName;

    @Value("${nats.jetstream.audit.stream.subjects:sx.audit.events.>}")
    private String streamSubjects;

    @Value("${nats.jetstream.audit.consumer.name:ai-consumer}")
    private String consumerName;

    @Value("${nats.jetstream.audit.consumer.durable:true}")
    private boolean durable;

    @Value("${nats.jetstream.audit.consumer.ack-policy:Explicit}")
    private String ackPolicy;

    @Value("${nats.jetstream.audit.consumer.deliver-policy:All}")
    private String deliverPolicy;

    @Value("${nats.jetstream.audit.consumer.replay-policy:Instant}")
    private String replayPolicy;

    @Value("${nats.consumer.batch-size:10}")
    private int batchSize;

    @Value("${nats.consumer.fetch-interval-ms:2000}")
    private long fetchIntervalMs;

    @Value("${nats.consumer.max-reconnect-attempts:5}")
    private int maxReconnectAttempts;

    @Value("${nats.consumer.reconnect-wait-ms:1000}")
    private long reconnectWaitMs;

    private ConsumerContext consumerContext;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final AtomicBoolean isRunning = new AtomicBoolean(true);

    @PostConstruct
    public void init() {
        log.info("Initializing AI NATS Consumer");
        natsConnectionManager.setupJetStream();
        consumerContext = natsConnectionManager.createConsumer();
        startConsumer();
    }

    @PreDestroy
    public void shutdown() {
        log.info("Shutting down AI NATS Consumer");
        isRunning.set(false);
        scheduler.shutdown();
    }



    private void startConsumer() {
        // Start the consumer in a scheduled thread for pull-based consumption
        scheduler.scheduleAtFixedRate(() -> {
            if (!isRunning.get()) return;
            try {
                // Use fetchMessages with batch size for pull-based consumption
                try (FetchConsumer fetchConsumer = consumerContext.fetchMessages(batchSize)) {
                    Message msg = fetchConsumer.nextMessage();
                    while (msg != null) {
                        try {
                            handleMessage(msg);
                            msg.ack();
                        } catch (Exception e) {
                            log.error("Error processing message: {}", e.getMessage(), e);
                            msg.nak();
                        }
                        msg = fetchConsumer.nextMessage();
                    }
                }
            } catch (Exception e) {
                log.error("Error in consumer loop: {}", e.getMessage(), e);
                natsConnectionManager.handleConnectionError();
            }
        }, 0, fetchIntervalMs, TimeUnit.MILLISECONDS);
    }



    private void handleMessage(Message message) {
        try {
            String messageJson = new String(message.getData(), StandardCharsets.UTF_8);
            log.info("AI Consumer received message on subject: {}", message.getSubject());
            log.debug("Message payload: {}", messageJson);

            // Parse the message as JSON
            JsonNode eventData = objectMapper.readTree(messageJson);
            
            // Extract event type from subject (audit events format: sx.audit.events.ticket.INSERT)
            String subject = message.getSubject();
            String eventType = extractAuditEventType(subject);
            
            log.info("Processing audit event type: {} for subject: {}", eventType, subject);

            // Route to appropriate handler via EventRouter
            eventRouter.route(eventType, eventData, subject);

        } catch (JsonProcessingException e) {
            log.error("Error parsing JSON message: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to parse message JSON", e);
        } catch (Exception e) {
            log.error("Error processing AI message: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process message", e);
        }
    }



    private String extractAuditEventType(String subject) {
        // Extract event type from subject like "sx.audit.events.ticket.INSERT"
        String[] parts = subject.split("\\.");
        if (parts.length >= 5) {
            String entity = parts[3]; // "ticket"
            String operation = parts[4]; // "INSERT" or "UPDATE"
            
            // Map the operations to our enum values
            if ("ticket".equals(entity)) {
                switch (operation) {
                    case "INSERT":
                        return "ticket.c";
                    case "UPDATE":
                        return "ticket.u";
                    default:
                        return entity + "." + operation;
                }
            }
            return entity + "." + operation;
        }
        return subject;
    }


} 