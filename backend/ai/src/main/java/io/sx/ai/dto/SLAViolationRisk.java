package io.sx.ai.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SLAViolationRisk {
    private Long ticketId;
    private String riskLevel;
    private Long timeToViolation; // seconds
    private Double violationProbability;
    private List<String> riskFactors;
    private List<String> recommendedActions;
} 