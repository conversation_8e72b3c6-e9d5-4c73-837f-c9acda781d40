package io.sx.ai.abstraction;

import java.util.List;
import java.util.Map;

/**
 * Framework-agnostic function interface for function calling
 * Abstracts function definitions across different AI frameworks
 */
public interface SxFunction {
    
    /**
     * Get the function name
     */
    String getName();
    
    /**
     * Get the function description
     */
    String getDescription();
    
    /**
     * Get the function parameters schema
     */
    Map<String, Object> getParameters();
    
    /**
     * Get the function parameters as a list
     */
    List<SxParameter> getParameterList();
    
    /**
     * Execute the function with the given arguments
     */
    Object execute(Map<String, Object> arguments);
    
    /**
     * Check if the function is available
     */
    boolean isAvailable();
    
    /**
     * Get the function metadata
     */
    Map<String, Object> getMetadata();
    
    /**
     * Function parameter definition
     */
    class SxParameter {
        private final String name;
        private final String type;
        private final String description;
        private final boolean required;
        private final Object defaultValue;
        
        public SxParameter(String name, String type, String description, boolean required, Object defaultValue) {
            this.name = name;
            this.type = type;
            this.description = description;
            this.required = required;
            this.defaultValue = defaultValue;
        }
        
        // Getters
        public String getName() { return name; }
        public String getType() { return type; }
        public String getDescription() { return description; }
        public boolean isRequired() { return required; }
        public Object getDefaultValue() { return defaultValue; }
    }
} 