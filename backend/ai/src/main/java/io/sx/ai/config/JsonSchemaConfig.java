package io.sx.ai.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.victools.jsonschema.generator.*;
import com.github.victools.jsonschema.module.jackson.JacksonModule;
import com.github.victools.jsonschema.module.swagger2.Swagger2Module;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for JSON Schema generation and validation.
 * Provides SchemaGenerator bean for dependency injection.
 */
@Configuration
public class JsonSchemaConfig {

    /**
     * Configure and provide SchemaGenerator bean.
     * This bean can throw exceptions during configuration, so it's properly managed by Spring.
     */
    @Bean
    public SchemaGenerator schemaGenerator(ObjectMapper objectMapper) {
        // Configure schema generator with Jackson and Swagger2 modules
        SchemaGeneratorConfigBuilder configBuilder = new SchemaGeneratorConfigBuilder(
            objectMapper, 
            SchemaVersion.DRAFT_2020_12, 
            OptionPreset.PLAIN_JSON
        );
        
        configBuilder
            .with(new JacksonModule())
            .with(new Swagger2Module())
            .with(Option.DEFINITIONS_FOR_ALL_OBJECTS)
            .with(Option.NULLABLE_FIELDS_BY_DEFAULT);
        
        return new SchemaGenerator(configBuilder.build());
    }
} 