package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Structured output result classes for Spring AI integration
 * These classes define the structure for AI-generated responses
 */
public class StructuredOutputResults {
    
    /**
     * Structured result for numeric operations
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NumericResult {
        /**
         * The calculated numeric result
         */
        private Double result;
        
        /**
         * Unit of measurement
         */
        private String unit;
        
        /**
         * Confidence score (0.0 to 1.0)
         */
        private Double confidence;
        
        /**
         * Brief explanation of the calculation
         */
        private String explanation;
        
        /**
         * The operation that was performed
         */
        private String operation;
    }
    
    /**
     * Structured result for text analysis
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TextAnalysisResult {
        /**
         * Detailed text analysis
         */
        private String analysis;
        
        /**
         * Overall sentiment (positive/negative/neutral)
         */
        private String sentiment;
        
        /**
         * Confidence score (0.0 to 1.0)
         */
        private Double confidence;
        
        /**
         * List of key terms found
         */
        private List<String> keywords;
        
        /**
         * Total number of words
         */
        private Integer wordCount;
        
        /**
         * Detected language if different from specified
         */
        private String language;
    }
    
    /**
     * Structured result for sentiment analysis
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentResult {
        /**
         * Overall sentiment (positive/negative/neutral)
         */
        private String sentiment;
        
        /**
         * Confidence score (0.0 to 1.0)
         */
        private Double confidence;
        
        /**
         * Brief explanation of the sentiment
         */
        private String explanation;
        
        /**
         * Sentiment intensity (low/medium/high)
         */
        private String intensity;
        
        /**
         * List of detected emotions
         */
        private List<String> emotions;
    }
    
    /**
     * Structured result for summary generation
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SummaryResult {
        /**
         * The generated summary
         */
        private String summary;
        
        /**
         * Actual number of words in summary
         */
        private Integer wordCount;
        
        /**
         * Confidence score (0.0 to 1.0)
         */
        private Double confidence;
        
        /**
         * List of main points covered
         */
        private List<String> keyPoints;
    }
} 