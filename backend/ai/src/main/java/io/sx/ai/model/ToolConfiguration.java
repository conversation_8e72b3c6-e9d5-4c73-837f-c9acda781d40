package io.sx.ai.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.sx.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * ToolConfiguration model for tool-specific retry configurations and execution parameters
 */
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ToolConfiguration extends BaseModel {
    
    @JsonProperty("tool_name")
    private String toolName; // Primary key - name of the tool
    
    @JsonProperty("retry_config")
    private Map<String, Object> retryConfig; // Retry configuration as JSONB
    
    @JsonProperty("timeout_ms")
    private Long timeoutMs; // Timeout for tool execution in milliseconds
    
    @JsonProperty("max_attempts")
    private Integer maxAttempts; // Maximum number of attempts
    
    @JsonProperty("backoff_multiplier")
    private BigDecimal backoffMultiplier; // Multiplier for exponential backoff
    
    @JsonProperty("retry_conditions")
    private List<String> retryConditions; // Conditions that trigger retries
    
    @JsonProperty("skip_retry_conditions")
    private List<String> skipRetryConditions; // Conditions that skip retries
    
    @JsonProperty("created_at")
    private java.time.LocalDateTime createdAt;
    
    @JsonProperty("updated_at")
    private java.time.LocalDateTime updatedAt;
} 