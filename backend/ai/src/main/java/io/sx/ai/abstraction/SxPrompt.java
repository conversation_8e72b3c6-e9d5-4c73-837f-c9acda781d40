package io.sx.ai.abstraction;

import java.util.List;
import java.util.Map;

/**
 * Framework-agnostic prompt interface
 * Abstracts prompt creation and management across different AI frameworks
 */
public interface SxPrompt {
    
    /**
     * Get the messages in this prompt
     */
    List<SxMessage> getMessages();
    
    /**
     * Get the rendered text of this prompt
     */
    String getText();
    
    /**
     * Get metadata associated with this prompt
     */
    Map<String, Object> getMetadata();
    
    /**
     * Add a message to this prompt
     */
    SxPrompt addMessage(SxMessage message);
    
    /**
     * Add multiple messages to this prompt
     */
    SxPrompt addMessages(List<SxMessage> messages);
    
    /**
     * Get the prompt type
     */
    PromptType getType();
    
    /**
     * Prompt types for categorization
     */
    enum PromptType {
        CHAT,
        COMPLETION,
        STRUCTURED_OUTPUT,
        FUNCTION_CALLING,
        EMBEDDING
    }
} 