package io.sx.ai.tools.logic;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * Plain tool logic for datetime operations.
 * Framework-agnostic implementation that can be used by any AI framework.
 */
public class DateTimeLogic {
    
    /**
     * Get the current date and time in the specified timezone
     */
    public String getCurrentDateTime(ZoneId zoneId) {
        if (zoneId == null) {
            zoneId = ZoneId.systemDefault();
        }
        return LocalDateTime.now().atZone(zoneId).toString();
    }
    
    /**
     * Get the current date and time in system default timezone
     */
    public String getCurrentDateTime() {
        return getCurrentDateTime(ZoneId.systemDefault());
    }
    
    /**
     * Format a datetime string using the specified pattern
     */
    public String formatDateTime(String dateTimeString, String pattern) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            throw new IllegalArgumentException("DateTime string cannot be null or empty");
        }
        
        if (pattern == null || pattern.trim().isEmpty()) {
            pattern = "yyyy-MM-dd HH:mm:ss";
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeString);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return dateTime.format(formatter);
        } catch (DateTimeParseException e) {
            throw new RuntimeException("Failed to parse datetime: " + dateTimeString, e);
        } catch (Exception e) {
            throw new RuntimeException("Failed to format datetime: " + dateTimeString, e);
        }
    }
    
    /**
     * Calculate the difference between two dates in days
     */
    public long calculateDateDifference(String startDate, String endDate) {
        if (startDate == null || startDate.trim().isEmpty()) {
            throw new IllegalArgumentException("Start date cannot be null or empty");
        }
        
        if (endDate == null || endDate.trim().isEmpty()) {
            throw new IllegalArgumentException("End date cannot be null or empty");
        }
        
        try {
            LocalDateTime start = LocalDateTime.parse(startDate);
            LocalDateTime end = LocalDateTime.parse(endDate);
            return Duration.between(start, end).toDays();
        } catch (DateTimeParseException e) {
            throw new RuntimeException("Failed to parse date: " + startDate + " or " + endDate, e);
        } catch (Exception e) {
            throw new RuntimeException("Failed to calculate date difference", e);
        }
    }
    
    /**
     * Calculate the difference between two dates in hours
     */
    public long calculateDateDifferenceHours(String startDate, String endDate) {
        if (startDate == null || startDate.trim().isEmpty()) {
            throw new IllegalArgumentException("Start date cannot be null or empty");
        }
        
        if (endDate == null || endDate.trim().isEmpty()) {
            throw new IllegalArgumentException("End date cannot be null or empty");
        }
        
        try {
            LocalDateTime start = LocalDateTime.parse(startDate);
            LocalDateTime end = LocalDateTime.parse(endDate);
            return Duration.between(start, end).toHours();
        } catch (DateTimeParseException e) {
            throw new RuntimeException("Failed to parse date: " + startDate + " or " + endDate, e);
        } catch (Exception e) {
            throw new RuntimeException("Failed to calculate date difference", e);
        }
    }
    
    /**
     * Calculate the difference between two dates in minutes
     */
    public long calculateDateDifferenceMinutes(String startDate, String endDate) {
        if (startDate == null || startDate.trim().isEmpty()) {
            throw new IllegalArgumentException("Start date cannot be null or empty");
        }
        
        if (endDate == null || endDate.trim().isEmpty()) {
            throw new IllegalArgumentException("End date cannot be null or empty");
        }
        
        try {
            LocalDateTime start = LocalDateTime.parse(startDate);
            LocalDateTime end = LocalDateTime.parse(endDate);
            return Duration.between(start, end).toMinutes();
        } catch (DateTimeParseException e) {
            throw new RuntimeException("Failed to parse date: " + startDate + " or " + endDate, e);
        } catch (Exception e) {
            throw new RuntimeException("Failed to calculate date difference", e);
        }
    }
    
    /**
     * Add days to a datetime
     */
    public String addDays(String dateTimeString, long days) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            throw new IllegalArgumentException("DateTime string cannot be null or empty");
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeString);
            LocalDateTime result = dateTime.plusDays(days);
            return result.toString();
        } catch (DateTimeParseException e) {
            throw new RuntimeException("Failed to parse datetime: " + dateTimeString, e);
        } catch (Exception e) {
            throw new RuntimeException("Failed to add days to datetime", e);
        }
    }
    
    /**
     * Add hours to a datetime
     */
    public String addHours(String dateTimeString, long hours) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            throw new IllegalArgumentException("DateTime string cannot be null or empty");
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeString);
            LocalDateTime result = dateTime.plusHours(hours);
            return result.toString();
        } catch (DateTimeParseException e) {
            throw new RuntimeException("Failed to parse datetime: " + dateTimeString, e);
        } catch (Exception e) {
            throw new RuntimeException("Failed to add hours to datetime", e);
        }
    }
    
    /**
     * Check if a datetime string is valid
     */
    public boolean isValidDateTime(String dateTimeString) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            return false;
        }
        
        try {
            LocalDateTime.parse(dateTimeString);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }
    
    /**
     * Get the day of week for a datetime
     */
    public String getDayOfWeek(String dateTimeString) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            throw new IllegalArgumentException("DateTime string cannot be null or empty");
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeString);
            return dateTime.getDayOfWeek().toString();
        } catch (DateTimeParseException e) {
            throw new RuntimeException("Failed to parse datetime: " + dateTimeString, e);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get day of week", e);
        }
    }
} 