package io.sx.ai.orchestration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * REST Controller for ticket processing operations
 * Exposes endpoints for processing tickets through the AI workflow
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/tickets")
@RequiredArgsConstructor
public class TicketOrchestratorController {
    
    private final TicketOrchestrator ticketOrchestrator;
    
    /**
     * Process a ticket creation event
     */
    @PostMapping("/process")
    public ResponseEntity<TicketOrchestrator.TicketProcessingResult> processTicket(
            @RequestBody Map<String, Object> request) {
        
        log.info("REST: Processing ticket with request: {}", request);
        
        try {
            // Extract required fields
            Long ticketId = extractLong(request, "ticketId");
            Long organizationId = extractLong(request, "organizationId");
            Long supportOrgId = extractLong(request, "supportOrgId");
            Long userId = extractLong(request, "userId");
            Map<String, Object> ticketData = extractTicketData(request);
            
            if (ticketId == null || organizationId == null || supportOrgId == null) {
                return ResponseEntity.badRequest().body(
                    TicketOrchestrator.TicketProcessingResult.error("invalid-session", null, 
                        "Missing required fields: ticketId, organizationId, supportOrgId"));
            }
            
            // Process the ticket
            TicketOrchestrator.TicketProcessingResult result = ticketOrchestrator.processTicketCreation(
                    ticketId, ticketData, organizationId, supportOrgId, userId);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("REST: Error processing ticket: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(
                TicketOrchestrator.TicketProcessingResult.error("error-session", null, 
                    "Processing failed: " + e.getMessage()));
        }
    }
    
    /**
     * Process a ticket update event
     */
    @PostMapping("/update")
    public ResponseEntity<TicketOrchestrator.TicketProcessingResult> processTicketUpdate(
            @RequestBody Map<String, Object> request) {
        
        log.info("REST: Processing ticket update with request: {}", request);
        
        try {
            // Extract required fields
            Long ticketId = extractLong(request, "ticketId");
            Long organizationId = extractLong(request, "organizationId");
            Long supportOrgId = extractLong(request, "supportOrgId");
            Long userId = extractLong(request, "userId");
            Map<String, Object> ticketData = extractTicketData(request);
            
            if (ticketId == null || organizationId == null || supportOrgId == null) {
                return ResponseEntity.badRequest().body(
                    TicketOrchestrator.TicketProcessingResult.error("invalid-session", null, 
                        "Missing required fields: ticketId, organizationId, supportOrgId"));
            }
            
            // Process the ticket update
            TicketOrchestrator.TicketProcessingResult result = ticketOrchestrator.processTicketUpdate(
                    ticketId, ticketData, organizationId, supportOrgId, userId);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("REST: Error processing ticket update: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(
                TicketOrchestrator.TicketProcessingResult.error("error-session", null, 
                    "Processing failed: " + e.getMessage()));
        }
    }
    
    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        return ResponseEntity.ok(Map.of(
            "status", "healthy",
            "service", "Ticket Orchestrator",
            "framework", "AI Decision Framework"
        ));
    }
    
    // Helper methods
    private Long extractLong(Map<String, Object> request, String key) {
        Object value = request.get(key);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return null;
    }
    
    @SuppressWarnings("unchecked")
    private Map<String, Object> extractTicketData(Map<String, Object> request) {
        Object ticketData = request.get("ticketData");
        if (ticketData instanceof Map) {
            return (Map<String, Object>) ticketData;
        }
        // If no separate ticketData, use the entire request as ticket data
        return request;
    }
} 