package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Analysis output wrapper for analysis result objects with Spring AI integration
 * Supports text analysis, sentiment analysis, and other analysis results
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisOutput<T> implements WorkflowOutput<T> {
    
    /**
     * The analysis result value
     */
    private T value;
    
    /**
     * Analysis type (e.g., "text_analysis", "sentiment_analysis", "comprehensive_analysis")
     */
    private String analysisType;
    
    /**
     * Format specification (e.g., "structured", "json", "detailed")
     */
    private String format;
    
    /**
     * Confidence score (0.0 to 1.0)
     */
    private Double confidence;
    
    /**
     * Additional metadata
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    @Override
    public String getType() {
        return "ANALYSIS";
    }
    
    @Override
    public Map<String, Object> getMetadata() {
        Map<String, Object> enhancedMetadata = new HashMap<>(metadata);
        enhancedMetadata.put("analysisType", analysisType);
        enhancedMetadata.put("format", format);
        return enhancedMetadata;
    }
    
    /**
     * Static factory method for creating analysis outputs
     */
    public static <T> AnalysisOutput<T> of(T value) {
        return AnalysisOutput.<T>builder()
            .value(value)
            .analysisType("general_analysis")
            .format("structured")
            .confidence(1.0)
            .build();
    }
    
    /**
     * Static factory method for creating analysis outputs with type
     */
    public static <T> AnalysisOutput<T> of(T value, String analysisType) {
        return AnalysisOutput.<T>builder()
            .value(value)
            .analysisType(analysisType)
            .format("structured")
            .confidence(1.0)
            .build();
    }
    
    /**
     * Static factory method for creating analysis outputs with confidence
     */
    public static <T> AnalysisOutput<T> of(T value, String analysisType, Double confidence) {
        return AnalysisOutput.<T>builder()
            .value(value)
            .analysisType(analysisType)
            .format("structured")
            .confidence(confidence)
            .build();
    }
} 