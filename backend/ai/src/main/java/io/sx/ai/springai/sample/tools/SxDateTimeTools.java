package io.sx.ai.springai.sample.tools;

import io.sx.ai.tools.logic.DateTimeLogic;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.time.ZoneId;

/**
 * Spring AI wrapper methods for datetime operations that use plain tool logic classes.
 * Provides @Tool annotations for Spring AI integration.
 */
@Component
public class SxDateTimeTools {
    
    private final DateTimeLogic dateTimeLogic = new DateTimeLogic();
    
    @Tool(description = "Get current date and time in system default timezone")
    public String getCurrentDateTime() {
        return dateTimeLogic.getCurrentDateTime();
    }
    
    @Tool(description = "Format a datetime string using a pattern")
    public String formatDateTime(String dateTimeString, String pattern) {
        return dateTimeLogic.formatDateTime(dateTimeString, pattern);
    }
    
    @Tool(description = "Calculate the difference between two dates in days")
    public long calculateDateDifference(String startDate, String endDate) {
        return dateTimeLogic.calculateDateDifference(startDate, endDate);
    }
    
    @Tool(description = "Calculate the difference between two dates in hours")
    public long calculateDateDifferenceHours(String startDate, String endDate) {
        return dateTimeLogic.calculateDateDifferenceHours(startDate, endDate);
    }
    
    @Tool(description = "Calculate the difference between two dates in minutes")
    public long calculateDateDifferenceMinutes(String startDate, String endDate) {
        return dateTimeLogic.calculateDateDifferenceMinutes(startDate, endDate);
    }
    
    @Tool(description = "Add days to a datetime")
    public String addDays(String dateTimeString, long days) {
        return dateTimeLogic.addDays(dateTimeString, days);
    }
    
    @Tool(description = "Add hours to a datetime")
    public String addHours(String dateTimeString, long hours) {
        return dateTimeLogic.addHours(dateTimeString, hours);
    }
    
    @Tool(description = "Check if a datetime string is valid")
    public boolean isValidDateTime(String dateTimeString) {
        return dateTimeLogic.isValidDateTime(dateTimeString);
    }
    
    @Tool(description = "Get the day of week for a datetime")
    public String getDayOfWeek(String dateTimeString) {
        return dateTimeLogic.getDayOfWeek(dateTimeString);
    }
} 