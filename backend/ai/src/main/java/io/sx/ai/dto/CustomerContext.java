package io.sx.ai.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerContext {
    private Long customerId;
    private String customerTier;
    private Integer totalTickets;
    private Integer openTickets;
    private Double averageResolutionTime; // hours
    private Integer escalationHistory;
    private Double customerSatisfaction;
} 