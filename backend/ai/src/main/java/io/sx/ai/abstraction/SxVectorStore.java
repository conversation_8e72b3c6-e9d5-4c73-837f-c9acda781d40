package io.sx.ai.abstraction;

import java.util.List;
import java.util.Map;

/**
 * Framework-agnostic vector store interface
 * Abstracts vector store operations across different AI frameworks
 */
public interface SxVectorStore {
    
    /**
     * Add a document to the vector store
     */
    void addDocument(SxDocument document);
    
    /**
     * Add multiple documents to the vector store
     */
    void addDocuments(List<SxDocument> documents);
    
    /**
     * Search for similar documents
     */
    List<SxDocument> similaritySearch(String query, int maxResults);
    
    /**
     * Search for similar documents with score threshold
     */
    List<SxDocument> similaritySearch(String query, int maxResults, double scoreThreshold);
    
    /**
     * Search for similar documents using embeddings
     */
    List<SxDocument> similaritySearch(List<Double> embeddings, int maxResults);
    
    /**
     * Delete a document by ID
     */
    boolean deleteDocument(String documentId);
    
    /**
     * Get a document by ID
     */
    SxDocument getDocument(String documentId);
    
    /**
     * Update a document
     */
    boolean updateDocument(SxDocument document);
    
    /**
     * Get the total number of documents
     */
    long getDocumentCount();
    
    /**
     * Clear all documents
     */
    void clear();
    
    /**
     * Get store metadata
     */
    Map<String, Object> getMetadata();
    
    /**
     * Check if the store is available
     */
    boolean isAvailable();
    
    /**
     * Get the embedding dimensions
     */
    int getEmbeddingDimensions();
} 