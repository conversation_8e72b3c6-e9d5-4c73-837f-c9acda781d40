package io.sx.ai.decision.strategies;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Factory for creating AI decision strategies
 */
@Component
public class AIDecisionStrategyFactory {
    
    private final Map<String, AIDecisionStrategy> strategies = new ConcurrentHashMap<>();
    
    public AIDecisionStrategyFactory(List<AIDecisionStrategy> strategyList) {
        for (AIDecisionStrategy strategy : strategyList) {
            strategies.put(strategy.getStrategyId(), strategy);
        }
    }
    
    /**
     * Get strategy by ID
     */
    public AIDecisionStrategy getStrategy(String strategyId) {
        return strategies.get(strategyId);
    }
    
    /**
     * Check if strategy exists
     */
    public boolean hasStrategy(String strategyId) {
        return strategies.containsKey(strategyId);
    }
    
    /**
     * Get all available strategy IDs
     */
    public List<String> getAvailableStrategies() {
        return List.copyOf(strategies.keySet());
    }
} 