package io.sx.ai.tools;

import java.util.Map;

/**
 * Enhanced AI Tool interface that supports categorization and autonomy modes
 * All AI tools (information gathering, business operations, etc.) implement this interface
 */
public interface AITool {
    
    /**
     * Get the unique identifier for this tool
     */
    String getToolId();
    
    /**
     * Get the human-readable name for this tool
     */
    String getToolName();
    
    /**
     * Get the description of what this tool does
     */
    String getToolDescription();
    
    /**
     * Get the category of this tool (determines approval requirements)
     */
    ToolCategory getToolCategory();
    
    /**
     * Get the MCP tool schema for this tool
     * This defines the parameters and return type for MCP integration
     */
    ToolSchema getToolSchema();
    
    /**
     * Get the tool version
     */
    default String getToolVersion() {
        return "1.0.0";
    }
    
    /**
     * Get the tool parameters schema
     */
    default Map<String, Object> getParameters() {
        return getToolSchema().getParameters().entrySet().stream()
            .collect(java.util.stream.Collectors.toMap(
                Map.Entry::getKey,
                entry -> Map.of(
                    "type", entry.getValue().getType(),
                    "description", entry.getValue().getDescription(),
                    "required", entry.getValue().isRequired(),
                    "defaultValue", entry.getValue().getDefaultValue()
                )
            ));
    }
    
    /**
     * Execute the tool with the given parameters
     * @param parameters Tool-specific parameters
     * @param context Execution context (organization, user, etc.)
     * @return Tool execution result
     */
    ToolResult execute(Map<String, Object> parameters, ToolContext context);
    
    /**
     * Check if this tool requires approval in the current autonomy mode
     * @param autonomyMode Current AI autonomy mode
     * @return true if approval is required
     */
    default boolean requiresApproval(ToolCategory.AIAutonomyMode autonomyMode) {
        return getToolCategory().requiresApprovalInMode(autonomyMode);
    }
    
    /**
     * Validate parameters before execution
     * @param parameters Tool parameters to validate
     * @return Validation result
     */
    default ValidationResult validateParameters(Map<String, Object> parameters) {
        return ValidationResult.success();
    }
    
    /**
     * Get estimated execution time for this tool
     * @return Estimated time in milliseconds
     */
    default long getEstimatedExecutionTime() {
        return 1000; // Default 1 second
    }
    
    /**
     * Check if this tool can be executed in the given context
     * @param context Execution context
     * @return true if tool can be executed
     */
    default boolean canExecute(ToolContext context) {
        return true; // Default implementation allows execution
    }
    
    /**
     * Tool execution context
     */
    class ToolContext {
        private final Long organizationId;
        private final Long onBehalfOfId;
        private final ToolCategory.AIAutonomyMode autonomyMode;
        private final String sessionId;
        private final Map<String, Object> additionalContext;
        
        public ToolContext(Long organizationId, Long onBehalfOfId, 
                          ToolCategory.AIAutonomyMode autonomyMode, 
                          String sessionId, Map<String, Object> additionalContext) {
            this.organizationId = organizationId;
            this.onBehalfOfId = onBehalfOfId;
            this.autonomyMode = autonomyMode;
            this.sessionId = sessionId;
            this.additionalContext = additionalContext;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        // Getters
        public Long getOrganizationId() { return organizationId; }
        public Long getOnBehalfOfId() { return onBehalfOfId; }
        public ToolCategory.AIAutonomyMode getAutonomyMode() { return autonomyMode; }
        public String getSessionId() { return sessionId; }
        public Map<String, Object> getAdditionalContext() { return additionalContext; }
        
        public static class Builder {
            private Long organizationId;
            private Long onBehalfOfId;
            private ToolCategory.AIAutonomyMode autonomyMode;
            private String sessionId;
            private Map<String, Object> additionalContext = Map.of();
            
            public Builder organizationId(Long organizationId) {
                this.organizationId = organizationId;
                return this;
            }
            
            public Builder onBehalfOfId(Long onBehalfOfId) {
                this.onBehalfOfId = onBehalfOfId;
                return this;
            }
            
            public Builder autonomyMode(ToolCategory.AIAutonomyMode autonomyMode) {
                this.autonomyMode = autonomyMode;
                return this;
            }
            
            public Builder sessionId(String sessionId) {
                this.sessionId = sessionId;
                return this;
            }
            
            public Builder additionalContext(Map<String, Object> additionalContext) {
                this.additionalContext = additionalContext;
                return this;
            }
            
            public ToolContext build() {
                return new ToolContext(organizationId, onBehalfOfId, autonomyMode, sessionId, additionalContext);
            }
        }
    }
    
    /**
     * Tool execution result
     */
    class ToolResult {
        private final boolean success;
        private final Object data;
        private final String errorMessage;
        private final long executionTimeMs;
        private final Map<String, Object> metadata;
        
        public ToolResult(boolean success, Object data, String errorMessage, 
                         long executionTimeMs, Map<String, Object> metadata) {
            this.success = success;
            this.data = data;
            this.errorMessage = errorMessage;
            this.executionTimeMs = executionTimeMs;
            this.metadata = metadata;
        }
        
        public static ToolResult success(Object data) {
            return new ToolResult(true, data, null, 0, Map.of());
        }
        
        public static ToolResult success(Object data, long executionTimeMs) {
            return new ToolResult(true, data, null, executionTimeMs, Map.of());
        }
        
        public static ToolResult error(String errorMessage) {
            return new ToolResult(false, null, errorMessage, 0, Map.of());
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public Object getData() { return data; }
        public String getErrorMessage() { return errorMessage; }
        public long getExecutionTimeMs() { return executionTimeMs; }
        public Map<String, Object> getMetadata() { return metadata; }
    }
    
    /**
     * Tool parameter validation result
     */
    class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final Map<String, String> fieldErrors;
        
        public ValidationResult(boolean valid, String errorMessage, Map<String, String> fieldErrors) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.fieldErrors = fieldErrors;
        }
        
        public static ValidationResult success() {
            return new ValidationResult(true, null, Map.of());
        }
        
        public static ValidationResult error(String errorMessage) {
            return new ValidationResult(false, errorMessage, Map.of());
        }
        
        public static ValidationResult fieldError(String field, String error) {
            return new ValidationResult(false, null, Map.of(field, error));
        }
        
        // Getters
        public boolean isValid() { return valid; }
        public String getErrorMessage() { return errorMessage; }
        public Map<String, String> getFieldErrors() { return fieldErrors; }
    }
    
    /**
     * MCP Tool Schema definition
     */
    class ToolSchema {
        private final String name;
        private final String description;
        private final Map<String, ParameterSchema> parameters;
        private final String returnType;
        
        public ToolSchema(String name, String description, 
                         Map<String, ParameterSchema> parameters, String returnType) {
            this.name = name;
            this.description = description;
            this.parameters = parameters;
            this.returnType = returnType;
        }
        
        // Getters
        public String getName() { return name; }
        public String getDescription() { return description; }
        public Map<String, ParameterSchema> getParameters() { return parameters; }
        public String getReturnType() { return returnType; }
    }
    
    /**
     * Parameter schema for MCP tools
     */
    class ParameterSchema {
        private final String type;
        private final String description;
        private final boolean required;
        private final Object defaultValue;
        
        public ParameterSchema(String type, String description, boolean required, Object defaultValue) {
            this.type = type;
            this.description = description;
            this.required = required;
            this.defaultValue = defaultValue;
        }
        
        // Getters
        public String getType() { return type; }
        public String getDescription() { return description; }
        public boolean isRequired() { return required; }
        public Object getDefaultValue() { return defaultValue; }
    }
} 