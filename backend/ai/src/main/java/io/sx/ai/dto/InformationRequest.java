package io.sx.ai.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * DTO for LLM information requests
 * Represents a request from the LLM for specific information to aid in decision making
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InformationRequest {
    
    /**
     * Unique identifier for this information request
     */
    private String requestId;
    
    /**
     * Type of information being requested
     * Examples: SIMILAR_TICKETS, CUSTOMER_CONTEXT, TEAM_AVAILABILITY, TICKET_HISTORY
     */
    private String informationType;
    
    /**
     * Request type (alias for informationType for compatibility)
     */
    private String requestType;
    
    /**
     * Priority level of this request (HIGH, MEDIUM, LOW)
     */
    private String priority;
    
    /**
     * Specific parameters for the information request
     * Example: {"ticketId": 123, "limit": 5} for similar tickets
     */
    private Map<String, Object> parameters;
    
    /**
     * Context about why this information is needed
     */
    private String context;
    
    /**
     * Whether this request can be executed in parallel with others
     */
    private boolean parallelizable;
    
    /**
     * Maximum time to wait for this information (in milliseconds)
     */
    private Long timeoutMs;
} 