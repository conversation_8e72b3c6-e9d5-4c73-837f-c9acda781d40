package io.sx.ai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Structured response model for ticket categorization
 * Used with LLM structured output for consistent categorization results
 * Focused on categorization only - other aspects like priority, assignment, etc. are handled separately
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketCategorization {
    
    /**
     * The category of ticket (Bug, Feature Request, Question, Incident, Task, Other)
     */
    @JsonProperty("category")
    private String category;
    
    /**
     * Overall confidence score (0.0 to 1.0)
     */
    @JsonProperty("confidence")
    private Double confidence;
    
    /**
     * Detailed reasoning for the categorization
     */
    @JsonProperty("reasoning")
    private String reasoning;
} 