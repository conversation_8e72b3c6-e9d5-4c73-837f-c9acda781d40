package io.sx.ai.springai.framework.xstatic.workflow;

import io.sx.ai.springai.framework.common.BaseStepResult;
import io.sx.ai.springai.framework.common.BaseWorkflowStep;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.ai.chat.messages.Message;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents a single step in the dynamic workflow execution, extends BaseStepResult<Double, Double>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
public class WorkflowStepResult extends BaseStepResult<Double, Double> {
    // LLM/dynamic-specific fields
    private String stepId;
    private int iteration;
    private LocalDateTime timestamp;
    private String llmPrompt;
    private List<Message> conversationContext;
    private String llmDecision;
    private String llmReasoning;
    private Double confidenceScore;
    private List<String> requestedTools;
    private Map<String, Object> toolResults;
    private Long toolExecutionTimeMs;
    private String nextAction;
    private boolean shouldContinue;
    private Map<String, Object> metadata;
    private Long processingTimeMs;

    public enum StepType {
        LLM_ANALYSIS,
        TOOL_EXECUTION,
        DECISION_POINT,
        TERMINATION
    }

    /**
     * Create a new workflow step
     */
    public static WorkflowStepResult create(String stepId, int stepNumber, StepType stepType) {
        WorkflowStepResult result = new WorkflowStepResult();
        result.setStepId(stepId);
        result.setStepNumber(stepNumber); // Set the stepNumber field from BaseStepResult
        result.setIteration(stepNumber); // Also set iteration for backward compatibility
        result.setTimestamp(LocalDateTime.now());
        result.setStepType(BaseWorkflowStep.StepType.LLM_ANALYSIS);
        result.setMetadata(new HashMap<>());
        return result;
    }

    /**
     * Convert local StepType to base class StepType
     */
    public static BaseWorkflowStep.StepType convertStepType(StepType stepType) {
        switch (stepType) {
            case LLM_ANALYSIS:
                return BaseWorkflowStep.StepType.LLM_ANALYSIS;
            case TOOL_EXECUTION:
                return BaseWorkflowStep.StepType.TOOL_EXECUTION;
            case DECISION_POINT:
                return BaseWorkflowStep.StepType.DECISION_POINT;
            case TERMINATION:
                return BaseWorkflowStep.StepType.TERMINATION;
            default:
                return BaseWorkflowStep.StepType.GENERAL;
        }
    }

    /**
     * Set LLM analysis results
     */
    public void setLLMAnalysis(String decision, String reasoning, Double confidence) {
        this.llmDecision = decision;
        this.llmReasoning = reasoning;
        this.confidenceScore = confidence;
    }

    /**
     * Set tool execution results
     */
    public void setToolResults(List<String> tools, Map<String, Object> results, Long executionTime) {
        this.requestedTools = tools;
        this.toolResults = results;
        this.toolExecutionTimeMs = executionTime;
    }

    /**
     * Set step outcome
     */
    public void setOutcome(double outputNumber, String nextAction, boolean shouldContinue) {
        this.setOutput(outputNumber);
        this.nextAction = nextAction;
        this.shouldContinue = shouldContinue;
    }

    /**
     * Add metadata to the step
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }

    /**
     * Convert to Map for storage in thinking_logs
     */
    public Map<String, Object> toMap() {
        Map<String, Object> stepMap = new HashMap<>();
        stepMap.put("step_id", stepId);
        stepMap.put("iteration", iteration);
        stepMap.put("timestamp", timestamp.toString());
        stepMap.put("step_type", getStepType().name());
        stepMap.put("input_number", getInput());
        stepMap.put("output_number", getOutput());
        stepMap.put("llm_decision", llmDecision);
        stepMap.put("llm_reasoning", llmReasoning);
        stepMap.put("confidence_score", confidenceScore);
        stepMap.put("requested_tools", requestedTools);
        stepMap.put("tool_results", toolResults);
        stepMap.put("tool_execution_time_ms", toolExecutionTimeMs);
        stepMap.put("next_action", nextAction);
        stepMap.put("should_continue", shouldContinue);
        stepMap.put("processing_time_ms", processingTimeMs);
        stepMap.put("metadata", metadata);
        
        return stepMap;
    }

    /**
     * Get a human-readable description of the step
     */
    public String getDescription() {
        StringBuilder description = new StringBuilder();
        description.append("Step ").append(iteration).append(" (").append(getStepType()).append("): ");
        
        switch (getStepType()) {
            case LLM_ANALYSIS:
                description.append("LLM analyzed ").append(getInput())
                          .append(" and decided: ").append(llmDecision);
                break;
            case TOOL_EXECUTION:
                description.append("Executed tools: ").append(requestedTools)
                          .append(" on ").append(getInput())
                          .append(" -> ").append(getOutput());
                break;
            case DECISION_POINT:
                description.append("Decision point: ").append(llmDecision)
                          .append(" (confidence: ").append(confidenceScore).append(")");
                break;
            case TERMINATION:
                description.append("Workflow terminated: ").append(llmDecision);
                break;
        }
        
        return description.toString();
    }

    // Convenience methods for backward compatibility
    public double getInputNumber() {
        return getInput() != null ? getInput() : 0.0;
    }

    public double getOutputNumber() {
        return getOutput() != null ? getOutput() : 0.0;
    }

    public void setInputNumber(double inputNumber) {
        setInput(inputNumber);
    }

    public void setOutputNumber(double outputNumber) {
        setOutput(outputNumber);
    }

    public void setLlmPrompt(String llmPrompt) {
        this.llmPrompt = llmPrompt;
    }

    public String getLlmPrompt() {
        return llmPrompt;
    }

    public void setStepType(BaseWorkflowStep.StepType stepType) {
        // This method is inherited from BaseStepResult
    }

    public BaseWorkflowStep.StepType getStepType() {
        // This method is inherited from BaseStepResult
        return super.getStepType();
    }
} 