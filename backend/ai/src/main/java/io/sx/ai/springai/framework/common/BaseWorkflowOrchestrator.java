package io.sx.ai.springai.framework.common;

import java.util.List;
import java.util.Map;

/**
 * Base interface for workflow orchestrators with type safety
 * Provides common contract for workflow execution and management
 */
public interface BaseWorkflowOrchestrator<I, O> {
    
    /**
     * Execute a workflow with the given typed input
     * @param input The typed input value for the workflow
     * @param sessionId Unique session identifier
     * @param organizationId Organization ID (optional)
     * @param onBehalfOfId User ID on whose behalf this is processed (optional)
     * @return Workflow execution result
     */
    BaseWorkflowResult<I, O> executeWorkflow(WorkflowInput<I> input, String sessionId, Long organizationId, Long onBehalfOfId);
    
    /**
     * Execute a workflow from event data
     * @param eventData Event payload data
     * @param sessionId Unique session identifier
     * @param organizationId Organization ID (optional)
     * @param onBehalfOfId User ID on whose behalf this is processed (optional)
     * @return Workflow execution result
     */
    BaseWorkflowResult<I, O> executeWorkflowFromEvent(Map<String, Object> eventData, String sessionId, Long organizationId, Long onBehalfOfId);
    
    /**
     * Execute a specific workflow step
     * @param sessionId Current workflow session ID
     * @param stepId Step to execute
     * @param context Current workflow context
     * @param organizationId Organization ID (optional)
     * @param onBehalfOfId User ID on whose behalf this is processed (optional)
     * @return Updated workflow state with next steps
     */
    BaseStepResult<I, O> executeWorkflowStep(String sessionId, String stepId, 
                                         Map<String, Object> context,
                                         Long organizationId, Long onBehalfOfId);
    
    /**
     * Get workflow session status and current state
     * @param sessionId Workflow session ID
     * @return Current workflow state
     */
    BaseWorkflowContext<I, O> getWorkflowStatus(String sessionId);
    
    /**
     * Get available workflow types
     * @return Map of workflow type names to descriptions
     */
    Map<String, String> getAvailableWorkflowTypes();
    
    /**
     * Validate workflow parameters
     * @param workflowType Type of workflow
     * @param parameters Parameters to validate
     * @return true if parameters are valid
     */
    boolean validateWorkflowParameters(String workflowType, Map<String, Object> parameters);
    
    /**
     * Get workflow statistics
     * @param organizationId Organization ID (optional)
     * @return Workflow execution statistics
     */
    Map<String, Object> getWorkflowStats(Long organizationId);
    
    /**
     * Cancel a running workflow
     * @param sessionId Workflow session ID
     * @param reason Reason for cancellation
     * @return Cancellation result
     */
    boolean cancelWorkflow(String sessionId, String reason);
    
    /**
     * Get workflow execution history
     * @param organizationId Organization ID (optional)
     * @param limit Maximum number of results
     * @return List of workflow execution results
     */
    List<BaseWorkflowResult<I, O>> getWorkflowHistory(Long organizationId, int limit);
} 