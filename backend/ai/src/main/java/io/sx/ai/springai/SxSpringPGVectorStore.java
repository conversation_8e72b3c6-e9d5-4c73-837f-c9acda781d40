package io.sx.ai.springai;

import io.sx.ai.abstraction.SxDocument;
import io.sx.ai.abstraction.SxVectorStore;
import io.sx.ai.springai.spring.SxSpringDocument;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@ConditionalOnProperty(name = "spring.ai.custom.vector.store", havingValue = "pgvector")
public class SxSpringPGVectorStore implements SxVectorStore {

    private final PgVectorStore vectorStore;
    private final EmbeddingModel embeddingModel;

    public SxSpringPGVectorStore(EmbeddingModel embeddingModel, PgVectorStore vectorStore) {
        this.embeddingModel = embeddingModel;
        this.vectorStore = vectorStore;
    }

    @Override
    public void addDocument(SxDocument document) {
        Document springDocument = new Document(document.getContent(), document.getMetadata());
        vectorStore.add(List.of(springDocument));
    }

    @Override
    public void addDocuments(List<SxDocument> documents) {
        List<Document> springDocuments = documents.stream()
            .map(doc -> new Document(doc.getContent(), doc.getMetadata()))
            .collect(Collectors.toList());
        vectorStore.add(springDocuments);
    }

    @Override
    public List<SxDocument> similaritySearch(String query, int maxResults) {
        List<Document> results = vectorStore.similaritySearch(
            SearchRequest.builder().query(query).topK(maxResults).build()
        );
        return results.stream()
            .map(doc -> new SxSpringDocument(doc))
            .map(d -> (SxDocument) d)
            .collect(Collectors.toList());
    }

    @Override
    public List<SxDocument> similaritySearch(String query, int maxResults, double scoreThreshold) {
        List<Document> results = vectorStore.similaritySearch(
            SearchRequest.builder().query(query)
                .topK(maxResults)
                .similarityThreshold(scoreThreshold)
                .build()
        );
        return results.stream()
            .map(SxSpringDocument::new)
            .map(d -> (SxDocument) d)
            .collect(Collectors.toList());
    }

    @Override
    public List<SxDocument> similaritySearch(List<Double> embeddings, int maxResults) {
        // TODO: Implement embedding-based search if supported by PGVectorStore
        return List.of();
    }

    @Override
    public boolean deleteDocument(String documentId) {
        // TODO: Implement if supported by PGVectorStore
        return false;
    }

    @Override
    public SxDocument getDocument(String documentId) {
        // TODO: Implement if supported by PGVectorStore
        return null;
    }

    @Override
    public boolean updateDocument(SxDocument document) {
        // TODO: Implement if supported by PGVectorStore
        return false;
    }

    @Override
    public long getDocumentCount() {
        // TODO: Implement if supported by PGVectorStore
        return 0;
    }

    @Override
    public void clear() {
        // TODO: Implement if supported by PGVectorStore
    }

    @Override
    public Map<String, Object> getMetadata() {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("type", "PGVectorStore");
        metadata.put("implementation", "Spring AI PGVectorStore");
        metadata.put("embeddingModel", embeddingModel.getClass().getSimpleName());
        return metadata;
    }

    @Override
    public boolean isAvailable() {
        try {
            embeddingModel.embed("test");
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public int getEmbeddingDimensions() {
        try {
            var response = embeddingModel.embedForResponse(List.of("test"));
            if (!response.getResults().isEmpty()) {
                return response.getResults().get(0).getOutput().length;
            }
            return 0;
        } catch (Exception e) {
            return 0;
        }
    }
} 