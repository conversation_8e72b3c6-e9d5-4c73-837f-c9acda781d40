package io.sx.ai.abstraction;

import java.util.Map;

/**
 * Framework-agnostic message interface
 * Abstracts message types across different AI frameworks
 */
public interface SxMessage {
    
    /**
     * Get the message content
     */
    String getContent();
    
    /**
     * Get the message type
     */
    PromptRole getType();
    
    /**
     * Get metadata associated with this message
     */
    Map<String, Object> getMetadata();
    
    /**
     * Get the role of this message
     */
    String getRole();
    
    /**
     * Check if this message has content
     */
    boolean hasContent();
    
    /**
     * Get the message ID if available
     */
    String getId();
    
    /**
     * Message types for categorization
     */
    enum PromptRole {
        SYSTEM,
        USER,
        ASSISTANT,
        FUNCTION,//?
        TOOL
    }
} 