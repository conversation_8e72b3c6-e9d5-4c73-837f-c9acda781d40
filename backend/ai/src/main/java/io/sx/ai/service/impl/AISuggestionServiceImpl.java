package io.sx.ai.service.impl;

import io.sx.ai.mapper.AISuggestionMapper;
import io.sx.ai.model.AISuggestion;
import io.sx.ai.repository.AISuggestionRepository;
import io.sx.ai.service.AISuggestionService;
import io.sx.dto.AISuggestionDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Collections;

/**
 * Service implementation for AI suggestions
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class AISuggestionServiceImpl implements AISuggestionService {
    
    private final AISuggestionRepository aiSuggestionRepository;
    private final AISuggestionMapper aiSuggestionMapper;
    
    @Override
    public List<AISuggestionDTO> getSuggestionsForTicket(Long ticketId) {
        log.info("Getting AI suggestions for ticket: {}", ticketId);
        List<AISuggestion> suggestions = aiSuggestionRepository.findByTicketId(ticketId);
        return aiSuggestionMapper.toDTOList(suggestions);
    }
    
    @Override
    public List<AISuggestionDTO> getSuggestionsByStatus(String status) {
        log.info("Getting AI suggestions by status: {}", status);
        try {
            AISuggestion.SuggestionStatus suggestionStatus = AISuggestion.SuggestionStatus.valueOf(status.toUpperCase());
            List<AISuggestion> suggestions = aiSuggestionRepository.findByStatus(suggestionStatus);
            return aiSuggestionMapper.toDTOList(suggestions);
        } catch (IllegalArgumentException e) {
            log.error("Invalid status: {}", status);
            throw new IllegalArgumentException("Invalid status: " + status);
        }
    }
    
    @Override
    public List<AISuggestionDTO> getSuggestionsByType(String type) {
        log.info("Getting AI suggestions by type: {}", type);
        try {
            AISuggestion.SuggestionType suggestionType = AISuggestion.SuggestionType.valueOf(type.toUpperCase());
            // Note: Repository doesn't have findByType method yet, so we'll filter in service
            // Note: Repository doesn't have findByType method yet, so we'll filter in service
            // For now, return empty list since we need organizationId for findAll
            List<AISuggestion> filteredSuggestions = Collections.emptyList();
            return aiSuggestionMapper.toDTOList(filteredSuggestions);
        } catch (IllegalArgumentException e) {
            log.error("Invalid type: {}", type);
            throw new IllegalArgumentException("Invalid type: " + type);
        }
    }
    
    @Override
    public AISuggestionDTO acceptSuggestion(String suggestionId, Long userId) {
        log.info("Accepting AI suggestion: {} by user: {}", suggestionId, userId);
        
        Optional<AISuggestion> optionalSuggestion = aiSuggestionRepository.findBySuggestionId(suggestionId);
        if (optionalSuggestion.isEmpty()) {
            throw new IllegalArgumentException("Suggestion not found: " + suggestionId);
        }
        
        AISuggestion suggestion = optionalSuggestion.get();
        suggestion.setStatus(AISuggestion.SuggestionStatus.APPROVED);
        suggestion.setApprovedBy(userId);
        suggestion.setApprovedAt(LocalDateTime.now().toString());
        suggestion.setUpdatedAt(LocalDateTime.now());
        suggestion.setUpdatedById(userId);
        
        AISuggestion savedSuggestion = aiSuggestionRepository.save(suggestion);
        return aiSuggestionMapper.toDTO(savedSuggestion);
    }
    
    @Override
    public AISuggestionDTO rejectSuggestion(String suggestionId, Long userId, String reason) {
        log.info("Rejecting AI suggestion: {} by user: {}", suggestionId, userId);
        
        Optional<AISuggestion> optionalSuggestion = aiSuggestionRepository.findBySuggestionId(suggestionId);
        if (optionalSuggestion.isEmpty()) {
            throw new IllegalArgumentException("Suggestion not found: " + suggestionId);
        }
        
        AISuggestion suggestion = optionalSuggestion.get();
        suggestion.setStatus(AISuggestion.SuggestionStatus.REJECTED);
        suggestion.setApprovedBy(userId);
        suggestion.setApprovedAt(LocalDateTime.now().toString());
        suggestion.setUpdatedAt(LocalDateTime.now());
        suggestion.setUpdatedById(userId);
        
        // Store rejection reason in metadata
        if (reason != null && !reason.trim().isEmpty()) {
            if (suggestion.getMetadata() == null) {
                suggestion.setMetadata(new java.util.HashMap<>());
            }
            suggestion.getMetadata().put("rejection_reason", reason);
        }
        
        AISuggestion savedSuggestion = aiSuggestionRepository.save(suggestion);
        return aiSuggestionMapper.toDTO(savedSuggestion);
    }
    
    @Override
    public AISuggestionDTO executeSuggestion(String suggestionId) {
        log.info("Executing AI suggestion: {}", suggestionId);
        
        Optional<AISuggestion> optionalSuggestion = aiSuggestionRepository.findBySuggestionId(suggestionId);
        if (optionalSuggestion.isEmpty()) {
            throw new IllegalArgumentException("Suggestion not found: " + suggestionId);
        }
        
        AISuggestion suggestion = optionalSuggestion.get();
        
        // Check if suggestion is approved
        if (suggestion.getStatus() != AISuggestion.SuggestionStatus.APPROVED) {
            throw new IllegalStateException("Suggestion must be approved before execution");
        }
        
        suggestion.setStatus(AISuggestion.SuggestionStatus.EXECUTING);
        suggestion.setUpdatedAt(LocalDateTime.now());
        
        try {
            // TODO: Implement actual execution logic based on toolName and toolParameters
            // For now, just mark as executed
            suggestion.setStatus(AISuggestion.SuggestionStatus.EXECUTED);
            suggestion.setExecutedAt(LocalDateTime.now().toString());
            
            AISuggestion savedSuggestion = aiSuggestionRepository.save(suggestion);
            return aiSuggestionMapper.toDTO(savedSuggestion);
        } catch (Exception e) {
            log.error("Error executing suggestion: {}", suggestionId, e);
            suggestion.setStatus(AISuggestion.SuggestionStatus.FAILED);
            suggestion.setErrorMessage(e.getMessage());
            AISuggestion savedSuggestion = aiSuggestionRepository.save(suggestion);
            return aiSuggestionMapper.toDTO(savedSuggestion);
        }
    }
    
    @Override
    public AISuggestionDTO getBySuggestionId(String suggestionId) {
        log.info("Getting AI suggestion by ID: {}", suggestionId);
        
        Optional<AISuggestion> optionalSuggestion = aiSuggestionRepository.findBySuggestionId(suggestionId);
        if (optionalSuggestion.isEmpty()) {
            throw new IllegalArgumentException("Suggestion not found: " + suggestionId);
        }
        
        return aiSuggestionMapper.toDTO(optionalSuggestion.get());
    }
    
    @Override
    public AISuggestionDTO save(AISuggestionDTO dto) {
        log.info("Saving AI suggestion: {}", dto.getSuggestionId());
        AISuggestion entity = aiSuggestionMapper.toEntity(dto);
        AISuggestion savedEntity = aiSuggestionRepository.save(entity);
        return aiSuggestionMapper.toDTO(savedEntity);
    }
    
    @Override
    public AISuggestionDTO update(AISuggestionDTO dto) {
        log.info("Updating AI suggestion: {}", dto.getSuggestionId());
        AISuggestion entity = aiSuggestionMapper.toEntity(dto);
        aiSuggestionRepository.update(entity);
        return aiSuggestionMapper.toDTO(entity);
    }
    
    @Override
    public void delete(Long id) {
        log.info("Deleting AI suggestion: {}", id);
        aiSuggestionRepository.deleteById(id);
    }
    
    @Override
    public AISuggestionDTO findById(Long id) {
        log.info("Finding AI suggestion by ID: {}", id);
        Optional<AISuggestion> entity = aiSuggestionRepository.findById(id);
        return entity.map(aiSuggestionMapper::toDTO).orElse(null);
    }
    
    @Override
    public List<AISuggestionDTO> findAll() {
        log.info("Finding all AI suggestions");
        // For now, return empty list since we need organizationId for findAll
        // In a real implementation, this would need to be filtered by organization
        return Collections.emptyList();
    }
} 