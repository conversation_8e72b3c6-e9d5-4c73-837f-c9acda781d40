package io.sx.ai.config;

import io.sx.ai.tools.AIToolRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * Configuration for AI Tool Registry initialization
 * Ensures all AI tools are properly registered and available
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class AIToolRegistryConfiguration {
    
    private final AIToolRegistry aiToolRegistry;
    
    /**
     * Initialize the AI Tool Registry after Spring context is ready
     */
    @PostConstruct
    public void initializeAIToolRegistry() {
        log.info("Initializing AI Tool Registry...");
        
        try {
            aiToolRegistry.initialize();
            
            // Log registry statistics
            var stats = aiToolRegistry.getStats();
            log.info("AI Tool Registry initialized successfully:");
            log.info("  Total tools: {}", stats.getTotalTools());
            log.info("  Total categories: {}", stats.getTotalCategories());
            
            for (var entry : stats.getToolsPerCategory().entrySet()) {
                log.info("  Category {}: {} tools", entry.getKey().getCategoryId(), entry.getValue());
            }
            
        } catch (Exception e) {
            log.error("Failed to initialize AI Tool Registry: {}", e.getMessage(), e);
            throw new RuntimeException("AI Tool Registry initialization failed", e);
        }
    }
} 