package io.sx.ai.springai.sample.dynamic.service;

import io.sx.ai.springai.framework.common.BaseWorkflowResult;
import io.sx.ai.springai.framework.common.BaseStepResult;
import io.sx.ai.springai.sample.dynamic.workflow.LLMAppHybridWorkflow;
import io.sx.ai.springai.framework.xstatic.workflow.WorkflowResult;
import io.sx.ai.springai.framework.xstatic.workflow.WorkflowStepResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Service for executing dynamic workflows.
 * Coordinates the execution of LLM-controlled workflows.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DynamicWorkflowService {

    private final LLMAppHybridWorkflow llmControlledWorkflow;

    /**
     * Execute a dynamic workflow with the given parameters.
     */
    public BaseWorkflowResult<Double, Double> executeWorkflow(double inputNumber, int maxIterations, Long organizationId, Long onBehalfOfId) {
        log.info("Executing dynamic workflow: input={}, maxIterations={}, orgId={}", 
                inputNumber, maxIterations, organizationId);
        try {
            // Validate parameters first
            validateParameters(inputNumber, maxIterations);
            
            WorkflowResult result = llmControlledWorkflow.execute(inputNumber, maxIterations, organizationId, onBehalfOfId);
            return convertToBaseWorkflowResult(result);
        } catch (Exception e) {
            log.error("Error executing dynamic workflow: input={}", inputNumber, e);
            return BaseWorkflowResult.error("Dynamic workflow execution failed: " + e.getMessage());
        }
    }

    /**
     * Execute a dynamic workflow with default parameters.
     */
    public BaseWorkflowResult<Double, Double> executeWorkflow(double inputNumber, Long organizationId, Long onBehalfOfId) {
        return executeWorkflow(inputNumber, 4, organizationId, onBehalfOfId);
    }

    /**
     * Validate workflow parameters.
     */
    public void validateParameters(double inputNumber, int maxIterations) {
        if (Double.isNaN(inputNumber) || Double.isInfinite(inputNumber)) {
            throw new IllegalArgumentException("Input number must be a finite number");
        }
        if (maxIterations <= 0 || maxIterations > 10) {
            throw new IllegalArgumentException("Max iterations must be between 1 and 10");
        }
        
        log.debug("Workflow parameters validated: input={}, maxIterations={}", inputNumber, maxIterations);
    }

    /**
     * Convert WorkflowResult to BaseWorkflowResult
     */
    private BaseWorkflowResult<Double, Double> convertToBaseWorkflowResult(WorkflowResult result) {
        try {
            List<BaseStepResult<Double, Double>> baseSteps = new ArrayList<>();
            for (BaseStepResult<Double, Double> step : result.getSteps()) {
                // Convert WorkflowStepResult to BaseStepResult
                WorkflowStepResult workflowStep = (WorkflowStepResult) step;
                BaseStepResult<Double, Double> baseStep = BaseStepResult.success(
                    workflowStep.getIteration(),
                    workflowStep.getStepId(),
                    workflowStep.getInputNumber(),
                    workflowStep.getOutputNumber(),
                    workflowStep.getLlmDecision()
                );
                baseSteps.add(baseStep);
            }
            
            return BaseWorkflowResult.success(
                result.getOriginalInput(),
                result.getFinalOutput(),
                baseSteps,
                result.getSessionId(),
                "LLM_CONTROLLED_DYNAMIC_WORKFLOW"
            );
        } catch (Exception e) {
            log.error("Error converting WorkflowResult to BaseWorkflowResult", e);
            return BaseWorkflowResult.error("Conversion failed: " + e.getMessage());
        }
    }
} 