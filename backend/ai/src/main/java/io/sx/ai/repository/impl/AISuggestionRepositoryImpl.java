package io.sx.ai.repository.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sx.ai.model.AISuggestion;
import io.sx.ai.repository.AISuggestionRepository;
import io.sx.repository.impl.BaseRepositoryImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.ArrayList;

/**
 * JDBC implementation of AI suggestion repository
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AISuggestionRepositoryImpl extends BaseRepositoryImpl<AISuggestion> 
    implements AISuggestionRepository {

    private final JdbcTemplate jdbcTemplate;
    private final ObjectMapper objectMapper;

    private final RowMapper<AISuggestion> rowMapper = new RowMapper<AISuggestion>() {
        @Override
        public AISuggestion mapRow(ResultSet rs, int rowNum) throws SQLException {
            AISuggestion suggestion = AISuggestion.builder()
                .id(rs.getLong("id"))
                .suggestionId(rs.getString("suggestion_id"))
                .sessionId(rs.getString("session_id"))
                .ticketId(rs.getObject("ticket_id", Long.class))
                .autonomyLevel(AISuggestion.AutonomyLevel.valueOf(rs.getString("autonomy_level")))
                .suggestionType(AISuggestion.SuggestionType.valueOf(rs.getString("suggestion_type")))
                .title(rs.getString("title"))
                .description(rs.getString("description"))
                .reasoning(rs.getString("reasoning"))
                .confidenceScore(rs.getBigDecimal("confidence_score"))
                .priority(rs.getInt("priority"))
                .toolName(rs.getString("tool_name"))
                .status(AISuggestion.SuggestionStatus.valueOf(rs.getString("status")))
                .approvalRequired(rs.getBoolean("approval_required"))
                .approvedBy(rs.getLong("approved_by"))
                .approvedAt(rs.getString("approved_at"))
                .executedAt(rs.getString("executed_at"))
                .errorMessage(rs.getString("error_message"))
                .organizationId(rs.getLong("organization_id"))
                .supportOrganizationId(rs.getLong("support_organization_id"))
                .createdAt(rs.getTimestamp("created_at").toLocalDateTime())
                .updatedAt(rs.getTimestamp("updated_at").toLocalDateTime())
                .createdById(rs.getLong("created_by_id"))
                .updatedById(rs.getLong("updated_by_id"))
                .createdBy(rs.getString("created_by"))
                .updatedBy(rs.getString("updated_by"))
                .onBehalfOfId(rs.getLong("on_behalf_of_id"))
                .build();

            // Parse JSON fields
            try {
                String toolParametersJson = rs.getString("tool_parameters");
                if (toolParametersJson != null) {
                    Map<String, Object> toolParameters = objectMapper.readValue(toolParametersJson, new TypeReference<Map<String, Object>>() {});
                    suggestion.setToolParameters(toolParameters);
                }
                

                
                String metadataJson = rs.getString("metadata");
                if (metadataJson != null) {
                    Map<String, Object> metadata = objectMapper.readValue(metadataJson, new TypeReference<Map<String, Object>>() {});
                    suggestion.setMetadata(metadata);
                }
            } catch (Exception e) {
                log.warn("Error parsing JSON fields for suggestion {}: {}", rs.getLong("id"), e.getMessage());
            }
            
            return suggestion;
        }
    };

    @Override
    public List<AISuggestion> findBySessionId(String sessionId) {
        log.debug("Finding suggestions by session ID: {}", sessionId);
        
        String sql = """
            SELECT * FROM ai.suggestions 
            WHERE session_id = ? 
            ORDER BY created_at DESC
            """;
        
        try {
            return jdbcTemplate.query(sql, rowMapper, sessionId);
        } catch (Exception e) {
            log.error("Error finding suggestions by session ID: {}", sessionId, e);
            return List.of();
        }
    }

    @Override
    public List<AISuggestion> findByTicketId(Long ticketId) {
        log.debug("Finding suggestions by ticket ID: {}", ticketId);
        
        String sql = """
            SELECT * FROM ai.suggestions 
            WHERE ticket_id = ? 
            ORDER BY created_at DESC
            """;
        
        try {
            return jdbcTemplate.query(sql, rowMapper, ticketId);
        } catch (Exception e) {
            log.error("Error finding suggestions by ticket ID: {}", ticketId, e);
            return List.of();
        }
    }

    @Override
    public List<AISuggestion> findByStatus(AISuggestion.SuggestionStatus status) {
        log.debug("Finding suggestions by status: {}", status);
        
        String sql = """
            SELECT * FROM ai.suggestions 
            WHERE status = ? 
            ORDER BY created_at DESC
            """;
        
        try {
            return jdbcTemplate.query(sql, rowMapper, status.name());
        } catch (Exception e) {
            log.error("Error finding suggestions by status: {}", status, e);
            return List.of();
        }
    }

    @Override
    public List<AISuggestion> findByOrganization(Long organizationId, Long supportOrgId) {
        log.debug("Finding suggestions by organization: {}, support org: {}", organizationId, supportOrgId);
        
        String sql = """
            SELECT * FROM ai.suggestions 
            WHERE organization_id = ? AND support_organization_id = ? 
            ORDER BY created_at DESC
            """;
        
        try {
            return jdbcTemplate.query(sql, rowMapper, organizationId, supportOrgId);
        } catch (Exception e) {
            log.error("Error finding suggestions by organization: {}, support org: {}", organizationId, supportOrgId, e);
            return List.of();
        }
    }

    @Override
    public Optional<AISuggestion> findBySuggestionId(String suggestionId) {
        log.debug("Finding suggestion by suggestion ID: {}", suggestionId);
        
        String sql = "SELECT * FROM ai.suggestions WHERE suggestion_id = ?";
        
        try {
            List<AISuggestion> results = jdbcTemplate.query(sql, rowMapper, suggestionId);
            return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
        } catch (Exception e) {
            log.error("Error finding suggestion by suggestion ID: {}", suggestionId, e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<AISuggestion> findById(Long id) {
        log.debug("Finding suggestion by ID: {}", id);
        
        String sql = "SELECT * FROM ai.suggestions WHERE id = ?";
        
        try {
            List<AISuggestion> results = jdbcTemplate.query(sql, rowMapper, id);
            return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
        } catch (Exception e) {
            log.error("Error finding suggestion by ID: {}", id, e);
            return Optional.empty();
        }
    }

    @Override
    public void deleteById(Long id) {
        log.debug("Deleting suggestion by ID: {}", id);
        
        String sql = "DELETE FROM ai.suggestions WHERE id = ?";
        
        try {
            int rowsAffected = jdbcTemplate.update(sql, id);
            if (rowsAffected == 0) {
                log.warn("No suggestion found with ID: {}", id);
            } else {
                log.debug("Successfully deleted suggestion with ID: {}", id);
            }
        } catch (Exception e) {
            log.error("Error deleting suggestion by ID: {}", id, e);
            throw new RuntimeException("Failed to delete AI suggestion", e);
        }
    }

    @Override
    public AISuggestion save(AISuggestion suggestion) {
        log.debug("Saving AI suggestion: {}", suggestion.getSuggestionId());
        
        if (suggestion.getId() == null) {
            return insert(suggestion);
        } else {
            update(suggestion);
            return suggestion;
        }
    }

    private AISuggestion insert(AISuggestion suggestion) {
        String sql = """
            INSERT INTO ai.suggestions (
                suggestion_id, session_id, ticket_id, autonomy_level, suggestion_type, title, description, 
                reasoning, confidence_score, priority, tool_name, tool_parameters, status, 
                execution_result, approval_required, approved_by, approved_at, executed_at, 
                error_message, metadata, organization_id, support_organization_id,
                created_by_id, updated_by_id, created_by, updated_by, on_behalf_of_id
            ) VALUES (
             ?, ?, ?, ?, ?, ?, ?,
             ?, ?, ?, ?, ?::jsonb,?,
             ?, ?, ?, ?, ?,
             ?,?::jsonb, ?, ?,
             ?, ?, ?, ?, ? )
            RETURNING *
            """;
        
        try {
            return jdbcTemplate.queryForObject(sql, rowMapper,
               // suggestion_id, session_id, ticket_id, autonomy_level, suggestion_type, title, description,

                suggestion.getSuggestionId(),
                suggestion.getSessionId(),
                suggestion.getTicketId(),
                suggestion.getAutonomyLevel().name(),
                suggestion.getSuggestionType().name(),
                suggestion.getTitle(),
                suggestion.getDescription(),

              //  reasoning, confidence_score, priority, tool_name, tool_parameters, status,

                suggestion.getReasoning(),
                suggestion.getConfidenceScore(),
                suggestion.getPriority(),
                suggestion.getToolName(),
                suggestion.getToolParameters() != null ? objectMapper.writeValueAsString(suggestion.getToolParameters()) : null,
                suggestion.getStatus().name(),

                // execution_result, approval_required, approved_by, approved_at, executed_at,

                null,
                suggestion.getApprovalRequired(),
                suggestion.getApprovedBy(),
                suggestion.getApprovedAt(),
                suggestion.getExecutedAt(),

                // error_message, metadata, organization_id, support_organization_id,


                suggestion.getErrorMessage(),
                suggestion.getMetadata() != null ? objectMapper.writeValueAsString(suggestion.getMetadata()) : null,
                suggestion.getOrganizationId(),
                suggestion.getSupportOrganizationId(),
                // created_by_id, updated_by_id, created_by, updated_by, on_behalf_of_id

                suggestion.getCreatedById(),
                suggestion.getUpdatedById(),
                suggestion.getCreatedBy(),
                suggestion.getUpdatedBy(),
                suggestion.getOnBehalfOfId()
            );
        } catch (Exception e) {
            log.error("Error inserting AI suggestion: {}", suggestion.getSuggestionId(), e);
            throw new RuntimeException("Failed to insert AI suggestion", e);
        }
    }

    @Override
    public Long update(AISuggestion suggestion) {
        String sql = """
            UPDATE ai.suggestions SET 
                title = ?, description = ?, reasoning = ?, confidence_score = ?, 
                priority = ?, tool_name = ?, tool_parameters = ?::jsonb, status = ?, 
                execution_result = ?::jsonb, approval_required = ?, approved_by = ?, 
                approved_at = ?, executed_at = ?, error_message = ?, metadata = ?::jsonb,
                ticket_id = ?, updated_by_id = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """;
        
        try {
            int rowsAffected = jdbcTemplate.update(sql,
                suggestion.getTitle(),
                suggestion.getDescription(),
                suggestion.getReasoning(),
                suggestion.getConfidenceScore(),
                suggestion.getPriority(),
                suggestion.getToolName(),
                suggestion.getToolParameters() != null ? objectMapper.writeValueAsString(suggestion.getToolParameters()) : null,
                suggestion.getStatus().name(),
                null,
                suggestion.getApprovalRequired(),
                suggestion.getApprovedBy(),
                suggestion.getApprovedAt(),
                suggestion.getExecutedAt(),
                suggestion.getErrorMessage(),
                suggestion.getMetadata() != null ? objectMapper.writeValueAsString(suggestion.getMetadata()) : null,
                suggestion.getTicketId(),
                suggestion.getUpdatedById(),
                suggestion.getUpdatedBy(),
                suggestion.getId()
            );
            
            return rowsAffected > 0 ? suggestion.getId() : null;
        } catch (Exception e) {
            log.error("Error updating AI suggestion {}: {}", suggestion.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to update AI suggestion", e);
        }
    }

    @Override
    public List<AISuggestion> saveAll(List<AISuggestion> suggestions) {
        log.debug("Saving {} AI suggestions", suggestions.size());
        
        List<AISuggestion> savedSuggestions = new ArrayList<>();
        for (AISuggestion suggestion : suggestions) {
            try {
                AISuggestion saved = save(suggestion);
                savedSuggestions.add(saved);
            } catch (Exception e) {
                log.error("Error saving suggestion: {}", suggestion.getSuggestionId(), e);
                // Continue with other suggestions
            }
        }
        
        log.info("Successfully saved {}/{} AI suggestions", savedSuggestions.size(), suggestions.size());
        return savedSuggestions;
    }

    @Override
    public List<AISuggestion> findAll(Long organizationId) {
        log.debug("Finding all AI suggestions for organization: {}", organizationId);
        
        String sql = """
            SELECT * FROM ai.suggestions 
            WHERE organization_id = ? 
            ORDER BY created_at DESC
            """;
        
        try {
            return jdbcTemplate.query(sql, rowMapper, organizationId);
        } catch (Exception e) {
            log.error("Error finding AI suggestions by organization: {}", organizationId, e);
            return List.of();
        }
    }
} 