package io.sx.ai.service;

import java.util.Map;

/**
 * Service for classifying tickets and generating assignment suggestions
 */
public interface TicketCategorizationService {
    
    /**
     * Classify a ticket based on its content and context
     * 
     * @param ticketId The ticket ID to classify
     * @param organizationId The organization ID
     * @param supportOrgId The support organization ID
     * @return Map containing classification results
     */
    Map<String, Object> categorize(Long ticketId, Long organizationId, Long supportOrgId);
    
    /**
     * Get detailed classification analysis
     * 
     * @param ticketId The ticket ID to analyze
     * @param organizationId The organization ID
     * @param supportOrgId The support organization ID
     * @return Map containing detailed classification analysis
     */
    Map<String, Object> getClassificationAnalysis(Long ticketId, Long organizationId, Long supportOrgId);
} 