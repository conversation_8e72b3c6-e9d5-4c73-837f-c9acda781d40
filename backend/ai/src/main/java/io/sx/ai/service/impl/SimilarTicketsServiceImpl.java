package io.sx.ai.service.impl;

import io.sx.ai.service.SimilarTicketsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Implementation of SimilarTicketsService
 * Finds and analyzes similar tickets based on content and context
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SimilarTicketsServiceImpl implements SimilarTicketsService {

    @Override
    public List<Map<String, Object>> findSimilarTickets(Long ticketId, Long organizationId, Long supportOrgId, int maxResults) {
        log.debug("Finding similar tickets for ticket: {}, org: {}, max results: {}", ticketId, organizationId, maxResults);
        
        try {
            // For now, return mock similar tickets
            // In production, this would:
            // 1. Get current ticket content
            // 2. Perform vector similarity search
            // 3. Apply business rules filtering
            // 4. Return ranked results
            
            List<Map<String, Object>> similarTickets = new ArrayList<>();
            
            // Generate mock similar tickets
            for (int i = 1; i <= Math.min(maxResults, 5); i++) {
                Map<String, Object> similarTicket = new HashMap<>();
                similarTicket.put("id", ticketId - i);
                similarTicket.put("title", "Similar ticket " + i + " - " + getMockTicketTitle(i));
                similarTicket.put("description", getMockTicketDescription(i));
                similarTicket.put("status", getMockTicketStatus(i));
                similarTicket.put("priority", getMockTicketPriority(i));
                similarTicket.put("category", getMockTicketCategory(i));
                similarTicket.put("similarity_score", getMockSimilarityScore(i));
                similarTicket.put("resolution_time_hours", getMockResolutionTime(i));
                similarTicket.put("resolved_by", "agent" + i + "@example.com");
                similarTicket.put("created_at", "2024-01-" + String.format("%02d", i) + "T10:00:00Z");
                similarTicket.put("resolved_at", "2024-01-" + String.format("%02d", i) + "T14:30:00Z");
                
                similarTickets.add(similarTicket);
            }
            
            // Sort by similarity score (highest first)
            similarTickets.sort((a, b) -> {
                Double scoreA = (Double) a.get("similarity_score");
                Double scoreB = (Double) b.get("similarity_score");
                return scoreB.compareTo(scoreA);
            });
            
            log.debug("Found {} similar tickets for ticket: {}", similarTickets.size(), ticketId);
            return similarTickets;
            
        } catch (Exception e) {
            log.error("Error finding similar tickets for ticket: {}", ticketId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getSimilarityAnalysis(Long ticketId, Long organizationId, Long supportOrgId) {
        log.debug("Getting similarity analysis for ticket: {}", ticketId);
        
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            List<Map<String, Object>> similarTickets = findSimilarTickets(ticketId, organizationId, supportOrgId, 10);
            
            analysis.put("similar_tickets_count", similarTickets.size());
            analysis.put("similar_tickets", similarTickets);
            analysis.put("avg_similarity_score", calculateAverageSimilarityScore(similarTickets));
            analysis.put("avg_resolution_time", calculateAverageResolutionTime(similarTickets));
            analysis.put("most_common_category", getMostCommonCategory(similarTickets));
            analysis.put("most_common_priority", getMostCommonPriority(similarTickets));
            analysis.put("resolution_pattern", analyzeResolutionPattern(similarTickets));
            analysis.put("escalation_rate", calculateEscalationRate(similarTickets));
            
        } catch (Exception e) {
            log.error("Error getting similarity analysis for ticket: {}", ticketId, e);
            analysis.put("error", "Failed to analyze similar tickets: " + e.getMessage());
        }
        
        return analysis;
    }

    /**
     * Helper methods for similarity analysis
     */
    private Double calculateAverageSimilarityScore(List<Map<String, Object>> similarTickets) {
        if (similarTickets.isEmpty()) return 0.0;
        
        return similarTickets.stream()
            .mapToDouble(ticket -> (Double) ticket.get("similarity_score"))
            .average()
            .orElse(0.0);
    }

    private Double calculateAverageResolutionTime(List<Map<String, Object>> similarTickets) {
        if (similarTickets.isEmpty()) return 0.0;
        
        return similarTickets.stream()
            .mapToDouble(ticket -> (Double) ticket.get("resolution_time_hours"))
            .average()
            .orElse(24.0);
    }

    private String getMostCommonCategory(List<Map<String, Object>> similarTickets) {
        if (similarTickets.isEmpty()) return "GENERAL";
        
        Map<String, Long> categoryCounts = new HashMap<>();
        similarTickets.forEach(ticket -> {
            String category = (String) ticket.get("category");
            categoryCounts.merge(category, 1L, Long::sum);
        });
        
        return categoryCounts.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("GENERAL");
    }

    private String getMostCommonPriority(List<Map<String, Object>> similarTickets) {
        if (similarTickets.isEmpty()) return "MEDIUM";
        
        Map<String, Long> priorityCounts = new HashMap<>();
        similarTickets.forEach(ticket -> {
            String priority = (String) ticket.get("priority");
            priorityCounts.merge(priority, 1L, Long::sum);
        });
        
        return priorityCounts.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("MEDIUM");
    }

    private Map<String, Object> analyzeResolutionPattern(List<Map<String, Object>> similarTickets) {
        Map<String, Object> pattern = new HashMap<>();
        
        if (similarTickets.isEmpty()) {
            pattern.put("pattern", "NO_DATA");
            pattern.put("confidence", 0.0);
            return pattern;
        }
        
        // Analyze resolution patterns
        long quickResolutions = similarTickets.stream()
            .filter(ticket -> (Double) ticket.get("resolution_time_hours") < 4.0)
            .count();
        
        long mediumResolutions = similarTickets.stream()
            .filter(ticket -> {
                Double time = (Double) ticket.get("resolution_time_hours");
                return time >= 4.0 && time < 24.0;
            })
            .count();
        
        long slowResolutions = similarTickets.stream()
            .filter(ticket -> (Double) ticket.get("resolution_time_hours") >= 24.0)
            .count();
        
        pattern.put("quick_resolutions", quickResolutions);
        pattern.put("medium_resolutions", mediumResolutions);
        pattern.put("slow_resolutions", slowResolutions);
        pattern.put("total_tickets", similarTickets.size());
        
        // Determine pattern
        if (quickResolutions > mediumResolutions && quickResolutions > slowResolutions) {
            pattern.put("pattern", "QUICK_RESOLUTION");
            pattern.put("confidence", (double) quickResolutions / similarTickets.size());
        } else if (mediumResolutions > quickResolutions && mediumResolutions > slowResolutions) {
            pattern.put("pattern", "MEDIUM_RESOLUTION");
            pattern.put("confidence", (double) mediumResolutions / similarTickets.size());
        } else {
            pattern.put("pattern", "SLOW_RESOLUTION");
            pattern.put("confidence", (double) slowResolutions / similarTickets.size());
        }
        
        return pattern;
    }

    private Double calculateEscalationRate(List<Map<String, Object>> similarTickets) {
        if (similarTickets.isEmpty()) return 0.0;
        
        long escalatedTickets = similarTickets.stream()
            .filter(ticket -> "ESCALATED".equals(ticket.get("status")))
            .count();
        
        return (double) escalatedTickets / similarTickets.size();
    }

    /**
     * Mock data methods (replace with real data in production)
     */
    private String getMockTicketTitle(int index) {
        String[] titles = {
            "Login issue with SSO",
            "API rate limiting problem",
            "Database connection timeout",
            "Email notification not working",
            "Dashboard loading slowly"
        };
        return titles[index % titles.length];
    }

    private String getMockTicketDescription(int index) {
        String[] descriptions = {
            "Users are unable to login using SSO authentication",
            "API calls are being rate limited unexpectedly",
            "Database connections are timing out after 30 seconds",
            "Email notifications are not being sent to users",
            "Dashboard is taking more than 10 seconds to load"
        };
        return descriptions[index % descriptions.length];
    }

    private String getMockTicketStatus(int index) {
        String[] statuses = {"RESOLVED", "CLOSED", "RESOLVED", "ESCALATED", "RESOLVED"};
        return statuses[index % statuses.length];
    }

    private String getMockTicketPriority(int index) {
        String[] priorities = {"HIGH", "MEDIUM", "LOW", "HIGH", "MEDIUM"};
        return priorities[index % priorities.length];
    }

    private String getMockTicketCategory(int index) {
        String[] categories = {"TECHNICAL", "INTEGRATION", "PERFORMANCE", "NOTIFICATION", "UI"};
        return categories[index % categories.length];
    }

    private Double getMockSimilarityScore(int index) {
        // Generate decreasing similarity scores
        return Math.max(0.3, 0.9 - (index * 0.1));
    }

    private Double getMockResolutionTime(int index) {
        // Generate realistic resolution times
        return 2.0 + (index * 1.5);
    }
} 