package io.sx.ai.abstraction;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for validating resolved prompts
 * Ensures prompts are properly resolved before being sent to the LLM
 */
@Slf4j
public class ResolvedPromptValidator {
    
    // Pattern to match template variables like {variable} or {variable:format}
    // This pattern ignores escaped braces \{ and \}
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("(?<!\\\\)\\{([^}]+)\\}");
    
    /**
     * Check if a resolved prompt is fully resolved (no unresolved variables)
     */
    public static boolean isFullyResolved(ResolvedPrompt prompt) {
        if (prompt == null || prompt.getResolvedText() == null) {
            return false;
        }
        
        return isFullyResolved(prompt.getResolvedText());
    }
    
    /**
     * Check if a text is fully resolved (no unresolved variables)
     */
    public static boolean isFullyResolved(String text) {
        if (text == null || text.trim().isEmpty()) {
            return true; // Empty text is considered resolved
        }
        
        // Check for any remaining template variables
        return !VARIABLE_PATTERN.matcher(text).find();
    }
    
    /**
     * Check if resolved text is valid for LLM consumption
     * This method is used for text that has already been processed by the template engine
     * and may contain literal {variable} patterns that are meant for the LLM
     */
    public static boolean isValidForLLM(String resolvedText) {
        if (resolvedText == null || resolvedText.trim().isEmpty()) {
            return false; // Empty text is not valid for LLM
        }
        
        // For resolved text, we assume it's valid for LLM consumption
        // The template resolution process should have already handled all template variables
        // Any remaining {variable} patterns are meant to be literal text for the LLM
        return true;
    }
    
    /**
     * Validate that a resolved prompt is fully resolved
     */
    public static void validateFullyResolved(ResolvedPrompt prompt) {
        if (prompt == null) {
            throw new IllegalArgumentException("Prompt cannot be null");
        }
        
        if (!isFullyResolved(prompt)) {
            throw new IllegalStateException(
                String.format("Prompt is not fully resolved. Template: %s, Resolved: %s", 
                    prompt.getTemplateSource(), prompt.getResolvedText()));
        }
    }
    
    /**
     * Find unresolved variables in a template
     */
    public static List<String> findUnresolvedVariables(String template) {
        List<String> variables = new ArrayList<>();
        
        if (template == null || template.trim().isEmpty()) {
            return variables;
        }
        
        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        while (matcher.find()) {
            String variable = matcher.group(1);
            if (!variable.contains(":")) { // Simple variable without format specifier
                variables.add(variable);
            } else { // Variable with format specifier like {name:format}
                variables.add(variable.split(":")[0]);
            }
        }
        
        return variables;
    }
    
    /**
     * Validate that all required variables are provided
     */
    public static void validateRequiredVariables(String template, Map<String, Object> variables) {
        if (template == null || template.trim().isEmpty()) {
            return;
        }
        
        List<String> requiredVariables = findUnresolvedVariables(template);
        List<String> missingVariables = new ArrayList<>();
        
        for (String required : requiredVariables) {
            if (!variables.containsKey(required)) {
                missingVariables.add(required);
            }
        }
        
        if (!missingVariables.isEmpty()) {
            throw new IllegalArgumentException(
                String.format("Missing required variables: %s. Template: %s", 
                    missingVariables, template));
        }
    }
    
    /**
     * Validate that a resolved prompt has the expected prompt type
     */
    public static void validatePromptType(ResolvedPrompt prompt, SxPrompt.PromptType expectedPromptType) {
        if (prompt == null) {
            throw new IllegalArgumentException("Prompt cannot be null");
        }
        
        if (prompt.getPromptType() != expectedPromptType) {
            throw new IllegalArgumentException(
                String.format("Expected prompt type %s, but got %s", 
                    expectedPromptType, prompt.getPromptType()));
        }
    }
    
    /**
     * Validate that a resolved prompt has the expected message role
     */
    public static void validateMessageRole(ResolvedPrompt prompt, SxMessage.PromptRole expectedMessageRole) {
        if (prompt == null) {
            throw new IllegalArgumentException("Prompt cannot be null");
        }
        
        if (prompt.getMessageRole() != expectedMessageRole) {
            throw new IllegalArgumentException(
                String.format("Expected message role %s, but got %s", 
                    expectedMessageRole, prompt.getMessageRole()));
        }
    }
    
    /**
     * Check if a template contains any variables
     */
    public static boolean hasVariables(String template) {
        if (template == null || template.trim().isEmpty()) {
            return false;
        }
        return VARIABLE_PATTERN.matcher(template).find();
    }
    
    /**
     * Get a summary of variable usage in a template
     */
    public static String getVariableSummary(String template) {
        if (template == null || template.trim().isEmpty()) {
            return "No template provided";
        }
        
        List<String> variables = findUnresolvedVariables(template);
        if (variables.isEmpty()) {
            return "No variables found";
        }
        
        return String.format("Found %d variables: %s", variables.size(), String.join(", ", variables));
    }
} 