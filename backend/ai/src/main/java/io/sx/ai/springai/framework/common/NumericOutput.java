package io.sx.ai.springai.framework.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Numeric output wrapper for Double values with Spring AI integration
 * Supports mathematical operations and structured output
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NumericOutput implements WorkflowOutput<Double> {
    
    /**
     * The numeric value
     */
    private Double value;
    
    /**
     * Unit of measurement (e.g., "percentage", "currency", "count")
     */
    private String unit;
    
    /**
     * Format specification (e.g., "decimal", "percentage", "currency")
     */
    private String format;
    
    /**
     * Confidence score (0.0 to 1.0)
     */
    private Double confidence;
    
    /**
     * Additional metadata
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    @Override
    public String getType() {
        return "NUMERIC";
    }
    
    @Override
    public Map<String, Object> getMetadata() {
        Map<String, Object> enhancedMetadata = new HashMap<>(metadata);
        enhancedMetadata.put("unit", unit);
        enhancedMetadata.put("format", format);
        return enhancedMetadata;
    }
    
    /**
     * Static factory method for creating numeric outputs
     */
    public static NumericOutput of(Double value) {
        return NumericOutput.builder()
            .value(value)
            .unit("")
            .format("decimal")
            .confidence(1.0)
            .build();
    }
    
    /**
     * Static factory method for creating numeric outputs with unit
     */
    public static NumericOutput of(Double value, String unit) {
        return NumericOutput.builder()
            .value(value)
            .unit(unit)
            .format("decimal")
            .confidence(1.0)
            .build();
    }
    
    /**
     * Static factory method for creating percentage outputs
     */
    public static NumericOutput percentage(Double value) {
        return NumericOutput.builder()
            .value(value)
            .unit("percentage")
            .format("percentage")
            .confidence(1.0)
            .build();
    }
    
    /**
     * Static factory method for creating currency outputs
     */
    public static NumericOutput currency(Double value, String currencyCode) {
        return NumericOutput.builder()
            .value(value)
            .unit(currencyCode)
            .format("currency")
            .confidence(1.0)
            .build();
    }
    
    /**
     * Static factory method for creating outputs with confidence
     */
    public static NumericOutput of(Double value, String unit, Double confidence) {
        return NumericOutput.builder()
            .value(value)
            .unit(unit)
            .format("decimal")
            .confidence(confidence)
            .build();
    }
} 