package io.sx.ai.decision.model;

import io.sx.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * Result of AI decision evaluation
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AIDecisionResult extends BaseModel {
    
    private Boolean shouldApplyAI;
    private String aiStrategy; // "app_only", "llm_only", "hybrid", "custom"
    private List<String> aiMethods; // ["vector", "llm", "rules"]
    private Map<String, Object> strategyConfig;
    private Double confidenceThreshold;
    private String reasoning;
    private String ruleId;
    private Map<String, Object> metadata;
    
    public static AIDecisionResult noAI() {
        return AIDecisionResult.builder()
            .shouldApplyAI(false)
            .aiStrategy("none")
            .aiMethods(List.of())
            .build();
    }
    
    public static AIDecisionResult appOnly(Double confidenceThreshold) {
        return AIDecisionResult.builder()
            .shouldApplyAI(true)
            .aiStrategy("app_only")
            .aiMethods(List.of("vector", "rules"))
            .confidenceThreshold(confidenceThreshold)
            .build();
    }
    
    public static AIDecisionResult hybrid(Double confidenceThreshold) {
        return AIDecisionResult.builder()
            .shouldApplyAI(true)
            .aiStrategy("hybrid")
            .aiMethods(List.of("vector", "llm"))
            .confidenceThreshold(confidenceThreshold)
            .build();
    }
    
    public static AIDecisionResult llmOnly(Double confidenceThreshold) {
        return AIDecisionResult.builder()
            .shouldApplyAI(true)
            .aiStrategy("llm_only")
            .aiMethods(List.of("llm"))
            .confidenceThreshold(confidenceThreshold)
            .build();
    }
} 