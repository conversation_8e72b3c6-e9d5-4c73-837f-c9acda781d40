package io.sx.ai.decision.rules;

import io.sx.ai.decision.model.AIDecisionContext;
import io.sx.ai.decision.model.AIDecisionResult;

/**
 * Interface for AI decision rules
 */
public interface AIDecisionRule {
    
    /**
     * Get unique rule identifier
     */
    String getRuleId();
    
    /**
     * Get human-readable rule name
     */
    String getRuleName();
    
    /**
     * Get rule description
     */
    String getDescription();
    
    /**
     * Get rule priority (higher priority rules are evaluated first)
     */
    int getPriority();
    
    /**
     * Check if rule is applicable for the given trigger event and context
     */
    boolean isApplicable(String triggerEvent, AIDecisionContext context);
    
    /**
     * Evaluate the rule and return decision result
     */
    AIDecisionResult evaluate(AIDecisionContext context);
} 