package io.sx.ai.springai.sample.tools;

import io.sx.ai.springai.framework.common.BaseWorkflowStep;
import io.sx.ai.springai.framework.common.BaseStepResult;
import io.sx.ai.springai.framework.common.NumericOutput;
import io.sx.ai.springai.framework.common.WorkflowInput;
import io.sx.ai.springai.framework.common.WorkflowOutput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * A workflow step that divides the input by 2
 */
@Slf4j
@Component
public class HalvingStep implements BaseWorkflowStep<Double, Double> {
    
    private int currentStepNumber = 1;
    private String stepName = "Halving Step";
    
    /**
     * Execute the halving operation
     */
    @Override
    public BaseStepResult<Double, Double> execute(WorkflowInput<Double> input, BaseStepResult<?, ?> previousStep) {
        try {
            Double inputValue = input.getValue();
            Double result = inputValue / 2.0;
            
            return BaseStepResult.success(
                getCurrentStepNumber(),
                getStepName(),
                inputValue,
                result,
                "halved"
            );
        } catch (Exception e) {
            return BaseStepResult.error(
                getCurrentStepNumber(),
                getStepName(),
                input.getValue(),
                "Error halving value: " + e.getMessage()
            );
        }
    }

    @Override
    public WorkflowOutput<Double> processInput(WorkflowInput<Double> input, BaseStepResult<?, ?> previousStep) {
        try {
            Double inputValue = input.getValue();
            Double result = inputValue / 2.0;
            return NumericOutput.of(result);
        } catch (Exception e) {
            log.error("Error processing input in halving step: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String getStepName() {
        return stepName;
    }

    @Override
    public String getStepDescription() {
        return "Halves the input value by dividing it by 2";
    }

    @Override
    public boolean isFinalStep() {
        return false;
    }

    @Override
    public StepType getStepType() {
        return StepType.MATHEMATICAL_OPERATION;
    }

    public int getCurrentStepNumber() {
        return currentStepNumber;
    }

    public void setCurrentStepNumber(int currentStepNumber) {
        this.currentStepNumber = currentStepNumber;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
    }
} 