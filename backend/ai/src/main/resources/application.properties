# =============================================================================
# AI MODULE - BASE CONFIGURATION
# =============================================================================

# Default profile (can be overridden with --spring.profiles.active=prod)
spring.profiles.active=dev

# Application metadata
spring.application.name=sx-aif
spring.application.description=AI-powered ticketing system with similarity analysis and agents

# =============================================================================
# SPRING BOOT CONFIGURATION
# =============================================================================

# Server Configuration
server.port=8082
server.servlet.context-path=/ai

# Database Configuration (AI Schema in sx_cdc_db)
spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.schemas=ai
spring.flyway.table=ai_flyway_schema_history
spring.flyway.baseline-on-migrate=false
spring.flyway.validate-on-migrate=false
spring.flyway.repair-on-migrate=true
spring.flyway.clean-disabled=false

# Jackson configuration
spring.jackson.default-property-inclusion=non_null
spring.jackson.serialization.write-dates-as-timestamps=false

# =============================================================================
# MAIN SX APPLICATION CONFIGURATION
# =============================================================================

sx.main-app.base-url=http://localhost:8080

# =============================================================================
# CREDENTIAL STORE CONFIGURATION
# =============================================================================

# Use properties-based credential store
sx.app.credentials.store.type=spring

# Credentials for tickets context (app module will be renamed to tickets)
tickets.api-key=key
tickets.api-secret=secret

# =============================================================================
# NATS CONFIGURATION
# =============================================================================

nats.url=nats://localhost:4222
nats.server.url=nats://localhost:4222

# NATS Connection Configuration
nats.connection.timeout=5000
nats.connection.reconnect.wait=1000
nats.connection.max.reconnect.attempts=5

# NATS JetStream Audit Stream (for AI consumption)
nats.jetstream.audit.stream.name=sx-audit-stream
nats.jetstream.audit.stream.subjects=sx.audit.events.>
nats.jetstream.audit.consumer.name=ai-consumer
nats.jetstream.audit.consumer.durable=true
nats.jetstream.audit.consumer.ack-policy=Explicit
nats.jetstream.audit.consumer.deliver-policy=All
nats.jetstream.audit.consumer.replay-policy=Instant

# NATS Consumer Configuration
nats.consumer.batch-size=10
nats.consumer.fetch-interval-ms=2000
nats.consumer.max-reconnect-attempts=5
nats.consumer.reconnect-wait-ms=1000

# =============================================================================
# AI CONFIGURATION
# =============================================================================

ai.enabled=true
ai.environment=DEVELOPMENT
ai.default-provider=ollama

# AI Provider Configuration
ai.provider.ollama.enabled=true
ai.provider.openai.enabled=false

# =============================================================================
# LLM ABSTRACTION LAYER CONFIGURATION
# =============================================================================

# LLM Provider Selection (using Spring AI auto-configuration)
ai.llm.openai.api-key=${OPENAI_API_KEY:}
ai.llm.openai.model=gpt-3.5-turbo

# =============================================================================
# THINKING SYSTEM CONFIGURATION  
# =============================================================================

# Default thinking strategy (CHAIN_OF_THOUGHT, TREE_OF_THOUGHT)
ai.thinking.default-strategy=CHAIN_OF_THOUGHT
ai.thinking.max-processing-time-ms=30000
ai.thinking.enable-parallel-processing=true

# Chain of Thought specific settings
ai.thinking.cot.max-steps=10
ai.thinking.cot.confidence-threshold=0.7

# Tree of Thought specific settings  
ai.thinking.tot.max-branches=5
ai.thinking.tot.max-depth=3
ai.thinking.tot.branch-confidence-threshold=0.6

# Ollama Configuration
ai.ollama.base-url=http://localhost:11434
ai.ollama.default-model=llama3.2:latest
ai.ollama.timeout=30000

# OpenAI Configuration (for production)
ai.openai.api-key=${OPENAI_API_KEY:}
ai.openai.base-url=https://api.openai.com/v1
ai.openai.default-model=gpt-4

# AI Model Configuration
ai.model.routing.model-id=llama3.2:latest
ai.model.routing.temperature=0.1
ai.model.routing.max-tokens=1000
ai.model.routing.confidence-threshold=0.7

ai.model.evaluation.model-id=llama3.2:latest
ai.model.evaluation.temperature=0.2
ai.model.evaluation.max-tokens=1500
ai.model.evaluation.confidence-threshold=0.8

# AI Suggestion Configuration
ai.suggestion.enabled=true
ai.suggestion.max-suggestions-per-event=3
ai.suggestion.fallback-enabled=true

# AI Agent Configuration
ai.agent.ticketing.enabled=true
ai.agent.priority.enabled=true
ai.agent.assignment.enabled=true
ai.agent.comment.enabled=true
ai.agent.status-change.enabled=true
ai.agent.customer-satisfaction.enabled=true
ai.agent.user-onboarding.enabled=true

# AI Performance Configuration
ai.performance.timeout-seconds=30
ai.performance.retry-attempts=3
ai.performance.cache-enabled=true
ai.performance.cache-ttl-seconds=300

# Chain of Thought Configuration
ai.chain-of-thought.enabled=true
ai.chain-of-thought.max-chain-length=10
ai.chain-of-thought.max-depth=5
ai.chain-of-thought.confidence-threshold=0.6
ai.chain-of-thought.evolution-enabled=true
ai.chain-of-thought.optimization-enabled=true
ai.chain-of-thought.persistence-enabled=true

# Tree of Thought Configuration
ai.tree-of-thought.enabled=true
ai.tree-of-thought.max-branches=5
ai.tree-of-thought.max-branch-depth=3
ai.tree-of-thought.branch-exploration-limit=10
ai.tree-of-thought.confidence-threshold=0.7

# Chain of Thought Optimization Strategies
ai.chain-of-thought.optimization.confidence-boost.enabled=true
ai.chain-of-thought.optimization.simplification.enabled=true
ai.chain-of-thought.optimization.consolidation.enabled=true
ai.chain-of-thought.optimization.merge.enabled=true

# Chain of Thought Persistence
ai.chain-of-thought.persistence.cleanup-enabled=true
ai.chain-of-thought.persistence.cleanup-days-old=30
ai.chain-of-thought.persistence.archive-enabled=true
ai.chain-of-thought.persistence.validation-enabled=true

# Vector Store (pgvector) configuration
#ai.vectorstore.dimensions=768
#ai.vectorstore.distance-type=COSINE_DISTANCE
#ai.vectorstore.index-type=HNSW
#ai.vectorstore.schema-name=ai
#ai.vectorstore.table-name=vector_store
#ai.vectorstore.max-batch-size=10000

# =============================================================================
# SPRING AI CONFIGURATION
# =============================================================================

# Default to Ollama for development
spring.ai.chat.client.impl=ollama
spring.ai.ollama.base-url=http://localhost:11434
spring.ai.ollama.chat.options.model=llama3.2:latest
spring.ai.ollama.chat.options.temperature=0.7
spring.ai.ollama.chat.options.max-tokens=1000
spring.ai.ollama.chat.options.timeout=300s
#embedding
spring.ai.model.embedding=ollama
spring.ai.ollama.embedding.options.model=nomic-embed-text


# Spring AI HTTP Client Configuration
spring.ai.ollama.client.timeout=600s
spring.ai.ollama.client.connect-timeout=30s
spring.ai.ollama.client.read-timeout=600s
spring.ai.ollama.client.write-timeout=600s

# OpenAI (for production)
spring.ai.openai.api-key=${OPENAI_API_KEY:}
spring.ai.openai.base-url=${OPENAI_BASE_URL:https://api.openai.com}
spring.ai.openai.chat.options.model=${OPENAI_MODEL:gpt-4}

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

spring.security.enabled=false

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

logging.level.root=INFO
logging.level.io.sx.ai=DEBUG
logging.level.io.nats=INFO

# =============================================================================
# ACTUATOR CONFIGURATION
# =============================================================================

management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always 

# =============================================================================
# AI AUTONOMY CONFIGURATION
# =============================================================================

# Default autonomy level for suggestions
ai.autonomy.default-level=HITL

# Granular autonomy configuration per suggestion type
ai.autonomy.assignment=HITL
ai.autonomy.priority-adjustment=HITL
ai.autonomy.escalation=ADVISOR
ai.autonomy.notification=FULL_AUTONOMY
ai.autonomy.status-update=HITL
ai.autonomy.comment-addition=FULL_AUTONOMY
ai.autonomy.sla-alert=HITL
ai.autonomy.process-improvement=ADVISOR
ai.autonomy.customer-communication=HITL
ai.autonomy.resource-allocation=ADVISOR

# Confidence thresholds for auto-execution
ai.autonomy.confidence-threshold.full-autonomy=0.9
ai.autonomy.confidence-threshold.hitl=0.7
ai.autonomy.confidence-threshold.advisor=0.5

# Risk-based autonomy adjustment
ai.autonomy.risk.high-priority-ticket=ADVISOR
ai.autonomy.risk.customer-facing-action=HITL
ai.autonomy.risk.system-configuration=ADVISOR
ai.autonomy.risk.data-modification=HITL 