Current state:
- Input number: {inputNumber}
- Current iteration: {currentIteration}
- Max iterations: {maxIterations}
- Target range: 990-1000 (inclusive)

ANALYSIS:
1. Is {inputNumber} between 990 and 1000 (inclusive)?
   - YES: The number is in the target range.
   - NO: The number is outside the target range.
2. If the number is outside the target range, determine the appropriate tool:
   - If {inputNumber} < 990: Use quadruple_number (multiply by 4 to increase the number).
   - If {inputNumber} > 1000: Use halve_number (divide by 2 to decrease the number).

DECISION:
- If the number is IN the target range (990-1000 inclusive): TERMINATE and return {inputNumber} as final result.
- If the number is OUTSIDE the target range: CONTINUE with the specific tool determined in step 2 above.

IMPORTANT: You MUST NOT terminate unless the number is between 990 and 1000 inclusive. If the number is outside this range, you MUST continue with the appropriate tool.

RESPONSE FORMAT: You must return a valid JSON object with the following structure:

For CONTINUE decisions:
\{
"decision": "CONTINUE",
"reasoning": "Detailed reasoning for the decision",
"confidence": 0.9,
"toolsToExecute": ["tool_name"]
\}

For TERMINATE decisions:
\{
"decision": "TERMINATE",
"reasoning": "Detailed reasoning for termination",
"confidence": 0.9,
"finalResult": {inputNumber},
"terminationReason": "Reason for termination"
\}

CRITICAL: Return ONLY the JSON object. Do NOT wrap it in markdown code blocks (```json), do NOT add any text before or after the JSON. Return pure JSON only. 