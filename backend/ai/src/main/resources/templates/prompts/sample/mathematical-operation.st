INPUT: {input}
OPERATION: {operation}

RESPONSE FORMAT: Return a JSON object with the following structure:
\{
"decision": "TERMINATE",
"reasoning": "Performed {operation} on {input}",
"confidence": 1.0,
"finalResult": <actual_numerical_result>,
"terminationReason": "Operation completed"
\}

Perform the operation: {operation} on the input value {input}.

IMPORTANT: You must return valid JSON. Replace <actual_numerical_result> with the actual numerical result of the operation. 