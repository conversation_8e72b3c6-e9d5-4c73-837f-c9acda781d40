Please categorize the following support ticket.

## Ticket Information
**Title**: {title}
**Description**: {description}
**Priority**: {priority}
**Status**: {status}
**Created**: {createdAt}
**Updated**: {updatedAt}

## Allowed Categories
- Bug: A defect or error in the product.
- Feature Request: A request for new functionality.
- Question: A general inquiry or request for information.
- Incident: An unplanned interruption or reduction in quality.
- Task: A routine or scheduled work item.
- Other: Does not fit any of the above.

## Response Format

Return your classification as a JSON object with the following structure:

\{
  "category": "The category label (Bug, Feature Request, Question, Incident, Task, or Other)",
  "confidence": 0.85,
  "reasoning": "Detailed explanation of why you chose this category"
\}


Be thorough in your analysis and provide specific reasoning for your classification decision. 