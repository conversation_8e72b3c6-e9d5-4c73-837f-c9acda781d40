-- Migration: Create HNSW index on ai.vector_store.embedding for pgvector
-- This migration ensures the index exists for fast vector similarity search

CREATE INDEX IF NOT EXISTS spring_ai_vector_index
    ON ai.vector_store
    USING HNSW (embedding vector_cosine_ops);

-- Add comment for documentation
COMMENT ON INDEX spring_ai_vector_index IS 'HNSW index for vector similarity search on ai.vector_store.embedding'; 