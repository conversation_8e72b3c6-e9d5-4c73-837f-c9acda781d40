-- Migration: Add thinking_log_id foreign key to suggestions table for XAI traceability
-- This links suggestions to their thinking process for complete explainability

-- Add thinking_log_id column to suggestions table
ALTER TABLE ai.suggestions 
ADD COLUMN thinking_log_id bigint;

-- Add foreign key constraint
ALTER TABLE ai.suggestions 
ADD CONSTRAINT fk_suggestions_thinking_log 
FOREIGN KEY (thinking_log_id) REFERENCES ai.thinking_logs(id);

-- Add index for performance
CREATE INDEX idx_suggestions_thinking_log_id ON ai.suggestions(thinking_log_id);

-- Add comment explaining the relationship
COMMENT ON COLUMN ai.suggestions.thinking_log_id IS 'Foreign key to thinking_logs table for XAI traceability - links suggestions to their reasoning process';

-- Update existing suggestions to link to thinking logs if they exist
-- This is a best-effort linking based on session_id and creation time proximity
UPDATE ai.suggestions s
SET thinking_log_id = (
    SELECT tl.id 
    FROM ai.thinking_logs tl 
    WHERE tl.session_id = s.session_id 
    AND tl.created_at <= s.created_at 
    AND tl.created_at >= s.created_at - INTERVAL '5 minutes'
    ORDER BY tl.created_at DESC 
    LIMIT 1
)
WHERE s.thinking_log_id IS NULL;

-- Add comment to table
COMMENT ON TABLE ai.suggestions IS 'AI suggestions linked to thinking logs for complete XAI traceability'; 