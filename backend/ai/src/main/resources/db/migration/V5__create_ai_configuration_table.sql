-- Add new columns to existing AI configuration table for Decision Framework
-- V3 already created ai.configurations with basic structure
-- This migration adds the new columns needed for the AI Decision Framework

-- Add new columns to existing table
ALTER TABLE ai.configurations 
ADD COLUMN IF NOT EXISTS configuration_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS configuration_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Update existing rows to have default values
UPDATE ai.configurations 
SET configuration_type = 'classification',
    configuration_data = config_value,
    is_active = true
WHERE configuration_type IS NULL;

-- Create indexes (only if they don't exist)
CREATE INDEX IF NOT EXISTS idx_ai_config_type_org ON ai.configurations(configuration_type, support_organization_id);
CREATE INDEX IF NOT EXISTS idx_ai_config_active ON ai.configurations(is_active);
CREATE INDEX IF NOT EXISTS idx_ai_config_jsonb ON ai.configurations USING GIN (configuration_data);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_ai_configurations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_update_ai_configurations_updated_at') THEN
        CREATE TRIGGER trigger_update_ai_configurations_updated_at
            BEFORE UPDATE ON ai.configurations
            FOR EACH ROW
            EXECUTE FUNCTION update_ai_configurations_updated_at();
    END IF;
END $$;

-- Insert default AI configuration for SX organization (only if not exists)
INSERT INTO ai.configurations (
    organization_id,
    config_key,
    config_value,
    configuration_type, 
    support_organization_id, 
    configuration_data, 
    created_by,
    on_behalf_of_id
) 
SELECT 
    1, -- Default organization ID
    'classification',
    '{
        "enabled": true,
        "default_strategy": "hybrid",
        "min_description_length": 10,
        "confidence_thresholds": {
            "app_only": 0.8,
            "hybrid": 0.7,
            "llm_only": 0.6
        },
        "customer_importance_thresholds": {
            "high": 8.0,
            "medium": 5.0,
            "low": 3.0
        },
        "priority_weights": {
            "CRITICAL": 1.5,
            "HIGH": 1.2,
            "MEDIUM": 1.0,
            "LOW": 0.8
        }
    }'::jsonb,
    'classification',
    1, -- Default support organization ID
    '{
        "enabled": true,
        "default_strategy": "hybrid",
        "min_description_length": 10,
        "confidence_thresholds": {
            "app_only": 0.8,
            "hybrid": 0.7,
            "llm_only": 0.6
        },
        "customer_importance_thresholds": {
            "high": 8.0,
            "medium": 5.0,
            "low": 3.0
        },
        "priority_weights": {
            "CRITICAL": 1.5,
            "HIGH": 1.2,
            "MEDIUM": 1.0,
            "LOW": 0.8
        }
    }'::jsonb,
    'system',
    1
WHERE NOT EXISTS (
    SELECT 1 FROM ai.configurations 
    WHERE config_key = 'classification' AND organization_id = 1
); 