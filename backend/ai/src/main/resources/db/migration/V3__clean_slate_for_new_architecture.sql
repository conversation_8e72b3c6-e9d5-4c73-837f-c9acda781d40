-- Clean slate migration for new AI architecture
-- Drops all complex relational AI structures and keeps only essential infrastructure

-- Drop all existing AI tables
DROP TABLE IF EXISTS ai.ai_suggestions CASCADE;
DROP TABLE IF EXISTS ai.thinking_sessions CASCADE;
DROP TABLE IF EXISTS ai.thinking_nodes CASCADE;
DROP TABLE IF EXISTS ai.thinking_iterations CASCADE;
DROP TABLE IF EXISTS ai.context_data CASCADE;
DROP TABLE IF EXISTS ai.ai_actions CASCADE;
DROP TABLE IF EXISTS ai.ai_audit_log CASCADE;
DROP TABLE IF EXISTS ai.ai_configurations CASCADE;
DROP TABLE IF EXISTS ai.ai_performance_metrics CASCADE;
DROP TABLE IF EXISTS ai.ai_suggestion_sessions CASCADE;
DROP TABLE IF EXISTS ai.ai_suggestion_chains CASCADE;
DROP TABLE IF EXISTS ai.ai_suggestion_compositions CASCADE;
DROP TABLE IF EXISTS ai.ai_suggestion_contexts CASCADE;
DROP TABLE IF EXISTS ai.ai_suggestion_executions CASCADE;
DROP TABLE IF EXISTS ai.ai_thoughts CASCADE;
DROP TABLE IF EXISTS ai.ai_workflows CASCADE;
DROP TABLE IF EXISTS ai.ai_workflow_steps CASCADE;
DROP TABLE IF EXISTS ai.ai_workflow_visualizations CASCADE;
DROP TABLE IF EXISTS ai.agent_configs CASCADE;
DROP TABLE IF EXISTS ai.agent_metrics CASCADE;
DROP TABLE IF EXISTS ai.similarity_analysis CASCADE;
DROP TABLE IF EXISTS ai.similarity_results CASCADE;
DROP TABLE IF EXISTS ai.ticket_embeddings CASCADE;
DROP TABLE IF EXISTS ai.customer_similarity_rules CASCADE;
DROP TABLE IF EXISTS ai.rule_embeddings CASCADE;
DROP TABLE IF EXISTS ai.rule_usage_stats CASCADE;
DROP TABLE IF EXISTS ai.similarity_analysis_cache CASCADE;

-- Keep only essential infrastructure
-- vector_store table is already created and should be kept for RAG functionality

-- Create a simple JSON-based thinking log table for Chain of Thought / Tree of Thought
CREATE TABLE IF NOT EXISTS ai.thinking_logs (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    trigger_event VARCHAR(100) NOT NULL,
    trigger_entity_id BIGINT NOT NULL,
    trigger_entity_type VARCHAR(50) NOT NULL,
    thinking_strategy VARCHAR(50) NOT NULL, -- 'CHAIN_OF_THOUGHT', 'TREE_OF_THOUGHT'
    
    -- Complete thinking process stored as JSON
    thinking_process JSONB NOT NULL,
    
    -- Final suggestions as JSON array
    suggestions JSONB,
    
    -- Metadata
    session_status VARCHAR(50) DEFAULT 'ACTIVE',
    confidence_score DECIMAL(3,2),
    processing_time_ms BIGINT,
    
    -- Standard audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT,
    updated_by_id BIGINT,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    organization_id BIGINT,
    support_organization_id BIGINT,
    on_behalf_of_id BIGINT
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_thinking_logs_session_id ON ai.thinking_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_thinking_logs_trigger_entity ON ai.thinking_logs(trigger_entity_type, trigger_entity_id);
CREATE INDEX IF NOT EXISTS idx_thinking_logs_status ON ai.thinking_logs(session_status);
CREATE INDEX IF NOT EXISTS idx_thinking_logs_created_at ON ai.thinking_logs(created_at);

-- Create a simple configuration table for AI settings
CREATE TABLE IF NOT EXISTS ai.configurations (
    id BIGSERIAL PRIMARY KEY,
    organization_id BIGINT NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    config_value JSONB NOT NULL,
    
    -- Standard audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT,
    updated_by_id BIGINT,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    support_organization_id BIGINT,
    on_behalf_of_id BIGINT,
    
    UNIQUE(organization_id, config_key)
);

-- Create index for configuration lookups
CREATE INDEX IF NOT EXISTS idx_ai_config_org_key ON ai.configurations(organization_id, config_key);

-- Add comments
COMMENT ON TABLE ai.thinking_logs IS 'JSON-based logging for Chain of Thought and Tree of Thought AI reasoning processes';
COMMENT ON TABLE ai.configurations IS 'AI configuration settings per organization';
COMMENT ON COLUMN ai.thinking_logs.thinking_process IS 'Complete thinking process including steps, reasoning, and decisions as JSON';
COMMENT ON COLUMN ai.thinking_logs.suggestions IS 'Final AI suggestions generated from the thinking process as JSON array'; 