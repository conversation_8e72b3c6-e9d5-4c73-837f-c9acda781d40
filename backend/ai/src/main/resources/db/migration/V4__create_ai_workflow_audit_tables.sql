-- AI Workflow Audit Tables for Enhanced Tracking and Analysis
-- This migration creates tables to track AI decision-making, tool executions, and performance metrics

-- AI Analysis Sessions Table
CREATE TABLE ai.analysis_sessions (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    execution_mode VARCHAR(50) NOT NULL, -- HY<PERSON><PERSON>, FULL_MCP
    organization_id BIGINT NOT NULL,
    on_behalf_of_id BIGINT NOT NULL,
    analysis_request JSONB NOT NULL,
    analysis_response JSONB,
    confidence_score DECIMAL(3,2),
    total_analysis_time_ms BIGINT,
    llm_response_time_ms BIGINT,
    total_tokens_used INTEGER,
    llm_provider VARCHAR(100),
    model_version VARCHAR(100),
    cost_estimate DECIMAL(10,4),
    memory_usage_mb DECIMAL(8,2),
    cpu_usage_percent DECIMAL(5,2),
    status VARCHAR(50) DEFAULT 'IN_PROGRESS', -- IN_PROGRESS, COMPLETED, FAILED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT NOT NULL,
    updated_by_id BIGINT NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL
);

-- AI Tool Executions Table
CREATE TABLE ai.tool_executions (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL REFERENCES ai.analysis_sessions(session_id),
    tool_name VARCHAR(255) NOT NULL,
    parameters JSONB NOT NULL,
    execution_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    execution_duration_ms BIGINT,
    success BOOLEAN NOT NULL,
    result JSONB,
    error_message TEXT,
    llm_reasoning TEXT,
    confidence_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT NOT NULL,
    updated_by_id BIGINT NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL
);

-- AI Workflow Steps Table
CREATE TABLE ai.workflow_steps (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL REFERENCES ai.analysis_sessions(session_id),
    step_id VARCHAR(255) NOT NULL,
    step_type VARCHAR(100) NOT NULL, -- ANALYSIS, TOOL_CALL, WAIT, COMPLETE
    description TEXT NOT NULL,
    start_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_timestamp TIMESTAMP,
    duration_ms BIGINT,
    success BOOLEAN,
    parallel_execution BOOLEAN DEFAULT FALSE,
    dependencies JSONB, -- Array of step IDs this step depends on
    output JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT NOT NULL,
    updated_by_id BIGINT NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL
);

-- AI Decision Trees Table
CREATE TABLE ai.decision_trees (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL REFERENCES ai.analysis_sessions(session_id),
    decision_id VARCHAR(255) NOT NULL,
    decision_type VARCHAR(100) NOT NULL, -- CLASSIFICATION, ASSIGNMENT, PRIORITY, etc.
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    confidence DECIMAL(3,2) NOT NULL,
    reasoning TEXT,
    parent_decision_id VARCHAR(255), -- For hierarchical decisions
    action_taken VARCHAR(255),
    tool_called VARCHAR(255),
    decision_path JSONB, -- Array of decision IDs in the path
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT NOT NULL,
    updated_by_id BIGINT NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL
);

-- AI Performance Metrics Table
CREATE TABLE ai.performance_metrics (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL REFERENCES ai.analysis_sessions(session_id),
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_unit VARCHAR(50),
    metric_category VARCHAR(100), -- TIMING, RESOURCE, COST, QUALITY
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT NOT NULL,
    updated_by_id BIGINT NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL
);

-- AI Analysis Decisions Table
CREATE TABLE ai.analysis_decisions (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL REFERENCES ai.analysis_sessions(session_id),
    decision_id VARCHAR(255) NOT NULL,
    decision_type VARCHAR(100) NOT NULL, -- CLASSIFICATION, ASSIGNMENT, PRIORITY, etc.
    current_value VARCHAR(255),
    recommended_value VARCHAR(255),
    confidence DECIMAL(3,2) NOT NULL,
    reasoning TEXT,
    requires_action BOOLEAN DEFAULT TRUE,
    action_taken VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT NOT NULL,
    updated_by_id BIGINT NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL
);

-- AI Recommended Actions Table
CREATE TABLE ai.recommended_actions (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL REFERENCES ai.analysis_sessions(session_id),
    action_id VARCHAR(255) NOT NULL,
    action_name VARCHAR(255) NOT NULL,
    priority INTEGER NOT NULL,
    reasoning TEXT,
    estimated_impact VARCHAR(255),
    dependencies JSONB, -- Array of action IDs this action depends on
    executed BOOLEAN DEFAULT FALSE,
    execution_timestamp TIMESTAMP,
    execution_result JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT NOT NULL,
    updated_by_id BIGINT NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL
);

-- Create indexes for performance
CREATE INDEX idx_analysis_sessions_org_id ON ai.analysis_sessions(organization_id);
CREATE INDEX idx_analysis_sessions_event_type ON ai.analysis_sessions(event_type);
CREATE INDEX idx_analysis_sessions_execution_mode ON ai.analysis_sessions(execution_mode);
CREATE INDEX idx_analysis_sessions_created_at ON ai.analysis_sessions(created_at);
CREATE INDEX idx_analysis_sessions_session_id ON ai.analysis_sessions(session_id);

CREATE INDEX idx_tool_executions_session_id ON ai.tool_executions(session_id);
CREATE INDEX idx_tool_executions_tool_name ON ai.tool_executions(tool_name);
CREATE INDEX idx_tool_executions_success ON ai.tool_executions(success);
CREATE INDEX idx_tool_executions_timestamp ON ai.tool_executions(execution_timestamp);

CREATE INDEX idx_workflow_steps_session_id ON ai.workflow_steps(session_id);
CREATE INDEX idx_workflow_steps_step_type ON ai.workflow_steps(step_type);
CREATE INDEX idx_workflow_steps_success ON ai.workflow_steps(success);

CREATE INDEX idx_decision_trees_session_id ON ai.decision_trees(session_id);
CREATE INDEX idx_decision_trees_decision_type ON ai.decision_trees(decision_type);
CREATE INDEX idx_decision_trees_confidence ON ai.decision_trees(confidence);

CREATE INDEX idx_performance_metrics_session_id ON ai.performance_metrics(session_id);
CREATE INDEX idx_performance_metrics_category ON ai.performance_metrics(metric_category);
CREATE INDEX idx_performance_metrics_timestamp ON ai.performance_metrics(timestamp);

CREATE INDEX idx_analysis_decisions_session_id ON ai.analysis_decisions(session_id);
CREATE INDEX idx_analysis_decisions_decision_type ON ai.analysis_decisions(decision_type);
CREATE INDEX idx_analysis_decisions_requires_action ON ai.analysis_decisions(requires_action);

CREATE INDEX idx_recommended_actions_session_id ON ai.recommended_actions(session_id);
CREATE INDEX idx_recommended_actions_priority ON ai.recommended_actions(priority);
CREATE INDEX idx_recommended_actions_executed ON ai.recommended_actions(executed);

-- Add comments for documentation
COMMENT ON TABLE ai.analysis_sessions IS 'Tracks AI analysis sessions with performance metrics and execution mode';
COMMENT ON TABLE ai.tool_executions IS 'Records individual tool executions with timing and results';
COMMENT ON TABLE ai.workflow_steps IS 'Tracks workflow step execution with parallel/sequential processing';
COMMENT ON TABLE ai.decision_trees IS 'Stores hierarchical decision trees for AI reasoning analysis';
COMMENT ON TABLE ai.performance_metrics IS 'Captures detailed performance metrics for AI operations';
COMMENT ON TABLE ai.analysis_decisions IS 'Records individual decisions made during AI analysis';
COMMENT ON TABLE ai.recommended_actions IS 'Tracks AI-recommended actions and their execution status'; 