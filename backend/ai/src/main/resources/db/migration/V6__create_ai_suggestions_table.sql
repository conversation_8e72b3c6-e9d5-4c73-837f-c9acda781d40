-- Create AI suggestions table for storing AI-generated suggestions
CREATE TABLE IF NOT EXISTS ai.suggestions (
    id BIGSERIAL PRIMARY KEY,
    suggestion_id VARCHAR(255) NOT NULL UNIQUE,
    session_id VARCHAR(255) NOT NULL,
    autonomy_level VARCHAR(50) NOT NULL, -- ADVISOR, HIT<PERSON>, FULL_AUTONOMY
    suggestion_type VARCHAR(100) NOT NULL, -- ASSIGNMENT, PRIORITY_ADJUSTMENT, etc.
    title VARCHAR(500) NOT NULL,
    description TEXT,
    reasoning TEXT,
    confidence_score DECIMAL(3,2),
    priority INTEGER,
    tool_name VARCHAR(255),
    tool_parameters JSONB,
    status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED, EXECUTED, FAILED
    execution_result JSONB,
    approval_required BOOLEAN DEFAULT true,
    approved_by BIGINT,
    approved_at TIMESTAMP,
    executed_at TIMESTAMP,
    error_message TEXT,
    metadata JSONB,
    
    -- Standard audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by_id BIGINT,
    updated_by_id BIGINT,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    organization_id BIGINT,
    support_organization_id BIGINT,
    on_behalf_of_id BIGINT
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_suggestions_session_id ON ai.suggestions(session_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_suggestion_id ON ai.suggestions(suggestion_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_status ON ai.suggestions(status);
CREATE INDEX IF NOT EXISTS idx_suggestions_type ON ai.suggestions(suggestion_type);
CREATE INDEX IF NOT EXISTS idx_suggestions_autonomy_level ON ai.suggestions(autonomy_level);
CREATE INDEX IF NOT EXISTS idx_suggestions_org ON ai.suggestions(organization_id, support_organization_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_created_at ON ai.suggestions(created_at);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_suggestions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_update_suggestions_updated_at') THEN
        CREATE TRIGGER trigger_update_suggestions_updated_at
            BEFORE UPDATE ON ai.suggestions
            FOR EACH ROW
            EXECUTE FUNCTION update_suggestions_updated_at();
    END IF;
END $$;

-- Add comments
COMMENT ON TABLE ai.suggestions IS 'AI-generated suggestions for ticket processing';
COMMENT ON COLUMN ai.suggestions.suggestion_id IS 'Unique identifier for the suggestion';
COMMENT ON COLUMN ai.suggestions.session_id IS 'Session ID for grouping related suggestions';
COMMENT ON COLUMN ai.suggestions.autonomy_level IS 'Autonomy level: ADVISOR, HITL, FULL_AUTONOMY';
COMMENT ON COLUMN ai.suggestions.suggestion_type IS 'Type of suggestion: ASSIGNMENT, PRIORITY_ADJUSTMENT, etc.';
COMMENT ON COLUMN ai.suggestions.confidence_score IS 'AI confidence score (0.0 to 1.0)';
COMMENT ON COLUMN ai.suggestions.tool_parameters IS 'Parameters for tool execution as JSON';
COMMENT ON COLUMN ai.suggestions.execution_result IS 'Result after tool execution as JSON';
COMMENT ON COLUMN ai.suggestions.metadata IS 'Additional metadata including safety info'; 