-- Migration: Simplified tool execution design with clear terminology
-- This implements the correct design where:
-- 1. retry_config is tool-dependent, not suggestion-dependent
-- 2. suggestions only hold status, not results
-- 3. tool_executions have clear attempt tracking
-- 4. support partial execution status

-- 1. Create tool configurations table for tool-specific retry configs
CREATE TABLE ai.tool_configurations (
    tool_name VARCHAR(255) PRIMARY KEY,
    retry_config JSONB NOT NULL,
    timeout_ms BIGINT DEFAULT 30000,
    max_attempts INTEGER DEFAULT 3,
    backoff_multiplier DECIMAL(3,2) DEFAULT 2.0,
    retry_conditions JSONB DEFAULT '[]',
    skip_retry_conditions JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Remove result fields from suggestions (results belong in tool_executions)
ALTER TABLE ai.suggestions DROP COLUMN IF EXISTS execution_result;
ALTER TABLE ai.suggestions DROP COLUMN IF EXISTS tool_executions;
ALTER TABLE ai.suggestions DROP COLUMN IF EXISTS retry_config;

-- 3. Add suggestion_id to tool_executions for direct linkage
ALTER TABLE ai.tool_executions 
ADD COLUMN suggestion_id bigint;

-- 4. Enhance tool_executions with clear attempt tracking
ALTER TABLE ai.tool_executions 
ADD COLUMN attempt_number INTEGER DEFAULT 1,           -- Which attempt this is (1st, 2nd, 3rd...)
ADD COLUMN max_attempts INTEGER DEFAULT 3,             -- Max attempts allowed for this tool
ADD COLUMN execution_status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, EXECUTING, SUCCESS, FAILED
ADD COLUMN next_attempt_at TIMESTAMP,                  -- When to try next attempt (if failed)
ADD COLUMN failure_reason TEXT,                        -- Why this attempt failed
ADD COLUMN tool_config_hash VARCHAR(64),               -- Hash of tool configuration used
ADD COLUMN depends_on_tool_execution_id BIGINT,        -- Dependency on another tool execution
ADD COLUMN execution_order INTEGER DEFAULT 0,          -- Order within suggestion
ADD COLUMN rollback_required BOOLEAN DEFAULT FALSE,    -- Whether rollback is needed
ADD COLUMN rollback_executed BOOLEAN DEFAULT FALSE,    -- Whether rollback was performed
ADD COLUMN rollback_result JSONB,                      -- Result of rollback operation
ADD COLUMN scheduled_at TIMESTAMP,                     -- When to execute (for delayed execution)
ADD COLUMN execution_priority INTEGER DEFAULT 5,       -- Priority (1=highest, 10=lowest)
ADD COLUMN execution_context JSONB,                    -- Context at execution time
ADD COLUMN state_snapshot JSONB;                       -- System state at execution time

-- 5. Add foreign key constraint for tool execution dependencies
ALTER TABLE ai.tool_executions 
ADD CONSTRAINT fk_tool_executions_dependency 
FOREIGN KEY (depends_on_tool_execution_id) REFERENCES ai.tool_executions(id);

-- 6. Add indexes for performance
CREATE INDEX idx_tool_executions_suggestion_id ON ai.tool_executions(suggestion_id);
CREATE INDEX idx_tool_executions_status ON ai.tool_executions(execution_status);
CREATE INDEX idx_tool_executions_attempt ON ai.tool_executions(attempt_number);
CREATE INDEX idx_tool_executions_next_attempt ON ai.tool_executions(next_attempt_at);
CREATE INDEX idx_tool_executions_scheduled ON ai.tool_executions(scheduled_at);
CREATE INDEX idx_tool_executions_priority ON ai.tool_executions(execution_priority);

-- 7. Create tool execution metrics table
CREATE TABLE ai.tool_execution_metrics (
    tool_name VARCHAR(255) PRIMARY KEY,
    total_executions BIGINT DEFAULT 0,
    successful_executions BIGINT DEFAULT 0,
    failed_executions BIGINT DEFAULT 0,
    avg_execution_time_ms BIGINT DEFAULT 0,
    avg_attempts_per_execution DECIMAL(3,2) DEFAULT 1.0,
    last_execution_timestamp TIMESTAMP,
    last_success_timestamp TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. Insert default tool configurations
INSERT INTO ai.tool_configurations (tool_name, retry_config) VALUES
('slack_notify', '{"max_attempts": 5, "retry_delay_ms": 2000, "backoff_multiplier": 2.0, "retry_conditions": ["RATE_LIMIT", "NETWORK_ERROR"], "skip_retry_conditions": ["AUTHENTICATION_ERROR"]}'),
('email_notify', '{"max_attempts": 3, "retry_delay_ms": 1000, "backoff_multiplier": 1.5, "retry_conditions": ["SMTP_ERROR", "NETWORK_ERROR"], "skip_retry_conditions": ["AUTHENTICATION_ERROR", "INVALID_EMAIL"]}'),
('math_calculation_tool', '{"max_attempts": 1, "retry_delay_ms": 0, "backoff_multiplier": 1.0, "retry_conditions": [], "skip_retry_conditions": ["INVALID_PARAMETERS"]}'),
('add_ticket_participant', '{"max_attempts": 3, "retry_delay_ms": 500, "backoff_multiplier": 1.5, "retry_conditions": ["DATABASE_LOCK", "NETWORK_ERROR"], "skip_retry_conditions": ["INVALID_USER", "PERMISSION_DENIED"]}');

-- 9. Add comments explaining the design
COMMENT ON TABLE ai.tool_configurations IS 'Tool-specific retry configurations and execution parameters';
COMMENT ON TABLE ai.tool_executions IS 'Individual tool execution attempts with status and dependency tracking';
COMMENT ON TABLE ai.tool_execution_metrics IS 'Performance and reliability metrics for tool executions';
COMMENT ON COLUMN ai.suggestions.status IS 'Suggestion status: PENDING, APPROVED, EXECUTING, EXECUTED, FAILED, PARTIAL_SUCCESS';
COMMENT ON COLUMN ai.tool_executions.execution_status IS 'Tool execution status: PENDING, EXECUTING, SUCCESS, FAILED';
COMMENT ON COLUMN ai.tool_executions.attempt_number IS 'Which attempt this is (1st, 2nd, 3rd attempt for the same tool)';
COMMENT ON COLUMN ai.tool_executions.max_attempts IS 'Maximum attempts allowed for this tool execution';
COMMENT ON COLUMN ai.tool_executions.next_attempt_at IS 'When to try the next attempt (if current attempt failed)';
COMMENT ON COLUMN ai.tool_executions.depends_on_tool_execution_id IS 'Dependency on another tool execution within the same suggestion';
COMMENT ON COLUMN ai.tool_executions.execution_order IS 'Order of execution within the suggestion (0 = no order)'; 