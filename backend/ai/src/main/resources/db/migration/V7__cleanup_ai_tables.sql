-- Migration: V7__cleanup_ai_tables.sql
-- Description: Clean up unused AI tables, keeping only relevant ones for hybrid approach
-- Date: 2024-07-19

-- Drop unused tables that are not needed for our AI implementation
-- These tables were part of a legacy/complex architecture that we're simplifying

-- Drop analysis_decisions table (unused)
DROP TABLE IF EXISTS ai.analysis_decisions CASCADE;

-- Drop analysis_sessions table (unused)
DROP TABLE IF EXISTS ai.analysis_sessions CASCADE;

-- Drop decision_trees table (unused)
DROP TABLE IF EXISTS ai.decision_trees CASCADE;

-- Drop performance_metrics table (unused)
DROP TABLE IF EXISTS ai.performance_metrics CASCADE;

-- Drop recommended_actions table (unused)
DROP TABLE IF EXISTS ai.recommended_actions CASCADE;

-- Drop workflow_steps table (unused)
DROP TABLE IF EXISTS ai.workflow_steps CASCADE;

-- Keep the following tables for our hybrid approach:
-- ai.configurations - AI configuration settings
-- ai.suggestions - AI-generated suggestions (with <PERSON><PERSON><PERSON><PERSON> for flexibility)
-- ai.thinking_logs - Chain of Thought / Tree of Thought logs
-- ai.vector_store - Vector embeddings storage
-- ai.tool_executions - AI tool execution tracking
-- ai.ai_flyway_schema_history - Migration history

-- Migration completed successfully
-- Removed 6 unused tables, kept 6 relevant tables for hybrid approach 