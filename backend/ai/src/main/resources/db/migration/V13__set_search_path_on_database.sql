-- Set search path to include the ai schema:
-- Without which the pg vector operators are not recognized
-- Resulting in bad SQL grammar [SELECT *, embedding <=> ? AS distance FROM ai.vector_store WHERE embedding <=> ? < ?  ORDER BY distance LIMIT ? ]
-- Caused by: org.postgresql.util.PSQLException: ERROR: operator does not exist: ai.vector <=> ai.vector
--     Hint: No operator matches the given name and argument types. You might need to add explicit type casts.
-- keep the db name same for the test containers or other places as well.
-- TODO : Use palce holders
-- Another option: ******************************************************,public
-- ALTER ROLE my_app1_user SET search_path = app1,public;
-- ALTER ROLE my_app1_user IN DATABASE tst SET search_path = app1,public;

DO $$
BEGIN
ALTER DATABASE sx_cdc_db SET search_path TO ai, public;
END $$;