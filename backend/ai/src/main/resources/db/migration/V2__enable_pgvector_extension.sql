-- Migration: Enable pgvector extension for AI vector operations
-- This migration ensures pgvector is available for Spring AI PgVectorStore

-- Check if pgvector extension is available
DO $$
BEGIN
    -- Try to create the vector extension
    CREATE EXTENSION IF NOT EXISTS vector;
    
    -- Log successful extension creation
    RAISE NOTICE 'pgvector extension enabled successfully';
    
EXCEPTION WHEN OTHERS THEN
    -- Log error and provide guidance
    RAISE EXCEPTION 'Failed to enable pgvector extension. Error: %. Please ensure pgvector is installed on your PostgreSQL server.', SQLERRM;
END $$;

-- Verify the extension is working by checking version
DO $$
DECLARE
    ext_version text;
BEGIN
    SELECT extversion INTO ext_version 
    FROM pg_extension 
    WHERE extname = 'vector';
    
    IF ext_version IS NULL THEN
        RAISE EXCEPTION 'pgvector extension is not properly installed';
    END IF;
    
    RAISE NOTICE 'pgvector version: %', ext_version;
END $$;

-- Add comments for documentation
COMMENT ON EXTENSION vector IS 'pgvector extension for vector similarity search operations'; 