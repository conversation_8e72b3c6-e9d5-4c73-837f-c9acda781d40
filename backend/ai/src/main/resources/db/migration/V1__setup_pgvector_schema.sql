-- Enable required PostgreSQL extensions for pgvector
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS hstore;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create vector_store table in ai schema
CREATE TABLE IF NOT EXISTS ai.vector_store (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    content text,
    metadata json,
    embedding vector(768)  -- OpenAI embedding dimensions 1536
);

-- Note: HNSW index creation is handled by Spring AI PgVectorStore
-- The index will be created automatically when the application starts
-- This avoids version compatibility issues with different pgvector versions

-- Add comments for documentation
COMMENT ON TABLE ai.vector_store IS 'Vector store for AI embeddings and similarity search';
COMMENT ON COLUMN ai.vector_store.embedding IS 'OpenAI embedding vector (1536 dimensions)';
COMMENT ON COLUMN ai.vector_store.metadata IS 'JSON metadata for filtering and context'; 