package io.sx.ai.springai;

import io.sx.ai.abstraction.SxDocument;
import io.sx.ai.springai.spring.SxSpringDocument;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.Embedding;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingResponse;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class SxSpringSimpleVectorStoreTest {

    private SxSpringSimpleVectorStore vectorStore;
    private EmbeddingModel embeddingModel;

    @BeforeEach
    void setUp() {
        embeddingModel = mock(EmbeddingModel.class);
        // Mock embedding model to return simple embeddings
        when(embeddingModel.embed(anyString())).thenReturn(
            new float[]{0.1f, 0.2f, 0.3f});
        
        // Mock embedForResponse method for getEmbeddingDimensions
        EmbeddingResponse mockResponse = mock(EmbeddingResponse.class);
        when(mockResponse.getResults()).thenReturn(List.of());
        when(embeddingModel.embedForResponse(any())).thenReturn(mockResponse);
        
        vectorStore = new SxSpringSimpleVectorStore(embeddingModel);
    }

    @Test
    void testAddAndRetrieveDocument() {
        // Test basic functionality without relying on actual vector store
        // This test verifies the interface works correctly
        
        // Test metadata
        Map<String, Object> metadata = vectorStore.getMetadata();
        assertNotNull(metadata);
        assertEquals("SimpleVectorStore", metadata.get("type"));
        
        // Test unimplemented methods return expected values
        assertFalse(vectorStore.deleteDocument("test"));
        assertNull(vectorStore.getDocument("test"));
        assertEquals(0, vectorStore.getDocumentCount());
        
        // Test that clear doesn't throw exception
        assertDoesNotThrow(() -> vectorStore.clear());
    }

    @Test
    void testSimilaritySearchWithThreshold() {
        // Test that similarity search with embeddings returns empty list
        // (as implemented for SimpleVectorStore)
        List<SxDocument> results = vectorStore.similaritySearch(List.of(0.1, 0.2, 0.3), 2);
        assertTrue(results.isEmpty());
        
        // Test that updateDocument returns false
        SxDocument doc = new SxSpringDocument(new Document("test", "content", new HashMap<>()));
        assertFalse(vectorStore.updateDocument(doc));
    }
}