# Ticket Event Processing & AI Suggestion Generation Flow

## Overview

This document illustrates the complete flow of ticket events through the AI system, from ticket creation to AI suggestion generation and storage.

## High-Level Architecture Flow

```mermaid
graph TB
    %% External Systems
    subgraph "External Systems"
        UI[User Interface]
        API[Main Application API]
        NATS[NATS Message Broker]
    end
    
    %% AI Module Components
    subgraph "AI Module (Port 8082)"
        subgraph "Event Processing"
            NATS_CONSUMER[NATS Consumer]
            TICKET_ORCH[TicketOrchestrator]
        end
        
        subgraph "AI Decision Framework"
            DECISION_ENGINE[AIDecisionEngine]
            DECISION_RULES[AIDecisionRules]
            STRATEGY_FACTORY[AIDecisionStrategyFactory]
        end
        
        subgraph "AI Strategies"
            APP_ONLY[App-Only Strategy]
            HYBRID[Hybrid Strategy]
            LLM_ONLY[LLM-Only Strategy]
        end
        
        subgraph "AI Processing"
            VECTOR_SEARCH[Vector Search]
            LLM_PROCESSING[LLM Processing]
            RULES_ENGINE[Rules Engine]
        end
        
        subgraph "Data Storage"
            AI_CONFIG[AI Configuration DB]
            SUGGESTIONS[AI Suggestions DB]
            REASONING[Reasoning Logs DB]
        end
    end
    
    %% Flow Connections
    UI --> API
    API --> NATS
    NATS --> NATS_CONSUMER
    NATS_CONSUMER --> TICKET_ORCH
    
    TICKET_ORCH --> DECISION_ENGINE
    DECISION_ENGINE --> DECISION_RULES
    DECISION_ENGINE --> STRATEGY_FACTORY
    
    STRATEGY_FACTORY --> APP_ONLY
    STRATEGY_FACTORY --> HYBRID
    STRATEGY_FACTORY --> LLM_ONLY
    
    APP_ONLY --> VECTOR_SEARCH
    APP_ONLY --> RULES_ENGINE
    
    HYBRID --> VECTOR_SEARCH
    HYBRID --> LLM_PROCESSING
    HYBRID --> RULES_ENGINE
    
    LLM_ONLY --> LLM_PROCESSING
    
    VECTOR_SEARCH --> SUGGESTIONS
    LLM_PROCESSING --> SUGGESTIONS
    RULES_ENGINE --> SUGGESTIONS
    
    DECISION_ENGINE --> REASONING
    TICKET_ORCH --> REASONING
    
    AI_CONFIG --> DECISION_ENGINE
    AI_CONFIG --> STRATEGY_FACTORY
    
    %% Styling
    classDef external fill:#e1f5fe
    classDef core fill:#f3e5f5
    classDef decision fill:#e8f5e8
    classDef strategy fill:#fff3e0
    classDef processing fill:#fce4ec
    classDef storage fill:#f1f8e9
    
    class UI,API,NATS external
    class NATS_CONSUMER,TICKET_ORCH core
    class DECISION_ENGINE,DECISION_RULES,STRATEGY_FACTORY decision
    class APP_ONLY,HYBRID,LLM_ONLY strategy
    class VECTOR_SEARCH,LLM_PROCESSING,RULES_ENGINE processing
    class AI_CONFIG,SUGGESTIONS,REASONING storage
```

## Detailed Ticket Creation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant API as Main API
    participant NATS as NATS Broker
    participant CONSUMER as NATS Consumer
    participant ORCH as TicketOrchestrator
    participant DECISION as AIDecisionEngine
    participant RULES as AIDecisionRules
    participant FACTORY as StrategyFactory
    participant STRATEGY as AI Strategy
    participant PROCESSING as AI Processing
    participant DB as AI Database

    U->>API: Create Ticket
    API->>API: Validate & Store
    API->>NATS: Publish ticket.created event
    NATS->>CONSUMER: Receive event
    
    CONSUMER->>ORCH: processTicketCreation()
    ORCH->>ORCH: Generate Session ID
    
    ORCH->>DECISION: evaluateAIDecision()
    DECISION->>RULES: Find applicable rules
    RULES->>DECISION: Return decision result
    
    alt AI Recommended
        DECISION->>ORCH: Return AI decision
        ORCH->>FACTORY: getStrategy(strategy)
        FACTORY->>ORCH: Return strategy instance
        
        ORCH->>STRATEGY: executeTicketProcessing()
        
        alt App-Only Strategy
            STRATEGY->>PROCESSING: Vector search + Rules
        else Hybrid Strategy
            STRATEGY->>PROCESSING: Vector search + LLM
        else LLM-Only Strategy
            STRATEGY->>PROCESSING: LLM processing only
        end
        
        PROCESSING->>DB: Store suggestions
        STRATEGY->>ORCH: Return processing result
        
    else No AI Recommended
        DECISION->>ORCH: Return no-AI decision
        ORCH->>ORCH: Create no-AI result
    end
    
    ORCH->>DB: Log reasoning
    ORCH->>CONSUMER: Return result
    CONSUMER->>NATS: Acknowledge message
```

## AI Decision Framework Flow

```mermaid
graph TD
    subgraph "Ticket Event"
        EVENT[ticket.created]
        TICKET_DATA[Ticket Data]
    end
    
    subgraph "Decision Context"
        CONTEXT[AIDecisionContext]
        TRIGGER[triggerEvent: ticket.created]
        ENTITY[entityId: ticketId]
        ORG[organizationId]
        DATA[entityData: ticketData]
    end
    
    subgraph "Decision Rules"
        RULE1[TicketClassificationDecisionRule]
        RULE2[PriorityBasedDecisionRule]
        RULE3[CustomerImportanceRule]
    end
    
    subgraph "Rule Evaluation"
        EVAL1[Evaluate Priority]
        EVAL2[Evaluate Customer Importance]
        EVAL3[Evaluate Content Length]
        EVAL4[Check Organization Config]
    end
    
    subgraph "Decision Result"
        RESULT[AIDecisionResult]
        APPLY_AI[shouldApplyAI: true/false]
        STRATEGY[aiStrategy: app_only/hybrid/llm_only]
        CONFIDENCE[confidenceThreshold]
        REASONING[reasoning]
    end
    
    EVENT --> CONTEXT
    TICKET_DATA --> CONTEXT
    
    CONTEXT --> RULE1
    CONTEXT --> RULE2
    CONTEXT --> RULE3
    
    RULE1 --> EVAL1
    RULE2 --> EVAL2
    RULE3 --> EVAL3
    
    EVAL1 --> EVAL4
    EVAL2 --> EVAL4
    EVAL3 --> EVAL4
    
    EVAL4 --> RESULT
    
    RESULT --> APPLY_AI
    RESULT --> STRATEGY
    RESULT --> CONFIDENCE
    RESULT --> REASONING
```

## AI Strategy Execution Flow

```mermaid
graph TD
    subgraph "Strategy Selection"
        DECISION[AIDecisionResult]
        FACTORY[AIDecisionStrategyFactory]
        STRATEGY[Selected Strategy]
    end
    
    subgraph "App-Only Strategy"
        APP_VECTOR[Vector Search]
        APP_RULES[Rules Engine]
        APP_SUGGESTIONS[Generate Suggestions]
    end
    
    subgraph "Hybrid Strategy"
        HYBRID_VECTOR[Vector Search]
        HYBRID_LLM[LLM Processing]
        HYBRID_COMBINE[Combine Results]
        HYBRID_SUGGESTIONS[Generate Suggestions]
    end
    
    subgraph "LLM-Only Strategy"
        LLM_PROCESS[LLM Processing]
        LLM_ANALYSIS[Content Analysis]
        LLM_SUGGESTIONS[Generate Suggestions]
    end
    
    subgraph "Result Processing"
        SUGGESTIONS[AI Suggestions]
        REASONING[Reasoning Logs]
        METADATA[Processing Metadata]
    end
    
    DECISION --> FACTORY
    FACTORY --> STRATEGY
    
    STRATEGY --> APP_VECTOR
    STRATEGY --> HYBRID_VECTOR
    STRATEGY --> LLM_PROCESS
    
    APP_VECTOR --> APP_RULES
    APP_RULES --> APP_SUGGESTIONS
    
    HYBRID_VECTOR --> HYBRID_LLM
    HYBRID_LLM --> HYBRID_COMBINE
    HYBRID_COMBINE --> HYBRID_SUGGESTIONS
    
    LLM_PROCESS --> LLM_ANALYSIS
    LLM_ANALYSIS --> LLM_SUGGESTIONS
    
    APP_SUGGESTIONS --> SUGGESTIONS
    HYBRID_SUGGESTIONS --> SUGGESTIONS
    LLM_SUGGESTIONS --> SUGGESTIONS
    
    SUGGESTIONS --> REASONING
    SUGGESTIONS --> METADATA
```

## Data Flow Architecture

```mermaid
graph LR
    subgraph "Input Data"
        TICKET[Ticket Data]
        CONFIG[AI Configuration]
        HISTORY[Historical Data]
    end
    
    subgraph "Processing Pipeline"
        PREPROCESS[Data Preprocessing]
        DECISION[Decision Engine]
        STRATEGY[Strategy Execution]
        POSTPROCESS[Result Processing]
    end
    
    subgraph "Output Data"
        SUGGESTIONS[AI Suggestions]
        REASONING[Reasoning Logs]
        METRICS[Processing Metrics]
    end
    
    TICKET --> PREPROCESS
    CONFIG --> PREPROCESS
    HISTORY --> PREPROCESS
    
    PREPROCESS --> DECISION
    DECISION --> STRATEGY
    STRATEGY --> POSTPROCESS
    
    POSTPROCESS --> SUGGESTIONS
    POSTPROCESS --> REASONING
    POSTPROCESS --> METRICS
```

## Component Responsibilities

### 1. **NATS Consumer**
- **Role**: Event listener and entry point
- **Responsibilities**:
  - Listen for ticket events from NATS
  - Parse event data
  - Route to appropriate orchestrator
  - Handle message acknowledgment

### 2. **TicketOrchestrator**
- **Role**: Main coordination point
- **Responsibilities**:
  - Generate unique session IDs
  - Coordinate AI decision evaluation
  - Execute selected AI strategies
  - Handle error scenarios
  - Return structured results

### 3. **AIDecisionEngine**
- **Role**: Decision-making core
- **Responsibilities**:
  - Build decision context
  - Find applicable rules
  - Evaluate rules in priority order
  - Return AI decision with strategy

### 4. **AIDecisionRules**
- **Role**: Business logic implementation
- **Responsibilities**:
  - Evaluate ticket characteristics
  - Apply organization-specific rules
  - Determine AI applicability
  - Provide reasoning for decisions

### 5. **AIDecisionStrategyFactory**
- **Role**: Strategy management
- **Responsibilities**:
  - Manage available strategies
  - Return appropriate strategy instances
  - Handle strategy configuration

### 6. **AI Strategies**
- **Role**: Execution engines
- **Responsibilities**:
  - **App-Only**: Vector search + rules engine
  - **Hybrid**: Vector search + LLM processing
  - **LLM-Only**: Pure LLM-based processing

## Key Data Structures

### Ticket Data
```json
{
  "id": 123,
  "organizationId": 1,
  "description": "Critical system failure...",
  "priority": "HIGH",
  "customerImportance": 9.5,
  "title": "System Down - All Users Affected"
}
```

### AI Decision Result
```json
{
  "shouldApplyAI": true,
  "aiStrategy": "hybrid",
  "aiMethods": ["vector", "llm"],
  "confidenceThreshold": 0.7,
  "reasoning": "High priority ticket with critical content",
  "ruleId": "ticket_classification_rule"
}
```

### Processing Result
```json
{
  "sessionId": "ticket-123-abc12345",
  "ticketId": 123,
  "success": true,
  "strategy": "hybrid",
  "reasoning": "AI processing completed successfully",
  "suggestions": [
    {
      "type": "classification",
      "value": "technical_issue",
      "confidence": 0.85
    }
  ]
}
```

## Error Handling

### 1. **Decision Engine Errors**
- Fallback to conservative "no AI" decision
- Log error with context
- Continue processing

### 2. **Strategy Execution Errors**
- Return error result with details
- Log failure for analysis
- Preserve session for debugging

### 3. **Database Errors**
- Retry with exponential backoff
- Fallback to in-memory storage
- Alert monitoring systems

## Performance Considerations

### 1. **Parallel Processing**
- Vector search and LLM processing can run in parallel
- Multiple rules can be evaluated concurrently
- Database operations use connection pooling

### 2. **Caching**
- AI configuration cached in memory
- Vector embeddings cached for similar tickets
- Decision results cached for identical contexts

### 3. **Async Processing**
- Non-blocking event processing
- Background suggestion generation
- Real-time result streaming

## Monitoring & Observability

### 1. **Metrics**
- Event processing latency
- Decision accuracy rates
- Strategy execution times
- Error rates by component

### 2. **Logging**
- Structured JSON logging
- Session correlation IDs
- Decision reasoning trails
- Performance timestamps

### 3. **Health Checks**
- Component health endpoints
- Database connectivity
- NATS connection status
- Strategy availability 