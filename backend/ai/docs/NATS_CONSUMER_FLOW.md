# NATS Consumer to AI Processing Flow

## 🎯 **Complete Flow Starting from NATS Consumer**

```mermaid
graph TD
    subgraph "1. NATS Event Reception"
        NATS[📡 NATS Message Broker]
        EVENT[ticket.created event]
        PAYLOAD[Event Payload: ticketId, orgId, data]
    end
    
    subgraph "2. NATS Consumer Processing"
        CONSUMER[👂 NATS Consumer]
        PARSER[📝 Event Parser]
        ROUTER[🔄 Event Router]
        ACK[✅ Message Acknowledgment]
    end
    
    subgraph "3. Ticket Orchestrator"
        ORCH[🎯 TicketOrchestrator]
        SESSION[🆔 Generate Session ID]
        CONTEXT[📋 Build Processing Context]
        COORD[🎛️ Coordinate AI Flow]
    end
    
    subgraph "4. AI Decision Framework"
        DECISION_ENGINE[🧠 AIDecisionEngine]
        CONTEXT_BUILDER[🔨 Build Decision Context]
        RULE_FINDER[🔍 Find Applicable Rules]
        RULE_EVAL[⚖️ Evaluate Rules]
        DECISION_RESULT[📊 AI Decision Result]
    end
    
    subgraph "5. Strategy Selection & Execution"
        STRATEGY_FACTORY[🏭 AIDecisionStrategyFactory]
        STRATEGY_SELECT[🎯 Select Strategy]
        STRATEGY_EXEC[⚙️ Execute Strategy]
        PROCESSING_RESULT[💡 Processing Result]
    end
    
    subgraph "6. AI Processing Strategies"
        subgraph "App-Only Strategy"
            APP_VECTOR[🔍 Vector Search]
            APP_RULES[📋 Rules Engine]
            APP_COMBINE[🔗 Combine Results]
        end
        
        subgraph "Hybrid Strategy"
            HYBRID_VECTOR[🔍 Vector Search]
            HYBRID_LLM[🤖 LLM Processing]
            HYBRID_COMBINE[🔗 Combine Results]
        end
        
        subgraph "LLM-Only Strategy"
            LLM_PROCESS[🤖 LLM Processing]
            LLM_ANALYSIS[📊 Content Analysis]
        end
    end
    
    subgraph "7. Result Processing & Storage"
        RESULT_BUILDER[🏗️ Build Final Result]
        SUGGESTIONS[💡 AI Suggestions]
        REASONING[📝 Reasoning Logs]
        METADATA[📊 Processing Metadata]
        DB_STORE[💾 Database Storage]
    end
    
    subgraph "8. Response & Monitoring"
        RESPONSE[📤 Return Response]
        LOGGING[📋 Structured Logging]
        METRICS[📈 Performance Metrics]
        HEALTH[❤️ Health Check]
    end
    
    %% Flow Connections
    NATS --> EVENT
    EVENT --> CONSUMER
    CONSUMER --> PARSER
    PARSER --> ROUTER
    ROUTER --> ORCH
    
    ORCH --> SESSION
    SESSION --> CONTEXT
    CONTEXT --> COORD
    
    COORD --> DECISION_ENGINE
    DECISION_ENGINE --> CONTEXT_BUILDER
    CONTEXT_BUILDER --> RULE_FINDER
    RULE_FINDER --> RULE_EVAL
    RULE_EVAL --> DECISION_RESULT
    
    DECISION_RESULT --> STRATEGY_FACTORY
    STRATEGY_FACTORY --> STRATEGY_SELECT
    STRATEGY_SELECT --> STRATEGY_EXEC
    
    STRATEGY_EXEC --> APP_VECTOR
    STRATEGY_EXEC --> HYBRID_VECTOR
    STRATEGY_EXEC --> LLM_PROCESS
    
    APP_VECTOR --> APP_RULES
    APP_RULES --> APP_COMBINE
    
    HYBRID_VECTOR --> HYBRID_LLM
    HYBRID_LLM --> HYBRID_COMBINE
    
    LLM_PROCESS --> LLM_ANALYSIS
    
    APP_COMBINE --> PROCESSING_RESULT
    HYBRID_COMBINE --> PROCESSING_RESULT
    LLM_ANALYSIS --> PROCESSING_RESULT
    
    PROCESSING_RESULT --> RESULT_BUILDER
    RESULT_BUILDER --> SUGGESTIONS
    RESULT_BUILDER --> REASONING
    RESULT_BUILDER --> METADATA
    
    SUGGESTIONS --> DB_STORE
    REASONING --> DB_STORE
    METADATA --> DB_STORE
    
    DB_STORE --> RESPONSE
    RESPONSE --> ACK
    
    RESPONSE --> LOGGING
    LOGGING --> METRICS
    METRICS --> HEALTH
    
    %% Styling
    classDef nats fill:#e1f5fe
    classDef consumer fill:#f3e5f5
    classDef orchestrator fill:#e8f5e8
    classDef decision fill:#fff3e0
    classDef strategy fill:#fce4ec
    classDef processing fill:#f1f8e9
    classDef storage fill:#fafafa
    classDef monitoring fill:#e0f2f1
    
    class NATS,EVENT,PAYLOAD nats
    class CONSUMER,PARSER,ROUTER,ACK consumer
    class ORCH,SESSION,CONTEXT,COORD orchestrator
    class DECISION_ENGINE,CONTEXT_BUILDER,RULE_FINDER,RULE_EVAL,DECISION_RESULT decision
    class STRATEGY_FACTORY,STRATEGY_SELECT,STRATEGY_EXEC,PROCESSING_RESULT strategy
    class APP_VECTOR,APP_RULES,APP_COMBINE,HYBRID_VECTOR,HYBRID_LLM,HYBRID_COMBINE,LLM_PROCESS,LLM_ANALYSIS processing
    class RESULT_BUILDER,SUGGESTIONS,REASONING,METADATA,DB_STORE storage
    class RESPONSE,LOGGING,METRICS,HEALTH monitoring
```

## 🔄 **Detailed Sequence Flow**

```mermaid
sequenceDiagram
    participant NATS as NATS Broker
    participant CONSUMER as NATS Consumer
    participant ORCH as TicketOrchestrator
    participant DECISION as AIDecisionEngine
    participant RULES as AIDecisionRules
    participant FACTORY as StrategyFactory
    participant STRATEGY as AI Strategy
    participant PROCESSING as AI Processing
    participant DB as AI Database
    participant LOG as Logger

    NATS->>CONSUMER: ticket.created event
    Note over CONSUMER: Parse event payload
    
    CONSUMER->>ORCH: processTicketCreation(ticketData)
    Note over ORCH: Generate session ID: ticket-{id}-{uuid}
    
    ORCH->>DECISION: evaluateAIDecision(triggerEvent, eventData, orgId)
    Note over DECISION: Build AIDecisionContext
    
    DECISION->>RULES: findApplicableRules(context)
    RULES->>DECISION: Return matching rules
    
    loop For each rule
        DECISION->>RULES: evaluateRule(rule, context)
        RULES->>DECISION: Return rule result
    end
    
    DECISION->>ORCH: Return AIDecisionResult
    
    alt AI Recommended
        ORCH->>FACTORY: getStrategy(aiStrategy)
        FACTORY->>ORCH: Return strategy instance
        
        ORCH->>STRATEGY: executeTicketProcessing(context, sessionId)
        
        alt App-Only Strategy
            STRATEGY->>PROCESSING: Vector search + Rules engine
        else Hybrid Strategy
            STRATEGY->>PROCESSING: Vector search + LLM processing
        else LLM-Only Strategy
            STRATEGY->>PROCESSING: LLM processing only
        end
        
        PROCESSING->>DB: Store suggestions
        STRATEGY->>ORCH: Return TicketProcessingResult
        
    else No AI Recommended
        ORCH->>ORCH: Create no-AI result
    end
    
    ORCH->>DB: Log reasoning and metadata
    ORCH->>LOG: Log processing completion
    
    ORCH->>CONSUMER: Return processing result
    CONSUMER->>NATS: Acknowledge message
```

## 📊 **NATS Consumer Implementation Details**

### **Event Message Structure**
```json
{
  "eventType": "ticket.created",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "ticketId": 123,
    "organizationId": 1,
    "title": "System Down - All Users Affected",
    "description": "Critical system failure affecting all users...",
    "priority": "HIGH",
    "customerImportance": 9.5,
    "createdBy": "<EMAIL>"
  },
  "metadata": {
    "source": "main-api",
    "version": "1.0"
  }
}
```

### **Consumer Processing Steps**

#### **1. Event Reception**
```java
@NatsListener(subject = "ticket.*")
public void handleTicketEvent(Message message) {
    String eventType = message.getSubject();
    String payload = new String(message.getData());
    
    // Parse event
    TicketEvent event = parseEvent(payload);
    
    // Route to appropriate handler
    routeEvent(event);
}
```

#### **2. Event Routing**
```java
private void routeEvent(TicketEvent event) {
    switch (event.getEventType()) {
        case "ticket.created":
            ticketOrchestrator.processTicketCreation(event.getData());
            break;
        case "ticket.updated":
            ticketOrchestrator.processTicketUpdate(event.getData());
            break;
        default:
            log.warn("Unknown event type: {}", event.getEventType());
    }
}
```

#### **3. Session Management**
```java
private String generateSessionId(Long ticketId) {
    return String.format("ticket-%d-%s", 
        ticketId, 
        UUID.randomUUID().toString().substring(0, 8));
}
```

## 🎯 **Key Processing Points**

### **1. Event Validation**
- ✅ Validate event schema
- ✅ Check required fields
- ✅ Verify organization access
- ✅ Handle malformed events

### **2. Error Handling**
- 🔄 Retry failed processing
- 📝 Log error details
- ⚠️ Dead letter queue for failed events
- 🚨 Alert on repeated failures

### **3. Performance Optimization**
- ⚡ Async processing
- 🔄 Connection pooling
- 📊 Batch processing for high volume
- 💾 Result caching

### **4. Monitoring & Observability**
- 📈 Processing latency metrics
- 🔍 Event processing success rates
- 📊 Queue depth monitoring
- ❤️ Consumer health checks

## 🔧 **Configuration**

### **NATS Consumer Configuration**
```properties
# NATS Connection
nats.url=nats://localhost:4222
nats.connection.timeout=5000
nats.reconnect.wait=1000

# Consumer Settings
nats.consumer.durable=ticket-ai-consumer
nats.consumer.queue=ticket-ai-group
nats.consumer.ack.wait=30s

# Processing Settings
ai.processing.timeout=30s
ai.processing.retries=3
ai.processing.batch.size=10
```

### **Health Check Endpoints**
```java
@RestController
public class ConsumerHealthController {
    
    @GetMapping("/health/consumer")
    public HealthStatus getConsumerHealth() {
        return HealthStatus.builder()
            .status("UP")
            .natsConnection(connectionStatus)
            .processingQueue(queueDepth)
            .lastProcessedEvent(lastEventTime)
            .build();
    }
}
```

## 📈 **Performance Metrics**

| **Metric** | **Description** | **Target** |
|------------|-----------------|------------|
| **Event Processing Latency** | Time from event receipt to completion | < 5 seconds |
| **Processing Success Rate** | Percentage of successful processing | > 99% |
| **Queue Depth** | Number of pending events | < 100 |
| **Error Rate** | Percentage of failed processing | < 1% |
| **Memory Usage** | Consumer memory consumption | < 512MB |

## 🚨 **Error Scenarios & Recovery**

### **1. NATS Connection Loss**
- **Detection**: Connection timeout
- **Recovery**: Automatic reconnection
- **Fallback**: Retry with exponential backoff

### **2. Processing Timeout**
- **Detection**: Processing timeout
- **Recovery**: Retry with circuit breaker
- **Fallback**: Dead letter queue

### **3. Database Connection Issues**
- **Detection**: Database timeout
- **Recovery**: Connection pool refresh
- **Fallback**: In-memory storage

### **4. Strategy Execution Failure**
- **Detection**: Strategy exception
- **Recovery**: Fallback to app-only strategy
- **Fallback**: No-AI processing

## 🎯 **Benefits of This Architecture**

1. **Scalability**: Horizontal scaling of consumers
2. **Reliability**: Event persistence and replay
3. **Observability**: Complete audit trail
4. **Flexibility**: Pluggable processing strategies
5. **Performance**: Async processing with batching
6. **Resilience**: Comprehensive error handling 