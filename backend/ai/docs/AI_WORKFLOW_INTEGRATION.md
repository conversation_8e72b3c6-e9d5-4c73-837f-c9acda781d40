# AI Workflow Integration Guide

## Overview

This document demonstrates how the enhanced AI tool architecture integrates with the AI workflow orchestrator to provide a complete, flexible, and efficient AI-driven workflow system.

## Architecture Integration

### Core Components

```
┌─────────────────────────────────────────────────────────────────┐
│                    AI Workflow Orchestrator                     │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Hybrid Mode    │  │  Full MCP Mode  │  │  Dynamic Flow   │  │
│  │  Orchestrator   │  │  Orchestrator   │  │  Orchestrator   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Tool Execution Layer                         │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ Parallel Tool   │  │ AI Tool         │  │ Autonomy Config │  │
│  │ Executor        │  │ Registry        │  │ Service         │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Tool Categories                              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ SYSTEM      │  │ BUSINESS    │  │ CRITICAL    │  │ READONLY│ │
│  │ TOOLS       │  │ TOOLS       │  │ TOOLS       │  │ TOOLS   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Integration Flow

### 1. Event Processing Flow

```mermaid
graph TD
    A[Event Received] --> B[AIHybridOrchestrator]
    B --> C{Event Type?}
    C -->|Complex| D[executeDynamicWorkflow]
    C -->|Simple| E[processSimpleEvent]
    
    D --> F[LLM Analysis]
    F --> G[Information Requests]
    G --> H[Parallel Tool Execution]
    H --> I[Context Gathering]
    I --> J[Next Steps]
    J --> K[Tool Execution]
    
    E --> L[Basic Processing]
    
    H --> M[ParallelToolExecutor]
    M --> N[Tool Registry]
    N --> O[Autonomy Check]
    O --> P[Execute Tools]
```

### 2. Tool Execution Integration

```java
// Example: How the orchestrator integrates with the tool system
@Service
public class AIHybridOrchestratorImpl implements AIHybridOrchestrator {
    
    private final ParallelToolExecutor parallelToolExecutor;
    private final AIToolRegistry toolRegistry;
    private final AutonomyConfigurationService autonomyConfig;
    
    @Override
    public AIAnalysisResponse executeDynamicWorkflow(String eventType, 
                                                   Map<String, Object> eventData,
                                                   Long organizationId, 
                                                   Long onBehalfOfId) {
        
        // 1. Get organization's autonomy configuration
        ToolCategory.AutonomyConfiguration config = 
            autonomyConfig.getConfiguration(organizationId);
        
        // 2. Build tool execution context
        AITool.ToolContext context = AITool.ToolContext.builder()
            .organizationId(organizationId)
            .onBehalfOfId(onBehalfOfId)
            .autonomyMode(config.getDefaultMode())
            .sessionId(sessionId)
            .build();
        
        // 3. Execute parallel information gathering
        List<ParallelToolExecutor.ToolCall> infoGatheringCalls = 
            buildInformationGatheringCalls(eventData);
        
        Map<String, AITool.ToolResult> infoResults = 
            parallelToolExecutor.executeTools(infoGatheringCalls, context);
        
        // 4. Use gathered information for LLM analysis
        AIAnalysisResponse analysis = performAnalysisWithContext(infoResults);
        
        // 5. Execute business actions based on autonomy level
        List<ParallelToolExecutor.ToolCall> actionCalls = 
            buildActionCalls(analysis, config);
        
        Map<String, AITool.ToolResult> actionResults = 
            parallelToolExecutor.executeTools(actionCalls, context);
        
        return buildFinalResponse(analysis, actionResults);
    }
}
```

## Parallel Execution Integration

### Information Gathering Example

```java
// Example: Parallel execution of information gathering tools
private List<ParallelToolExecutor.ToolCall> buildInformationGatheringCalls(
    Map<String, Object> eventData) {
    
    List<ParallelToolExecutor.ToolCall> calls = new ArrayList<>();
    
    // These can execute in parallel (SYSTEM_TOOLS)
    calls.add(new ParallelToolExecutor.ToolCall(
        "similar_tickets_tool",
        Map.of("ticket_id", eventData.get("ticket_id")),
        "Find similar tickets for context"
    ));
    
    calls.add(new ParallelToolExecutor.ToolCall(
        "customer_context_tool", 
        Map.of("customer_id", eventData.get("customer_id")),
        "Get customer satisfaction and history"
    ));
    
    calls.add(new ParallelToolExecutor.ToolCall(
        "team_availability_tool",
        Map.of("team_id", eventData.get("team_id")),
        "Check team availability and workload"
    ));
    
    return calls;
}
```

### Business Action Example

```java
// Example: Sequential execution of business impact tools
private List<ParallelToolExecutor.ToolCall> buildActionCalls(
    AIAnalysisResponse analysis, 
    ToolCategory.AutonomyConfiguration config) {
    
    List<ParallelToolExecutor.ToolCall> calls = new ArrayList<>();
    
    // These execute sequentially (BUSINESS_TOOLS)
    for (AIAnalysisResponse.ToolCall llmToolCall : analysis.getToolCalls()) {
        
        // Check if tool requires approval in current autonomy mode
        if (toolRegistry.requiresApproval(llmToolCall.getToolName(), 
                                        config.getDefaultMode())) {
            
            // Create approval request
            createApprovalRequest(llmToolCall, config);
            
        } else {
            // Execute directly
            calls.add(new ParallelToolExecutor.ToolCall(
                llmToolCall.getToolName(),
                llmToolCall.getParameters(),
                llmToolCall.getReasoning()
            ));
        }
    }
    
    return calls;
}
```

## User-Configurable Autonomy Integration

### Autonomy Configuration Flow

```java
// Example: How autonomy configuration affects tool execution
@Service
public class AutonomyConfigurationService {
    
    public ToolCategory.AutonomyConfiguration getConfiguration(Long organizationId) {
        // Get user-configured settings
        ToolCategory.AutonomyConfiguration config = configurations.get(organizationId);
        
        if (config == null) {
            // Return sensible defaults
            config = ToolCategory.AutonomyConfiguration.builder()
                .defaultMode(ToolCategory.AIAutonomyMode.HYBRID)
                .systemToolsApproval(ToolCategory.ApprovalRequirement.NEVER)
                .readonlyToolsApproval(ToolCategory.ApprovalRequirement.NEVER)
                .businessToolsApproval(ToolCategory.ApprovalRequirement.USER_DECISION)
                .criticalToolsApproval(ToolCategory.ApprovalRequirement.ALWAYS)
                .adminToolsApproval(ToolCategory.ApprovalRequirement.ALWAYS)
                .build();
        }
        
        return config;
    }
}
```

### Tool Approval Logic

```java
// Example: Tool approval checking in the registry
public class AIToolRegistry {
    
    public boolean requiresApproval(String toolId, ToolCategory.AIAutonomyMode mode) {
        Optional<AITool> tool = getToolById(toolId);
        
        if (tool.isEmpty()) {
            return true; // Default to requiring approval
        }
        
        ToolCategory category = tool.get().getToolCategory();
        
        switch (mode) {
            case FULL_MCP:
                return category.getApprovalRequirement() == ToolCategory.ApprovalRequirement.ALWAYS;
                
            case HYBRID:
                return category.getApprovalRequirement() == ToolCategory.ApprovalRequirement.ALWAYS ||
                       category.getApprovalRequirement() == ToolCategory.ApprovalRequirement.USER_DECISION;
                       
            case LOW_AUTONOMY:
                return category.getApprovalRequirement() != ToolCategory.ApprovalRequirement.NEVER;
                
            default:
                return true;
        }
    }
}
```

## Complete Workflow Example

### Multi-Step Ticket Assignment Workflow

```java
// Example: Complete workflow showing all integration points
public AIAnalysisResponse handleTicketAssignment(String eventType, 
                                               Map<String, Object> eventData,
                                               Long organizationId, 
                                               Long onBehalfOfId) {
    
    String sessionId = UUID.randomUUID().toString();
    
    // 1. Get autonomy configuration
    ToolCategory.AutonomyConfiguration config = 
        autonomyConfig.getConfiguration(organizationId);
    
    // 2. Build execution context
    AITool.ToolContext context = AITool.ToolContext.builder()
        .organizationId(organizationId)
        .onBehalfOfId(onBehalfOfId)
        .autonomyMode(config.getDefaultMode())
        .sessionId(sessionId)
        .build();
    
    // 3. Parallel information gathering (SYSTEM_TOOLS - no approval needed)
    List<ParallelToolExecutor.ToolCall> infoCalls = Arrays.asList(
        new ParallelToolExecutor.ToolCall("similar_tickets_tool", 
            Map.of("ticket_id", eventData.get("ticket_id")), 
            "Find similar tickets"),
        new ParallelToolExecutor.ToolCall("customer_context_tool",
            Map.of("customer_id", eventData.get("customer_id")),
            "Get customer context"),
        new ParallelToolExecutor.ToolCall("team_availability_tool",
            Map.of("team_id", eventData.get("team_id")),
            "Check team availability")
    );
    
    Map<String, AITool.ToolResult> infoResults = 
        parallelToolExecutor.executeTools(infoCalls, context);
    
    // 4. LLM analysis with gathered context
    AIAnalysisRequest request = AIAnalysisRequest.builder()
        .eventType(eventType)
        .eventData(eventData)
        .organizationId(organizationId)
        .onBehalfOfId(onBehalfOfId)
        .context(buildContextWithGatheredInfo(infoResults))
        .availableActions(getAvailableActions(eventType, organizationId))
        .analysisGoals(Arrays.asList(
            "Analyze ticket complexity",
            "Determine optimal assignment",
            "Identify escalation needs"
        ))
        .expectedOutputFormat("structured_analysis_with_tool_calls")
        .build();
    
    AIAnalysisResponse analysis = llmService.performStructuredAnalysis(request);
    
    // 5. Execute business actions based on autonomy level
    List<ParallelToolExecutor.ToolCall> actionCalls = new ArrayList<>();
    
    for (AIAnalysisResponse.ToolCall llmCall : analysis.getToolCalls()) {
        
        if (toolRegistry.requiresApproval(llmCall.getToolName(), config.getDefaultMode())) {
            // Create approval request for user decision
            createApprovalRequest(llmCall, sessionId, organizationId);
            analysis.getMetadata().put("pending_approvals", 
                analysis.getMetadata().getOrDefault("pending_approvals", 0) + 1);
        } else {
            // Execute directly
            actionCalls.add(new ParallelToolExecutor.ToolCall(
                llmCall.getToolName(),
                llmCall.getParameters(),
                llmCall.getReasoning()
            ));
        }
    }
    
    // 6. Execute approved actions
    if (!actionCalls.isEmpty()) {
        Map<String, AITool.ToolResult> actionResults = 
            parallelToolExecutor.executeTools(actionCalls, context);
        analysis.getMetadata().put("executed_actions", actionResults);
    }
    
    return analysis;
}
```

## Benefits of Integration

### 1. **Performance Optimization**
- **Parallel Information Gathering**: Similar tickets, customer context, and team availability gathered simultaneously
- **Sequential Business Actions**: Critical operations executed in proper order to maintain data consistency
- **Automatic Grouping**: Tools automatically grouped by execution compatibility

### 2. **Flexible Autonomy**
- **User-Configurable**: Support org admins can set approval requirements per tool category
- **Context-Aware**: Different autonomy levels for different operation types
- **Audit Trail**: All decisions and executions logged for transparency

### 3. **Scalable Architecture**
- **Tool Registry**: Centralized tool management with categorization
- **Parallel Execution**: Efficient resource utilization
- **Modular Design**: Easy to add new tools and categories

### 4. **Robust Error Handling**
- **Graceful Degradation**: Failed tools don't stop entire workflow
- **Detailed Logging**: Comprehensive audit trail for debugging
- **Approval Workflow**: Human oversight for critical operations

## Configuration Examples

### Organization Autonomy Settings

```json
{
  "organizationId": 123,
  "defaultMode": "HYBRID",
  "toolSettings": {
    "SYSTEM_TOOLS": {
      "approvalRequired": "NEVER",
      "canParallelize": true
    },
    "BUSINESS_TOOLS": {
      "approvalRequired": "USER_DECISION",
      "canParallelize": false
    },
    "CRITICAL_TOOLS": {
      "approvalRequired": "ALWAYS",
      "canParallelize": false
    },
    "READONLY_TOOLS": {
      "approvalRequired": "NEVER",
      "canParallelize": true
    }
  }
}
```

### Workflow Execution Flow

1. **Event Reception**: NATS event triggers workflow
2. **Autonomy Check**: Determine execution mode based on organization settings
3. **Parallel Information Gathering**: Execute system tools in parallel
4. **LLM Analysis**: AI analyzes gathered information and suggests actions
5. **Approval Check**: Determine which actions need approval
6. **Action Execution**: Execute approved actions (parallel or sequential)
7. **Audit Logging**: Log all decisions and executions
8. **Response**: Return comprehensive workflow results

This integration provides a powerful, flexible, and efficient AI workflow system that can adapt to different organizational needs while maintaining proper controls and audit trails. 