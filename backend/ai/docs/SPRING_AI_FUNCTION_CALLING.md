# Spring AI Function Calling Architecture

## Overview

This document describes the Spring AI Function Calling implementation that enables LLM-controlled dynamic workflows with tool execution capabilities.

## Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Workflow UI]
        Viz[Workflow Visualization]
    end
    
    subgraph "API Layer"
        Controller[Workflow Controller]
        Service[Enhanced Dynamic Workflow Service]
    end
    
    subgraph "AI Layer"
        FCS[Function Calling Service]
        LLM[Spring AI ChatModel]
        History[Workflow History Service]
    end
    
    subgraph "Tool Layer"
        Registry[Tool Registry]
        Tools[Tool Implementations]
        Results[Tool Results]
    end
    
    subgraph "Storage Layer"
        Memory[In-Memory History]
        DB[(Database - Planned)]
    end
    
    UI --> Controller
    Controller --> Service
    Service --> FCS
    FCS --> LLM
    FCS --> Registry
    Registry --> Tools
    Tools --> Results
    FCS --> History
    History --> Memory
    History --> DB
    Service --> Viz
```

## Core Components

### 1. Function Calling Service
**Purpose**: Orchestrates LLM-tool interactions using Spring AI

**Key Responsibilities**:
- Manages conversation with LLM
- Parses LLM responses for tool calls
- Executes tools via registry
- Handles iteration control

```java
@Service
public class FunctionCallingService {
    private final ChatModel chatModel;
    private final ToolRegistry toolRegistry;
    
    public WorkflowResult executeWorkflow(String systemPrompt, 
                                        String userPrompt, 
                                        ToolContext context, 
                                        int maxIterations)
}
```

### 2. Tool Registry
**Purpose**: Centralized tool management and discovery

**Key Features**:
- Automatic tool discovery via Spring context
- Tool name resolution
- Lifecycle management

```java
@Component
public class ToolRegistry {
    private final Map<String, Tool> tools = new HashMap<>();
    
    @EventListener
    public void onApplicationEvent(ContextRefreshedEvent event) {
        registerTools(); // Auto-discover @Component tools
    }
}
```

### 3. Tool Interface
**Purpose**: Standardized tool contract

```java
public interface Tool {
    String getName();
    String getDescription();
    Map<String, Object> getParameterSchema();
    ToolResult execute(Map<String, Object> parameters);
}
```

## Tool Calling Flow

```mermaid
sequenceDiagram
    participant User
    participant Service as Enhanced Workflow Service
    participant FCS as Function Calling Service
    participant LLM as Spring AI ChatModel
    participant Registry as Tool Registry
    participant Tool as Tool Implementation

    User->>Service: Execute workflow(input, range, maxIterations)
    Service->>FCS: executeWorkflow(systemPrompt, userPrompt, context)
    
    FCS->>LLM: Send initial prompt
    LLM->>FCS: Response with tool call request
    
    FCS->>Registry: Get tool by name
    Registry->>FCS: Return tool instance
    
    FCS->>Tool: Execute with parameters
    Tool->>FCS: Return tool result
    
    FCS->>LLM: Add tool result to conversation
    LLM->>FCS: Response with next tool call or final result
    
    alt Tool call requested
        FCS->>Registry: Get next tool
        Registry->>FCS: Return tool instance
        FCS->>Tool: Execute tool
        Tool->>FCS: Return result
        FCS->>LLM: Add result to conversation
    else Final result
        FCS->>Service: Return workflow result
        Service->>User: Return enhanced result
    end
```

## Tool Execution Architecture

```mermaid
graph LR
    subgraph "LLM Decision Layer"
        A[LLM Analyzes Context]
        B[LLM Decides Tool Call]
        C[LLM Provides Parameters]
    end
    
    subgraph "App Execution Layer"
        D[Parse LLM Response]
        E[Validate Parameters]
        F[Execute Tool]
        G[Handle Errors]
    end
    
    subgraph "Tool Implementation Layer"
        H[Range Checker Tool]
        I[Quadruple Tool]
        J[Halve Tool]
        K[Custom Tools]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> H
    F --> I
    F --> J
    F --> K
    F --> G
```

## Data Flow Diagram

```mermaid
flowchart TD
    A[User Input] --> B[Parameter Validation]
    B --> C[Create Tool Context]
    C --> D[Initialize History]
    D --> E[Load Prompt Templates]
    E --> F[Render User Prompt]
    F --> G[Start LLM Conversation]
    
    G --> H{LLM Response}
    H --> I{Contains Tool Call?}
    
    I -->|Yes| J[Parse Tool Call]
    I -->|No| K{Contains Final Result?}
    
    J --> L[Get Tool from Registry]
    L --> M[Execute Tool]
    M --> N[Add Result to Conversation]
    N --> H
    
    K -->|Yes| O[Return Workflow Result]
    K -->|No| P[Error: Invalid Response]
    
    O --> Q[Update History]
    Q --> R[Return Enhanced Result]
    
    P --> S[Return Error Result]
```

## Tool Implementation Example

### Range Checker Tool
```java
@Component
public class RangeCheckerTool implements Tool {
    
    @Override
    public String getName() {
        return "range_checker";
    }
    
    @Override
    public String getDescription() {
        return "Check if a number is within a specified range";
    }
    
    @Override
    public Map<String, Object> getParameterSchema() {
        return Map.of(
            "number", Map.of("type", "number", "description", "Number to check"),
            "min", Map.of("type", "number", "description", "Minimum value"),
            "max", Map.of("type", "number", "description", "Maximum value")
        );
    }
    
    @Override
    public ToolResult execute(Map<String, Object> parameters) {
        double number = (Double) parameters.get("number");
        double min = (Double) parameters.get("min");
        double max = (Double) parameters.get("max");
        
        String position = number < min ? "BELOW" : 
                         number > max ? "ABOVE" : "WITHIN";
        
        return ToolResult.success(Map.of(
            "position", position,
            "inRange", position.equals("WITHIN")
        ));
    }
}
```

## Workflow History Management

```mermaid
graph TD
    subgraph "History Service"
        A[Create History]
        B[Add Entry]
        C[Get Full History]
        D[Get Latest State]
        E[Create Checkpoint]
    end
    
    subgraph "Entry Types"
        F[System Message]
        G[User Message]
        H[Assistant Message]
        I[Tool Call]
        J[Tool Result]
        K[Error]
        L[Checkpoint]
    end
    
    subgraph "Storage"
        M[In-Memory Storage]
        N[Database Storage - Planned]
    end
    
    A --> M
    B --> F
    B --> G
    B --> H
    B --> I
    B --> J
    B --> K
    B --> L
    C --> M
    D --> M
    E --> M
    M --> N
```

## Security Model

```mermaid
graph LR
    subgraph "Security Layers"
        A[LLM Decision Only]
        B[App Execution Control]
        C[Parameter Validation]
        D[Tool Isolation]
    end
    
    subgraph "Protection"
        E[No Direct Code Execution]
        F[Controlled Tool Access]
        G[Input Sanitization]
        H[Error Containment]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
```

## Error Handling Strategy

```mermaid
flowchart TD
    A[Tool Execution] --> B{Success?}
    B -->|Yes| C[Return Success Result]
    B -->|No| D[Log Error]
    D --> E[Create Error Result]
    E --> F[Add to History]
    F --> G{Retry Allowed?}
    G -->|Yes| H[Retry Tool]
    G -->|No| I[Return Error to LLM]
    H --> A
    I --> J[LLM Decides Next Action]
```

## Configuration

### Spring Configuration
```yaml
spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      chat:
        options:
          model: gpt-4
          temperature: 0.1
          max-tokens: 2000
```

### Tool Registration
```java
@Configuration
public class ToolConfiguration {
    
    @Bean
    public ToolRegistry toolRegistry(ApplicationContext context) {
        return new ToolRegistry(context);
    }
    
    @Bean
    public FunctionCallingService functionCallingService(
            ChatModel chatModel, 
            ToolRegistry toolRegistry) {
        return new FunctionCallingService(chatModel, toolRegistry);
    }
}
```

## Usage Examples

### Basic Workflow Execution
```java
@Service
public class WorkflowService {
    
    public EnhancedWorkflowResult executeWorkflow(
            double inputNumber, 
            double minValue, 
            double maxValue) {
        
        return enhancedDynamicWorkflowService.executeWorkflow(
            inputNumber, minValue, maxValue, 
            10, // maxIterations
            organizationId, onBehalfOfId
        );
    }
}
```

### Custom Tool Implementation
```java
@Component
public class CustomTool implements Tool {
    
    @Override
    public String getName() {
        return "custom_tool";
    }
    
    @Override
    public ToolResult execute(Map<String, Object> parameters) {
        // Custom logic here
        return ToolResult.success(result);
    }
}
```

## Benefits

### 1. **Security**
- LLM never executes code directly
- App controls all tool execution
- Parameter validation and sanitization

### 2. **Reliability**
- Comprehensive error handling
- Retry mechanisms
- Graceful degradation

### 3. **Extensibility**
- Easy to add new tools
- Pluggable architecture
- Configurable prompts

### 4. **Observability**
- Full conversation history
- Tool execution tracking
- Performance metrics

### 5. **Maintainability**
- Clear separation of concerns
- Well-defined interfaces
- Comprehensive testing support

## Future Enhancements

### 1. **Database Persistence**
- Workflow history storage
- Tool execution logs
- Performance analytics

### 2. **Advanced Tool Features**
- Tool versioning
- Tool composition
- Async tool execution

### 3. **Enhanced Security**
- Tool access control
- Rate limiting
- Audit logging

### 4. **Performance Optimization**
- Tool result caching
- Conversation optimization
- Parallel tool execution

## Conclusion

The Spring AI Function Calling architecture provides a robust, secure, and extensible foundation for LLM-controlled workflows. It leverages Spring AI's native capabilities while maintaining clear separation between LLM decision-making and application execution. 