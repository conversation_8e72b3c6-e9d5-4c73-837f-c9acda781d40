# Spring AI Function Calling - Quick Reference

## 🚀 Quick Start

### 1. Create a New Tool
```java
@Component
public class MyCustomTool implements Tool {
    
    @Override
    public String getName() {
        return "my_custom_tool";
    }
    
    @Override
    public String getDescription() {
        return "Description of what this tool does";
    }
    
    @Override
    public Map<String, Object> getParameterSchema() {
        return Map.of(
            "param1", Map.of("type", "string", "description", "First parameter"),
            "param2", Map.of("type", "number", "description", "Second parameter")
        );
    }
    
    @Override
    public ToolResult execute(Map<String, Object> parameters) {
        // Your tool logic here
        String param1 = (String) parameters.get("param1");
        Double param2 = (Double) parameters.get("param2");
        
        // Process and return result
        return ToolResult.success(result);
    }
}
```

### 2. Execute a Workflow
```java
@Service
public class MyService {
    
    @Autowired
    private EnhancedDynamicWorkflowService workflowService;
    
    public EnhancedWorkflowResult runWorkflow(double input, double min, double max) {
        return workflowService.executeWorkflow(
            input, min, max,           // Parameters
            10,                        // Max iterations
            organizationId,            // Organization context
            onBehalfOfId,              // User context
            true                       // Include history
        );
    }
}
```

## 📋 Core Components

| Component | Purpose | Key Methods |
|-----------|---------|-------------|
| `FunctionCallingService` | Orchestrates LLM-tool interactions | `executeWorkflow()` |
| `ToolRegistry` | Manages available tools | `getTool()`, `registerTool()` |
| `Tool` | Interface for all tools | `getName()`, `execute()` |
| `WorkflowHistoryService` | Tracks conversation history | `createHistory()`, `addEntry()` |

## 🔧 Tool Interface

### Required Methods
```java
public interface Tool {
    String getName();                    // Unique identifier
    String getDescription();             // LLM description
    Map<String, Object> getParameterSchema(); // Parameter definition
    ToolResult execute(Map<String, Object> parameters); // Execution logic
}
```

### Tool Result
```java
// Success
ToolResult.success(data);
ToolResult.success(data, metadata);

// Failure
ToolResult.failure("Error message");
ToolResult.failure("Error message", metadata);
```

## 📊 Workflow Execution Flow

```mermaid
graph LR
    A[User Input] --> B[Validate Parameters]
    B --> C[Create Context]
    C --> D[Load Prompts]
    D --> E[Start LLM Conversation]
    E --> F{LLM Response}
    F --> G{Contains Tool Call?}
    G -->|Yes| H[Execute Tool]
    G -->|No| I{Contains Final Result?}
    H --> J[Add Result to Conversation]
    J --> F
    I -->|Yes| K[Return Result]
    I -->|No| L[Error Handling]
```

## 🎯 Common Patterns

### 1. Parameter Validation
```java
@Override
public ToolResult execute(Map<String, Object> parameters) {
    try {
        // Validate required parameters
        if (!parameters.containsKey("required_param")) {
            return ToolResult.failure("required_param is missing");
        }
        
        // Type validation
        Object value = parameters.get("required_param");
        if (!(value instanceof String)) {
            return ToolResult.failure("required_param must be a string");
        }
        
        // Process parameters
        String param = (String) value;
        // ... tool logic
        
    } catch (Exception e) {
        return ToolResult.failure("Tool execution failed: " + e.getMessage());
    }
}
```

### 2. Error Handling
```java
@Override
public ToolResult execute(Map<String, Object> parameters) {
    try {
        // Tool logic
        return ToolResult.success(result);
    } catch (ValidationException e) {
        return ToolResult.failure("Validation error: " + e.getMessage());
    } catch (Exception e) {
        log.error("Tool execution failed", e);
        return ToolResult.failure("Internal error occurred");
    }
}
```

### 3. Metadata and Logging
```java
@Override
public ToolResult execute(Map<String, Object> parameters) {
    long startTime = System.currentTimeMillis();
    
    try {
        log.info("Executing tool with parameters: {}", parameters);
        
        // Tool logic
        Object result = processParameters(parameters);
        
        long executionTime = System.currentTimeMillis() - startTime;
        log.info("Tool completed in {}ms", executionTime);
        
        return ToolResult.success(result, executionTime);
        
    } catch (Exception e) {
        long executionTime = System.currentTimeMillis() - startTime;
        log.error("Tool failed after {}ms", executionTime, e);
        return ToolResult.failure(e.getMessage(), executionTime);
    }
}
```

## 🔍 Debugging

### 1. Enable Debug Logging
```yaml
# application.yml
logging:
  level:
    io.sx.ai.springai: DEBUG
    io.sx.ai.tool: DEBUG
    io.sx.ai.history: DEBUG
```

### 2. Check Tool Registration
```java
@Autowired
private ToolRegistry toolRegistry;

public void checkTools() {
    log.info("Registered tools: {}", toolRegistry.getToolNames());
    log.info("Tool count: {}", toolRegistry.getToolCount());
}
```

### 3. Monitor Workflow Execution
```java
@Autowired
private WorkflowHistoryService historyService;

public void checkHistory(String sessionId) {
    WorkflowHistory history = historyService.getHistory(sessionId);
    if (history != null) {
        log.info("History entries: {}", history.size());
        log.info("Full history: {}", history.getFullHistory());
    }
}
```

## 🚨 Common Issues

### 1. Tool Not Found
**Problem**: `Tool not found: my_tool`
**Solution**: Ensure tool is annotated with `@Component` and implements `Tool` interface

### 2. Parameter Validation Error
**Problem**: `Invalid parameters: param1 is required`
**Solution**: Check parameter schema and validation logic

### 3. LLM Response Parsing Error
**Problem**: `LLM response contains neither tool call nor final result`
**Solution**: Check prompt templates and LLM response format

### 4. Maximum Iterations Reached
**Problem**: `Maximum iterations reached`
**Solution**: Increase maxIterations or check for infinite loops in tool logic

## 📝 Best Practices

### 1. Tool Design
- ✅ Use descriptive tool names
- ✅ Provide clear parameter schemas
- ✅ Implement proper error handling
- ✅ Add logging for debugging
- ✅ Keep tools focused and single-purpose

### 2. Parameter Validation
- ✅ Validate all required parameters
- ✅ Check parameter types
- ✅ Provide meaningful error messages
- ✅ Handle edge cases

### 3. Error Handling
- ✅ Catch specific exceptions
- ✅ Return structured error messages
- ✅ Log errors with context
- ✅ Don't expose sensitive information

### 4. Performance
- ✅ Cache expensive operations
- ✅ Use efficient data structures
- ✅ Monitor execution times
- ✅ Implement timeouts for long-running operations

## 🔧 Configuration

### Spring AI Configuration
```yaml
spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      chat:
        options:
          model: gpt-4
          temperature: 0.1
          max-tokens: 2000
```

### Tool Configuration
```java
@Configuration
public class ToolConfig {
    
    @Bean
    public ToolRegistry toolRegistry(ApplicationContext context) {
        return new ToolRegistry(context);
    }
    
    @Bean
    public FunctionCallingService functionCallingService(
            ChatModel chatModel, 
            ToolRegistry toolRegistry) {
        return new FunctionCallingService(chatModel, toolRegistry);
    }
}
```

## 📚 Related Documentation

- [Spring AI Function Calling Architecture](./SPRING_AI_FUNCTION_CALLING.md)
- [Component Diagram](./COMPONENT_DIAGRAM.md)
- [Spring AI Documentation](https://docs.spring.io/spring-ai/reference/)
- [Tool Implementation Examples](../src/main/java/io/sx/ai/springai/sample/tools/)

## 🆘 Getting Help

1. **Check Logs**: Enable debug logging to see detailed execution flow
2. **Validate Tools**: Use `ToolRegistry` to verify tool registration
3. **Test Prompts**: Validate prompt templates and LLM responses
4. **Review History**: Check workflow history for execution details
5. **Common Patterns**: Refer to existing tool implementations for examples 