# AI Module Architecture

## Overview

The AI module is designed as a framework-agnostic, extensible system that provides intelligent ticket processing capabilities through RAG (Retrieval-Augmented Generation), Chain of Thought (CoT), and Tree of Thought (ToT) reasoning. The architecture emphasizes modularity, scalability, and the ability to switch between different LLM providers and frameworks seamlessly.

## High-Level Architecture

```
                +-------------------+
                |   User Input/API  |
                +---------+---------+
                          |
                          v
              +-----------+-----------+
              |   Ticket Preprocessor |
              +-----------+-----------+
                          |
                          v
        +----------------+-------------------+
        | AI Reasoning Orchestrator (Spring) |
        +----------------+-------------------+
                          |
       +---------+--------+---------+----------+
       |         |                  |          |
       v         v                  v          v
 +---------+ +-----------+   +-----------+  +------------+
 | RAG KB  | | Classifier|   | Prioritizer|  | Acknowledger|
 +---------+ +-----------+   +-----------+  +------------+
       |          |               |                |
       |          v               v                v
       |    +--------------------------------------------+
       |    |           LLM Abstraction Layer            |
       |    | (Switchable Agents: OpenAI, LLAMA, etc.)   |
       |    +--------------------------------------------+
       |
+--------------------------+
| CoT / ToT Logger (JSON)  |
+--------------------------+
            |
            v
+-----------------------------+
| Visualization Microservice |
| (Tree + Timeline View)     |
+-----------------------------+
```

## Core Principles

1. **Framework Agnostic**: Switch between Spring AI, LangChain4j, or custom implementations
2. **LLM Provider Independent**: Support OpenAI, Anthropic, Llama, and other providers
3. **Reasoning Transparency**: Log all AI reasoning steps for audit and visualization
4. **Modular Design**: Each component can be developed, tested, and deployed independently
5. **Extensible Architecture**: Easy to add new AI capabilities and reasoning patterns

## Layer Responsibilities

### 1. Ticket Preprocessor
- **Purpose**: Prepare ticket data for AI processing
- **Responsibilities**:
  - Extract relevant ticket information
  - Normalize text content
  - Apply preprocessing rules
  - Validate ticket structure
- **Interface**: `TicketPreprocessor`

### 2. AI Reasoning Orchestrator
- **Purpose**: Coordinate multiple AI reasoning steps
- **Responsibilities**:
  - Manage parallel AI operations
  - Coordinate event-driven processing
  - Handle reasoning step dependencies
  - Aggregate results from multiple AI agents
- **Interface**: `AIReasoningOrchestrator`

### 3. AI Agents (Specialized Components)

#### RAG Knowledge Base
- **Purpose**: Provide contextual information for AI decisions
- **Responsibilities**:
  - Document ingestion and embedding
  - Semantic search and retrieval
  - Context assembly for prompts
- **Interface**: `RAGService`

#### Classifier
- **Purpose**: Categorize tickets by type and severity
- **Responsibilities**:
  - Determine ticket category (bug, feature, question)
  - Assess severity levels
  - Identify technical domains
- **Interface**: `TicketClassifier`

#### Prioritizer
- **Purpose**: Determine ticket priority and urgency
- **Responsibilities**:
  - Calculate priority scores
  - Consider business impact
  - Factor in user context
- **Interface**: `TicketPrioritizer`

#### Acknowledger
- **Purpose**: Generate appropriate responses to users
- **Responsibilities**:
  - Create acknowledgment messages
  - Generate status updates
  - Provide helpful information
- **Interface**: `TicketAcknowledger`

### 4. LLM Abstraction Layer
- **Purpose**: Provide unified interface for different LLM providers
- **Responsibilities**:
  - Abstract LLM provider differences
  - Handle prompt construction
  - Manage response parsing
  - Provide fallback mechanisms
- **Interface**: `LLMClient`

### 5. Chain of Thought / Tree of Thought Logger
- **Purpose**: Capture and persist AI reasoning steps
- **Responsibilities**:
  - Log reasoning steps in structured format
  - Support multiple storage backends
  - Enable reasoning visualization
  - Provide audit trail
- **Interface**: `ReasoningLogger`

### 6. Visualization Microservice
- **Purpose**: Present AI reasoning in visual formats
- **Responsibilities**:
  - Generate timeline views for CoT
  - Create decision trees for ToT
  - Provide interactive visualizations
  - Support real-time updates
- **Interface**: `ReasoningVisualizer`

## Implementation Strategy

### Phase 1: Core Infrastructure
1. **LLM Abstraction Layer**
   - Implement `LLMClient` interface
   - Create adapters for Spring AI and LangChain4j
   - Add configuration for different providers

2. **Reasoning Logger**
   - Design JSON schema for CoT/ToT logging
   - Implement PostgreSQL storage backend
   - Create abstraction for multiple storage options

3. **Basic AI Orchestrator**
   - Implement event-driven processing
   - Add parallel step execution
   - Create result aggregation

### Phase 2: AI Agents
1. **RAG Implementation**
   - Document ingestion pipeline
   - Vector database integration
   - Context retrieval and assembly

2. **Specialized Agents**
   - Ticket classifier implementation
   - Priority calculation logic
   - Acknowledgment generation

### Phase 3: Visualization
1. **Timeline View**
   - Sequential step visualization
   - Interactive timeline component
   - Real-time updates

2. **Decision Tree View**
   - Tree structure visualization
   - Confidence score display
   - Branch exploration interface

## Key Interfaces

### LLMClient Interface
```java
public interface LLMClient {
    String chat(String prompt);
    String generate(String taskName, String context, Map<String, String> metadata);
    CompletableFuture<String> generateAsync(String taskName, String context, Map<String, String> metadata);
}
```

### Reasoning Logger Interface
```java
public interface ReasoningLogger {
    void logChainOfThought(String ticketId, List<ReasoningStep> steps);
    void logTreeOfThought(String ticketId, List<ThoughtBranch> branches);
    ReasoningSession getSession(String ticketId);
    List<ReasoningSession> getSessionsByDateRange(LocalDateTime start, LocalDateTime end);
}
```

### AI Orchestrator Interface
```java
public interface AIReasoningOrchestrator {
    void processTicketCreated(TicketCreatedEvent event);
    void processTicketUpdated(TicketUpdatedEvent event);
    CompletableFuture<AIProcessingResult> processTicketAsync(TicketDTO ticket);
}
```

## Data Models

### Chain of Thought Structure
```json
{
  "ticketId": "TKT123",
  "sessionId": "session-456",
  "timestamp": "2024-01-15T10:30:00Z",
  "steps": [
    {
      "stepId": "step-1",
      "stepType": "CLASSIFY",
      "input": "Login fails with 500 error",
      "output": "Authentication Bug",
      "confidence": 0.85,
      "timestamp": "2024-01-15T10:30:05Z",
      "metadata": {
        "model": "gpt-4",
        "processingTime": 1200
      }
    },
    {
      "stepId": "step-2", 
      "stepType": "PRIORITIZE",
      "input": "Severity: High, User blocked from access",
      "output": "P1 - Critical",
      "confidence": 0.92,
      "timestamp": "2024-01-15T10:30:08Z",
      "metadata": {
        "model": "gpt-4",
        "processingTime": 800
      }
    }
  ]
}
```

### Tree of Thought Structure
```json
{
  "ticketId": "TKT124",
  "sessionId": "session-457",
  "timestamp": "2024-01-15T11:00:00Z",
  "thoughts": [
    {
      "stepId": "step-1",
      "stepType": "CLASSIFY",
      "options": [
        {
          "label": "Bug",
          "score": 0.8,
          "reasoning": "System error prevents normal operation"
        },
        {
          "label": "Feature Request", 
          "score": 0.2,
          "reasoning": "Could be enhancement request"
        }
      ],
      "selectedOption": "Bug",
      "timestamp": "2024-01-15T11:00:05Z"
    }
  ]
}
```

## Configuration

### LLM Provider Configuration
```yaml
ai:
  llm:
    provider: openai  # openai, anthropic, llama, custom
    model: gpt-4
    api-key: ${OPENAI_API_KEY}
    timeout: 30000
    max-retries: 3
  framework:
    type: spring-ai  # spring-ai, langchain4j, custom
    version: 1.0.0
```

### Reasoning Logger Configuration
```yaml
ai:
  reasoning:
    storage:
      type: postgresql  # postgresql, elasticsearch, mongodb
      table: ai_reasoning_sessions
      retention-days: 90
    visualization:
      enabled: true
      timeline-view: true
      tree-view: true
```

## Integration Points

### NATS Consumer Integration
The `AINatsConsumer` will be updated to use the new orchestration layer:

```java
@Component
public class AINatsConsumer {
    private final AIReasoningOrchestrator orchestrator;
    
    public void handleTicketCreated(TicketCreatedEvent event) {
        orchestrator.processTicketCreated(event);
    }
}
```

### Event-Driven Processing
```java
@Component
public class AIReasoningOrchestrator {
    private final ApplicationEventPublisher eventPublisher;
    
    public void processTicketCreated(TicketCreatedEvent event) {
        // Fire parallel events for different AI operations
        eventPublisher.publishEvent(new ClassificationEvent(event.getTicket()));
        eventPublisher.publishEvent(new PrioritizationEvent(event.getTicket()));
        eventPublisher.publishEvent(new AcknowledgmentEvent(event.getTicket()));
    }
}
```

## Migration Strategy

1. **Preserve Existing NATS Consumer**: Keep `AINatsConsumer` as the entry point
2. **Gradual Migration**: Implement new components alongside existing ones
3. **Feature Flags**: Use configuration to switch between old and new implementations
4. **Data Migration**: Migrate existing AI data to new storage format
5. **Testing**: Comprehensive testing of new components before full migration

## Success Metrics

1. **Performance**: AI response time < 5 seconds for standard operations
2. **Accuracy**: > 90% accuracy for classification and prioritization
3. **Scalability**: Support for 1000+ concurrent AI operations
4. **Transparency**: 100% reasoning step logging and visualization
5. **Flexibility**: Ability to switch LLM providers with configuration change only

## Future Enhancements

1. **Multi-Modal AI**: Support for image and document analysis
2. **Learning System**: Continuous improvement based on user feedback
3. **Custom Models**: Fine-tuned models for specific domains
4. **Advanced Reasoning**: Integration with external knowledge bases
5. **Real-time Collaboration**: Multi-agent AI systems for complex problems 