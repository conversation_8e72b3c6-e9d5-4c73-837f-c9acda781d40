# AI Strategy and Architecture Documentation

## Overview

This document captures the strategic vision and architectural approach for the AI-powered ticketing system, focusing on creating a sophisticated, configurable AI system that balances generic intelligence with customer-specific customization while maintaining minimal configuration requirements.

## Core Philosophy

### 1. **Generic but Decisive AI**
- AI should be action-oriented and decisive, not conservative
- Avoid hardcoded frameworks in favor of contextual decision-making
- Use "consider these factors" lists in prompts rather than strict rules
- Default to specific, actionable suggestions over generic fallbacks

### 2. **Customer-Specific Customization**
- Support organizations can configure escalation rules, response templates, and routing preferences
- All customization is optional with sensible defaults
- Provide starter templates (conservative, decisive, customer-first) for quick setup
- Allow fine-tuning as organizations mature

### 3. **Minimal Configuration Requirements**
- Organizations should be able to start with zero configuration
- Provide 2-3 starter templates for immediate value
- All advanced configuration is optional and can be changed later
- Focus on out-of-the-box functionality

## Multi-Agent Architecture

### Core AI Agents

#### 1. **Customer Importance Agent**
- **Purpose**: Determine customer importance based on multiple contextual factors
- **Factors**: Contract value, revenue, strategic importance, historical relationship, current satisfaction, business impact, competitive risk, growth potential, industry position
- **Output**: Importance score (1-10), importance level, rationale, confidence
- **Caching**: Results cached to prevent repeated AI calls for same customer

#### 2. **Customer Satisfaction Agent**
- **Purpose**: Analyze customer satisfaction levels and predict trends
- **Factors**: Historical satisfaction, current ticket context, customer communication, support experience, business impact, competitive context, relationship health
- **Output**: Satisfaction score, trend (improving/stable/declining), risk indicators, opportunity indicators
- **Caching**: Results cached for efficiency

#### 3. **Similar Tickets Agent**
- **Purpose**: Find and analyze similar tickets for context and pattern recognition
- **Scope**: Customer's own tickets (higher weight) + similar tickets from other customers
- **Output**: Summary of similar issues, resolution patterns, success rates, escalation frequency
- **Caching**: Vector embeddings cached, LLM analysis cached for 6 hours

#### 4. **Customer Health Agent**
- **Purpose**: Assess overall customer health and churn risk
- **Factors**: Account status, contract renewal, expansion potential, churn risk indicators
- **Output**: Health score, risk assessment, recommended actions

#### 5. **Routing Agent**
- **Purpose**: Determine optimal ticket routing based on availability and experience
- **Factors**: Team availability, holidays, work hours, time zones, leaves, past experience with similar tickets
- **Output**: Recommended team/agent assignment with rationale

### Agent Orchestration

#### **Orchestrator Pattern**
- Main agent orchestrates sub-agents and merges their outputs
- Each agent provides specialized analysis in its domain
- Orchestrator combines insights into comprehensive context for final LLM decision

#### **RAG (Retrieval-Augmented Generation)**
- Use vector search or keyword search to pull relevant context
- Include similar tickets, org rules, recent customer interactions
- Provide context snippets to LLM for informed decisions

## Similarity Analysis Strategy

### **Hybrid Approach: Vector + LLM + Rules**

#### **Layer 1: Deterministic Vector Similarity (Primary)**
- **Technology**: Sentence transformers (all-MiniLM-L6-v2) for semantic similarity
- **Performance**: Very fast (<100ms), low cost, deterministic
- **Use Case**: Primary filtering mechanism for finding similar tickets
- **Attributes**: Title, description, organization, user, priority, tags, custom fields

#### **Layer 2: LLM Contextual Analysis (Secondary)**
- **Technology**: LLM for deeper pattern analysis and reasoning
- **Performance**: Slower (1-2s), higher cost, but adds human-like understanding
- **Use Case**: Analyze WHY tickets are similar, provide reasoning and insights
- **Trigger**: Only for high-priority tickets or when vector similarity finds >3 similar tickets

#### **Layer 3: Rule-Based Filtering (Tertiary)**
- **Technology**: Business rules and customer-specific configurations
- **Performance**: Fast, deterministic, configurable
- **Use Case**: Apply organization-specific rules and customer-driven prompts

### **Similarity Attributes**

#### **Core Attributes (Always Used)**
- **Ticket Title & Description**: Primary semantic content
- **Organization**: Same org tickets often share context and terminology
- **User Who Raised Ticket**: Same user may report recurring issues
- **Priority**: High-priority tickets may have different resolution patterns

#### **Extended Attributes (When Available)**
- **Tags/Labels**: Strong signals for similarity if available
- **Product/Module/Component**: Highly relevant for technical issues
- **Custom Fields**: Organization-specific fields (environment, region, etc.)
- **Comments/Updates**: Recent activity and context

#### **Customer-Driven Rules**
- **Storage**: Store as structured prompts or rules in database
- **Vectorization**: Convert "tickets like these" rules to embeddings
- **Matching**: Compare new tickets to rule vectors at runtime
- **Actions**: Trigger specific actions (escalation, priority setting) based on similarity

### **Spring AI Integration**

#### **Pros of Spring AI**
- **Unified API**: Abstracts provider-specific details (OpenAI, Ollama, HuggingFace)
- **Easy Switching**: Swap LLMs/vector DBs via config, not code changes
- **Built-in Vector Store Support**: Integrates with Pinecone, Chroma, Redis, etc.
- **Prompt Templates**: Supports prompt engineering and chaining
- **Spring Ecosystem**: Leverages Spring Boot features (config, DI, profiles)
- **Community Momentum**: Rapidly growing, likely to become standard

#### **Cons of Spring AI**
- **Maturity**: Still evolving; some advanced features may be missing
- **Performance Tuning**: May abstract away low-level optimizations
- **Vendor Lock-in (Abstraction)**: Tied to Spring AI's abstraction layer
- **Debugging**: More layers can make debugging harder
- **Open Source Support**: Ollama support present but may lag behind direct APIs

#### **Recommendation**
Use Spring AI for LLM and vector store abstraction unless specific advanced features are needed. Provides excellent flexibility and maintainability.

### **Vendor Flexibility & Model Choice**

#### **Abstraction Strategy**
- Use interfaces for LLM and vector store access
- Configure provider/model via Spring config or environment variables
- Support both open source (Ollama, HuggingFace) and commercial (OpenAI, Azure) backends

#### **Development Mode**
- Default to open source, small/efficient models (`llama3.2:latest` via Ollama)
- Add efficiency/cost comments in code/config:
  - **Tiny LLMs**: Fast, cheap, good for dev/test, less context
  - **Large LLMs**: Slower, more expensive, better for complex reasoning

## Configuration and Interface Structure

### **Similarity Analysis Configuration**

```yaml
similarity_analysis:
  # Primary method (always used)
  vector_similarity:
    enabled: true
    threshold: 0.75
    model: "all-MiniLM-L6-v2"
    max_results: 20
    attributes:
      title_weight: 0.4
      description_weight: 0.3
      organization_weight: 0.15
      user_weight: 0.1
      priority_weight: 0.05
  
  # Secondary method (optional)
  llm_analysis:
    enabled: true
    min_similar_tickets: 3
    max_tokens: 1000
    temperature: 0.3
    trigger_conditions:
      - priority: "HIGH"
      - priority: "CRITICAL"
      - customer_importance_score: 8.0
  
  # Business rules (optional)
  business_rules:
    enabled: true
    customer_type_filter: true
    priority_weighting: true
    recent_tickets_weight: 1.2
  
  # Caching configuration
  caching:
    vector_embeddings_ttl: 86400  # 24 hours
    llm_analysis_ttl: 21600      # 6 hours
    similarity_results_ttl: 3600 # 1 hour
```

### **Spring AI Configuration**

```yaml
spring:
  ai:
    # LLM Configuration
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      chat:
        options:
          model: ${OPENAI_MODEL:gpt-3.5-turbo}
          temperature: 0.3
          max-tokens: 2000
    
    # Ollama Configuration (Development)
    ollama:
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      chat:
        options:
          model: ${OLLAMA_MODEL:llama3.2:latest}
          temperature: 0.3
          max-tokens: 2000
    
    # Vector Store Configuration
    vectorstore:
      chroma:
        host: ${CHROMA_HOST:localhost}
        port: ${CHROMA_PORT:8000}
        collection-name: "ticket-similarity"
      
      # Alternative: Redis
      redis:
        host: ${REDIS_HOST:localhost}
        port: ${REDIS_PORT:6379}
        index-name: "ticket-similarity"
```

### **Interface Structure**

#### **Similarity Analysis Service Interface**

```java
public interface SimilarityAnalysisService {
    
    /**
     * Find similar tickets using hybrid approach
     * @param ticket Current ticket to find similarities for
     * @param organizationId Organization context
     * @param options Analysis options
     * @return Similarity analysis results
     */
    SimilarityAnalysisResult findSimilarTickets(
        Ticket ticket, 
        Long organizationId, 
        SimilarityAnalysisOptions options
    );
    
    /**
     * Generate embeddings for a ticket
     * @param ticket Ticket to embed
     * @return Vector embedding
     */
    float[] generateTicketEmbedding(Ticket ticket);
    
    /**
     * Store customer-driven rules as embeddings
     * @param rule Customer similarity rule
     * @param organizationId Organization context
     */
    void storeCustomerRule(CustomerSimilarityRule rule, Long organizationId);
    
    /**
     * Match ticket against customer rules
     * @param ticket Ticket to match
     * @param organizationId Organization context
     * @return Matching rules with actions
     */
    List<RuleMatch> matchCustomerRules(Ticket ticket, Long organizationId);
}
```

#### **Similarity Analysis Options**

```java
public class SimilarityAnalysisOptions {
    private boolean useVectorSimilarity = true;
    private boolean useLLMAnalysis = false;
    private boolean useBusinessRules = true;
    private double vectorThreshold = 0.75;
    private int maxResults = 20;
    private Set<String> attributes = Set.of("title", "description", "organization", "user", "priority");
    private Map<String, Double> attributeWeights = Map.of(
        "title", 0.4,
        "description", 0.3,
        "organization", 0.15,
        "user", 0.1,
        "priority", 0.05
    );
    
    // Builder pattern for easy configuration
    public static SimilarityAnalysisOptions builder() {
        return new SimilarityAnalysisOptions();
    }
}
```

#### **Similarity Analysis Result**

```java
public class SimilarityAnalysisResult {
    private List<TicketSimilarity> similarTickets;
    private SimilarityContext llmContext;
    private List<RuleMatch> ruleMatches;
    private double vectorConfidence;
    private Double llmConfidence;
    private long processingTimeMs;
    private String analysisMethod; // "vector_only", "hybrid", "llm_only"
    
    // Performance and cost metrics
    private CostMetrics costMetrics;
    private PerformanceMetrics performanceMetrics;
}

public class TicketSimilarity {
    private Ticket ticket;
    private double similarityScore;
    private Map<String, Double> attributeSimilarities;
    private String similarityReason;
    private List<String> commonPatterns;
}

public class SimilarityContext {
    private String analysis;
    private List<String> patterns;
    private String resolutionTrends;
    private String escalationFrequency;
    private List<String> successFactors;
    private String recommendedApproach;
    private double confidence;
}
```

#### **Customer-Driven Rules Interface**

```java
public interface CustomerSimilarityRule {
    String getId();
    String getOrganizationId();
    String getDescription(); // "tickets like these"
    String getAction(); // "escalate", "set_priority_high", "assign_team"
    Map<String, Object> getActionParameters();
    double getSimilarityThreshold();
    boolean isEnabled();
    LocalDateTime getCreatedAt();
    LocalDateTime getUpdatedAt();
}

public class RuleMatch {
    private CustomerSimilarityRule rule;
    private double similarityScore;
    private String matchReason;
    private Map<String, Object> suggestedActions;
}
```

### **Agent Implementation Structure**

```java
@Component
public class SimilarTicketsAgent implements AIAgent {
    
    private final SimilarityAnalysisService similarityService;
    private final UnifiedAIService aiService;
    private final SXClientService sxClientService;
    
    @Override
    public String getAgentId() {
        return "similar-tickets-agent";
    }
    
    @Override
    public String getAgentType() {
        return "SIMILAR_TICKETS";
    }
    
    @Override
    public List<AISuggestion> processTicket(Long ticketId, Map<String, Object> context) {
        // 1. Get ticket data
        JsonNode ticketData = sxClientService.getTicket(ticketId, 
            (Long) context.get("supportOrganizationId"), 1L);
        
        // 2. Perform similarity analysis
        SimilarityAnalysisResult result = similarityService.findSimilarTickets(
            convertToTicket(ticketData),
            (Long) context.get("organizationId"),
            buildAnalysisOptions(context)
        );
        
        // 3. Generate AI suggestions based on similarity
        return generateSuggestionsFromSimilarity(result, ticketData, context);
    }
    
    private SimilarityAnalysisOptions buildAnalysisOptions(Map<String, Object> context) {
        return SimilarityAnalysisOptions.builder()
            .useVectorSimilarity(true)
            .useLLMAnalysis(shouldUseLLM(context))
            .useBusinessRules(true)
            .vectorThreshold(0.75)
            .maxResults(20)
            .build();
    }
    
    private boolean shouldUseLLM(Map<String, Object> context) {
        // Use LLM for high-priority tickets or when we have enough similar tickets
        String priority = (String) context.get("priority");
        return "HIGH".equals(priority) || "CRITICAL".equals(priority);
    }
}
```

### **Performance and Cost Optimization**

#### **Caching Strategy**
```java
@Service
public class SimilarityCacheService {
    
    @Cacheable(value = "ticket-embeddings", key = "#ticket.id")
    public float[] getTicketEmbedding(Ticket ticket) {
        return generateEmbedding(ticket);
    }
    
    @Cacheable(value = "similarity-results", key = "#ticket.id + '_' + #orgId")
    public SimilarityAnalysisResult getCachedSimilarity(Ticket ticket, Long orgId) {
        return performSimilarityAnalysis(ticket, orgId);
    }
    
    @CacheEvict(value = "ticket-embeddings", key = "#ticket.id")
    public void invalidateTicketEmbedding(Ticket ticket) {
        // Clear cache when ticket is updated
    }
}
```

#### **Cost Monitoring**
```java
@Component
public class SimilarityCostMonitor {
    
    public void recordVectorSimilarityCost(long processingTimeMs, int ticketsProcessed) {
        // Record cost metrics for vector similarity
        // Very low cost, mostly compute time
    }
    
    public void recordLLMAnalysisCost(String model, int tokensUsed, long processingTimeMs) {
        // Record cost metrics for LLM analysis
        // Higher cost, track per-token usage
    }
    
    public CostReport generateCostReport() {
        // Generate cost analysis report
        return CostReport.builder()
            .vectorSimilarityCost(calculateVectorCost())
            .llmAnalysisCost(calculateLLMCost())
            .totalCost(calculateTotalCost())
            .costPerTicket(calculateCostPerTicket())
            .build();
    }
}
```

## Contextual Decision Factors

### 1. **Customer Importance (AI-Driven)**
- **Agent**: CustomerImportanceAgent
- **Decision Factors**:
  - Customer Value: Contract value, revenue, strategic importance
  - Historical Relationship: Length of relationship, loyalty, past issues
  - Current Satisfaction: Recent feedback, satisfaction scores, NPS
  - Business Impact: Production systems, user base, critical services
  - Competitive Risk: Risk of losing customer to competitors
  - Growth Potential: Upsell opportunities, expansion potential
  - Industry Position: Market leader, reference customer, brand value

### 2. **Customer Satisfaction (AI-Driven)**
- **Agent**: CustomerSatisfactionAgent
- **Decision Factors**:
  - Historical Satisfaction: Past feedback, NPS scores, satisfaction trends
  - Current Ticket Context: Issue severity, response time, resolution quality
  - Customer Communication: Tone, urgency, frustration indicators
  - Support Experience: Agent interactions, solution quality, follow-up
  - Business Impact: Service disruption, user experience, revenue impact
  - Competitive Context: Market position, alternative solutions
  - Relationship Health: Account status, contract renewal, expansion potential

### 3. **Similar Tickets Analysis**
- **Scope**: Customer's own tickets (higher weight) + similar tickets from other customers
- **Analysis**: Resolution patterns, success rates, escalation frequency
- **Weighting**: Customer's own tickets get higher weight in decision-making

### 4. **Recent Customer Health**
- **Factors**: Account status, contract renewal dates, expansion potential, churn risk
- **Trends**: Recent satisfaction trends, feedback patterns, engagement levels

### 5. **Routing Factors**
- **Availability**: Holidays, work hours, time zones, leaves, calendar integration
- **Experience**: Past experience with similar tickets, success rates
- **Load Balancing**: Current team workload and capacity

## Prompt Strategy

### Generic, Decisive Prompts

**Example Prompt Structure:**
```
You are an AI support agent. 
Consider the following:
- Ticket details: {ticket}
- Customer importance: {importance_score} ({importance_level})
- Customer satisfaction: {satisfaction_score} ({trend})
- Similar tickets: {summary}
- Org escalation rules: {org_rules}
- Routing preferences: {routing_prefs}
- Availability: {availability}
- Past experience: {past_experience}

Be decisive. Suggest the best next action. Use this response template: {response_template}
```

### Support Organization Context Injection

**Configuration Schema:**
```yaml
escalation_rules:
  - "production down always escalate"
  - "critical customers escalate within 2 hours"
  
response_templates:
  default: "We are working on your issue..."
  escalation: "We have escalated your issue to our senior team..."
  
routing_preferences:
  - "prefer team A for billing issues"
  - "prefer team B for technical issues"
```

### Minimal Configuration Approach

1. **Starter Templates**:
   - Conservative: Safe, thorough approach
   - Decisive: Action-oriented, quick decisions
   - Customer-first: Prioritizes customer satisfaction

2. **Default Behavior**:
   - All configuration is optional
   - Sensible defaults for all settings
   - Fallback to generic logic if factors are disabled

## Response Template System

### Configurable at Organization Level

- Each organization can pick default response templates
- Templates are simple text blocks with variables
- LLM prompt includes template for consistent responses
- Examples: "formal", "friendly", "concise"

### Template Variables

```yaml
templates:
  escalation:
    text: "Dear {customer}, we have escalated your issue to {team}..."
    variables: ["customer", "team", "estimated_time"]
  
  resolution:
    text: "Your issue has been resolved. {resolution_details}..."
    variables: ["resolution_details", "prevention_tips"]
```

## Implementation Guidelines

### 1. **Agent Development**
- Each agent should be focused on a specific domain
- Agents should cache results to avoid repeated AI calls
- Use confidence scores to indicate reliability
- Provide clear rationale for all decisions

### 2. **Configuration Management**
- Store org-specific config in database
- Provide UI for easy configuration
- Validate configuration changes
- Support configuration versioning

### 3. **Caching Strategy**
- Cache agent results to improve performance
- Implement cache invalidation based on data freshness
- Use Redis or similar for distributed caching
- Cache customer importance and satisfaction for 24-48 hours

### 4. **Error Handling**
- Graceful fallbacks when agents fail
- Clear error messages for debugging
- Retry mechanisms for transient failures
- Monitoring and alerting for agent health

### 5. **Performance Optimization**
- Parallel execution of independent agents
- Async processing where possible
- Timeout handling for long-running operations
- Cost monitoring and optimization

## Evolution Strategy

### Phase 1: Foundation
- Implement core agents (CustomerImportance, CustomerSatisfaction)
- Basic orchestration and RAG
- Simple configuration system
- Starter templates

### Phase 2: Enhancement
- Add more specialized agents
- Improve RAG with better vector search
- Enhanced configuration options
- Advanced analytics and metrics

### Phase 3: Optimization
- Machine learning for agent improvement
- Advanced caching and performance
- Predictive analytics
- Automated configuration optimization

## Success Metrics

### 1. **Decision Quality**
- Suggestion acceptance rate
- Time to resolution improvement
- Customer satisfaction scores
- Escalation rate reduction

### 2. **Performance**
- Response time for AI suggestions
- Cost per ticket processed
- Cache hit rates
- Agent availability and reliability

### 3. **User Experience**
- Configuration complexity
- Time to first value
- User satisfaction with AI suggestions
- Reduction in manual work

### 4. **Business Impact**
- Support efficiency improvement
- Customer retention rates
- Support cost reduction
- Agent productivity increase

## Technical Implementation Notes

### Database Schema
- Store agent results with timestamps
- Cache configuration with versioning
- Track agent performance metrics
- Store similar ticket relationships

### API Design
- RESTful endpoints for each agent
- Batch processing capabilities
- Real-time and async processing options
- Comprehensive error responses

### Monitoring and Observability
- Agent performance metrics
- Decision quality tracking
- Cost monitoring
- User feedback collection

## Future Considerations

### 1. **Advanced AI Techniques**
- Fine-tuning models on organization-specific data
- Multi-modal AI (text, images, logs)
- Reinforcement learning for continuous improvement
- Federated learning for privacy-preserving improvements

### 2. **Integration Opportunities**
- Calendar systems for availability
- PagerDuty for on-call management
- CRM systems for customer data
- Analytics platforms for insights

### 3. **Scalability**
- Horizontal scaling of agents
- Geographic distribution
- Multi-tenant isolation
- Performance optimization

## Conclusion

This AI strategy provides a comprehensive framework for building a sophisticated, configurable AI system that delivers immediate value while supporting long-term evolution. The multi-agent architecture ensures specialized expertise while the orchestration layer provides cohesive decision-making. The minimal configuration approach ensures organizations can start quickly while the customization options support growth and maturity.

The key success factors are:
1. **Decisive AI**: Action-oriented, specific suggestions
2. **Contextual Intelligence**: Rich, multi-factor decision-making
3. **Minimal Friction**: Easy setup, optional advanced configuration
4. **Continuous Improvement**: Learning and optimization over time
5. **Business Alignment**: Clear metrics and success criteria

This approach balances the need for sophisticated AI capabilities with the practical requirements of real-world support organizations. 