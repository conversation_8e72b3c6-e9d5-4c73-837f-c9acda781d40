# LLM ↔ App Multi-Step Flow Implementation

## Overview

The AI system implements a sophisticated multi-step flow between the LLM and the application, allowing for dynamic, context-aware decision making and action execution. This flow supports both **Hybrid Mode** (LLM suggests, system executes) and **Full MCP Mode** (LLM autonomously executes).

## Architecture Flow

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           Multi-Step LLM ↔ App Flow                        │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │
│  │   Event     │───▶│   LLM       │───▶│   App       │───▶│   LLM       │  │
│  │  Trigger    │    │  Analysis   │    │  Action     │    │  Analysis   │  │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘  │
│         │                   │                   │                   │      │
│         ▼                   ▼                   ▼                   ▼      │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │
│  │  Context    │    │ Information │    │  Results    │    │ Next Steps  │  │
│  │  Gathering  │    │  Requests   │    │  Processing │    │  Decision   │  │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 1. Hybrid Mode Flow

### Step-by-Step Process

```java
// 1. Event triggers AI processing
AIMessageHandlerImpl.handleMessage(eventType, eventData, organizationId, onBehalfOfId)

// 2. LLM performs initial analysis
LLMService.performStructuredAnalysis(request)
├── Analyzes event context
├── Identifies information needs
├── Suggests tool calls
└── Returns structured response

// 3. System executes information gathering (parallel)
InformationGatheringService.gatherInformation(requests)
├── Similar tickets lookup
├── Customer satisfaction scores
├── Team availability check
└── Historical performance data

// 4. LLM re-analyzes with gathered context
LLMService.performStructuredAnalysis(updatedRequest)
├── Incorporates new information
├── Makes refined decisions
├── Suggests business actions
└── Returns final recommendations

// 5. System executes business actions
HybridToolExecutor.executeToolCalls(toolCalls)
├── Assign ticket to team
├── Update ticket priority
├── Send notifications
└── Log all actions
```

### Example: Ticket Participant Removal

```java
// Initial LLM Analysis
{
  "session_id": "uuid-123",
  "reasoning": "Participant removed from ticket - need to assess impact",
  "information_requests": [
    {
      "information_type": "SIMILAR_TICKETS",
      "description": "Find similar tickets to understand patterns",
      "parameters": {"ticket_id": 50, "limit": 5}
    },
    {
      "information_type": "TEAM_AVAILABILITY", 
      "description": "Check team availability for reassignment",
      "parameters": {"team_id": 6}
    }
  ],
  "next_steps": [
    {
      "step_id": "analyze_impact",
      "step_name": "Analyze Impact",
      "description": "Assess impact of participant removal"
    }
  ]
}

// After Information Gathering
{
  "session_id": "uuid-123",
  "reasoning": "Based on similar tickets and team availability...",
  "tool_calls": [
    {
      "tool_name": "assign_ticket",
      "parameters": {
        "ticket_id": 50,
        "assignee_id": 15,
        "reason": "Reassignment due to original assignee removal"
      }
    }
  ]
}
```

## 2. Full MCP Mode Flow

### Recursive Tool Execution Loop

```java
// Full MCP Agent Implementation
FullMCPAgentImpl.executeTask(taskType, taskDescription, context, organizationId, onBehalfOfId)

// Recursive loop with context updates
while (toolCallCount < MAX_TOOL_CALLS) {
    // 1. LLM decides next action
    String llmResponse = llmService.generateChainOfThought(currentContext, context);
    
    // 2. Parse tool call from LLM response
    ToolCallResult toolCall = parseToolCall(llmResponse, availableTools);
    
    if (toolCall == null) {
        // LLM decided to complete the task
        break;
    }
    
    // 3. Execute the tool
    ToolCallResult result = executeTool(toolCall.getToolName(), toolCall.getParameters());
    
    // 4. Update context with result
    currentContext = updateContextWithResult(currentContext, result);
    toolCallCount++;
}
```

### Example: Autonomous Ticket Processing

```java
// Step 1: Initial Analysis
{
  "tool_name": "analyze_ticket",
  "parameters": {"ticket_id": 50},
  "reasoning": "Need to understand ticket details first"
}

// Step 2: After ticket analysis
{
  "tool_name": "find_similar_tickets", 
  "parameters": {"category": "bug", "priority": "high"},
  "reasoning": "Looking for similar high-priority bugs to understand patterns"
}

// Step 3: After finding similar tickets
{
  "tool_name": "assign_ticket",
  "parameters": {
    "ticket_id": 50,
    "assignee_id": 12,
    "reason": "Based on similar ticket patterns, user 12 has best track record"
  },
  "reasoning": "Assigning to most experienced team member for this type of issue"
}

// Step 4: Task Complete
"TASK_COMPLETE: Ticket successfully assigned to experienced team member based on historical patterns"
```

## 3. Dynamic Workflow Orchestration

### Context-Aware Step Execution

```java
// AIHybridOrchestratorImpl.processWorkflowSteps()
private AIAnalysisResponse processWorkflowSteps(AIAnalysisResponse initialResponse,
                                              Long organizationId, Long onBehalfOfId) {
    String sessionId = initialResponse.getSessionId();
    AIAnalysisResponse currentResponse = initialResponse;
    
    // 1. Process information requests first (parallel)
    if (currentResponse.getInformationRequests() != null) {
        for (AIAnalysisResponse.InformationRequest infoRequest : currentResponse.getInformationRequests()) {
            Map<String, Object> gatheredInfo = gatherContextualInformation(sessionId, infoRequest, organizationId);
            
            // Update workflow state with gathered information
            AIAnalysisResponse.WorkflowState workflowState = workflowSessions.get(sessionId);
            workflowState.getGatheredContext().put(infoRequest.getInformationType(), gatheredInfo);
        }
    }
    
    // 2. Execute workflow steps (sequential or parallel)
    if (currentResponse.getNextSteps() != null) {
        for (AIAnalysisResponse.NextStep step : currentResponse.getNextSteps()) {
            currentResponse = executeWorkflowStep(sessionId, step.getStepId(), 
                                               currentResponse.getMetadata(), 
                                               organizationId, onBehalfOfId);
            
            // 3. Check for additional information needs
            if (currentResponse.getInformationRequests() != null) {
                // Gather additional information and continue
            }
        }
    }
    
    return currentResponse;
}
```

## 4. Information Gathering Integration

### Parallel Data Retrieval

```java
// InformationGatheringServiceImpl.gatherInformation()
public Map<String, Object> gatherInformation(List<InformationRequest> informationRequests, 
                                           Map<String, Object> context) {
    Map<String, Object> gatheredData = new HashMap<>();
    
    // Process each request in parallel for efficiency
    List<CompletableFuture<Map.Entry<String, Object>>> futures = informationRequests.stream()
        .map(request -> CompletableFuture.supplyAsync(() -> {
            try {
                GatheringStrategy strategy = mapRequestToStrategy(request);
                Object rawData = executeGatheringStrategy(strategy, context);
                Object formattedData = formatDataForLLM(rawData, request.getRequestType());
                
                return Map.entry(request.getRequestId(), formattedData);
            } catch (Exception e) {
                return Map.entry(request.getRequestId(), createErrorResponse(request, e.getMessage()));
            }
        }))
        .collect(Collectors.toList());
    
    // Wait for all gathering operations to complete
    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    
    // Collect results
    for (CompletableFuture<Map.Entry<String, Object>> future : futures) {
        Map.Entry<String, Object> result = future.get();
        gatheredData.put(result.getKey(), result.getValue());
    }
    
    return gatheredData;
}
```

## 5. Tool Execution and Context Updates

### Seamless Context Propagation

```java
// Context updates after each tool execution
private String updateContextWithResult(String currentContext, ToolCallResult result) {
    return String.format("""
        %s
        
        Tool execution result:
        Tool: %s
        Success: %s
        Result: %s
        %s
        
        Continue with the next step or complete the task if finished.
        """, currentContext, result.getToolName(), result.isSuccess(), 
        result.getResult(), result.getErrorMessage() != null ? "Error: " + result.getErrorMessage() : "");
}
```

## 6. Session Management and State Tracking

### Workflow State Persistence

```java
// AIAnalysisResponse.WorkflowState
@Builder
public class WorkflowState {
    private String sessionId;
    private String currentStep;
    private List<String> completedSteps;
    private List<InformationRequest> pendingInformationRequests;
    private Map<String, Object> gatheredContext;
    private String workflowStatus; // IN_PROGRESS, COMPLETED, FAILED
    private Double confidenceScore;
    private String estimatedCompletionTime;
}
```

## 7. Error Handling and Recovery

### Graceful Failure Management

```java
// Error handling in multi-step flows
try {
    // Execute workflow step
    currentResponse = executeWorkflowStep(sessionId, stepId, context, organizationId, onBehalfOfId);
} catch (Exception e) {
    log.error("Error executing workflow step: {}", e.getMessage(), e);
    
    // Update workflow state to failed
    workflowState.setWorkflowStatus("FAILED");
    workflowSessions.put(sessionId, workflowState);
    
    // Return error response with context
    return createErrorResponse(sessionId, e.getMessage());
}
```

## 8. Performance Optimizations

### Parallel Execution Strategies

1. **Information Gathering**: All information requests are processed in parallel
2. **Tool Execution**: Compatible tools can execute simultaneously
3. **Context Updates**: Asynchronous context propagation
4. **Session Management**: In-memory caching with Redis fallback

## 9. Audit and Monitoring

### Complete Flow Tracking

```java
// Audit logging for each step
thinkingService.logAnalysis(sessionId, eventType, response);

// Tool execution logging
session.addToolCall(result);

// Performance metrics
long executionTime = System.currentTimeMillis() - startTime;
```

## Benefits of This Architecture

1. **Dynamic Decision Making**: LLM can adapt based on gathered information
2. **Parallel Processing**: Information gathering happens simultaneously
3. **Context Preservation**: All context is maintained throughout the flow
4. **Error Recovery**: Graceful handling of failures at any step
5. **Audit Trail**: Complete tracking of all decisions and actions
6. **Flexibility**: Supports both hybrid and full autonomous modes
7. **Scalability**: Parallel execution and efficient resource usage

This multi-step flow enables the AI system to make intelligent, context-aware decisions while maintaining full auditability and control over the process. 