# Spring AI Function Calling - Component Diagram

## High-Level Component Architecture

```mermaid
graph TB
    subgraph "Frontend Components"
        UI[Workflow UI]
        Viz[Workflow Visualization]
        Demo[Demo Component]
    end
    
    subgraph "API Controllers"
        WC[Workflow Controller]
        AIC[AI Controller]
    end
    
    subgraph "Service Layer"
        EWS[Enhanced Dynamic Workflow Service]
        FCS[Function Calling Service]
        WHS[Workflow History Service]
    end
    
    subgraph "AI Layer"
        LLM[Spring AI ChatModel]
        PT[Prompt Templates]
    end
    
    subgraph "Tool Management"
        TR[Tool Registry]
        TI[Tool Interface]
        TRES[Tool Result]
    end
    
    subgraph "Tool Implementations"
        RCT[Range Checker Tool]
        QT[Quadruple Tool]
        HT[Halve Tool]
        CT[Custom Tools]
    end
    
    subgraph "Storage"
        IMH[In-Memory History]
        DB[(Database - Planned)]
    end
    
    UI --> WC
    Viz --> WC
    Demo --> WC
    
    WC --> EWS
    AIC --> EWS
    
    EWS --> FCS
    EWS --> WHS
    EWS --> PT
    
    FCS --> LLM
    FCS --> TR
    FCS --> WHS
    
    TR --> TI
    TI --> TRES
    
    TR --> RCT
    TR --> QT
    TR --> HT
    TR --> CT
    
    RCT --> TRES
    QT --> TRES
    HT --> TRES
    CT --> TRES
    
    WHS --> IMH
    WHS --> DB
```

## Detailed Component Relationships

### 1. Service Layer Dependencies

```mermaid
graph LR
    subgraph "Enhanced Dynamic Workflow Service"
        A[Parameter Validation]
        B[Context Creation]
        C[Prompt Loading]
        D[Workflow Execution]
        E[Result Enhancement]
    end
    
    subgraph "Function Calling Service"
        F[Conversation Management]
        G[Tool Call Parsing]
        H[Tool Execution]
        I[Iteration Control]
    end
    
    subgraph "Workflow History Service"
        J[History Creation]
        K[Entry Management]
        L[Checkpoint Creation]
        M[State Retrieval]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    
    D --> F
    F --> G
    G --> H
    H --> I
    
    D --> J
    J --> K
    K --> L
    L --> M
```

### 2. Tool Registry Architecture

```mermaid
graph TB
    subgraph "Tool Registry"
        A[Application Context]
        B[Tool Discovery]
        C[Tool Registration]
        D[Tool Resolution]
        E[Lifecycle Management]
    end
    
    subgraph "Tool Interface"
        F[getName()]
        G[getDescription()]
        H[getParameterSchema()]
        I[execute()]
    end
    
    subgraph "Tool Implementations"
        J[Range Checker]
        K[Quadruple]
        L[Halve]
        M[Custom Tools]
    end
    
    subgraph "Spring Context"
        N[@Component]
        O[@Service]
        P[@Repository]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    
    C --> F
    C --> G
    C --> H
    C --> I
    
    N --> J
    O --> K
    P --> L
    N --> M
```

### 3. Data Flow Components

```mermaid
graph LR
    subgraph "Input Processing"
        A[User Input]
        B[Parameter Validation]
        C[Context Creation]
    end
    
    subgraph "AI Processing"
        D[Prompt Rendering]
        E[LLM Communication]
        F[Response Parsing]
    end
    
    subgraph "Tool Execution"
        G[Tool Resolution]
        H[Parameter Validation]
        I[Tool Execution]
        J[Result Processing]
    end
    
    subgraph "Output Generation"
        K[History Update]
        L[Result Enhancement]
        M[Response Formatting]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
    L --> M
```

## Component Responsibilities

### Enhanced Dynamic Workflow Service
- **Parameter Validation**: Ensures input parameters are valid
- **Context Creation**: Creates tool execution context
- **Prompt Management**: Loads and renders prompt templates
- **Workflow Orchestration**: Coordinates the entire workflow execution
- **Result Enhancement**: Adds metadata and history to results

### Function Calling Service
- **Conversation Management**: Maintains LLM conversation state
- **Tool Call Parsing**: Extracts tool calls from LLM responses
- **Tool Execution**: Executes tools via registry
- **Iteration Control**: Manages workflow iteration limits
- **Error Handling**: Handles tool execution errors

### Tool Registry
- **Tool Discovery**: Automatically discovers Spring components
- **Tool Registration**: Registers tools with unique names
- **Tool Resolution**: Resolves tool names to implementations
- **Lifecycle Management**: Manages tool registration/removal

### Workflow History Service
- **History Creation**: Creates new workflow histories
- **Entry Management**: Adds different types of history entries
- **Checkpoint Creation**: Creates checkpoints for differential history
- **State Retrieval**: Provides different views of history

## Interface Contracts

### Tool Interface
```java
public interface Tool {
    String getName();                    // Unique tool identifier
    String getDescription();             // Human-readable description
    Map<String, Object> getParameterSchema(); // Parameter definition
    ToolResult execute(Map<String, Object> parameters); // Execution logic
}
```

### Tool Result
```java
public class ToolResult {
    private boolean success;             // Execution success flag
    private Object data;                 // Result data
    private String errorMessage;         // Error message if failed
    private Object metadata;             // Additional metadata
}
```

### Workflow History Entry
```java
public class HistoryEntry {
    private EntryType type;              // Entry type (SYSTEM, USER, etc.)
    private String content;              // Entry content
    private Map<String, Object> metadata; // Additional metadata
    private long timestamp;              // Entry timestamp
}
```

## Configuration Components

### Spring Configuration
```java
@Configuration
public class AIConfiguration {
    
    @Bean
    public ChatModel chatModel() {
        // Configure Spring AI ChatModel
    }
    
    @Bean
    public ToolRegistry toolRegistry(ApplicationContext context) {
        return new ToolRegistry(context);
    }
    
    @Bean
    public FunctionCallingService functionCallingService(
            ChatModel chatModel, 
            ToolRegistry toolRegistry) {
        return new FunctionCallingService(chatModel, toolRegistry);
    }
}
```

### Prompt Template Configuration
```yaml
# application.yml
spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      chat:
        options:
          model: gpt-4
          temperature: 0.1
          max-tokens: 2000
```

## Error Handling Components

### Error Flow
```mermaid
graph TD
    A[Tool Execution] --> B{Success?}
    B -->|Yes| C[Return Success]
    B -->|No| D[Log Error]
    D --> E[Create Error Result]
    E --> F[Add to History]
    F --> G{Retry Allowed?}
    G -->|Yes| H[Retry Tool]
    G -->|No| I[Return Error to LLM]
    H --> A
    I --> J[LLM Decides Next Action]
```

### Error Types
- **Tool Execution Errors**: Tool-specific failures
- **Parameter Validation Errors**: Invalid input parameters
- **LLM Response Errors**: Malformed LLM responses
- **Registry Errors**: Tool not found or registration issues
- **History Errors**: Storage or retrieval failures

## Performance Considerations

### Caching Strategy
```mermaid
graph LR
    subgraph "Caching Layers"
        A[Tool Result Cache]
        B[Prompt Template Cache]
        C[Tool Registry Cache]
        D[History Cache]
    end
    
    subgraph "Cache Keys"
        E[Tool + Parameters]
        F[Prompt Template Name]
        G[Tool Name]
        H[Session ID]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
```

### Optimization Points
- **Tool Result Caching**: Cache tool results for repeated calls
- **Prompt Template Caching**: Cache rendered prompt templates
- **Tool Registry Caching**: Cache tool lookups
- **History Optimization**: Efficient history storage and retrieval

## Security Components

### Security Layers
```mermaid
graph TB
    subgraph "Security Model"
        A[LLM Decision Only]
        B[App Execution Control]
        C[Parameter Validation]
        D[Tool Isolation]
        E[Access Control]
    end
    
    subgraph "Protection Mechanisms"
        F[No Direct Code Execution]
        G[Controlled Tool Access]
        H[Input Sanitization]
        I[Error Containment]
        J[Audit Logging]
    end
    
    A --> F
    B --> G
    C --> H
    D --> I
    E --> J
```

This component diagram provides a comprehensive view of the Spring AI Function Calling architecture, showing all major components, their relationships, and responsibilities. 