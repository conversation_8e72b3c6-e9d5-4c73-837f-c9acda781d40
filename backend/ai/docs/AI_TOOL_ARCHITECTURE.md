# AI Tool Architecture - Enhanced

## Overview

This document describes the enhanced AI tool architecture that supports **parallel execution**, **user-configurable autonomy levels**, and **asymmetric tool categorization** for maximum flexibility and efficiency.

## Key Enhancements

### 1. **Parallel Tool Execution**
- **Information gathering tools** (system, readonly, analysis) can execute in parallel
- **Business impact tools** execute sequentially to maintain data consistency
- **Automatic grouping** of compatible tools for optimal performance
- **Example**: "Request similar tickets" and "Get customer context" run simultaneously

### 2. **Enhanced Tool Categorization**
The categorization is now **asymmetric** and **user-configurable**:

| Category | Description | Parallel Execution | Default Approval | Configurable |
|----------|-------------|-------------------|------------------|--------------|
| **SYSTEM_TOOLS** | Information gathering, analysis | ✅ Yes | Never | ✅ Yes |
| **READONLY_TOOLS** | Data retrieval (any category) | ✅ Yes | Never | ✅ Yes |
| **ANALYSIS_TOOLS** | AI classification, metadata updates | ✅ Yes | Never | ✅ Yes |
| **BUSINESS_TOOLS** | Core business operations | ❌ No | Configurable | ✅ Yes |
| **CRITICAL_BUSINESS_TOOLS** | High-impact business changes | ❌ No | Always | ✅ Yes (with warnings) |
| **ADMIN_TOOLS** | Administrative tasks | ❌ No | Never (unless admin) | ✅ Yes |
| **CRITICAL_ADMIN_TOOLS** | System-wide changes | ❌ No | Always | ✅ Yes (strict validation) |

### 3. **User-Configurable Autonomy Slider**
Support org admins can configure approval requirements:

#### **Autonomy Modes**
- **FULL_MCP**: Maximum autonomy (only critical tools require approval)
- **HYBRID**: Balanced autonomy (business and critical tools require approval)
- **LOW_AUTONOMY**: Minimal autonomy (most tools require approval)

#### **Configuration Options**
- **Per-category settings**: Configure approval requirements for each tool category
- **Per-operation settings**: Configure approval for specific operation types
- **Mode-specific settings**: Different settings for each autonomy mode
- **Organization-specific**: Each organization can have its own configuration

## Architecture Components

### 1. **ParallelToolExecutor**
```java
@Service
public class ParallelToolExecutor {
    public Map<String, ToolResult> executeTools(List<ToolCall> toolCalls, ToolContext context);
    public List<ToolCall> optimizeExecutionOrder(List<ToolCall> toolCalls);
}
```

**Features:**
- Groups tools by execution compatibility
- Executes compatible groups in parallel
- Handles failures gracefully
- Optimizes execution order for performance

### 2. **AutonomyConfigurationService**
```java
@Service
public class AutonomyConfigurationService {
    public void updateCategoryApproval(Long orgId, ToolCategory category, AIAutonomyMode mode, boolean required);
    public void updateOperationTypeApproval(Long orgId, String operationType, boolean required);
    public Map<String, Object> getRecommendedConfiguration(Long orgId, String orgType, int ticketVolume);
}
```

**Features:**
- User-configurable approval requirements
- Organization-specific settings
- Recommended configurations based on org type/volume
- Validation of dangerous combinations

### 3. **Enhanced ToolCategory**
```java
public enum ToolCategory {
    SYSTEM_TOOLS(ApprovalRequirement.NEVER, true, Set.of()),
    BUSINESS_TOOLS(ApprovalRequirement.CONFIGURABLE, false, Set.of("ticket_assignment")),
    CRITICAL_BUSINESS_TOOLS(ApprovalRequirement.ALWAYS, false, Set.of("ticket_closure"));
}
```

**Features:**
- Asymmetric categorization
- Parallel execution flags
- Operation type definitions
- Configurable approval requirements

## Workflow Example: Enhanced Multi-Step Processing

### **Scenario**: Ticket Created → LLM Analysis → Parallel Information Gathering → Classification → Assignment

```java
// 1. LLM analyzes ticket and requests information
List<ToolCall> informationRequests = Arrays.asList(
    new ToolCall("similar_tickets", Map.of("ticket_id", 123), "Find similar tickets"),
    new ToolCall("customer_context", Map.of("customer_id", 456), "Get customer history"),
    new ToolCall("team_availability", Map.of("team_id", 789), "Check team workload")
);

// 2. Parallel execution of information gathering
Map<String, ToolResult> infoResults = parallelExecutor.executeTools(informationRequests, context);
// All three tools execute simultaneously for better performance

// 3. LLM receives results and makes classification decision
ToolCall classificationCall = new ToolCall("classify_ticket", 
    Map.of("ticket_id", 123, "context", infoResults), "Classify ticket priority");

// 4. Sequential execution of business impact tools
List<ToolCall> businessActions = Arrays.asList(
    classificationCall,
    new ToolCall("assign_ticket", Map.of("ticket_id", 123, "assignee_id", 456), "Assign ticket")
);

Map<String, ToolResult> businessResults = parallelExecutor.executeTools(businessActions, context);
// These execute sequentially to maintain data consistency
```

## Configuration Examples

### **Enterprise Organization (High Volume, Conservative)**
```java
// Recommended settings
Map<String, Object> recommendations = autonomyService.getRecommendedConfiguration(
    orgId, "enterprise", 1500);

// Results in:
// - Default mode: HYBRID
// - Business tools approval: true
// - Critical tools approval: true
// - Parallel execution: true
```

### **Startup Organization (Low Volume, Autonomous)**
```java
// Recommended settings
Map<String, Object> recommendations = autonomyService.getRecommendedConfiguration(
    orgId, "startup", 50);

// Results in:
// - Default mode: FULL_MCP
// - Business tools approval: false
// - Critical tools approval: true
// - Parallel execution: true
```

### **Custom Configuration**
```java
// Support org admin customizes settings
autonomyService.updateCategoryApproval(orgId, userId, 
    ToolCategory.BUSINESS_TOOLS, AIAutonomyMode.HYBRID, false);

autonomyService.updateOperationTypeApproval(orgId, userId, 
    "ticket_assignment", false);

// Now ticket assignments don't require approval in hybrid mode
```

## Benefits

### **1. Performance**
- **Parallel execution** reduces total processing time
- **Optimized grouping** maximizes efficiency
- **Information gathering** happens simultaneously

### **2. Flexibility**
- **User-configurable** approval requirements
- **Organization-specific** settings
- **Mode-specific** configurations
- **Operation-type** granular control

### **3. Safety**
- **Validation** of dangerous configurations
- **Warnings** for critical changes
- **Strict validation** for admin operations
- **Graceful failure** handling

### **4. Scalability**
- **Organization-specific** configurations
- **Volume-based** recommendations
- **Type-based** defaults
- **Extensible** categorization

## Implementation Guidelines

### **Creating New Tools**
```java
@Component
public class MyTool implements AITool {
    @Override
    public ToolCategory getToolCategory() {
        return ToolCategory.SYSTEM_TOOLS; // Can execute in parallel
    }
    
    @Override
    public boolean canExecuteInParallel() {
        return true; // Override if needed
    }
}
```

### **Configuring Autonomy**
```java
// In your service
@Autowired
private AutonomyConfigurationService autonomyService;

public void processWithApproval(ToolCall toolCall, Long orgId) {
    ToolCategory category = toolRegistry.getToolById(toolCall.getToolId())
        .map(AITool::getToolCategory)
        .orElse(ToolCategory.SYSTEM_TOOLS);
    
    AIAutonomyMode currentMode = getCurrentMode(orgId);
    
    if (autonomyService.requiresApproval(orgId, category, currentMode)) {
        // Request approval
        requestApproval(toolCall);
    } else {
        // Execute directly
        executeTool(toolCall);
    }
}
```

### **Parallel Execution**
```java
// Group compatible tools
List<ToolCall> parallelTools = toolCalls.stream()
    .filter(tc -> toolRegistry.getToolById(tc.getToolId())
        .map(tool -> tool.getToolCategory().canExecuteInParallel())
        .orElse(false))
    .collect(Collectors.toList());

// Execute in parallel
Map<String, ToolResult> results = parallelExecutor.executeTools(parallelTools, context);
```

## Migration Path

### **From Current System**
1. **Update existing tools** to implement new categorization
2. **Add parallel execution** flags where appropriate
3. **Implement autonomy configuration** service
4. **Update orchestrators** to use parallel execution
5. **Configure organization settings** based on needs

### **Gradual Rollout**
1. **Start with system tools** (parallel execution)
2. **Add business tools** (configurable approval)
3. **Enable user configuration** (autonomy slider)
4. **Optimize based on usage** (performance tuning)

## Conclusion

This enhanced architecture provides:
- **Maximum efficiency** through parallel execution
- **Maximum flexibility** through user configuration
- **Maximum safety** through validation and warnings
- **Maximum scalability** through organization-specific settings

The key insight is that **all operations should be tools**, but **not all tools require approval**, and **not all tools need to execute sequentially**. This enables powerful, auditable, and efficient AI workflows that adapt to each organization's needs. 