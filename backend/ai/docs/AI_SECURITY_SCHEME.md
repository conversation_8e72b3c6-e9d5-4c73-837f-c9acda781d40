# AI Service Security Scheme

## Overview

The AI service implements a dual authentication system to handle both user-initiated actions and system-triggered operations securely.

## Authentication Methods

### 1. API Key Authentication (System-to-System)
Used for automated operations like ticket routing, AI suggestions, etc.

**Headers:**
```
X-API-Key: your_api_key
X-API-Secret: your_api_secret
```

**Usage:**
- Main app → AI service communication
- Automated ticket routing
- Background AI processing
- System-initiated operations

### 2. JWT Token Authentication (User-Initiated)
Used when users interact with AI features directly.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Usage:**
- User accepts AI suggestions
- User requests AI analysis
- User-initiated AI operations

## Security Architecture

### Components

1. **AISecurityConfig**: Configures security filters and endpoints
2. **AIApiKeyAuthenticationFilter**: Handles API key/secret authentication
3. **AIJwtAuthenticationFilter**: Handles JWT token authentication
4. **AIAuthenticationService**: Core authentication logic
5. **SXMainAppClient**: Communicates with main app for user validation

### Authentication Flow

#### Scenario 1: User Accepts AI Suggestion
```
1. User logs into main app → Gets JWT token
2. User clicks "Accept AI Suggestion" in UI
3. Frontend sends request to AI service with JWT token
4. AI service validates JWT with main app
5. AI service processes request with user context
6. AI service updates ticket on behalf of user
```

#### Scenario 2: System Routes Ticket Automatically
```
1. New ticket created in main app
2. Main app sends request to AI service with API key/secret
3. AI service validates API credentials
4. AI service gets SX bot user for organization
5. AI service processes routing with bot user context
6. AI service updates ticket on behalf of SX bot
```

## Configuration

### AI Service Properties
```properties
# Main app communication
sx.main-app.base-url=http://localhost:8080
sx.main-app.api-key=key
sx.main-app.api-secret=secret

# Security
spring.security.enabled=true
```

### Main App Properties
```properties
# AI service client
sx.client.ai.api-key=key
sx.client.ai.api-secret=secret
sx.client.ai.base-url=http://localhost:8880/ai
```

## Usage Examples

### 1. User Accepts AI Suggestion
```bash
curl -X POST http://localhost:8880/ai/api/ai/suggestions/accept \
  -H "Authorization: Bearer <user_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "suggestionId": "123",
    "ticketId": "456",
    "action": "accept"
  }'
```

### 2. System Routes Ticket
```bash
curl -X POST http://localhost:8880/ai/api/ai/routing/route \
  -H "X-API-Key: key" \
  -H "X-API-Secret: secret" \
  -H "Content-Type: application/json" \
  -d '{
    "ticketId": "789",
    "organizationId": "1",
    "ticketData": {...}
  }'
```

### 3. Validate API Credentials
```bash
curl -X POST http://localhost:8880/ai/api/ai/auth/validate \
  -H "X-API-Key: key" \
  -H "X-API-Secret: secret"
```

## Security Features

### 1. Dual Authentication
- **API Key**: For system operations
- **JWT Token**: For user operations

### 2. User Context Preservation
- User-initiated actions maintain user context
- System actions use SX bot user context
- Proper audit trail for all operations

### 3. Organization Isolation
- All operations are scoped to organization
- SX bot users are organization-specific
- Cross-organization access is prevented

### 4. Audit Trail
- All AI operations are logged
- User context is preserved in logs
- Operation timestamps and details recorded

## Error Handling

### Authentication Errors
```json
{
  "error": "Authentication failed",
  "timestamp": 1234567890,
  "details": "Invalid API credentials"
}
```

### Authorization Errors
```json
{
  "error": "Access denied",
  "timestamp": 1234567890,
  "details": "Insufficient permissions for AI_ACCESS"
}
```

## Best Practices

1. **Always use HTTPS in production**
2. **Rotate API keys regularly**
3. **Monitor authentication logs**
4. **Use least privilege principle**
5. **Validate all inputs**
6. **Log all AI operations**

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check API key/secret or JWT token
2. **403 Forbidden**: Check user permissions and scopes
3. **500 Internal Server Error**: Check main app connectivity

### Debug Mode
Enable debug logging:
```properties
logging.level.io.sx.ai.security=DEBUG
logging.level.io.sx.ai.service=DEBUG
``` 