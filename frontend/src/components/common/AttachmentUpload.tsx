import React, { useState, useCallback } from 'react';
import { Upload, Button, Progress, Alert, List, Typography, Space, message } from 'antd';
import { UploadOutlined, DeleteOutlined, InboxOutlined } from '@ant-design/icons';
import { RcFile, UploadFile } from 'antd/es/upload/interface';
import { 
  AttachmentUploadProps, 
  Attachment, 
  UploadProgress, 
  validateFile,
  formatFileSize,
  MAX_FILE_SIZE,
  BLOCKED_FILE_EXTENSIONS 
} from '../../types/attachment';
import attachmentService from '../../services/AttachmentService';
import AttachmentIcon from './AttachmentIcon';

const { Dragger } = Upload;
const { Text } = Typography;

/**
 * Drag-and-drop file upload component with progress indicators and validation
 */
const AttachmentUpload: React.FC<AttachmentUploadProps> = ({
  entityType,
  entityId,
  maxFiles = 10,
  allowedFileTypes,
  showFileList = true,
  onUploadComplete,
  onUploadError,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, UploadProgress>>({});
  const [uploading, setUploading] = useState(false);
  const [uploadedAttachments, setUploadedAttachments] = useState<Attachment[]>([]);

  // Handle file selection and validation
  const beforeUpload = useCallback((file: RcFile): boolean => {
    // Validate file
    const validationError = validateFile(file);
    if (validationError) {
      message.error(validationError.message);
      return false;
    }

    // Check max files limit
    if (fileList.length >= maxFiles) {
      message.error(`Maximum ${maxFiles} files allowed`);
      return false;
    }

    // Check allowed file types if specified
    if (allowedFileTypes && allowedFileTypes.length > 0) {
      const extension = file.name.split('.').pop()?.toLowerCase();
      if (!extension || !allowedFileTypes.includes(extension)) {
        message.error(`File type not allowed. Allowed types: ${allowedFileTypes.join(', ')}`);
        return false;
      }
    }

    return false; // Prevent automatic upload
  }, [fileList.length, maxFiles, allowedFileTypes]);

  // Handle file list changes
  const handleChange = useCallback((info: any) => {
    setFileList(info.fileList);
  }, []);

  // Handle manual upload
  const handleUpload = useCallback(async () => {
    if (fileList.length === 0) {
      message.warning('Please select files to upload');
      return;
    }

    setUploading(true);
    const progressMap: Record<string, UploadProgress> = {};

    try {
      // Initialize progress for all files
      fileList.forEach(file => {
        if (file.originFileObj) {
          progressMap[file.name] = {
            filename: file.name,
            progress: 0,
            status: 'uploading',
          };
        }
      });
      setUploadProgress(progressMap);

      // Upload files
      const uploadPromises = fileList.map(async (file) => {
        if (!file.originFileObj) return null;

        try {
          const attachment = await attachmentService.uploadAttachment({
            file: file.originFileObj,
            entityType,
            entityId,
          });

          // Update progress
          progressMap[file.name] = {
            filename: file.name,
            progress: 100,
            status: 'success',
          };
          setUploadProgress({ ...progressMap });

          return attachment;
        } catch (error) {
          // Update progress with error
          progressMap[file.name] = {
            filename: file.name,
            progress: 0,
            status: 'error',
            error: error instanceof Error ? error.message : 'Upload failed',
          };
          setUploadProgress({ ...progressMap });
          throw error;
        }
      });

      const results = await Promise.allSettled(uploadPromises);
      const successfulUploads = results
        .filter((result): result is PromiseFulfilledResult<Attachment> => 
          result.status === 'fulfilled' && result.value !== null
        )
        .map(result => result.value);

      const failedUploads = results.filter(result => result.status === 'rejected');

      if (successfulUploads.length > 0) {
        setUploadedAttachments(prev => [...prev, ...successfulUploads]);
        message.success(`Successfully uploaded ${successfulUploads.length} file(s)`);
        
        if (onUploadComplete) {
          onUploadComplete(successfulUploads);
        }
      }

      if (failedUploads.length > 0) {
        message.error(`Failed to upload ${failedUploads.length} file(s)`);
        
        if (onUploadError) {
          onUploadError(`Failed to upload ${failedUploads.length} file(s)`);
        }
      }

      // Clear file list after successful uploads
      if (successfulUploads.length > 0) {
        setFileList([]);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      message.error(errorMessage);
      
      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setUploading(false);
      // Clear progress after a delay
      setTimeout(() => {
        setUploadProgress({});
      }, 3000);
    }
  }, [fileList, entityType, entityId, onUploadComplete, onUploadError]);

  // Remove file from list
  const handleRemove = useCallback((file: UploadFile) => {
    setFileList(prev => prev.filter(f => f.uid !== file.uid));
  }, []);

  // Get upload restrictions text
  const getRestrictionsText = () => {
    const restrictions = [];
    restrictions.push(`Max size: ${formatFileSize(MAX_FILE_SIZE)}`);
    restrictions.push(`Max files: ${maxFiles}`);
    
    if (allowedFileTypes && allowedFileTypes.length > 0) {
      restrictions.push(`Allowed: ${allowedFileTypes.join(', ')}`);
    } else {
      restrictions.push(`Blocked: ${BLOCKED_FILE_EXTENSIONS.join(', ')}`);
    }
    
    return restrictions.join(' • ');
  };

  return (
    <div className="attachment-upload">
      <Dragger
        multiple
        beforeUpload={beforeUpload}
        onChange={handleChange}
        fileList={fileList}
        onRemove={handleRemove}
        showUploadList={showFileList}
        disabled={uploading}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">Click or drag files to this area to upload</p>
        <p className="ant-upload-hint">
          {getRestrictionsText()}
        </p>
      </Dragger>

      {fileList.length > 0 && (
        <div style={{ marginTop: 16 }}>
          <Button
            type="primary"
            icon={<UploadOutlined />}
            onClick={handleUpload}
            loading={uploading}
            disabled={fileList.length === 0}
          >
            Upload {fileList.length} file(s)
          </Button>
        </div>
      )}

      {/* Upload Progress */}
      {Object.keys(uploadProgress).length > 0 && (
        <div style={{ marginTop: 16 }}>
          <Typography.Title level={5}>Upload Progress</Typography.Title>
          {Object.values(uploadProgress).map((progress) => (
            <div key={progress.filename} style={{ marginBottom: 8 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                <Text>{progress.filename}</Text>
                <Text type={progress.status === 'error' ? 'danger' : 'secondary'}>
                  {progress.status === 'success' ? 'Complete' : 
                   progress.status === 'error' ? 'Failed' : 
                   `${progress.progress}%`}
                </Text>
              </div>
              <Progress
                percent={progress.progress}
                status={progress.status === 'error' ? 'exception' : 
                       progress.status === 'success' ? 'success' : 'active'}
                showInfo={false}
                size="small"
              />
              {progress.error && (
                <Text type="danger" style={{ fontSize: '12px' }}>
                  {progress.error}
                </Text>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Recently Uploaded Files */}
      {uploadedAttachments.length > 0 && (
        <div style={{ marginTop: 16 }}>
          <Typography.Title level={5}>Recently Uploaded</Typography.Title>
          <List
            size="small"
            dataSource={uploadedAttachments}
            renderItem={(attachment) => (
              <List.Item>
                <Space>
                  <AttachmentIcon attachment={attachment} size="small" />
                  <Text>{attachment.originalFilename || attachment.fileUrl}</Text>
                  <Text type="secondary">({attachment.formattedFileSize})</Text>
                </Space>
              </List.Item>
            )}
          />
        </div>
      )}
    </div>
  );
};

export default AttachmentUpload;
