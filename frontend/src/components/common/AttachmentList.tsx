import React, { useState, useEffect, useCallback } from 'react';
import { 
  List, 
  Button, 
  Space, 
  Typography, 
  Checkbox, 
  Popconfirm, 
  message, 
  Empty, 
  Spin,
  Card,
  Divider
} from 'antd';
import { 
  DownloadOutlined, 
  DeleteOutlined, 
  CloudDownloadOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { 
  AttachmentListProps, 
  Attachment 
} from '../../types/attachment';
import attachmentService from '../../services/AttachmentService';
import AttachmentIcon from './AttachmentIcon';
import AttachmentUpload from './AttachmentUpload';

const { Text, Title } = Typography;

/**
 * Component to display attachments with download and bulk operations
 */
const AttachmentList: React.FC<AttachmentListProps> = ({
  entityType,
  entityId,
  showUpload = true,
  showBulkDownload = true,
  maxFiles = 10,
  allowedFileTypes,
  onAttachmentAdded,
  onAttachmentRemoved,
}) => {
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAttachments, setSelectedAttachments] = useState<number[]>([]);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [bulkDownloading, setBulkDownloading] = useState(false);

  // Load attachments
  const loadAttachments = useCallback(async () => {
    try {
      setLoading(true);
      const data = await attachmentService.getAttachmentsByEntity(entityType, entityId);
      setAttachments(data);
    } catch (error) {
      console.error('Failed to load attachments:', error);
      message.error('Failed to load attachments');
    } finally {
      setLoading(false);
    }
  }, [entityType, entityId]);

  useEffect(() => {
    loadAttachments();
  }, [loadAttachments]);

  // Handle individual download
  const handleDownload = useCallback(async (attachment: Attachment) => {
    try {
      await attachmentService.downloadAttachment(attachment.id, attachment.originalFilename);
      message.success('Download started');
    } catch (error) {
      console.error('Download failed:', error);
      message.error('Download failed');
    }
  }, []);

  // Handle individual delete
  const handleDelete = useCallback(async (attachment: Attachment) => {
    try {
      await attachmentService.deleteAttachment(attachment.id);
      setAttachments(prev => prev.filter(a => a.id !== attachment.id));
      message.success('Attachment deleted');
      
      if (onAttachmentRemoved) {
        onAttachmentRemoved(attachment.id);
      }
    } catch (error) {
      console.error('Delete failed:', error);
      message.error('Failed to delete attachment');
    }
  }, [onAttachmentRemoved]);

  // Handle bulk download
  const handleBulkDownload = useCallback(async () => {
    if (selectedAttachments.length === 0) {
      message.warning('Please select attachments to download');
      return;
    }

    try {
      setBulkDownloading(true);
      await attachmentService.bulkDownloadByIds({
        attachmentIds: selectedAttachments,
        filename: `${entityType}_${entityId}_attachments.zip`,
      });
      message.success('Bulk download started');
      setSelectedAttachments([]);
    } catch (error) {
      console.error('Bulk download failed:', error);
      message.error('Bulk download failed');
    } finally {
      setBulkDownloading(false);
    }
  }, [selectedAttachments, entityType, entityId]);

  // Handle download all
  const handleDownloadAll = useCallback(async () => {
    if (attachments.length === 0) {
      message.warning('No attachments to download');
      return;
    }

    try {
      setBulkDownloading(true);
      await attachmentService.bulkDownloadByEntity({
        entityType,
        entityId,
        filename: `${entityType}_${entityId}_all_attachments.zip`,
      });
      message.success('Download started');
    } catch (error) {
      console.error('Download all failed:', error);
      message.error('Download failed');
    } finally {
      setBulkDownloading(false);
    }
  }, [attachments.length, entityType, entityId]);

  // Handle selection change
  const handleSelectionChange = useCallback((attachmentId: number, checked: boolean) => {
    setSelectedAttachments(prev => 
      checked 
        ? [...prev, attachmentId]
        : prev.filter(id => id !== attachmentId)
    );
  }, []);

  // Handle select all
  const handleSelectAll = useCallback((checked: boolean) => {
    setSelectedAttachments(checked ? attachments.map(a => a.id) : []);
  }, [attachments]);

  // Handle upload complete
  const handleUploadComplete = useCallback((newAttachments: Attachment[]) => {
    setAttachments(prev => [...prev, ...newAttachments]);
    setShowUploadForm(false);
    
    if (onAttachmentAdded) {
      newAttachments.forEach(attachment => onAttachmentAdded(attachment));
    }
  }, [onAttachmentAdded]);

  // Format file info
  const formatFileInfo = (attachment: Attachment) => {
    const parts = [];
    
    if (attachment.fileExtension) {
      parts.push(attachment.fileExtension.toUpperCase());
    }
    
    if (attachment.formattedFileSize) {
      parts.push(attachment.formattedFileSize);
    }
    
    return parts.join(' • ');
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>Loading attachments...</div>
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>Attachments ({attachments.length})</span>
          <Space>
            {showUpload && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                size="small"
                onClick={() => setShowUploadForm(!showUploadForm)}
                disabled={attachments.length >= maxFiles}
              >
                Add Files
              </Button>
            )}
            {showBulkDownload && attachments.length > 0 && (
              <Button
                icon={<CloudDownloadOutlined />}
                size="small"
                onClick={handleDownloadAll}
                loading={bulkDownloading}
              >
                Download All
              </Button>
            )}
          </Space>
        </div>
      }
      size="small"
    >
      {/* Upload Form */}
      {showUploadForm && (
        <>
          <AttachmentUpload
            entityType={entityType}
            entityId={entityId}
            maxFiles={maxFiles - attachments.length}
            allowedFileTypes={allowedFileTypes}
            onUploadComplete={handleUploadComplete}
            onUploadError={() => setShowUploadForm(false)}
          />
          <Divider />
        </>
      )}

      {/* Bulk Actions */}
      {showBulkDownload && attachments.length > 0 && (
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Checkbox
              checked={selectedAttachments.length === attachments.length}
              indeterminate={selectedAttachments.length > 0 && selectedAttachments.length < attachments.length}
              onChange={(e) => handleSelectAll(e.target.checked)}
            >
              Select All
            </Checkbox>
            {selectedAttachments.length > 0 && (
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                size="small"
                onClick={handleBulkDownload}
                loading={bulkDownloading}
              >
                Download Selected ({selectedAttachments.length})
              </Button>
            )}
          </Space>
        </div>
      )}

      {/* Attachments List */}
      {attachments.length === 0 ? (
        <Empty 
          description="No attachments"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <List
          dataSource={attachments}
          renderItem={(attachment) => (
            <List.Item
              actions={[
                <Button
                  type="text"
                  icon={<DownloadOutlined />}
                  size="small"
                  onClick={() => handleDownload(attachment)}
                  title="Download"
                />,
                <Popconfirm
                  title="Are you sure you want to delete this attachment?"
                  onConfirm={() => handleDelete(attachment)}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    size="small"
                    danger
                    title="Delete"
                  />
                </Popconfirm>,
              ]}
            >
              <List.Item.Meta
                avatar={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {showBulkDownload && (
                      <Checkbox
                        checked={selectedAttachments.includes(attachment.id)}
                        onChange={(e) => handleSelectionChange(attachment.id, e.target.checked)}
                        style={{ marginRight: 8 }}
                      />
                    )}
                    <AttachmentIcon attachment={attachment} />
                  </div>
                }
                title={
                  <Text strong>
                    {attachment.originalFilename || attachment.fileUrl}
                  </Text>
                }
                description={
                  <Space direction="vertical" size={0}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {formatFileInfo(attachment)}
                    </Text>
                    {attachment.description && (
                      <Text style={{ fontSize: '12px' }}>
                        {attachment.description}
                      </Text>
                    )}
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      Uploaded by {attachment.createdBy} • {new Date(attachment.createdAt).toLocaleDateString()}
                    </Text>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Card>
  );
};

export default AttachmentList;
