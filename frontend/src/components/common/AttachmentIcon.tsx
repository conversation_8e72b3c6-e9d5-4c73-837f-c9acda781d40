import React from 'react';
import { Tooltip } from 'antd';
import {
  FileImageOutlined,
  FileTextOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  FileZipOutlined,
  CodeOutlined,
  FileOutlined,
} from '@ant-design/icons';
import {
  Attachment,
  AttachmentIconProps,
  FILE_TYPE_CONFIG,
  getFileTypeCategory,
  formatFileSize,
  FileTypeCategory
} from '../../types/attachment';

/**
 * Component to display appropriate icons for different file types
 */
const AttachmentIcon: React.FC<AttachmentIconProps> = ({
  attachment,
  size = 'default',
  showTooltip = true,
}) => {
  // Determine file type category
  const category = attachment.fileTypeCategory || 
    getFileTypeCategory(attachment.originalFilename || attachment.fileUrl);
  
  const config = FILE_TYPE_CONFIG[category as FileTypeCategory];
  
  // Icon size mapping
  const iconSizes = {
    small: { fontSize: '14px' },
    default: { fontSize: '16px' },
    large: { fontSize: '20px' },
  };
  
  // Get appropriate icon component
  const getIconComponent = () => {
    const iconStyle = {
      color: config.color,
      ...iconSizes[size],
    };
    
    switch (config.icon) {
      case 'file-image':
        return <FileImageOutlined style={iconStyle} />;
      case 'file-text':
        return <FileTextOutlined style={iconStyle} />;
      case 'video-camera':
        return <VideoCameraOutlined style={iconStyle} />;
      case 'audio':
        return <AudioOutlined style={iconStyle} />;
      case 'file-zip':
        return <FileZipOutlined style={iconStyle} />;
      case 'code':
        return <CodeOutlined style={iconStyle} />;
      default:
        return <FileOutlined style={iconStyle} />;
    }
  };
  
  // Create tooltip content
  const getTooltipContent = () => {
    const parts = [];
    
    if (attachment.originalFilename) {
      parts.push(`File: ${attachment.originalFilename}`);
    }
    
    if (attachment.fileExtension) {
      parts.push(`Type: ${attachment.fileExtension.toUpperCase()}`);
    }
    
    if (attachment.formattedFileSize) {
      parts.push(`Size: ${attachment.formattedFileSize}`);
    } else if (attachment.fileSize) {
      parts.push(`Size: ${formatFileSize(attachment.fileSize)}`);
    }
    
    if (attachment.contentType) {
      parts.push(`MIME: ${attachment.contentType}`);
    }
    
    return parts.join('\n');
  };
  
  const iconComponent = getIconComponent();
  
  if (!showTooltip) {
    return iconComponent;
  }
  
  return (
    <Tooltip title={<pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>{getTooltipContent()}</pre>}>
      {iconComponent}
    </Tooltip>
  );
};

export default AttachmentIcon;
