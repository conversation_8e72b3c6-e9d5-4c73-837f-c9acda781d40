import React, { useState, useEffect } from 'react';
import { ticketService } from '../../../services/ticketService';
import { TicketStatus, TicketPriority } from '../../../types/ticket';
import { useAuth } from '../../../contexts/AuthContext';
import { Input, TextArea } from '../../common/Input';
import { Select } from '../../common/Select';
import { Button } from '../../common/Button';
import { Alert } from '../../common/Alert';
import { organizationService } from '../../../services/OrganizationService';
import { Organization } from '../../../types/organization';
import { RoleName } from '../../../types/permission';
import AttachmentUpload from '../../common/AttachmentUpload';
import { Attachment } from '../../../types/attachment';

interface CreateTicketProps {
    onSuccess: () => void;
    onCancel: () => void;
}

const CreateTicket: React.FC<CreateTicketProps> = ({ onSuccess, onCancel }) => {
    const { user, hasRole } = useAuth();
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        status: TicketStatus.OPEN,
        priority: TicketPriority.MEDIUM,
        account: '',
        organizationId: undefined as number | undefined,
        createdBy: user?.email || '',
        assignees: {users:[], groups:[]},
        followers:{users:[], groups:[]}
    });
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const [organizations, setOrganizations] = useState<Organization[]>([]);
    const [loadingOrganizations, setLoadingOrganizations] = useState(false);
    const [createdTicketId, setCreatedTicketId] = useState<number | null>(null);
    const [pendingAttachments, setPendingAttachments] = useState<Attachment[]>([]);

    // Check if user is a support org user (ADMIN, MANAGER, or has MANAGE_TICKETS permission)
    const isSupportOrgUser = hasRole('ADMIN' as RoleName) || 
                            hasRole('MANAGER' as RoleName) || 
                            hasRole('ACCOUNT_OWNER' as RoleName);

    // Fetch organizations if user is a support org user
    useEffect(() => {
        const fetchOrganizations = async () => {
            if (isSupportOrgUser) {
                setLoadingOrganizations(true);
                try {
                    const orgs = await organizationService.getSupportedOrganizationsBySupportOrg();
                    setOrganizations(orgs);
                } catch (err) {
                    console.error('Error fetching organizations:', err);
                    setError('Failed to load organizations');
                } finally {
                    setLoadingOrganizations(false);
                }
            }
        };

        fetchOrganizations();
    }, [isSupportOrgUser]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!user?.email) {
            setError('User email is required');
            return;
        }
        
        // Validate organization selection for support org users
        if (isSupportOrgUser && !formData.organizationId) {
            setError('Account Organization is required for support org users');
            return;
        }
        
        setLoading(true);
        try {
            const createdTicket = await ticketService.createTicket({
                ...formData,
                createdBy: user.email,
            });

            // Set the created ticket ID to show attachment upload
            setCreatedTicketId(parseInt(createdTicket.id));

            // If no pending attachments, complete immediately
            if (pendingAttachments.length === 0) {
                onSuccess();
            }
        } catch (err) {
            setError('Failed to create ticket');
            console.error('Error creating ticket:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSelectChange = (name: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [name]: name === 'organizationId' ? (value ? parseInt(value) : undefined) : value
        }));
    };

    return (
        <div className="p-4">
            {error && <Alert type="error" message={error} className="mb-4" />}
            <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
                    <Input
                        name="title"
                        value={formData.title}
                        onChange={handleChange}
                        required
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <TextArea
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        required
                        rows={4}
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <Select
                        value={formData.status}
                        onChange={(value) => handleSelectChange('status', value)}
                        options={Object.values(TicketStatus).map(status => ({
                            value: status,
                            label: status
                        }))}
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                    <Select
                        value={formData.priority}
                        onChange={(value) => handleSelectChange('priority', value)}
                        options={Object.values(TicketPriority).map(priority => ({
                            value: priority,
                            label: priority
                        }))}
                    />
                </div>

                {isSupportOrgUser && (
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Account Organization
                            <span className="text-red-500 text-xs ml-1">* Required</span>
                        </label>
                        <Select
                            value={formData.organizationId?.toString() || ''}
                            onChange={(value) => handleSelectChange('organizationId', value)}
                            placeholder="Select account organization"
                            loading={loadingOrganizations}
                            showSearch
                            filterOption={(input, option) =>
                                (String(option?.label) ?? '').toLowerCase().includes(input.toLowerCase())
                            }
                            options={organizations
                                .filter(org => org.type === 'ACCOUNT')
                                .map(org => ({
                                    value: org.id?.toString() || '',
                                    label: `${org.name}${org.domain ? ` (${org.domain})` : ''}`
                                }))}
                        />
                    </div>
                )}

                {/* Attachments Section - Show after ticket is created */}
                {createdTicketId && (
                    <div className="mt-6">
                        <h3 className="text-lg font-medium mb-4">Add Attachments (Optional)</h3>
                        <AttachmentUpload
                            entityType="ticket"
                            entityId={createdTicketId}
                            maxFiles={10}
                            showFileList={true}
                            onUploadComplete={(attachments) => {
                                setPendingAttachments(prev => [...prev, ...attachments]);
                            }}
                        />
                        <div className="mt-4 flex justify-end space-x-4">
                            <Button
                                type="default"
                                onClick={onSuccess}
                            >
                                Skip Attachments
                            </Button>
                            <Button
                                type="primary"
                                onClick={onSuccess}
                                disabled={pendingAttachments.length === 0}
                            >
                                Complete ({pendingAttachments.length} files)
                            </Button>
                        </div>
                    </div>
                )}

                {/* Initial form buttons - only show before ticket creation */}
                {!createdTicketId && (
                    <div className="flex justify-end space-x-4">
                    <Button
                        type="default"
                        onClick={onCancel}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                    >
                        Create Ticket
                    </Button>
                    </div>
                )}
            </form>
        </div>
    );
};

export default CreateTicket; 