import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../../contexts/AuthContext';
import { InternalComment } from '../../../../models/internal-comment.model';
import { format, differenceInSeconds } from 'date-fns';
import { internalCommentService } from '../../../../services/internal-comment.service';
import { ActionIcons } from '../../../common/icons/ActionIcons';
import AttachmentList from '../../../common/AttachmentList';

interface InternalCommentItemProps {
    comment: InternalComment;
    onDelete: () => void;
    onUpdate?: (comment: InternalComment) => void;
}

const InternalCommentItem: React.FC<InternalCommentItemProps> = ({ comment, onDelete, onUpdate }) => {
    const { user, hasRole } = useAuth();
    const [isDeleting, setIsDeleting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [content, setContent] = useState(comment.content);
    const [isEditing, setIsEditing] = useState(comment.isEditing || false);
    const [secondsLeft, setSecondsLeft] = useState<number | null>(null);
    const [currentComment, setCurrentComment] = useState<InternalComment>(comment);

    useEffect(() => {
        const createdAt = new Date(currentComment.createdAt);
        const now = new Date();
        const secondsSinceCreation = differenceInSeconds(now, createdAt);
        
        if (secondsSinceCreation < 10) {
            setSecondsLeft(10 - secondsSinceCreation);
            const timer = setInterval(() => {
                setSecondsLeft(prev => {
                    if (prev === null || prev <= 1) {
                        clearInterval(timer);
                        return null;
                    }
                    return prev - 1;
                });
            }, 1000);

            return () => clearInterval(timer);
        }
    }, [currentComment.createdAt]);

    const handleDelete = async () => {
        if (!user || isDeleting || secondsLeft === null) return;
        
        setIsDeleting(true);
        setError(null);

        try {
            await internalCommentService.deleteComment(currentComment.ticketId, currentComment.id);
            onDelete();
        } catch (error) {
            console.error('Failed to delete comment:', error);
            setError('Failed to delete comment. Please try again.');
        } finally {
            setIsDeleting(false);
        }
    };

    const handleEdit = () => {
        setIsEditing(true);
    };

    const handleCancel = () => {
        setIsEditing(false);
        setContent(currentComment.content);
        setError(null);
    };

    const handleSubmit = async (e?: React.FormEvent) => {
        if (e) {
            e.preventDefault();
        }
        if (!content.trim()) return;

        try {
            const updatedComment = await internalCommentService.updateComment(currentComment.ticketId, currentComment.id, {
                content: content.trim()
            });
            console.log('Comment updated:', updatedComment);
            setCurrentComment(updatedComment);
            setIsEditing(false);
            if (onUpdate) {
                onUpdate(updatedComment);
            }
        } catch (error) {
            console.error('Failed to update comment:', error);
            setError('Failed to update comment. Please try again.');
        }
    };

    const canModify = secondsLeft !== null && (user?.id === currentComment.authorId ||  hasRole('ADMIN') || hasRole('MANAGER'));
    const canDelete = canModify;
    const canEdit = canModify;

    return (
        <div className="mt-2 p-2 bg-gray-50 rounded-md border border-gray-100">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-xs text-gray-500">
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <span>Internal Note</span>
                    <span>•</span>
                    <span>{currentComment.createdBy}</span>
                    <span>•</span>
                    <span>{format(new Date(currentComment.createdAt), 'MMM d, h:mm a')}</span>
                </div>
                <div className="flex items-center gap-2">
                    {canEdit && !isEditing && (
                        <ActionIcons
                            onEdit={handleEdit}
                            onDelete={handleDelete}
                            showEdit={true}
                            isEditing={false}
                            disabled={false}
                        />
                    )}
                </div>
            </div>
            {error && (
                <p className="mt-1 text-xs text-red-600">{error}</p>
            )}
            {isEditing ? (
                <form onSubmit={handleSubmit} className="mt-2">
                    <textarea
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        placeholder="Add an internal note..."
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        rows={3}
                        autoFocus
                    />
                    <div className="flex justify-end gap-2 mt-2">
                        <ActionIcons
                            onSave={handleSubmit}
                            onCancel={handleCancel}
                            showSave={true}
                            showCancel={true}
                            isEditing={true}
                            disabled={!content.trim()}
                        />
                    </div>
                </form>
            ) : (
                <>
                    <p className="mt-1 text-sm text-gray-700">{currentComment.content}</p>

                    {/* Attachments */}
                    <div className="mt-3">
                        <AttachmentList
                            entityType="internal_comment"
                            entityId={currentComment.id}
                            showUpload={false}
                            showBulkDownload={true}
                            maxFiles={5}
                        />
                    </div>
                </>
            )}
        </div>
    );
};

export default InternalCommentItem; 