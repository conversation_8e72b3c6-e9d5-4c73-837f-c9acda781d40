import React, { useState } from 'react';
import { useAuth } from '../../../../contexts/AuthContext';
import { internalCommentService } from '../../../../services/internal-comment.service';
import { InternalComment } from '../../../../models/internal-comment.model';
import { format } from 'date-fns';
import { Button } from '../../../common/Button';
import { ActionIcons } from '../../../common/icons/ActionIcons';
import AttachmentUpload from '../../../common/AttachmentUpload';
import { Attachment } from '../../../../types/attachment';

interface AddInternalCommentProps {
    ticketId: number;
    onCommentAdded: (comment: InternalComment, isEditing: boolean) => void;
    onCommentsReload: () => void;
}

export const AddInternalComment: React.FC<AddInternalCommentProps> = ({ 
    ticketId, 
    onCommentAdded,
    onCommentsReload 
}) => {
    const { user } = useAuth();
    const [content, setContent] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [createdCommentId, setCreatedCommentId] = useState<number | null>(null);
    const [showAttachments, setShowAttachments] = useState(false);

    if (!user) return null;

    const handleSubmit = async (e?: React.FormEvent) => {
        if (e) {
            e.preventDefault();
        }
        if (!content.trim()) return;

        setIsSubmitting(true);
        setError(null);

        try {
            const comment = await internalCommentService.addComment(ticketId, {
                content: content.trim()
            });

            // Set the created comment ID for potential attachments
            setCreatedCommentId(comment.id);

            // If no attachments to add, complete immediately
            if (!showAttachments) {
                setContent('');
                onCommentAdded(comment, false);
                onCommentsReload();
            }
        } catch (error) {
            console.error('Failed to add comment:', error);
            setError('Failed to add comment. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        setContent('');
        setShowAttachments(false);
        setCreatedCommentId(null);
        onCommentAdded({} as InternalComment, false);
    };

    const handleAttachmentComplete = () => {
        setContent('');
        setShowAttachments(false);
        setCreatedCommentId(null);
        onCommentAdded({} as InternalComment, false);
        onCommentsReload();
    };

    const handleSave = () => {
        if (!content.trim() || isSubmitting) return;
        handleSubmit();
    };

    return (
        <div className="mt-4">
            <form onSubmit={handleSubmit}>
                <div className="mb-4">
                    <label className="block text-sm font-medium mb-2">
                        Internal Note
                    </label>
                    <textarea
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        placeholder="Type your internal note..."
                        className="w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2"
                        style={{
                            backgroundColor: 'var(--theme-card-bg)',
                            borderColor: 'var(--theme-border-primary)',
                            color: 'var(--theme-text-primary)'
                        }}
                        rows={4}
                        autoFocus
                    />
                </div>

                {error && (
                    <p className="mt-1 text-sm text-red-600">{error}</p>
                )}

                {/* Attachment section - show after comment is created */}
                {createdCommentId && showAttachments && (
                    <div className="mt-4 p-4 border rounded-md" style={{
                        backgroundColor: 'var(--theme-card-bg)',
                        borderColor: 'var(--theme-border-primary)'
                    }}>
                        <h4 className="text-sm font-medium mb-3">Add Attachments (Optional)</h4>
                        <AttachmentUpload
                            entityType="internal_comment"
                            entityId={createdCommentId}
                            maxFiles={5}
                            showFileList={true}
                            onUploadComplete={() => {}}
                        />
                        <div className="flex justify-end gap-2 mt-3">
                            <Button
                                variant="outline"
                                onClick={handleAttachmentComplete}
                                size="sm"
                            >
                                Skip Attachments
                            </Button>
                            <Button
                                variant="primary"
                                onClick={handleAttachmentComplete}
                                size="sm"
                            >
                                Done
                            </Button>
                        </div>
                    </div>
                )}

                <div className="flex justify-between items-center">
                    {/* Attachment toggle - only show before comment creation */}
                    {!createdCommentId && (
                        <label className="flex items-center text-sm">
                            <input
                                type="checkbox"
                                checked={showAttachments}
                                onChange={(e) => setShowAttachments(e.target.checked)}
                                className="mr-2"
                            />
                            Add attachments
                        </label>
                    )}

                    <div className="flex justify-end gap-2">
                    <ActionIcons
                        onSave={handleSave}
                        onCancel={handleCancel}
                        showSave={true}
                        showCancel={true}
                        isEditing={true}
                        disabled={!content.trim() || isSubmitting}
                    />
                    </div>
                </div>
            </form>
        </div>
    );
}; 