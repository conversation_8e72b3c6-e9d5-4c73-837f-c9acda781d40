import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { ticketService } from '../../../services/ticketService';
import { internalCommentService } from '../../../services/internal-comment.service';
import { publicCommentService } from '../../../services/public-comment.service';
import { userService } from '../../../services/user.service';
import { groupService } from '../../../services/group.service';
import { Ticket, TicketStatus, TicketPriority } from '../../../types/ticket';
import { useAuth } from '../../../contexts/AuthContext';
import { Input, TextArea } from '../../common/Input';
import { Select } from '../../common/Select';
import { Button } from '../../common/Button';
import { Alert } from '../../common/Alert';
import { Loading } from '../../common/Loading';
import { Card } from '../../common/Card';
import { RenderPermitted } from '../../common/security/RenderPermitted';
import InternalCommentItem from './internal-comments/internal-comment-item.component';
import { AddInternalComment } from './internal-comments/add-internal-comment.component';
import PublicCommentItem from './public-comments/public-comment-item.component';
import { AddPublicComment } from './public-comments/add-public-comment.component';
import { InternalComment } from '../../../models/internal-comment.model';
import { PublicComment } from '../../../models/public-comment.model';
import { RoleName } from '../../../types/permission';
import { useKeyboardShortcuts } from '../../../hooks/useKeyboardShortcuts';
import { Dropdown, Menu } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import AISuggestions from '../../common/AISuggestions';
import AttachmentList from '../../common/AttachmentList';

interface EditTicketProps {
    ticketId: string;
    onSuccess: () => void;
    onCancel: () => void;
}

const EditTicket: React.FC<EditTicketProps> = ({ ticketId, onSuccess, onCancel }) => {
    const { user } = useAuth();
    const [ticket, setTicket] = useState<Ticket | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        status: TicketStatus.OPEN,
        priority: TicketPriority.MEDIUM,
        createdBy: ''
    });
    const [internalComments, setInternalComments] = useState<InternalComment[]>([]);
    const [publicComments, setPublicComments] = useState<PublicComment[]>([]);
    const [availableUsers, setAvailableUsers] = useState<{ id: number; email: string }[]>([]);
    const [availableGroups, setAvailableGroups] = useState<{ id: number; name: string }[]>([]);
    const [showAddInternalNote, setShowAddInternalNote] = useState(false);
    const [showAddPublicReply, setShowAddPublicReply] = useState(false);
    const [isLoadingComments, setIsLoadingComments] = useState(false);
    const [showCommentMenu, setShowCommentMenu] = useState(false);

    useEffect(() => {
        const fetchTicket = async () => {
            try {
                const data = await ticketService.getTicketById(Number(ticketId));
                setTicket(data);
                setFormData({
                    title: data.title,
                    description: data.description,
                    status: data.status,
                    priority: data.priority,
                    createdBy: data.createdBy
                });
            } catch (err) {
                setError('Failed to fetch ticket');
                console.error('Error fetching ticket:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchTicket();
    }, [ticketId]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!user?.email) {
            setError('User email is required');
            return;
        }
        setLoading(true);
        try {
            await ticketService.updateTicket(Number(ticketId), {
                ...formData,
                createdBy: user.email
            });
            onSuccess();
        } catch (err) {
            setError('Failed to update ticket');
            console.error('Error updating ticket:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSelectChange = (name: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleCommentAdded = (comment: InternalComment, isEditing: boolean) => {
        if (isEditing) {
            setInternalComments(prev => [...prev, comment]);
        } else {
            setInternalComments(prev => prev.map(c => c.id === comment.id ? comment : c));
        }
    };

    const handleCommentDeleted = (commentId: number) => {
        setInternalComments(prev => prev.filter(c => c.id !== commentId));
    };

    const handleCommentsReload = () => {
        // Refresh comments if needed
        fetchInternalComments();
    };

    const fetchInternalComments = async () => {
        if (!ticketId) return;
        setIsLoadingComments(true);
        try {
            const comments = await internalCommentService.getCommentsForTicket(parseInt(ticketId));
            setInternalComments(comments);
        } catch (error) {
            console.error('Failed to fetch internal comments:', error);
        } finally {
            setIsLoadingComments(false);
        }
    };

    const fetchPublicComments = async () => {
        if (!ticketId) return;
        setIsLoadingComments(true);
        try {
            const comments = await publicCommentService.getCommentsForTicket(parseInt(ticketId));
            setPublicComments(comments);
        } catch (error) {
            console.error('Failed to fetch public comments:', error);
        } finally {
            setIsLoadingComments(false);
        }
    };

    useEffect(() => {
        fetchInternalComments();
        fetchPublicComments();
    }, [ticketId]);

    const handlePublicCommentAdded = (comment: PublicComment, isEditing: boolean) => {
        if (isEditing) {
            setPublicComments(prev => [...prev, comment]);
        } else {
            setPublicComments(prev => prev.map(c => c.id === comment.id ? comment : c));
        }
    };

    const handlePublicCommentDeleted = (commentId: number) => {
        setPublicComments(prev => prev.filter(c => c.id !== commentId));
    };

    const handlePublicCommentsReload = () => {
        fetchPublicComments();
    };

    const fetchAvailableUsersAndGroups = async () => {
        try {
            const [users, groups] = await Promise.all([
                userService.getUsers(),
                groupService.getGroups()
            ]);
            setAvailableUsers(users);
            setAvailableGroups(groups);
        } catch (error) {
            console.error('Failed to fetch users and groups:', error);
        }
    };

    useEffect(() => {
        fetchAvailableUsersAndGroups();
    }, []);

    // Handle clicking outside dropdown to close it
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as Element;
            if (showCommentMenu && !target.closest('.comment-dropdown-container')) {
                setShowCommentMenu(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showCommentMenu]);

    useKeyboardShortcuts([
        {
            key: 's',
            ctrlKey: true,
            handler: (e) => {
                e.preventDefault();
                handleSubmit(e as unknown as React.FormEvent);
            }
        },
        {
            key: 'Escape',
            handler: () => {
                if (showCommentMenu) {
                    setShowCommentMenu(false);
                } else {
                    onCancel();
                }
            }
        }
    ]);

    if (loading && !ticket) {
        return <Loading />;
    }

    if (error && !ticket) {
        return <Alert type="error" message={error} />;
    }

    return (
        <div className="space-y-6">
            {error && <Alert type="error" message={error} className="mb-4" />}
            
            {/* AI Suggestions Section */}
            <AISuggestions ticketId={parseInt(ticketId)} />
            
            <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                    <label className="block text-sm font-medium mb-1">Title</label>
                    <Input
                        name="title"
                        value={formData.title}
                        onChange={handleChange}
                        required
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium mb-1">Description</label>
                    <TextArea
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        required
                        rows={4}
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium mb-1">Status</label>
                    <Select
                        value={formData.status}
                        onChange={(value) => handleSelectChange('status', value)}
                        options={Object.values(TicketStatus).map(status => ({
                            value: status,
                            label: status
                        }))}
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium mb-1">Priority</label>
                    <Select
                        value={formData.priority}
                        onChange={(value) => handleSelectChange('priority', value)}
                        options={Object.values(TicketPriority).map(priority => ({
                            value: priority,
                            label: priority
                        }))}
                    />
                </div>

                <div className="flex justify-end space-x-4">
                    <Button
                        type="default"
                        onClick={onCancel}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                    >
                        Update Ticket
                    </Button>
                </div>
            </form>

            {/* Attachments Section */}
            <div className="mt-6">
                <AttachmentList
                    entityType="ticket"
                    entityId={parseInt(ticketId)}
                    showUpload={true}
                    showBulkDownload={true}
                    maxFiles={20}
                />
            </div>

            <RenderPermitted fallback={null}>
                <Card className="p-4">
                    <div className="mb-4">
                        <div className="flex justify-between items-center">
                            <div>
                                <h3 className="text-lg font-medium">Comments</h3>
                                <p className="mt-1 text-sm">
                                    Add internal notes and public replies about this ticket.</p>
                            </div>
                            <div className="relative comment-dropdown-container">
                                <Button
                                    type="default"
                                    icon={<PlusOutlined />}
                                    onClick={() => {
                                        console.log('Button clicked');
                                        setShowCommentMenu(!showCommentMenu);
                                    }}
                                >
                                    Add Comment
                                </Button>
                                {showCommentMenu && (
                                    <div className="absolute top-full left-0 right-0 mt-1 border rounded-md shadow-lg z-50"
                                         style={{
                                             backgroundColor: 'var(--theme-card-bg)',
                                             borderColor: 'var(--theme-border-primary)',
                                             color: 'var(--theme-text-primary)'
                                         }}>
                                        <div 
                                            className="px-4 py-2 cursor-pointer border-b"
                                            style={{
                                                borderBottomColor: 'var(--theme-border-primary)'
                                            }}
                                            onMouseEnter={(e) => {
                                                e.currentTarget.style.backgroundColor = 'var(--theme-card-hover)';
                                            }}
                                            onMouseLeave={(e) => {
                                                e.currentTarget.style.backgroundColor = 'var(--theme-card-bg)';
                                            }}
                                            onClick={() => {
                                                console.log('Internal note clicked');
                                                setShowAddInternalNote(true);
                                                setShowAddPublicReply(false);
                                                setShowCommentMenu(false);
                                            }}
                                        >
                                            + Add Internal Note
                                        </div>
                                        <div 
                                            className="px-4 py-2 cursor-pointer"
                                            onMouseEnter={(e) => {
                                                e.currentTarget.style.backgroundColor = 'var(--theme-card-hover)';
                                            }}
                                            onMouseLeave={(e) => {
                                                e.currentTarget.style.backgroundColor = 'var(--theme-card-bg)';
                                            }}
                                            onClick={() => {
                                                console.log('Public reply clicked');
                                                setShowAddPublicReply(true);
                                                setShowAddInternalNote(false);
                                                setShowCommentMenu(false);
                                            }}
                                        >
                                            + Add Public Reply
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="space-y-4">
                        {/* Comment forms at the top */}
                        {showAddInternalNote && (
                            <AddInternalComment
                                ticketId={parseInt(ticketId)}
                                onCommentAdded={(comment, isEditing) => {
                                    handleCommentAdded(comment, isEditing);
                                    setShowAddInternalNote(false);
                                }}
                                onCommentsReload={handleCommentsReload}
                            />
                        )}

                        {showAddPublicReply && (
                            <AddPublicComment
                                ticketId={parseInt(ticketId)}
                                onCommentAdded={(comment, isEditing) => {
                                    handlePublicCommentAdded(comment, isEditing);
                                    setShowAddPublicReply(false);
                                }}
                                onCommentsReload={handlePublicCommentsReload}
                                availableUsers={availableUsers}
                                availableGroups={availableGroups}
                            />
                        )}

                        {/* Comments list */}
                        <div className="space-y-4">
                            {([...internalComments, ...publicComments] as (InternalComment | PublicComment)[])
                                .sort((a, b) => {
                                    const dateA = new Date(a.createdAt).getTime();
                                    const dateB = new Date(b.createdAt).getTime();
                                    return dateB - dateA;
                                })
                                .map(comment => {
                                    if ('publicReply' in comment) {
                                        return (
                                            <PublicCommentItem
                                                key={`public-${comment.id}`}
                                                comment={comment}
                                                onDelete={() => handlePublicCommentDeleted(comment.id)}
                                                onUpdate={(comment) => handlePublicCommentAdded(comment, false)}
                                            />
                                        );
                                    } else {
                                        return (
                                            <InternalCommentItem
                                                key={`internal-${comment.id}`}
                                                comment={comment}
                                                onDelete={() => handleCommentDeleted(comment.id)}
                                                onUpdate={(comment) => handleCommentAdded(comment, false)}
                                            />
                                        );
                                    }
                                })}
                        </div>
                    </div>
                </Card>
            </RenderPermitted>
        </div>
    );
};

export default EditTicket; 