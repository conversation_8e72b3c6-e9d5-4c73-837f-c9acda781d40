import React, { useState } from 'react';
import { useAuth } from '../../../../contexts/AuthContext';
import { publicCommentService } from '../../../../services/public-comment.service';
import { PublicComment } from '../../../../models/public-comment.model';
import { Select, Space } from 'antd';
import { UserOutlined, TeamOutlined } from '@ant-design/icons';
import { ActionIcons } from '../../../common/icons/ActionIcons';
import AttachmentUpload from '../../../common/AttachmentUpload';
import { Attachment } from '../../../../types/attachment';

interface AddPublicCommentProps {
    ticketId: number;
    onCommentAdded: (comment: PublicComment, isEditing: boolean) => void;
    onCommentsReload: () => void;
    availableUsers: { id: number; email: string }[];
    availableGroups: { id: number; name: string }[];
}

export const AddPublicComment: React.FC<AddPublicCommentProps> = ({
    ticketId,
    onCommentAdded,
    onCommentsReload,
    availableUsers,
    availableGroups
}) => {
    const { user } = useAuth();
    const [content, setContent] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [toRecipients, setToRecipients] = useState<string[]>([]);
    const [ccRecipients, setCcRecipients] = useState<string[]>([]);
    const [createdCommentId, setCreatedCommentId] = useState<number | null>(null);
    const [showAttachments, setShowAttachments] = useState(false);

    if (!user) return null;

    const handleSubmit = async (e?: React.FormEvent) => {
        if (e) {
            e.preventDefault();
        }
        if (!content.trim()) return;

        setIsSubmitting(true);
        setError(null);

        try {
            const comment = await publicCommentService.addComment(ticketId, {
                ticketId,
                content: content.trim(),
                publicReply: true,
                toRecipients,
                ccRecipients,
                sourceIdentifier: user.email
            });

            // Set the created comment ID for potential attachments
            setCreatedCommentId(comment.id);

            // If no attachments to add, complete immediately
            if (!showAttachments) {
                setContent('');
                setToRecipients([]);
                setCcRecipients([]);
                onCommentAdded(comment, false);
                onCommentsReload();
            }
        } catch (error) {
            console.error('Failed to add comment:', error);
            setError('Failed to add comment. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        setContent('');
        setToRecipients([]);
        setCcRecipients([]);
        setShowAttachments(false);
        setCreatedCommentId(null);
        onCommentAdded({} as PublicComment, false);
    };

    const handleAttachmentComplete = () => {
        setContent('');
        setToRecipients([]);
        setCcRecipients([]);
        setShowAttachments(false);
        setCreatedCommentId(null);
        onCommentAdded({} as PublicComment, false);
        onCommentsReload();
    };

    const handleSave = () => {
        if (!content.trim() || isSubmitting) return;
        handleSubmit();
    };

    return (
        <div className="mt-4">
            <form onSubmit={handleSubmit}>
                <div className="mb-4">
                    <label className="block text-sm font-medium mb-2">
                        To Recipients
                    </label>
                    <Select
                        mode="multiple"
                        style={{ width: '100%' }}
                        placeholder="Select recipients"
                        value={toRecipients}
                        onChange={setToRecipients}
                        options={[
                            {
                                label: 'Users',
                                options: availableUsers.map(user => ({
                                    label: (
                                        <Space>
                                            <UserOutlined />
                                            {user.email}
                                        </Space>
                                    ),
                                    value: user.email
                                }))
                            },
                            {
                                label: 'Groups',
                                options: availableGroups.map(group => ({
                                    label: (
                                        <Space>
                                            <TeamOutlined />
                                            {group.name}
                                        </Space>
                                    ),
                                    value: group.name
                                }))
                            }
                        ]}
                    />
                </div>

                <div className="mb-4">
                    <label className="block text-sm font-medium mb-2">
                        CC Recipients
                    </label>
                    <Select
                        mode="multiple"
                        style={{ width: '100%' }}
                        placeholder="Select CC recipients"
                        value={ccRecipients}
                        onChange={setCcRecipients}
                        options={[
                            {
                                label: 'Users',
                                options: availableUsers.map(user => ({
                                    label: (
                                        <Space>
                                            <UserOutlined />
                                            {user.email}
                                        </Space>
                                    ),
                                    value: user.email
                                }))
                            },
                            {
                                label: 'Groups',
                                options: availableGroups.map(group => ({
                                    label: (
                                        <Space>
                                            <TeamOutlined />
                                            {group.name}
                                        </Space>
                                    ),
                                    value: group.name
                                }))
                            }
                        ]}
                    />
                </div>

                <div className="mb-4">
                    <label className="block text-sm font-medium mb-2">
                        Message
                    </label>
                    <textarea
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        placeholder="Type your message..."
                        className="w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2"
                        style={{
                            backgroundColor: 'var(--theme-card-bg)',
                            borderColor: 'var(--theme-border-primary)',
                            color: 'var(--theme-text-primary)'
                        }}
                        rows={4}
                    />
                </div>

                {error && (
                    <p className="mt-1 text-sm text-red-600">{error}</p>
                )}

                {/* Attachment section - show after comment is created */}
                {createdCommentId && showAttachments && (
                    <div className="mt-4 p-4 border rounded-md" style={{
                        backgroundColor: 'var(--theme-card-bg)',
                        borderColor: 'var(--theme-border-primary)'
                    }}>
                        <h4 className="text-sm font-medium mb-3">Add Attachments (Optional)</h4>
                        <AttachmentUpload
                            entityType="public_comment"
                            entityId={createdCommentId}
                            maxFiles={5}
                            showFileList={true}
                            onUploadComplete={() => {}}
                        />
                        <div className="flex justify-end gap-2 mt-3">
                            <button
                                type="button"
                                onClick={handleAttachmentComplete}
                                className="px-3 py-1 text-sm border rounded-md"
                                style={{
                                    borderColor: 'var(--theme-border-primary)',
                                    color: 'var(--theme-text-secondary)'
                                }}
                            >
                                Skip Attachments
                            </button>
                            <button
                                type="button"
                                onClick={handleAttachmentComplete}
                                className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                            >
                                Done
                            </button>
                        </div>
                    </div>
                )}

                <div className="flex justify-between items-center">
                    {/* Attachment toggle - only show before comment creation */}
                    {!createdCommentId && (
                        <label className="flex items-center text-sm">
                            <input
                                type="checkbox"
                                checked={showAttachments}
                                onChange={(e) => setShowAttachments(e.target.checked)}
                                className="mr-2"
                            />
                            Add attachments
                        </label>
                    )}

                    <div className="flex justify-end gap-2">
                    <ActionIcons
                        onSave={handleSave}
                        onCancel={handleCancel}
                        showSave={true}
                        showCancel={true}
                        isEditing={true}
                        disabled={!content.trim() || isSubmitting}
                    />
                    </div>
                </div>
            </form>
        </div>
    );
}; 