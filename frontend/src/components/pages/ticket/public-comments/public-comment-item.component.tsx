import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../../contexts/AuthContext';
import { CreatePublicCommentRequest, PublicComment } from '../../../../models/public-comment.model';
import { format, differenceInSeconds } from 'date-fns';
import { publicCommentService } from '../../../../services/public-comment.service';
import { Space, Tag } from 'antd';
import { UserOutlined, TeamOutlined } from '@ant-design/icons';
import { ActionIcons } from '../../../common/icons/ActionIcons';
import AttachmentList from '../../../common/AttachmentList';

interface PublicCommentItemProps {
    comment: PublicComment;
    onDelete: () => void;
    onUpdate?: (comment: PublicComment) => void;
}

const PublicCommentItem: React.FC<PublicCommentItemProps> = ({ comment, onDelete, onUpdate }) => {
    const { user, hasRole } = useAuth();
    const [isDeleting, setIsDeleting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [content, setContent] = useState(comment.content);
    const [isEditing, setIsEditing] = useState(comment.isEditing || false);
    const [secondsLeft, setSecondsLeft] = useState<number | null>(null);
    const [currentComment, setCurrentComment] = useState<PublicComment>(comment);

    useEffect(() => {
        const createdAt = new Date(currentComment.createdAt);
        const now = new Date();
        const secondsSinceCreation = differenceInSeconds(now, createdAt);
        
        if (secondsSinceCreation < 10) {
            setSecondsLeft(10 - secondsSinceCreation);
            const timer = setInterval(() => {
                setSecondsLeft(prev => {
                    if (prev === null || prev <= 1) {
                        clearInterval(timer);
                        return null;
                    }
                    return prev - 1;
                });
            }, 1000);

            return () => clearInterval(timer);
        }
    }, [currentComment.createdAt]);

    const handleDelete = async () => {
        if (!user || isDeleting || secondsLeft === null) return;
        
        setIsDeleting(true);
        setError(null);

        try {
            await publicCommentService.deleteComment(currentComment.ticketId, currentComment.id);
            onDelete();
        } catch (error) {
            console.error('Failed to delete comment:', error);
            setError('Failed to delete comment. Please try again.');
        } finally {
            setIsDeleting(false);
        }
    };

    const handleEdit = () => {
        setIsEditing(true);
    };

    const handleCancel = () => {
        setIsEditing(false);
        setContent(currentComment.content);
        setError(null);
    };

    const handleSubmit = async (e?: React.FormEvent) => {
        if (e) {
            e.preventDefault();
        }
        if (!content.trim()) return;

        try {
            const updatedComment = await publicCommentService.updateComment(currentComment.ticketId, currentComment.id, {
                content: content.trim(),
                publicReply: currentComment.publicReply,
                toRecipients: currentComment.toRecipients,
                ccRecipients: currentComment.ccRecipients
            } as CreatePublicCommentRequest);
            setCurrentComment(updatedComment);
            setIsEditing(false);
            if (onUpdate) {
                onUpdate(updatedComment);
            }
        } catch (error) {
            console.error('Failed to update comment:', error);
            setError('Failed to update comment. Please try again.');
        }
    };

    const canModify = secondsLeft !== null && (user?.id === currentComment.authorId || hasRole('ADMIN') || hasRole('MANAGER'));
    const canDelete = canModify;
    const canEdit = canModify;

    return (
        <div className="bg-white p-4 rounded-lg shadow mb-4">
            <div className="flex justify-between items-start">
                <div>
                    <div className="flex items-center space-x-2">
                        <span className="font-medium">{currentComment.createdBy}</span>
                        <span className="text-gray-500 text-sm">
                            {format(new Date(currentComment.createdAt), 'MMM d, yyyy h:mm a')}
                        </span>
                        {currentComment.publicReply && (
                            <Tag color="blue">Public Reply</Tag>
                        )}
                    </div>
                    {currentComment.toRecipients.length > 0 && (
                        <div className="mt-1">
                            <span className="text-sm text-gray-500">To: </span>
                            <Space wrap>
                                {currentComment.toRecipients.map((recipient, index) => (
                                    <Tag key={index} icon={<UserOutlined />}>
                                        {recipient}
                                    </Tag>
                                ))}
                            </Space>
                        </div>
                    )}
                    {currentComment.ccRecipients.length > 0 && (
                        <div className="mt-1">
                            <span className="text-sm text-gray-500">CC: </span>
                            <Space wrap>
                                {currentComment.ccRecipients.map((recipient, index) => (
                                    <Tag key={index} icon={<UserOutlined />}>
                                        {recipient}
                                    </Tag>
                                ))}
                            </Space>
                        </div>
                    )}
                </div>
                {canDelete && (
                    <div className="flex items-center gap-2">
                        {canEdit && !isEditing && (
                            <ActionIcons
                                onEdit={handleEdit}
                                onDelete={handleDelete}
                                showEdit={true}
                                isEditing={false}
                                disabled={false}
                            />
                        )}
                    </div>
                )}
            </div>

            {error && (
                <p className="mt-1 text-xs text-red-600">{error}</p>
            )}
            {isEditing ? (
                <form onSubmit={handleSubmit} className="mt-2">
                    <textarea
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        placeholder="Type your message..."
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        rows={3}
                        autoFocus
                    />
                    <div className="flex justify-end gap-2 mt-2">
                        <ActionIcons
                            onSave={handleSubmit}
                            onCancel={handleCancel}
                            showSave={true}
                            showCancel={true}
                            isEditing={true}
                            disabled={!content.trim()}
                        />
                    </div>
                </form>
            ) : (
                <>
                    <p className="mt-1 text-sm text-gray-700">{currentComment.content}</p>

                    {/* Attachments */}
                    <div className="mt-3">
                        <AttachmentList
                            entityType="public_comment"
                            entityId={currentComment.id}
                            showUpload={false}
                            showBulkDownload={true}
                            maxFiles={5}
                        />
                    </div>
                </>
            )}
        </div>
    );
};

export default PublicCommentItem; 