import React, { useState, useEffect } from 'react';
import {
    Card,
    Button,
    Table,
    Modal,
    Form,
    Input,
    Select,
    Switch,
    Space,
    Popconfirm,
    message,
    Tag,
    Tooltip,
    Row,
    Col,
    Divider,
    Alert,
    Spin,
    InputNumber
} from 'antd';
import {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    SettingOutlined,
    ExclamationCircleOutlined,
    PoweroffOutlined,
    CheckCircleOutlined
} from '@ant-design/icons';
import { customFieldsService, CustomFieldDefinition, CustomFieldGroup, CreateCustomFieldDefinitionRequest, UpdateCustomFieldDefinitionRequest, CreateCustomFieldGroupRequest, UpdateCustomFieldGroupRequest } from '../../../services/customFieldsService';

const { Option } = Select;
const { TextArea } = Input;

const CustomFieldsPage: React.FC = () => {
    // Mock user data for now
    const user = { role: 'SX_ADMIN', organizationId: 1, supportOrganizationId: 1 };
    
    const [customFields, setCustomFields] = useState<CustomFieldDefinition[]>([]);
    const [customFieldGroups, setCustomFieldGroups] = useState<CustomFieldGroup[]>([]);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingField, setEditingField] = useState<CustomFieldDefinition | null>(null);
    const [form] = Form.useForm();
    const [groupForm] = Form.useForm();
    const [groupModalVisible, setGroupModalVisible] = useState(false);
    const [editingGroup, setEditingGroup] = useState<CustomFieldGroup | null>(null);

    // Field type and data type options
    const fieldTypes = [
        { value: 'TEXT', label: 'Text Input' },
        { value: 'NUMBER', label: 'Number Input' },
        { value: 'DATE', label: 'Date Picker' },
        { value: 'DROPDOWN', label: 'Dropdown' },
        { value: 'CHECKBOX', label: 'Checkbox' },
        { value: 'MULTI_SELECT', label: 'Multi-Select' }
    ];

    const dataTypes = [
        { value: 'STRING', label: 'String' },
        { value: 'INTEGER', label: 'Integer' },
        { value: 'DECIMAL', label: 'Decimal' },
        { value: 'BOOLEAN', label: 'Boolean' },
        { value: 'DATE', label: 'Date' },
        { value: 'TIMESTAMP', label: 'Timestamp' }
    ];

    const isSxAdmin = user?.role === 'SX_ADMIN';
    const isSupportOrgAdmin = user?.role === 'SUPPORT_ORG_ADMIN';

    useEffect(() => {
        loadCustomFields();
        loadCustomFieldGroups();
    }, []);

    const loadCustomFields = async () => {
        setLoading(true);
        try {
            const fields = await customFieldsService.getCustomFieldDefinitions();
            setCustomFields(fields);
        } catch (error) {
            message.error('Failed to load custom fields');
        } finally {
            setLoading(false);
        }
    };

    const loadCustomFieldGroups = async () => {
        try {
            const groups = await customFieldsService.getCustomFieldGroups();
            setCustomFieldGroups(groups);
        } catch (error) {
            message.error('Failed to load custom field groups');
        }
    };

    const handleCreateField = () => {
        setEditingField(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleEditField = (field: CustomFieldDefinition) => {
        setEditingField(field);
        form.setFieldsValue({
            ...field,
            validationRules: field.validationRules ? JSON.stringify(field.validationRules, null, 2) : '',
            fieldOptions: field.fieldOptions ? JSON.stringify(field.fieldOptions, null, 2) : ''
        });
        setModalVisible(true);
    };

    const handleDeleteField = async (fieldId: number) => {
        try {
            await customFieldsService.deleteCustomFieldDefinition(fieldId);
            message.success('Custom field deleted successfully');
            loadCustomFields();
        } catch (error) {
            message.error('Failed to delete custom field');
        }
    };

    const handleActivateField = async (fieldId: number) => {
        try {
            await customFieldsService.activateCustomFieldDefinition(fieldId);
            message.success('Custom field activated successfully');
            loadCustomFields();
        } catch (error) {
            message.error('Failed to activate custom field');
        }
    };

    const handleDeactivateField = async (fieldId: number) => {
        try {
            await customFieldsService.deactivateCustomFieldDefinition(fieldId);
            message.success('Custom field deactivated successfully');
            loadCustomFields();
        } catch (error) {
            message.error('Failed to deactivate custom field');
        }
    };

    const handleSubmit = async (values: any) => {
        try {
            if (editingField) {
                // Update operation - include version
                const fieldData = {
                    ...values,
                    validationRules: values.validationRules ? JSON.parse(values.validationRules) : null,
                    fieldOptions: values.fieldOptions ? JSON.parse(values.fieldOptions) : null,
                    isTemplate: isSxAdmin,
                    version: editingField.version + 1
                };
                await customFieldsService.updateCustomFieldDefinition(editingField.id!, fieldData as UpdateCustomFieldDefinitionRequest);
                message.success('Custom field updated successfully');
            } else {
                // Create operation - set default version to 1
                const fieldData = {
                    ...values,
                    validationRules: values.validationRules ? JSON.parse(values.validationRules) : null,
                    fieldOptions: values.fieldOptions ? JSON.parse(values.fieldOptions) : null,
                    isTemplate: isSxAdmin,
                    version: 1
                };
                await customFieldsService.createCustomFieldDefinition(fieldData as CreateCustomFieldDefinitionRequest);
                message.success('Custom field created successfully');
            }
            setModalVisible(false);
            loadCustomFields();
        } catch (error) {
            message.error('Failed to save custom field');
        }
    };

    const handleCreateGroup = () => {
        setEditingGroup(null);
        groupForm.resetFields();
        setGroupModalVisible(true);
    };

    const handleEditGroup = (group: CustomFieldGroup) => {
        setEditingGroup(group);
        groupForm.setFieldsValue({
            name: group.name,
            description: group.description,
            displayOrder: group.displayOrder,
            isCollapsible: group.isCollapsible,
            isActive: group.isActive
        });
        setGroupModalVisible(true);
    };

    const handleDeleteGroup = async (groupId: number) => {
        try {
            await customFieldsService.deleteCustomFieldGroup(groupId);
            message.success('Custom field group deleted successfully');
            loadCustomFieldGroups();
        } catch (error) {
            message.error('Failed to delete custom field group');
        }
    };

    const handleGroupSubmit = async (values: any) => {
        try {
            const groupData = {
                ...values
            };

            if (editingGroup) {
                await customFieldsService.updateCustomFieldGroup(editingGroup.id!, groupData as UpdateCustomFieldGroupRequest);
                message.success('Group updated successfully');
            } else {
                await customFieldsService.createCustomFieldGroup(groupData as CreateCustomFieldGroupRequest);
                message.success('Group created successfully');
            }
            setGroupModalVisible(false);
            loadCustomFieldGroups();
        } catch (error) {
            message.error('Failed to save group');
        }
    };

    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
            render: (text: string, record: CustomFieldDefinition) => (
                <div>
                    <div className="font-medium text-theme-text-primary">{text}</div>
                    <div className="text-sm text-theme-text-secondary">{record.label}</div>
                </div>
            )
        },
        {
            title: 'Type',
            dataIndex: 'fieldType',
            key: 'fieldType',
            render: (fieldType: string, record: CustomFieldDefinition) => (
                <div>
                    <Tag color="blue">{fieldType}</Tag>
                    <div className="text-xs text-theme-text-secondary">{record.dataType}</div>
                </div>
            )
        },
        {
            title: 'Settings',
            key: 'settings',
            render: (record: CustomFieldDefinition) => (
                <div className="space-y-1">
                    {record.isRequired && <Tag color="red">Required</Tag>}
                    {record.isUnique && <Tag color="orange">Unique</Tag>}
                    {record.isTemplate && <Tag color="purple">Template</Tag>}
                </div>
            )
        },
        {
            title: 'Status',
            dataIndex: 'isActive',
            key: 'isActive',
            render: (isActive: boolean) => (
                <Tag color={isActive ? 'green' : 'red'}>
                    {isActive ? 'Active' : 'Inactive'}
                </Tag>
            )
        },
        {
            title: 'Order',
            dataIndex: 'displayOrder',
            key: 'displayOrder',
            width: 80
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (record: CustomFieldDefinition) => (
                <Space>
                    <Tooltip title="Edit">
                        <Button 
                            type="text" 
                            icon={<EditOutlined />} 
                            onClick={() => handleEditField(record)}
                            className="text-theme-primary"
                        />
                    </Tooltip>
                    {record.isActive ? (
                        <Tooltip title="Deactivate">
                            <Button 
                                type="text" 
                                icon={<PoweroffOutlined />} 
                                onClick={() => handleDeactivateField(record.id!)}
                                className="text-theme-warning"
                            />
                        </Tooltip>
                    ) : (
                        <Tooltip title="Activate">
                            <Button 
                                type="text" 
                                icon={<CheckCircleOutlined />} 
                                onClick={() => handleActivateField(record.id!)}
                                className="text-theme-success"
                            />
                        </Tooltip>
                    )}
                    <Popconfirm
                        title="Delete Custom Field"
                        description="This will soft delete the field. Historic tickets may still reference this field. Are you sure?"
                        onConfirm={() => handleDeleteField(record.id!)}
                        okText="Yes"
                        cancelText="No"
                        okButtonProps={{ 
                            type: 'default'
                        }}
                        cancelButtonProps={{ 
                            type: 'default'
                        }}
                        icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
                    >
                        <Tooltip title="Delete">
                            <Button 
                                type="text" 
                                icon={<DeleteOutlined />} 
                                className="text-theme-danger"
                            />
                        </Tooltip>
                    </Popconfirm>
                </Space>
            )
        }
    ];

    const groupColumns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
            render: (text: string, record: CustomFieldGroup) => (
                <div>
                    <div className="font-medium text-theme-text-primary">{text}</div>
                    <div className="text-sm text-theme-text-secondary">{record.description || 'No description'}</div>
                </div>
            )
        },
        {
            title: 'Description',
            dataIndex: 'description',
            key: 'description',
            render: (text: string) => text || '-'
        },
        {
            title: 'Settings',
            key: 'settings',
            render: (record: CustomFieldGroup) => (
                <div className="space-y-1">
                    {record.isCollapsible && <Tag color="blue">Collapsible</Tag>}
                    {record.isActive && <Tag color="green">Active</Tag>}
                </div>
            )
        },
        {
            title: 'Order',
            dataIndex: 'displayOrder',
            key: 'displayOrder',
            width: 80
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (record: CustomFieldGroup) => (
                <Space>
                    <Tooltip title="Edit">
                        <Button 
                            type="text" 
                            icon={<EditOutlined />} 
                            onClick={() => handleEditGroup(record)}
                            className="text-theme-primary"
                        />
                    </Tooltip>
                    <Popconfirm
                        title="Delete Group"
                        description="This will soft delete the group. Are you sure?"
                        onConfirm={() => handleDeleteGroup(record.id!)}
                        okText="Yes"
                        cancelText="No"
                        okButtonProps={{ 
                            type: 'default'
                        }}
                        cancelButtonProps={{ 
                            type: 'default'
                        }}
                        icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
                    >
                        <Tooltip title="Delete">
                            <Button 
                                type="text" 
                                icon={<DeleteOutlined />} 
                                className="text-theme-danger"
                            />
                        </Tooltip>
                    </Popconfirm>
                </Space>
            )
        }
    ];

    return (
        <div className="p-6">
            <div className="mb-6">
                <div className="flex items-center mb-2">
                    <SettingOutlined className="mr-2 text-theme-text-primary" />
                    <h1 className="text-2xl font-bold text-theme-text-primary m-0">
                        Custom Fields Management
                    </h1>
                </div>
                <p className="text-theme-text-secondary text-base m-0">
                    Manage custom fields and field groups for your organization
                </p>
            </div>

            {isSxAdmin && (
                <Alert
                    message="SX Admin Mode"
                    description="You can create default custom fields that will be available as templates for support organizations."
                    type="info"
                    showIcon
                    className="mb-4"
                />
            )}

            <Row gutter={[16, 16]}>
                <Col span={24}>
                    <Card
                        title={
                            <div className="flex items-center justify-between">
                                <span className="text-theme-text-primary">Custom Fields</span>
                                <Button 
                                    type="primary" 
                                    icon={<PlusOutlined />}
                                    onClick={handleCreateField}
                                    className="theme-aware-button-primary"
                                >
                                    Add Field
                                </Button>
                            </div>
                        }
                        className="theme-aware-card"
                    >
                        <Table
                            columns={columns}
                            dataSource={customFields}
                            rowKey="id"
                            loading={loading}
                            pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true
                            }}
                            scroll={{ x: 1200 }}
                        />
                    </Card>
                </Col>
            </Row>

            <Row gutter={[16, 16]} className="mt-4">
                <Col span={24}>
                    <Card
                        title={
                            <div className="flex items-center justify-between">
                                <span className="text-theme-text-primary">Field Groups</span>
                                <Button 
                                    type="primary" 
                                    icon={<PlusOutlined />}
                                    onClick={handleCreateGroup}
                                    size="small"
                                    className="theme-aware-button-primary"
                                >
                                    Add Group
                                </Button>
                            </div>
                        }
                        className="theme-aware-card"
                    >
                        <Table
                            columns={groupColumns}
                            dataSource={customFieldGroups}
                            rowKey="id"
                            pagination={{
                                pageSize: 5,
                                showSizeChanger: true,
                                showQuickJumper: true
                            }}
                            scroll={{ x: 800 }}
                        />
                    </Card>
                </Col>
            </Row>

            {/* Custom Field Modal */}
            <Modal
                title={editingField ? 'Edit Custom Field' : 'Create Custom Field'}
                open={modalVisible}
                onCancel={() => setModalVisible(false)}
                footer={null}
                width={800}
                className="theme-aware-modal"
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSubmit}
                    initialValues={{
                        isRequired: false,
                        isUnique: false,
                        isActive: true,
                        displayOrder: 0,
                        isTemplate: isSxAdmin
                    }}
                >
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="name"
                                label="Field Name"
                                rules={[
                                    { required: true, message: 'Please enter field name' },
                                    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: 'Field name must start with letter or underscore and contain only letters, numbers, and underscores' }
                                ]}
                            >
                                <Input placeholder="e.g., priority_level" className="theme-aware-input" />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="label"
                                label="Display Label"
                                rules={[{ required: true, message: 'Please enter display label' }]}
                            >
                                <Input placeholder="e.g., Priority Level" className="theme-aware-input" />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item
                        name="description"
                        label="Description"
                    >
                        <TextArea rows={2} placeholder="Optional description of the field" className="theme-aware-textarea" />
                    </Form.Item>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="fieldType"
                                label="Field Type"
                                rules={[{ required: true, message: 'Please select field type' }]}
                            >
                                <Select placeholder="Select field type" className="theme-aware-select">
                                    {fieldTypes.map(type => (
                                        <Option key={type.value} value={type.value}>
                                            {type.label}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="dataType"
                                label="Data Type"
                                rules={[{ required: true, message: 'Please select data type' }]}
                            >
                                <Select placeholder="Select data type" className="theme-aware-select">
                                    {dataTypes.map(type => (
                                        <Option key={type.value} value={type.value}>
                                            {type.label}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={8}>
                            <Form.Item
                                name="isRequired"
                                label="Required"
                                valuePropName="checked"
                            >
                                <Switch />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                name="isUnique"
                                label="Unique"
                                valuePropName="checked"
                            >
                                <Switch />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                name="isActive"
                                label="Active"
                                valuePropName="checked"
                            >
                                <Switch />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="defaultValue"
                                label="Default Value"
                            >
                                <Input placeholder="Default value for the field" className="theme-aware-input" />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="placeholder"
                                label="Placeholder"
                            >
                                <Input placeholder="Placeholder text" className="theme-aware-input" />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item
                        name="helpText"
                        label="Help Text"
                    >
                        <TextArea rows={2} placeholder="Help text to display to users" className="theme-aware-textarea" />
                    </Form.Item>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                name="displayOrder"
                                label="Display Order"
                                rules={[{ required: true, message: 'Please enter display order' }]}
                            >
                                <InputNumber 
                                    min={0} 
                                    style={{ width: '100%' }} 
                                    className="theme-aware-input"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                name="groupId"
                                label="Field Group"
                            >
                                <Select 
                                    placeholder="Select group (optional)" 
                                    allowClear
                                    className="theme-aware-select"
                                >
                                    {customFieldGroups.map(group => (
                                        <Option key={group.id} value={group.id}>
                                            {group.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                    <Divider className="text-theme-text-primary border-theme-border-primary">
                        <span className="text-theme-text-primary font-medium">Advanced Settings</span>
                    </Divider>

                    <Form.Item
                        name="validationRules"
                        label="Validation Rules (JSON)"
                        tooltip="JSON object containing validation rules like minLength, maxLength, pattern, etc."
                    >
                        <TextArea 
                            rows={4} 
                            placeholder='{"minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z]+$"}'
                            className="theme-aware-textarea"
                        />
                    </Form.Item>

                    <Form.Item
                        name="fieldOptions"
                        label="Field Options (JSON)"
                        tooltip="JSON object containing field-specific options like dropdown choices, etc."
                    >
                        <TextArea 
                            rows={4} 
                            placeholder='{"choices": ["Option 1", "Option 2", "Option 3"]}'
                            className="theme-aware-textarea"
                        />
                    </Form.Item>

                    <div className="flex justify-end space-x-2">
                        <Button onClick={() => setModalVisible(false)}>
                            Cancel
                        </Button>
                        <Button 
                            type="primary" 
                            htmlType="submit"
                            className="theme-aware-button-primary"
                        >
                            {editingField ? 'Update Field' : 'Create Field'}
                        </Button>
                    </div>
                </Form>
            </Modal>

            {/* Field Group Modal */}
            <Modal
                title={editingGroup ? 'Edit Field Group' : 'Create Field Group'}
                open={groupModalVisible}
                onCancel={() => setGroupModalVisible(false)}
                footer={null}
                width={600}
                className="theme-aware-modal"
            >
                <Form
                    form={groupForm}
                    layout="vertical"
                    onFinish={handleGroupSubmit}
                    initialValues={{
                        isCollapsible: false,
                        isActive: true,
                        displayOrder: 0
                    }}
                >
                    <Row gutter={16}>
                        <Col span={24}>
                            <Form.Item
                                name="name"
                                label="Group Name"
                                rules={[
                                    { required: true, message: 'Please enter group name' },
                                    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: 'Group name must start with letter or underscore and contain only letters, numbers, and underscores' }
                                ]}
                            >
                                <Input placeholder="e.g., ticket_details" className="theme-aware-input" />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Form.Item
                        name="description"
                        label="Description"
                    >
                        <TextArea rows={2} placeholder="Optional description of the group" className="theme-aware-textarea" />
                    </Form.Item>

                    <Row gutter={16}>
                        <Col span={8}>
                            <Form.Item
                                name="isCollapsible"
                                label="Collapsible"
                                valuePropName="checked"
                            >
                                <Switch />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                name="isActive"
                                label="Active"
                                valuePropName="checked"
                            >
                                <Switch />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                name="displayOrder"
                                label="Display Order"
                                rules={[{ required: true, message: 'Please enter display order' }]}
                            >
                                <InputNumber min={0} style={{ width: '100%' }} className="theme-aware-input" />
                            </Form.Item>
                        </Col>
                    </Row>

                    <div className="flex justify-end space-x-2">
                        <Button onClick={() => setGroupModalVisible(false)}>
                            Cancel
                        </Button>
                        <Button 
                            type="primary" 
                            htmlType="submit"
                            className="theme-aware-button-primary"
                        >
                            {editingGroup ? 'Update Group' : 'Create Group'}
                        </Button>
                    </div>
                </Form>
            </Modal>
        </div>
    );
};

export default CustomFieldsPage; 