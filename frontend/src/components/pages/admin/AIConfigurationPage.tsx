import React, { useState, useEffect } from 'react';
import {
    Card,
    Switch,
    Slider,
    Select,
    Form,
    Button,
    message,
    Spin,
    Divider,
    Row,
    Col,
    Space,
    Input,
    Alert
} from 'antd';
import { 
    RobotOutlined, 
    SaveOutlined,
    ReloadOutlined,
    InfoCircleOutlined
} from '@ant-design/icons';
import { useAuth } from '../../../contexts/AuthContext';
import { aiConfigurationService } from '../../../services/aiConfigurationService';
import { 
    AutonomyConfiguration, 
    CapabilitiesConfiguration, 
    AUTONOMY_LEVEL_LABELS
} from '../../../types/aiConfiguration';

const { Option } = Select;

// Enhanced suggestion types with capability information
const ENHANCED_SUGGESTION_TYPES = {
    'ticket_classification': {
        label: 'Ticket Classification',
        description: 'Automatically categorize tickets by type, priority, and category',
        capability: 'ticketClassification'
    },
    'priority_assessment': {
        label: 'Priority Assessment',
        description: 'AI-powered priority scoring and urgency evaluation',
        capability: 'priorityAssessment'
    },
    'auto_assignment': {
        label: 'Auto Assignment',
        description: 'Intelligent agent assignment based on skills and workload',
        capability: 'autoAssignment'
    },
    'response_generation': {
        label: 'Response Generation',
        description: 'Generate contextual responses and suggestions',
        capability: 'responseGeneration'
    },
    'sentiment_analysis': {
        label: 'Sentiment Analysis',
        description: 'Analyze customer sentiment and emotional context',
        capability: 'sentimentAnalysis'
    },
    'escalation_prediction': {
        label: 'Escalation Prediction',
        description: 'Predict when tickets are likely to escalate',
        capability: 'escalationPrediction'
    },
    'knowledge_base_search': {
        label: 'Knowledge Base Search',
        description: 'Intelligent search and retrieval from knowledge base',
        capability: 'knowledgeBaseSearch'
    },
    'workflow_automation': {
        label: 'Workflow Automation',
        description: 'Automate repetitive workflows and processes',
        capability: 'workflowAutomation'
    },
    'quality_assurance': {
        label: 'Quality Assurance',
        description: 'AI-powered quality checks and validation',
        capability: 'qualityAssurance'
    },
    'performance_analytics': {
        label: 'Performance Analytics',
        description: 'Advanced analytics and performance insights',
        capability: 'performanceAnalytics'
    }
};

const AIConfigurationPage: React.FC = () => {
    const { user } = useAuth();
    const [loading, setLoading] = useState(false);
    const [autonomyConfig, setAutonomyConfig] = useState<AutonomyConfiguration | null>(null);
    const [capabilitiesConfig, setCapabilitiesConfig] = useState<CapabilitiesConfiguration | null>(null);
    const [autonomyForm] = Form.useForm();
    const [error, setError] = useState<string | null>(null);

    const organizationId = user?.organizationId || 1;

    useEffect(() => {
        loadConfigurations();
    }, [organizationId]);

    const loadConfigurations = async () => {
        setLoading(true);
        setError(null);
        try {
            const combinedConfig = await aiConfigurationService.getAutonomyConfiguration(organizationId);

            setAutonomyConfig(combinedConfig);
            setCapabilitiesConfig(combinedConfig.capabilities || null);

            // Set form values
            autonomyForm.setFieldsValue(combinedConfig);
        } catch (error) {
            const errorMessage = 'Failed to load AI configurations. Please check if the AI service is running.';
            setError(errorMessage);
            message.error(errorMessage);
            console.error('Error loading configurations:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleAutonomySubmit = async (values: any) => {
        setLoading(true);
        setError(null);
        try {
            const updatedConfig: AutonomyConfiguration = {
                ...autonomyConfig!,
                ...values,
                organizationId
            };

            await aiConfigurationService.updateAutonomyConfiguration(organizationId, updatedConfig);
            setAutonomyConfig(updatedConfig);
            message.success('AI configuration updated successfully');
        } catch (error) {
            const errorMessage = 'Failed to update AI configuration';
            setError(errorMessage);
            message.error(errorMessage);
            console.error('Error updating AI configuration:', error);
        } finally {
            setLoading(false);
        }
    };

    const renderSuggestionTypeField = (type: string, config: any) => {
        return (
            <Col span={12} key={type}>
                <Form.Item
                    name={['suggestionTypeAutonomy', type]}
                    label={
                        <div className="flex items-center gap-2">
                            <span className="text-theme-text-primary font-medium">{config.label}</span>
                        </div>
                    }
                    extra={
                        <div className="text-theme-text-secondary text-sm mt-1">
                            {config.description}
                        </div>
                    }
                >
                    <Select
                        placeholder="Select autonomy level"
                        className="theme-aware-select"
                    >
                        {Object.entries(AUTONOMY_LEVEL_LABELS).map(([level, levelLabel]) => (
                            <Option key={level} value={level}>
                                {levelLabel}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
            </Col>
        );
    };

    if (loading && !autonomyConfig && !capabilitiesConfig) {
        return (
            <div className="flex items-center justify-center min-h-64 bg-theme-bg-primary">
                <div className="text-center">
                    <Spin size="large" />
                    <div className="mt-4 text-theme-text-primary">Loading AI configurations...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-theme-bg-primary text-theme-text-primary p-6">
            <div className="mb-6">
                <div className="flex items-center mb-2">
                    <RobotOutlined className="mr-2 text-theme-text-primary" />
                    <h1 className="text-2xl font-bold text-theme-text-primary m-0">
                        AI Configuration
                    </h1>
                </div>
                <p className="text-theme-text-secondary text-base m-0">
                    Configure AI autonomy levels and capabilities for your organization
                </p>
            </div>

            {error && (
                <Alert
                    message="Configuration Error"
                    description={error}
                    type="error"
                    showIcon
                    icon={<InfoCircleOutlined />}
                    className="mb-6"
                    action={
                        <Button size="small" onClick={loadConfigurations}>
                            Retry
                        </Button>
                    }
                />
            )}

            <Card className="theme-aware-card">
                <Form
                    form={autonomyForm}
                    layout="vertical"
                    onFinish={handleAutonomySubmit}
                    initialValues={autonomyConfig || undefined}
                >
                    <Row gutter={[24, 16]}>
                        <Col span={24}>
                            <Form.Item
                                name="enabled"
                                label={<span className="text-theme-text-primary font-medium">Enable AI Autonomy</span>}
                                valuePropName="checked"
                            >
                                <Switch />
                            </Form.Item>
                        </Col>

                        <Col span={24}>
                            <Form.Item
                                name="description"
                                label={<span className="text-theme-text-primary font-medium">Configuration Description</span>}
                            >
                                <Input.TextArea
                                    rows={3}
                                    placeholder="Describe this AI configuration and its purpose..."
                                    className="theme-aware-input"
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Divider className="border-theme-border-primary">
                        <span className="text-theme-text-primary font-semibold">Confidence Thresholds</span>
                    </Divider>
                    
                    <Row gutter={[24, 16]}>
                        <Col span={8}>
                            <Form.Item
                                name={['confidenceThresholds', 'advisor']}
                                label={<span className="text-theme-text-primary font-medium">Advisor Threshold</span>}
                            >
                                <Slider
                                    min={0}
                                    max={1}
                                    step={0.1}
                                    marks={{
                                        0: '0%',
                                        0.5: '50%',
                                        1: '100%'
                                    }}
                                    className="theme-aware-slider"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                name={['confidenceThresholds', 'hitl']}
                                label={<span className="text-theme-text-primary font-medium">HITL Threshold</span>}
                            >
                                <Slider
                                    min={0}
                                    max={1}
                                    step={0.1}
                                    marks={{
                                        0: '0%',
                                        0.5: '50%',
                                        1: '100%'
                                    }}
                                    className="theme-aware-slider"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                name={['confidenceThresholds', 'fullAutonomy']}
                                label={<span className="text-theme-text-primary font-medium">Full Autonomy Threshold</span>}
                            >
                                <Slider
                                    min={0}
                                    max={1}
                                    step={0.1}
                                    marks={{
                                        0: '0%',
                                        0.5: '50%',
                                        1: '100%'
                                    }}
                                    className="theme-aware-slider"
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Divider className="border-theme-border-primary">
                        <span className="text-theme-text-primary font-semibold">AI Capabilities & Autonomy Levels</span>
                        <div className="text-theme-text-secondary text-sm font-normal mt-1">
                            Configure autonomy levels for each AI capability. Choose "AI Disabled" to turn off specific capabilities.
                        </div>
                    </Divider>
                    
                    <Row gutter={[16, 16]}>
                        {Object.entries(ENHANCED_SUGGESTION_TYPES).map(([type, config]) => 
                            renderSuggestionTypeField(type, config)
                        )}
                    </Row>

                    <Form.Item>
                        <Space>
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={loading}
                                icon={<SaveOutlined />}
                                className="theme-aware-button-primary"
                            >
                                Save AI Configuration
                            </Button>
                            <Button
                                onClick={loadConfigurations}
                                icon={<ReloadOutlined />}
                                className="theme-aware-button-secondary"
                            >
                                Reset
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Card>
        </div>
    );
};

export default AIConfigurationPage;
