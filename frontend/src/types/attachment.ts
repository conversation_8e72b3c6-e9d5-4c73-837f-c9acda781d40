/**
 * Attachment-related TypeScript interfaces and types
 */

export interface Attachment {
  id: number;
  description?: string;
  fileUrl: string;
  entityType: string;
  entityId: number;
  originalFilename?: string;
  contentType?: string;
  fileSize?: number;
  fileExtension?: string;
  fileTypeCategory?: string;
  formattedFileSize?: string;
  organizationId: number;
  supportOrganizationId: number;
  createdAt: string;
  updatedAt: string;
  createdById: number;
  updatedById: number;
  createdBy: string;
  updatedBy: string;
  onBehalfOfId?: number;
}

export interface AttachmentUploadRequest {
  file: File;
  entityType: string;
  entityId: number;
  description?: string;
}

export interface AttachmentUploadResponse {
  attachment: Attachment;
  success: boolean;
  message?: string;
}

export interface BulkDownloadRequest {
  entityType: string;
  entityId: number;
  filename?: string;
}

export interface BulkDownloadByIdsRequest {
  attachmentIds: number[];
  filename?: string;
}

export interface FileValidationError {
  code: string;
  message: string;
  filename?: string;
}

export interface UploadProgress {
  filename: string;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
}

export type FileTypeCategory = 
  | 'image' 
  | 'document' 
  | 'video' 
  | 'audio' 
  | 'archive' 
  | 'code' 
  | 'other';

export interface FileTypeInfo {
  category: FileTypeCategory;
  icon: string;
  color: string;
  allowedExtensions: string[];
}

export const FILE_TYPE_CONFIG: Record<FileTypeCategory, FileTypeInfo> = {
  image: {
    category: 'image',
    icon: 'file-image',
    color: '#52c41a',
    allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico', 'tiff', 'tif']
  },
  document: {
    category: 'document',
    icon: 'file-text',
    color: '#1890ff',
    allowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'odt', 'ods', 'odp']
  },
  video: {
    category: 'video',
    icon: 'video-camera',
    color: '#722ed1',
    allowedExtensions: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v']
  },
  audio: {
    category: 'audio',
    icon: 'audio',
    color: '#eb2f96',
    allowedExtensions: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a']
  },
  archive: {
    category: 'archive',
    icon: 'file-zip',
    color: '#fa8c16',
    allowedExtensions: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2']
  },
  code: {
    category: 'code',
    icon: 'code',
    color: '#13c2c2',
    allowedExtensions: ['json', 'xml', 'csv', 'md', 'yml', 'yaml']
  },
  other: {
    category: 'other',
    icon: 'file',
    color: '#8c8c8c',
    allowedExtensions: []
  }
};

export const BLOCKED_FILE_EXTENSIONS = [
  'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'app', 'deb', 'pkg', 'dmg',
  'msi', 'run', 'bin', 'sh', 'ps1', 'psm1', 'psd1', 'reg', 'inf', 'scf', 'lnk', 'url'
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes

export interface AttachmentListProps {
  entityType: string;
  entityId: number;
  showUpload?: boolean;
  showBulkDownload?: boolean;
  maxFiles?: number;
  allowedFileTypes?: string[];
  onAttachmentAdded?: (attachment: Attachment) => void;
  onAttachmentRemoved?: (attachmentId: number) => void;
}

export interface AttachmentUploadProps {
  entityType: string;
  entityId: number;
  maxFiles?: number;
  allowedFileTypes?: string[];
  showFileList?: boolean;
  onUploadComplete?: (attachments: Attachment[]) => void;
  onUploadError?: (error: string) => void;
}

export interface AttachmentIconProps {
  attachment: Attachment;
  size?: 'small' | 'default' | 'large';
  showTooltip?: boolean;
}

/**
 * Utility functions for file type detection and validation
 */
export const getFileExtension = (filename: string): string => {
  if (!filename) return '';
  const lastDot = filename.lastIndexOf('.');
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
};

export const getFileTypeCategory = (filename: string): FileTypeCategory => {
  const extension = getFileExtension(filename);
  
  for (const [category, config] of Object.entries(FILE_TYPE_CONFIG)) {
    if (config.allowedExtensions.includes(extension)) {
      return category as FileTypeCategory;
    }
  }
  
  return 'other';
};

export const isFileTypeAllowed = (filename: string): boolean => {
  const extension = getFileExtension(filename);
  return !BLOCKED_FILE_EXTENSIONS.includes(extension);
};

export const formatFileSize = (bytes: number): string => {
  if (!bytes || bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

export const validateFile = (file: File): FileValidationError | null => {
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return {
      code: 'FILE_TOO_LARGE',
      message: `File size exceeds maximum allowed size of ${formatFileSize(MAX_FILE_SIZE)}`,
      filename: file.name
    };
  }
  
  // Check file type
  if (!isFileTypeAllowed(file.name)) {
    return {
      code: 'FILE_TYPE_NOT_ALLOWED',
      message: `File type not allowed: ${getFileExtension(file.name)}`,
      filename: file.name
    };
  }
  
  return null;
};
