export interface AIConfiguration {
    id?: number;
    configurationType: string;
    supportOrganizationId: number;
    configurationData: any;
    isActive: boolean;
    createdAt?: string;
    updatedAt?: string;
}

export interface AutonomyConfiguration {
    organizationId: number;
    enabled: boolean;
    description: string;
    suggestionTypeAutonomy: Record<string, AutonomyLevel>;
    confidenceThresholds: ConfidenceThresholds;
    capabilities?: CapabilitiesConfiguration;
}

export interface ConfidenceThresholds {
    fullAutonomy: number;
    hitl: number;
    advisor: number;
}

export interface CapabilitiesConfiguration {
    enabled: boolean;
    ticketClassification: boolean;
    priorityAssessment: boolean;
    autoAssignment: boolean;
    responseGeneration: boolean;
    sentimentAnalysis: boolean;
    escalationPrediction: boolean;
    knowledgeBaseSearch: boolean;
    workflowAutomation: boolean;
    qualityAssurance: boolean;
    performanceAnalytics: boolean;
}

export type AutonomyLevel = 'ADVISOR' | 'HITL' | 'FULL_AUTONOMY' | 'AI_DISABLED';

export type SuggestionType = 
    | 'CLASSIFICATION'
    | 'ASSIGNMENT'
    | 'PRIORITY_ADJUSTMENT'
    | 'ESCALATION'
    | 'NOTIFICATION'
    | 'STATUS_UPDATE'
    | 'COMMENT_ADDITION'
    | 'SLA_ALERT'
    | 'PROCESS_IMPROVEMENT'
    | 'CUSTOMER_COMMUNICATION'
    | 'RESOURCE_ALLOCATION';

export interface SuggestionTypesResponse {
    suggestionTypes: string[];
    autonomyLevels: string[];
}

export const AUTONOMY_LEVEL_LABELS: Record<AutonomyLevel, string> = {
    'ADVISOR': 'Advisor (Read-only suggestions)',
    'HITL': 'Human-in-the-Loop (Approval required)',
    'FULL_AUTONOMY': 'Full Autonomy (Automatic execution)',
    'AI_DISABLED': 'AI Disabled (Capability turned off)'
};

export const SUGGESTION_TYPE_LABELS: Record<SuggestionType, string> = {
    'CLASSIFICATION': 'Ticket Classification',
    'ASSIGNMENT': 'Ticket Assignment',
    'PRIORITY_ADJUSTMENT': 'Priority Adjustment',
    'ESCALATION': 'Escalation',
    'NOTIFICATION': 'Notifications',
    'STATUS_UPDATE': 'Status Updates',
    'COMMENT_ADDITION': 'Comment Addition',
    'SLA_ALERT': 'SLA Alerts',
    'PROCESS_IMPROVEMENT': 'Process Improvement',
    'CUSTOMER_COMMUNICATION': 'Customer Communication',
    'RESOURCE_ALLOCATION': 'Resource Allocation'
};
