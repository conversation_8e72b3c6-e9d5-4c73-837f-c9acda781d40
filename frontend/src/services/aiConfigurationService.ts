import { AIBaseService } from './AIBaseService';
import { 
    AIConfiguration, 
    AutonomyConfiguration, 
    CapabilitiesConfiguration,
    SuggestionTypesResponse 
} from '../types/aiConfiguration';

export class AIConfigurationService extends AIBaseService {
    
    /**
     * Get all AI configurations for an organization
     */
    async getAllConfigurations(organizationId: number): Promise<AIConfiguration[]> {
        try {
            const response = await this.get(`/ai/config/all/${organizationId}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching AI configurations:', error);
            throw error;
        }
    }

    /**
     * Get autonomy configuration for an organization
     */
    async getAutonomyConfiguration(organizationId: number): Promise<AutonomyConfiguration> {
        try {
            const response = await this.get(`/ai/config/autonomy/${organizationId}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching autonomy configuration:', error);
            throw error;
        }
    }

    /**
     * Update autonomy configuration for an organization
     */
    async updateAutonomyConfiguration(organizationId: number, config: AutonomyConfiguration): Promise<AIConfiguration> {
        try {
            const response = await this.put(`/ai/config/autonomy/${organizationId}`, config);
            return response.data;
        } catch (error) {
            console.error('Error updating autonomy configuration:', error);
            throw error;
        }
    }

    /**
     * Get AI capabilities configuration for an organization
     */
    async getCapabilitiesConfiguration(organizationId: number): Promise<CapabilitiesConfiguration> {
        try {
            const response = await this.get(`/ai/config/capabilities/${organizationId}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching capabilities configuration:', error);
            throw error;
        }
    }

    /**
     * Update AI capabilities configuration for an organization
     */
    async updateCapabilitiesConfiguration(organizationId: number, config: CapabilitiesConfiguration): Promise<AIConfiguration> {
        try {
            const response = await this.put(`/ai/config/capabilities/${organizationId}`, config);
            return response.data;
        } catch (error) {
            console.error('Error updating capabilities configuration:', error);
            throw error;
        }
    }

    /**
     * Get available suggestion types and autonomy levels
     */
    async getSuggestionTypes(): Promise<SuggestionTypesResponse> {
        try {
            const response = await this.get('/ai/config/suggestion-types');
            return response.data;
        } catch (error) {
            console.error('Error fetching suggestion types:', error);
            throw error;
        }
    }

    /**
     * Get general AI configuration for an organization
     */
    async getGeneralConfiguration(organizationId: number): Promise<any> {
        try {
            const response = await this.get(`/ai/config/${organizationId}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching general AI configuration:', error);
            throw error;
        }
    }

    /**
     * Update general AI configuration for an organization
     */
    async updateGeneralConfiguration(organizationId: number, config: any): Promise<AIConfiguration> {
        try {
            const response = await this.put(`/ai/config/${organizationId}`, config);
            return response.data;
        } catch (error) {
            console.error('Error updating general AI configuration:', error);
            throw error;
        }
    }

    /**
     * Health check for AI configuration service
     */
    async healthCheck(): Promise<any> {
        try {
            const response = await this.get('/ai/config/health');
            return response.data;
        } catch (error) {
            console.error('Error checking AI service health:', error);
            throw error;
        }
    }
}

// Export singleton instance
export const aiConfigurationService = new AIConfigurationService();
