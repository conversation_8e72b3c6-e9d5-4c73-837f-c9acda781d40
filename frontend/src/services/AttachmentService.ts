import axios, { AxiosResponse } from 'axios';
import { 
  Attachment, 
  AttachmentUploadRequest, 
  BulkDownloadRequest, 
  BulkDownloadByIdsRequest 
} from '../types/attachment';

/**
 * Service class for handling attachment API operations
 */
class AttachmentService {
  private readonly baseURL = '/api/v1/attachments';

  /**
   * Upload a single attachment
   */
  async uploadAttachment(request: AttachmentUploadRequest): Promise<Attachment> {
    const formData = new FormData();
    formData.append('file', request.file);
    formData.append('entityType', request.entityType);
    formData.append('entityId', request.entityId.toString());
    
    if (request.description) {
      formData.append('description', request.description);
    }

    const response: AxiosResponse<Attachment> = await axios.post(
      this.baseURL,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return response.data;
  }

  /**
   * Upload multiple attachments with progress tracking
   */
  async uploadAttachments(
    requests: AttachmentUploadRequest[],
    onProgress?: (filename: string, progress: number) => void
  ): Promise<Attachment[]> {
    const uploadPromises = requests.map(async (request) => {
      const formData = new FormData();
      formData.append('file', request.file);
      formData.append('entityType', request.entityType);
      formData.append('entityId', request.entityId.toString());
      
      if (request.description) {
        formData.append('description', request.description);
      }

      const response: AxiosResponse<Attachment> = await axios.post(
        this.baseURL,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            if (onProgress && progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              onProgress(request.file.name, progress);
            }
          },
        }
      );

      return response.data;
    });

    return Promise.all(uploadPromises);
  }

  /**
   * Get attachment by ID
   */
  async getAttachment(id: number): Promise<Attachment> {
    const response: AxiosResponse<Attachment> = await axios.get(`${this.baseURL}/${id}`);
    return response.data;
  }

  /**
   * Get attachments by entity type and ID
   */
  async getAttachmentsByEntity(entityType: string, entityId: number): Promise<Attachment[]> {
    const response: AxiosResponse<Attachment[]> = await axios.get(this.baseURL, {
      params: {
        entityType,
        entityId,
      },
    });
    return response.data;
  }

  /**
   * Get attachments by organization ID
   */
  async getAttachmentsByOrganization(organizationId: number): Promise<Attachment[]> {
    const response: AxiosResponse<Attachment[]> = await axios.get(
      `${this.baseURL}/organization/${organizationId}`
    );
    return response.data;
  }

  /**
   * Update attachment
   */
  async updateAttachment(id: number, attachment: Partial<Attachment>): Promise<Attachment> {
    const response: AxiosResponse<Attachment> = await axios.put(
      `${this.baseURL}/${id}`,
      attachment
    );
    return response.data;
  }

  /**
   * Delete attachment
   */
  async deleteAttachment(id: number): Promise<void> {
    await axios.delete(`${this.baseURL}/${id}`);
  }

  /**
   * Delete all attachments for an entity
   */
  async deleteAttachmentsByEntity(entityType: string, entityId: number): Promise<void> {
    await axios.delete(this.baseURL, {
      params: {
        entityType,
        entityId,
      },
    });
  }

  /**
   * Download a single attachment
   */
  async downloadAttachment(id: number, filename?: string): Promise<void> {
    const response = await axios.get(`${this.baseURL}/${id}/download`, {
      responseType: 'blob',
    });

    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // Get filename from response headers or use provided filename
    const contentDisposition = response.headers['content-disposition'];
    let downloadFilename = filename;
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        downloadFilename = filenameMatch[1];
      }
    }
    
    link.setAttribute('download', downloadFilename || 'attachment');
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  }

  /**
   * Bulk download attachments by entity
   */
  async bulkDownloadByEntity(request: BulkDownloadRequest): Promise<void> {
    const response = await axios.get(`${this.baseURL}/bulk-download`, {
      params: {
        entityType: request.entityType,
        entityId: request.entityId,
        filename: request.filename,
      },
      responseType: 'blob',
    });

    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // Get filename from response headers or use provided filename
    const contentDisposition = response.headers['content-disposition'];
    let downloadFilename = request.filename;
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        downloadFilename = filenameMatch[1];
      }
    }
    
    link.setAttribute('download', downloadFilename || 'attachments.zip');
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  }

  /**
   * Bulk download attachments by IDs
   */
  async bulkDownloadByIds(request: BulkDownloadByIdsRequest): Promise<void> {
    const response = await axios.post(`${this.baseURL}/bulk-download`, request.attachmentIds, {
      params: {
        filename: request.filename,
      },
      responseType: 'blob',
    });

    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // Get filename from response headers or use provided filename
    const contentDisposition = response.headers['content-disposition'];
    let downloadFilename = request.filename;
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        downloadFilename = filenameMatch[1];
      }
    }
    
    link.setAttribute('download', downloadFilename || 'attachments.zip');
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  }

  /**
   * Get direct download URL for an attachment
   */
  getDownloadUrl(id: number): string {
    return `${this.baseURL}/${id}/download`;
  }

  /**
   * Get bulk download URL for entity
   */
  getBulkDownloadUrl(entityType: string, entityId: number, filename?: string): string {
    const params = new URLSearchParams({
      entityType,
      entityId: entityId.toString(),
    });
    
    if (filename) {
      params.append('filename', filename);
    }
    
    return `${this.baseURL}/bulk-download?${params.toString()}`;
  }
}

// Export singleton instance
export const attachmentService = new AttachmentService();
export default attachmentService;
