import { BaseService } from './BaseService';

export interface CustomFieldDefinition {
    id?: number;
    name: string;
    label: string;
    description?: string;
    fieldType: string;
    dataType: string;
    isRequired: boolean;
    isUnique: boolean;
    defaultValue?: string;
    placeholder?: string;
    helpText?: string;
    displayOrder: number;
    groupId?: number;
    organizationId?: number; // Optional - populated by backend
    supportOrganizationId?: number; // Optional - populated by backend
    version: number;
    isActive: boolean;
    isTemplate: boolean;
    templateId?: number;
    validationRules?: Record<string, any>;
    fieldOptions?: Record<string, any>;
    createdAt?: string;
    updatedAt?: string;
    createdBy?: string;
    updatedBy?: string;
}

export interface CustomFieldGroup {
    id?: number;
    name: string;
    description?: string;
    displayOrder: number;
    isCollapsible: boolean;
    organizationId?: number; // Optional - populated by backend
    supportOrganizationId?: number; // Optional - populated by backend
    isActive: boolean;
    createdAt?: string;
    updatedAt?: string;
    createdBy?: string;
    updatedBy?: string;
}

export interface CreateCustomFieldDefinitionRequest {
    name: string;
    label: string;
    description?: string;
    fieldType: string;
    dataType: string;
    isRequired?: boolean;
    isUnique?: boolean;
    defaultValue?: string;
    placeholder?: string;
    helpText?: string;
    displayOrder?: number;
    groupId?: number;
    version?: number;
    isTemplate?: boolean;
    isActive?: boolean;
    templateId?: number;
    validationRules?: Record<string, any>;
    fieldOptions?: Record<string, any>;
}

export interface UpdateCustomFieldDefinitionRequest {
    name?: string;
    label?: string;
    description?: string;
    fieldType?: string;
    dataType?: string;
    isRequired?: boolean;
    isUnique?: boolean;
    defaultValue?: string;
    placeholder?: string;
    helpText?: string;
    displayOrder?: number;
    groupId?: number;
    isActive?: boolean;
    templateId?: number;
    validationRules?: Record<string, any>;
    fieldOptions?: Record<string, any>;
}

export interface CreateCustomFieldGroupRequest {
    name: string;
    description?: string;
    displayOrder?: number;
    isCollapsible?: boolean;
}

export interface UpdateCustomFieldGroupRequest {
    name?: string;
    description?: string;
    displayOrder?: number;
    isCollapsible?: boolean;
    isActive?: boolean;
}

class CustomFieldsService extends BaseService {
    private baseUrl = '/custom-fields';

    // Custom Field Definitions
    async getCustomFieldDefinitions(organizationId?: number): Promise<CustomFieldDefinition[]> {
        const params = organizationId ? `?organizationId=${organizationId}` : '';
        const response = await this.get(`${this.baseUrl}/definitions${params}`);
        return response.data;
    }

    async getCustomFieldDefinition(id: number): Promise<CustomFieldDefinition> {
        const response = await this.get(`${this.baseUrl}/definitions/${id}`);
        return response.data;
    }

    async createCustomFieldDefinition(data: CreateCustomFieldDefinitionRequest): Promise<CustomFieldDefinition> {
        const response = await this.post(`${this.baseUrl}/definitions`, data);
        return response.data;
    }

    async updateCustomFieldDefinition(id: number, data: UpdateCustomFieldDefinitionRequest): Promise<CustomFieldDefinition> {
        const response = await this.put(`${this.baseUrl}/definitions/${id}`, data);
        return response.data;
    }

    async deleteCustomFieldDefinition(id: number): Promise<void> {
        await this.delete(`${this.baseUrl}/definitions/${id}`);
    }

    async activateCustomFieldDefinition(id: number): Promise<void> {
        await this.put(`${this.baseUrl}/definitions/${id}/activate`, {});
    }

    async deactivateCustomFieldDefinition(id: number): Promise<void> {
        await this.put(`${this.baseUrl}/definitions/${id}/deactivate`, {});
    }

    async getTemplates(isActive?: boolean): Promise<CustomFieldDefinition[]> {
        const params = isActive !== undefined ? `?isActive=${isActive}` : '';
        const response = await this.get(`${this.baseUrl}/definitions/templates${params}`);
        return response.data;
    }

    async copyFromTemplate(templateId: number, targetOrganizationId: number): Promise<CustomFieldDefinition> {
        const response = await this.post(`${this.baseUrl}/definitions/templates/${templateId}/copy`, {
            targetOrganizationId
        });
        return response.data;
    }

    async reorderCustomFieldDefinitions(fieldIds: number[]): Promise<CustomFieldDefinition[]> {
        const response = await this.put(`${this.baseUrl}/definitions/reorder`, { fieldIds });
        return response.data;
    }

    // Custom Field Groups
    async getCustomFieldGroups(organizationId?: number): Promise<CustomFieldGroup[]> {
        const params = organizationId ? `?organizationId=${organizationId}` : '';
        const response = await this.get(`${this.baseUrl}/groups${params}`);
        return response.data;
    }

    async getCustomFieldGroup(id: number): Promise<CustomFieldGroup> {
        const response = await this.get(`${this.baseUrl}/groups/${id}`);
        return response.data;
    }

    async createCustomFieldGroup(data: CreateCustomFieldGroupRequest): Promise<CustomFieldGroup> {
        const response = await this.post(`${this.baseUrl}/groups`, data);
        return response.data;
    }

    async updateCustomFieldGroup(id: number, data: UpdateCustomFieldGroupRequest): Promise<CustomFieldGroup> {
        const response = await this.put(`${this.baseUrl}/groups/${id}`, data);
        return response.data;
    }

    async deleteCustomFieldGroup(id: number): Promise<void> {
        await this.delete(`${this.baseUrl}/groups/${id}`);
    }

    async reorderCustomFieldGroups(groupIds: number[]): Promise<CustomFieldGroup[]> {
        const response = await this.put(`${this.baseUrl}/groups/reorder`, { groupIds });
        return response.data;
    }

    // Custom Field Values (for tickets)
    async getCustomFieldValues(ticketId: number): Promise<any[]> {
        const response = await this.get(`${this.baseUrl}/values?ticketId=${ticketId}`);
        return response.data;
    }

    async saveCustomFieldValues(ticketId: number, values: Record<string, any>): Promise<any[]> {
        const response = await this.post(`${this.baseUrl}/values`, {
            ticketId,
            values
        });
        return response.data;
    }

    async updateCustomFieldValue(id: number, value: any): Promise<any> {
        const response = await this.put(`${this.baseUrl}/values/${id}`, value);
        return response.data;
    }

    async deleteCustomFieldValues(ticketId: number): Promise<void> {
        await this.delete(`${this.baseUrl}/values?ticketId=${ticketId}`);
    }

    // Validation helpers
    validateFieldName(name: string): boolean {
        return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name);
    }

    validateFieldTypeAndDataType(fieldType: string, dataType: string): boolean {
        const validFieldTypes = ['TEXT', 'NUMBER', 'DATE', 'DROPDOWN', 'CHECKBOX', 'MULTI_SELECT'];
        const validDataTypes = ['STRING', 'INTEGER', 'DECIMAL', 'BOOLEAN', 'DATE', 'TIMESTAMP'];
        
        return validFieldTypes.includes(fieldType) && validDataTypes.includes(dataType);
    }

    validateValidationRules(rules: Record<string, any>): boolean {
        // Basic validation for common rules
        const validRules = ['minLength', 'maxLength', 'pattern', 'min', 'max', 'required'];
        
        for (const key of Object.keys(rules)) {
            if (!validRules.includes(key)) {
                return false;
            }
        }
        
        return true;
    }

    // Utility methods
    getFieldTypeLabel(fieldType: string): string {
        const fieldTypeMap: Record<string, string> = {
            'TEXT': 'Text Input',
            'NUMBER': 'Number Input',
            'DATE': 'Date Picker',
            'DROPDOWN': 'Dropdown',
            'CHECKBOX': 'Checkbox',
            'MULTI_SELECT': 'Multi-Select'
        };
        return fieldTypeMap[fieldType] || fieldType;
    }

    getDataTypeLabel(dataType: string): string {
        const dataTypeMap: Record<string, string> = {
            'STRING': 'String',
            'INTEGER': 'Integer',
            'DECIMAL': 'Decimal',
            'BOOLEAN': 'Boolean',
            'DATE': 'Date',
            'TIMESTAMP': 'Timestamp'
        };
        return dataTypeMap[dataType] || dataType;
    }

    isFieldTypeCompatibleWithDataType(fieldType: string, dataType: string): boolean {
        const compatibilityMap: Record<string, string[]> = {
            'TEXT': ['STRING'],
            'NUMBER': ['INTEGER', 'DECIMAL'],
            'DATE': ['DATE', 'TIMESTAMP'],
            'DROPDOWN': ['STRING', 'INTEGER', 'DECIMAL'],
            'CHECKBOX': ['BOOLEAN'],
            'MULTI_SELECT': ['STRING']
        };
        
        const compatibleTypes = compatibilityMap[fieldType] || [];
        return compatibleTypes.includes(dataType);
    }
}

export const customFieldsService = new CustomFieldsService(); 