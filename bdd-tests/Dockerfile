FROM node:18-alpine

# Install required packages
RUN apk add --no-cache \
    bash \
    curl \
    netcat-openbsd \
    jq

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy test files
COPY features/ ./features/
COPY step_definitions/ ./step_definitions/
COPY support/ ./support/
COPY cucumber.js ./
COPY wait-for-services.sh ./

# Make scripts executable
RUN chmod +x wait-for-services.sh

# Set environment variables
ENV NODE_ENV=test
ENV BDD_APP_URL=http://app:4091
ENV BDD_AI_URL=http://ai:4082
ENV BDD_CDC_URL=http://cdc:4083

# Default command
CMD ["./wait-for-services.sh"] 