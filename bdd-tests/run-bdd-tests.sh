#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[BDD-INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[BDD-SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[BDD-WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[BDD-ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "docker-compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    
    print_success "Docker is ready"
}

# Function to check if ports are available
check_ports() {
    local ports=(4091 4082 4083 5432 4222 8222)
    local conflicts=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            conflicts+=($port)
        fi
    done
    
    if [ ${#conflicts[@]} -gt 0 ]; then
        print_warning "The following ports are already in use: ${conflicts[*]}"
        print_warning "This might cause conflicts with BDD tests."
        
        read -p "Do you want to continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_error "Aborting due to port conflicts"
            exit 1
        fi
    else
        print_success "All required ports are available"
    fi
}

# Function to build services
build_services() {
    print_status "Building backend services..."
    
    cd "$PROJECT_ROOT/backend"
    ./gradlew clean build -x test
    
    if [ $? -ne 0 ]; then
        print_error "Backend build failed"
        exit 1
    fi
    
    print_success "Backend services built successfully"
}

# Function to start services
start_services() {
    print_status "Starting BDD test environment..."
    
    cd "$SCRIPT_DIR"
    
    # Start infrastructure and application services
    docker-compose -f docker-compose-bdd.yml up -d --build postgres nats app ai cdc
    
    # Wait for services to be healthy
    print_status "Waiting for services to be ready..."
    
    local max_wait=300  # 5 minutes
    local wait_time=0
    
    while [ $wait_time -lt $max_wait ]; do
        if docker-compose -f docker-compose-bdd.yml ps | grep -q "unhealthy"; then
            print_status "Services still starting up... (${wait_time}s/${max_wait}s)"
            sleep 10
            wait_time=$((wait_time + 10))
        else
            break
        fi
    done
    
    # Check if all services are healthy
    if docker-compose -f docker-compose-bdd.yml ps | grep -q "unhealthy"; then
        print_error "Some services failed to start properly"
        docker-compose -f docker-compose-bdd.yml ps
        exit 1
    fi
    
    print_success "All services are ready!"
}

# Function to run BDD tests
run_tests() {
    local test_type="${1:-all}"
    
    print_status "Running BDD tests (type: $test_type)..."
    
    cd "$SCRIPT_DIR"
    
    # Set test command based on type
    case $test_type in
        management)
            export BDD_TEST_COMMAND="npm run test:management"
            ;;
        usage)
            export BDD_TEST_COMMAND="npm run test:usage"
            ;;
        search)
            export BDD_TEST_COMMAND="npm run test:search"
            ;;
        types)
            export BDD_TEST_COMMAND="npm run test:types"
            ;;
        integration)
            export BDD_TEST_COMMAND="npm run test:integration"
            ;;
        core)
            export BDD_TEST_COMMAND="npm run test:core"
            ;;
        advanced)
            export BDD_TEST_COMMAND="npm run test:advanced"
            ;;
        enterprise)
            export BDD_TEST_COMMAND="npm run test:enterprise"
            ;;
        custom-fields)
            export BDD_TEST_COMMAND="npm run test:custom-fields"
            ;;
        *)
            export BDD_TEST_COMMAND="npm test"
            ;;
    esac
    
    # Run the BDD test container
    docker-compose -f docker-compose-bdd.yml --profile bdd-test up --build bdd-tests
    
    # Get the exit code
    local test_result=$?
    
    if [ $test_result -eq 0 ]; then
        print_success "BDD tests completed successfully!"
    else
        print_error "BDD tests failed with exit code: $test_result"
    fi
    
    return $test_result
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up BDD test environment..."
    
    cd "$SCRIPT_DIR"
    docker-compose -f docker-compose-bdd.yml down -v --remove-orphans
    
    print_success "Cleanup completed"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [TEST_TYPE]"
    echo ""
    echo "Options:"
    echo "  --help, -h           Show this help message"
    echo "  --build-only         Only build services, don't run tests"
    echo "  --cleanup            Clean up containers and volumes"
    echo "  --no-build           Skip building services"
    echo "  --keep-running       Keep services running after tests"
    echo ""
    echo "Test Types:"
    echo "  all                  Run all custom field tests (default)"
    echo "  management           Run custom field management tests"
    echo "  usage                Run custom field usage tests"
    echo "  search               Run custom field search tests"
    echo "  types                Run custom field type tests"
    echo "  integration          Run custom field integration tests"
    echo "  core                 Run core functionality tests (usage + management)"
    echo "  advanced             Run advanced functionality tests (search + types)"
    echo "  enterprise           Run enterprise functionality tests (integration + migration)"
    echo "  custom-fields        Run all tests tagged with @custom-fields"
    echo ""
    echo "Examples:"
    echo "  $0                           # Run all tests"
    echo "  $0 management                # Run only management tests"
    echo "  $0 --no-build core          # Run core tests without rebuilding"
    echo "  $0 --keep-running usage     # Run usage tests and keep services running"
    echo "  $0 --cleanup                # Clean up containers and volumes"
}

# Main function
main() {
    local build_services=true
    local run_tests=true
    local cleanup_after=true
    local test_type="all"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_usage
                exit 0
                ;;
            --build-only)
                run_tests=false
                cleanup_after=false
                shift
                ;;
            --cleanup)
                cleanup
                exit 0
                ;;
            --no-build)
                build_services=false
                shift
                ;;
            --keep-running)
                cleanup_after=false
                shift
                ;;
            management|usage|search|types|integration|core|advanced|enterprise|custom-fields|all)
                test_type=$1
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_status "Starting SX Custom Fields BDD Tests"
    
    # Set up trap for cleanup
    if [ "$cleanup_after" = true ]; then
        trap cleanup EXIT
    fi
    
    # Pre-flight checks
    check_docker
    check_ports
    
    # Build services if requested
    if [ "$build_services" = true ]; then
        build_services
    fi
    
    # Start services
    start_services
    
    # Run tests if requested
    if [ "$run_tests" = true ]; then
        run_tests "$test_type"
        local test_result=$?
        
        if [ $test_result -eq 0 ]; then
            print_success "All BDD tests passed!"
            exit 0
        else
            print_error "BDD tests failed!"
            exit $test_result
        fi
    else
        print_success "Services are running. Use 'docker-compose -f docker-compose-bdd.yml ps' to check status."
        print_status "To run tests manually: docker-compose -f docker-compose-bdd.yml --profile bdd-test up bdd-tests"
        print_status "To stop services: docker-compose -f docker-compose-bdd.yml down"
    fi
}

# Run main function
main "$@" 