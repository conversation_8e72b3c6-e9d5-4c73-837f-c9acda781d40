# SX Custom Fields BDD Tests

This directory contains the containerized BDD (Behavior-Driven Development) tests for the SX Custom Fields functionality. The tests are written in Cucumber/Gherkin format and run in a Docker container alongside the application services.

## Overview

The BDD tests validate the custom fields functionality end-to-end by:
- Starting all necessary services (App, AI, CDC, Database, NATS)
- Running Cucumber tests against the live API endpoints
- Testing real user scenarios from field definition to usage
- Generating comprehensive test reports

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   App Service   │    │   AI Service    │    │  CDC Service    │
│   (Port 4091)   │    │   (Port 4082)   │    │   (Port 4083)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
         │   PostgreSQL    │    │      NATS       │    │   BDD Tests     │
         │   (Port 5432)   │    │   (Port 4222)   │    │   Container     │
         └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Quick Start

### Run All Tests
```bash
./run-bdd-tests.sh
```

### Run Specific Test Categories
```bash
# Core functionality (management + usage)
./run-bdd-tests.sh core

# Advanced functionality (search + types)
./run-bdd-tests.sh advanced

# Enterprise functionality (integration + migration)
./run-bdd-tests.sh enterprise

# Specific categories
./run-bdd-tests.sh management
./run-bdd-tests.sh usage
./run-bdd-tests.sh search
./run-bdd-tests.sh types
./run-bdd-tests.sh integration
```

### Development Mode
```bash
# Keep services running for debugging
./run-bdd-tests.sh --keep-running

# Skip building (faster for repeated runs)
./run-bdd-tests.sh --no-build core

# Only build services without running tests
./run-bdd-tests.sh --build-only
```

## Test Categories

### Core Tests (`@management` + `@usage`)
Essential functionality that must work:
- Custom field definition creation and management
- Setting field values on tickets
- Basic validation and data integrity

### Advanced Tests (`@search` + `@types`)
Enhanced functionality:
- Searching and filtering by custom fields
- Field type-specific behavior and validation
- Data conversion and normalization

### Enterprise Tests (`@integration` + `@migration`)
Enterprise-grade features:
- Data migration and evolution
- External system integration
- Bulk operations and reporting
- Audit trails and compliance

## Configuration

### Environment Variables
- `BDD_APP_URL`: App service URL (default: http://app:4091)
- `BDD_AI_URL`: AI service URL (default: http://ai:4082)
- `BDD_CDC_URL`: CDC service URL (default: http://cdc:4083)
- `BDD_TEST_COMMAND`: Test command to run (default: npm test)

### Docker Compose Profiles
- Default: Infrastructure and application services
- `bdd-test`: Include the BDD test runner container

## Test Reports

Test reports are generated in the `reports/` directory:
- `cucumber-report.html`: Human-readable HTML report
- `cucumber-report.json`: Machine-readable JSON report

## Feature Files

The BDD specifications are located in `../features/`:
- `custom-fields-management.feature`: Field definition management
- `custom-fields-usage.feature`: Using fields on tickets
- `custom-fields-search.feature`: Search and filtering
- `custom-fields-types.feature`: Type-specific behaviors
- `custom-fields-integration.feature`: Integration scenarios

## Step Definitions

Step definitions are in `step_definitions/`:
- `common_steps.js`: Common steps across all features
- Additional step files can be added for specific functionality

## Support Files

Support files are in `support/`:
- `world.js`: Test context and helper methods
- `hooks.js`: Setup and teardown hooks

## Development

### Adding New Test Scenarios

1. **Add to Feature File**: Write new scenarios in Gherkin format
2. **Implement Steps**: Create step definitions in JavaScript
3. **Test Locally**: Run with `./run-bdd-tests.sh --keep-running`
4. **Debug**: Check container logs with `docker-compose logs`

### Creating Custom Step Definitions

```javascript
const { Given, When, Then } = require('@cucumber/cucumber');

Given('I have a custom condition', async function () {
  // Setup code
});

When('I perform a specific action', async function () {
  // Action code using this.makeAuthenticatedRequest()
});

Then('I should see the expected result', function () {
  // Assertion code using expect()
});
```

### Using the Test Context

The test context (`this.context`) provides:
- `users`: Map of test users
- `organizations`: Map of test organizations
- `tickets`: Map of test tickets
- `customFields`: Map of custom field definitions
- `authTokens`: Map of authentication tokens
- `responses`: Array of HTTP responses

### Helper Methods

Available helper methods:
- `this.authenticate(username)`: Authenticate as a user
- `this.makeAuthenticatedRequest(method, url, data)`: Make API calls
- `this.generateTestData(type, overrides)`: Generate test data
- `this.waitForCondition(fn, maxAttempts, delay)`: Wait with retry
- `this.clearContext()`: Clear test context

## Troubleshooting

### Port Conflicts
If ports are already in use:
```bash
# Check what's using the ports
lsof -i :4091 -i :4082 -i :4083 -i :5432 -i :4222

# Stop conflicting services or use --cleanup
./run-bdd-tests.sh --cleanup
```

### Service Startup Issues
```bash
# Check service health
docker-compose -f docker-compose-bdd.yml ps

# View service logs
docker-compose -f docker-compose-bdd.yml logs app
docker-compose -f docker-compose-bdd.yml logs ai
docker-compose -f docker-compose-bdd.yml logs cdc
```

### Test Failures
```bash
# Run with more verbose output
BDD_TEST_COMMAND="npm test -- --verbose" ./run-bdd-tests.sh

# Keep services running for debugging
./run-bdd-tests.sh --keep-running core

# Check test container logs
docker-compose -f docker-compose-bdd.yml logs bdd-tests
```

### Database Issues
```bash
# Connect to test database
docker exec -it sx_bdd_postgres_db psql -U sx_user -d sx_main_db

# Reset database
./run-bdd-tests.sh --cleanup
```

## CI/CD Integration

The BDD tests are designed for CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run BDD Tests
  run: |
    cd bdd-tests
    ./run-bdd-tests.sh core
    
- name: Upload Test Reports
  uses: actions/upload-artifact@v3
  with:
    name: bdd-test-reports
    path: bdd-tests/reports/
```

## Performance Considerations

- Tests run in parallel (2 workers by default)
- Each scenario has a 30-second timeout
- Failed scenarios are retried once
- Database is reset between test runs
- Services use health checks for proper startup sequencing

## Security

- Tests use isolated database and network
- Authentication tokens are managed per test context
- Test data is automatically cleaned up
- No production data or credentials are used 