#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[BDD-INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[BDD-SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[BDD-WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[BDD-ERROR]${NC} $1"
}

# Function to wait for a service
wait_for_service() {
    local service_name=$1
    local service_url=$2
    local health_endpoint=${3:-/actuator/health}
    local max_attempts=60
    local attempt=1
    
    print_status "Waiting for $service_name at $service_url..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$service_url$health_endpoint" > /dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        if [ $((attempt % 10)) -eq 0 ]; then
            print_status "Still waiting for $service_name... (attempt $attempt/$max_attempts)"
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within timeout"
    return 1
}

# Function to test API connectivity
test_api_connectivity() {
    local service_name=$1
    local service_url=$2
    
    print_status "Testing API connectivity to $service_name..."
    
    # Try to get API information
    if curl -f -s "$service_url/actuator/info" > /dev/null 2>&1; then
        print_success "$service_name API is accessible"
        return 0
    else
        print_warning "$service_name API might not be fully ready"
        return 1
    fi
}

# Main execution
main() {
    print_status "Starting BDD Test Environment Setup"
    
    # Wait for core services
    wait_for_service "App Service" "$BDD_APP_URL"
    wait_for_service "AI Service" "$BDD_AI_URL"
    wait_for_service "CDC Service" "$BDD_CDC_URL"
    
    # Test API connectivity
    test_api_connectivity "App Service" "$BDD_APP_URL"
    test_api_connectivity "AI Service" "$BDD_AI_URL"
    test_api_connectivity "CDC Service" "$BDD_CDC_URL"
    
    print_success "All services are ready!"
    
    # Create reports directory
    mkdir -p reports
    
    # Run BDD tests based on environment variable or default
    local test_command=${BDD_TEST_COMMAND:-"npm test"}
    
    print_status "Running BDD tests with command: $test_command"
    
    # Execute the test command
    if eval "$test_command"; then
        print_success "BDD tests completed successfully!"
        exit 0
    else
        print_error "BDD tests failed!"
        exit 1
    fi
}

# Handle different test scenarios
case "${1:-}" in
    --management)
        export BDD_TEST_COMMAND="npm run test:management"
        ;;
    --usage)
        export BDD_TEST_COMMAND="npm run test:usage"
        ;;
    --search)
        export BDD_TEST_COMMAND="npm run test:search"
        ;;
    --types)
        export BDD_TEST_COMMAND="npm run test:types"
        ;;
    --integration)
        export BDD_TEST_COMMAND="npm run test:integration"
        ;;
    --core)
        export BDD_TEST_COMMAND="npm run test:core"
        ;;
    --advanced)
        export BDD_TEST_COMMAND="npm run test:advanced"
        ;;
    --enterprise)
        export BDD_TEST_COMMAND="npm run test:enterprise"
        ;;
    --custom-fields)
        export BDD_TEST_COMMAND="npm run test:custom-fields"
        ;;
    *)
        export BDD_TEST_COMMAND="npm test"
        ;;
esac

# Run main function
main "$@" 