const { setWorldConstructor, setDefaultTimeout } = require('@cucumber/cucumber');
const axios = require('axios');

class CustomWorld {
  constructor({ parameters }) {
    this.parameters = parameters;
    this.appUrl = parameters.appUrl;
    this.aiUrl = parameters.aiUrl;
    this.cdcUrl = parameters.cdcUrl;
    this.timeout = parameters.timeout;
    this.retryDelay = parameters.retryDelay;
    
    // Initialize HTTP clients
    this.appClient = axios.create({
      baseURL: this.appUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    this.aiClient = axios.create({
      baseURL: this.aiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    this.cdcClient = axios.create({
      baseURL: this.cdcUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    // Test context storage
    this.context = {
      users: new Map(),
      organizations: new Map(),
      tickets: new Map(),
      customFields: new Map(),
      authTokens: new Map(),
      responses: []
    };
  }
  
  // Helper method to authenticate and store token
  async authenticate(username) {
    const user = this.context.users.get(username);
    if (!user) {
      throw new Error(`User ${username} not found in context`);
    }
    
    const response = await this.appClient.post('/api/v1/auth/login', {
      email: user.email,
      password: user.password
    });
    
    const token = response.data.token;
    this.context.authTokens.set(username, token);
    
    // Set authorization header for subsequent requests
    this.appClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    return token;
  }
  
  // Helper method to make authenticated requests
  async makeAuthenticatedRequest(method, url, data = null, username = null) {
    if (username) {
      const token = this.context.authTokens.get(username);
      if (token) {
        this.appClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      }
    }
    
    try {
      let response;
      switch (method.toLowerCase()) {
        case 'get':
          response = await this.appClient.get(url);
          break;
        case 'post':
          response = await this.appClient.post(url, data);
          break;
        case 'put':
          response = await this.appClient.put(url, data);
          break;
        case 'delete':
          response = await this.appClient.delete(url);
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }
      
      this.context.responses.push(response);
      return response;
    } catch (error) {
      // Store error response for testing
      if (error.response) {
        this.context.responses.push(error.response);
      }
      throw error;
    }
  }
  
  // Helper method to wait with retry logic
  async waitForCondition(conditionFn, maxAttempts = 10, delay = 1000) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const result = await conditionFn();
        if (result) {
          return result;
        }
      } catch (error) {
        if (attempt === maxAttempts) {
          throw error;
        }
      }
      
      if (attempt < maxAttempts) {
        await this.sleep(delay);
      }
    }
    
    throw new Error(`Condition not met after ${maxAttempts} attempts`);
  }
  
  // Helper method to sleep
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  // Helper method to generate test data
  generateTestData(type, overrides = {}) {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    
    const templates = {
      organization: {
        name: `Test Org ${timestamp}`,
        domain: `test${random}.com`,
        type: 'ACCOUNT',
        active: true,
        ...overrides
      },
      user: {
        username: `testuser${timestamp}`,
        email: `test${random}@example.com`,
        password: 'Test123!',
        firstName: 'Test',
        lastName: 'User',
        ...overrides
      },
      ticket: {
        title: `Test Ticket ${timestamp}`,
        description: `Test ticket description ${random}`,
        status: 'OPEN',
        priority: 'MEDIUM',
        ...overrides
      },
      customField: {
        name: `test_field_${timestamp}`,
        label: `Test Field ${timestamp}`,
        fieldType: 'text',
        dataType: 'string',
        isRequired: false,
        isActive: true,
        ...overrides
      }
    };
    
    return templates[type] || {};
  }
  
  // Helper method to clear context
  clearContext() {
    this.context.users.clear();
    this.context.organizations.clear();
    this.context.tickets.clear();
    this.context.customFields.clear();
    this.context.authTokens.clear();
    this.context.responses = [];
    
    // Clear authorization headers
    delete this.appClient.defaults.headers.common['Authorization'];
  }
}

setWorldConstructor(CustomWorld);
setDefaultTimeout(30000); 