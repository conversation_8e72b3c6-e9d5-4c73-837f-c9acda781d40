const { Before, After, BeforeAll, AfterAll } = require('@cucumber/cucumber');

// Global setup before all scenarios
BeforeAll(async function () {
  console.log('🚀 Starting BDD Test Suite');
  console.log(`📡 App URL: ${this.parameters?.appUrl || process.env.BDD_APP_URL}`);
  console.log(`🤖 AI URL: ${this.parameters?.aiUrl || process.env.BDD_AI_URL}`);
  console.log(`📊 CDC URL: ${this.parameters?.cdcUrl || process.env.BDD_CDC_URL}`);
});

// Global teardown after all scenarios
AfterAll(async function () {
  console.log('✅ BDD Test Suite Completed');
});

// Setup before each scenario
Before(async function (scenario) {
  console.log(`\n🧪 Starting scenario: ${scenario.pickle.name}`);
  
  // Clear previous test context
  this.clearContext();
  
  // Add scenario info to context
  this.context.currentScenario = {
    name: scenario.pickle.name,
    tags: scenario.pickle.tags.map(tag => tag.name),
    startTime: Date.now()
  };
  
  // Log scenario tags for debugging
  const tags = scenario.pickle.tags.map(tag => tag.name).join(', ');
  if (tags) {
    console.log(`🏷️  Tags: ${tags}`);
  }
});

// Cleanup after each scenario
After(async function (scenario) {
  const duration = Date.now() - this.context.currentScenario.startTime;
  console.log(`⏱️  Scenario completed in ${duration}ms`);
  
  // Log scenario result
  if (scenario.result.status === 'PASSED') {
    console.log(`✅ Scenario PASSED: ${scenario.pickle.name}`);
  } else if (scenario.result.status === 'FAILED') {
    console.log(`❌ Scenario FAILED: ${scenario.pickle.name}`);
    console.log(`💥 Error: ${scenario.result.message}`);
    
    // Log last response for debugging
    if (this.context.responses.length > 0) {
      const lastResponse = this.context.responses[this.context.responses.length - 1];
      console.log(`📡 Last Response Status: ${lastResponse.status}`);
      if (lastResponse.status >= 400) {
        console.log(`📝 Error Response:`, JSON.stringify(lastResponse.data, null, 2));
      }
    }
  }
  
  // Clean up any test data created during the scenario
  await this.cleanupTestData();
});

// Helper method added to World for cleanup
async function cleanupTestData() {
  try {
    // Clean up in reverse order of creation to handle dependencies
    
    // 1. Clean up tickets
    for (const [ticketId, ticket] of this.context.tickets) {
      try {
        await this.makeAuthenticatedRequest('DELETE', `/api/v1/tickets/${ticketId}`);
        console.log(`🗑️  Cleaned up ticket: ${ticketId}`);
      } catch (error) {
        // Ignore cleanup errors - test data might already be gone
        console.log(`⚠️  Could not clean up ticket ${ticketId}: ${error.message}`);
      }
    }
    
    // 2. Clean up custom fields
    for (const [fieldId, field] of this.context.customFields) {
      try {
        await this.makeAuthenticatedRequest('DELETE', `/api/v1/custom-fields/definitions/${fieldId}`);
        console.log(`🗑️  Cleaned up custom field: ${fieldId}`);
      } catch (error) {
        console.log(`⚠️  Could not clean up custom field ${fieldId}: ${error.message}`);
      }
    }
    
    // 3. Clean up users (if needed)
    // Note: In most cases, we don't delete users as they might be referenced by other data
    
    // 4. Clean up organizations (if needed)
    // Note: In most cases, we don't delete organizations as they might be referenced by other data
    
  } catch (error) {
    console.log(`⚠️  Error during cleanup: ${error.message}`);
  }
}

// Add cleanup method to World prototype
Before(function() {
  this.cleanupTestData = cleanupTestData.bind(this);
}); 