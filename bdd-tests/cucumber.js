const config = {
  default: {
    // Feature files location
    features: ['features/*.feature'],
    
    // Step definitions location
    require: [
      'step_definitions/**/*.js',
      'support/**/*.js'
    ],
    
    // Output format
    format: [
      'progress-bar',
      'json:reports/cucumber-report.json',
      'html:reports/cucumber-report.html',
      '@cucumber/pretty-formatter'
    ],
    
    // Parallel execution
    parallel: 2,
    
    // Retry failed scenarios
    retry: 1,
    
    // Timeout for steps (30 seconds)
    timeout: 30000,
    
    // Publish results
    publish: false,
    
    // World parameters
    worldParameters: {
      appUrl: process.env.BDD_APP_URL || 'http://localhost:4091',
      aiUrl: process.env.BDD_AI_URL || 'http://localhost:4082',
      cdcUrl: process.env.BDD_CDC_URL || 'http://localhost:4083',
      timeout: 30000,
      retryDelay: 1000
    }
  }
};

module.exports = config; 