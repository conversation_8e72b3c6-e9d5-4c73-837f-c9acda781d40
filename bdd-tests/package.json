{"name": "sx-bdd-tests", "version": "1.0.0", "description": "BDD tests for SX Custom Fields functionality", "main": "index.js", "scripts": {"test": "cucumber-js", "test:management": "cucumber-js --tags '@management'", "test:usage": "cucumber-js --tags '@usage'", "test:search": "cucumber-js --tags '@search'", "test:types": "cucumber-js --tags '@types'", "test:integration": "cucumber-js --tags '@integration'", "test:core": "cucumber-js --tags '@usage or @management'", "test:advanced": "cucumber-js --tags '@search or @types'", "test:enterprise": "cucumber-js --tags '@integration or @migration'", "test:custom-fields": "cucumber-js --tags '@custom-fields'", "format": "prettier --write .", "lint": "eslint . --fix"}, "dependencies": {"@cucumber/cucumber": "^10.0.1", "axios": "^1.6.0", "chai": "^4.3.10", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"@types/chai": "^4.3.6", "@types/uuid": "^9.0.5", "eslint": "^8.50.0", "prettier": "^3.0.3", "typescript": "^5.2.2"}, "keywords": ["bdd", "cucumber", "testing", "sx", "custom-fields"], "author": "SX Team", "license": "MIT"}