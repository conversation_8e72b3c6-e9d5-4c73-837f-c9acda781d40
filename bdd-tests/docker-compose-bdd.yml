version: '3.8'

services:
  # PostgreSQL Database for BDD testing
  postgres:
    image: pgvector/pgvector:pg15
    container_name: sx_bdd_postgres_db
    environment:
      POSTGRES_USER: sx_user
      POSTGRES_PASSWORD: sx_password
      POSTGRES_DB: sx_main_db
      POSTGRES_MULTIPLE_DATABASES: sx_main_db,sx_cdc_db
      POSTGRES_SHARED_PRELOAD_LIBRARIES: "wal2json"
    volumes:
      - postgres_bdd_data:/var/lib/postgresql/data
      - ../initdb:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sx_user"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - bdd-network

  # NATS Server with JetStream for BDD testing
  nats:
    image: nats:2.10-alpine
    container_name: sx_bdd_nats_server
    command: nats-server -js -p 4222 -m 8222
    ports:
      - "4222:4222"
      - "8222:8222"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8222/healthz || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - bdd-network

  # App Service for BDD testing
  app:
    build:
      context: ../backend
      dockerfile: app/Dockerfile
    container_name: sx_bdd_app
    ports:
      - "4091:4091"
    environment:
      - SPRING_PROFILES_ACTIVE=bdd
      - SERVER_PORT=4091
      - SPRING_DATASOURCE_URL=******************************************
      - SPRING_DATASOURCE_USERNAME=sx_user
      - SPRING_DATASOURCE_PASSWORD=sx_password
      - NATS_URL=nats://nats:4222
      - SX_SECURITY_ENABLED=true
      - LOGGING_LEVEL_IO_SX=DEBUG
    depends_on:
      postgres:
        condition: service_healthy
      nats:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:4091/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - bdd-network

  # AI Service for BDD testing
  ai:
    build:
      context: ../backend
      dockerfile: ai/Dockerfile
    container_name: sx_bdd_ai
    ports:
      - "4082:4082"
    environment:
      - SPRING_PROFILES_ACTIVE=bdd
      - SERVER_PORT=4082
      - SPRING_DATASOURCE_URL=******************************************
      - SPRING_DATASOURCE_USERNAME=sx_user
      - SPRING_DATASOURCE_PASSWORD=sx_password
      - NATS_URL=nats://nats:4222
      - OLLAMA_URL=http://host.docker.internal:11434
      - LOGGING_LEVEL_IO_SX=DEBUG
    depends_on:
      postgres:
        condition: service_healthy
      nats:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:4082/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - bdd-network

  # CDC Service for BDD testing
  cdc:
    build:
      context: ../backend
      dockerfile: cdc/Dockerfile
    container_name: sx_bdd_cdc
    ports:
      - "4083:4083"
    environment:
      - SPRING_PROFILES_ACTIVE=bdd
      - SERVER_PORT=4083
      - SPRING_DATASOURCE_URL=******************************************
      - SPRING_DATASOURCE_USERNAME=sx_user
      - SPRING_DATASOURCE_PASSWORD=sx_password
      - NATS_URL=nats://nats:4222
      - LOGGING_LEVEL_IO_SX=DEBUG
    depends_on:
      postgres:
        condition: service_healthy
      nats:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:4083/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - bdd-network

  # BDD Test Runner
  bdd-tests:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sx_bdd_test_runner
    environment:
      - NODE_ENV=test
      - BDD_APP_URL=http://app:4091
      - BDD_AI_URL=http://ai:4082
      - BDD_CDC_URL=http://cdc:4083
      - BDD_TEST_COMMAND=${BDD_TEST_COMMAND:-npm test}
    volumes:
      - ./reports:/app/reports
      - ../features:/app/features:ro
    depends_on:
      app:
        condition: service_healthy
      ai:
        condition: service_healthy
      cdc:
        condition: service_healthy
    networks:
      - bdd-network
    profiles:
      - bdd-test

volumes:
  postgres_bdd_data:

networks:
  bdd-network:
    driver: bridge
    name: sx_bdd_network 