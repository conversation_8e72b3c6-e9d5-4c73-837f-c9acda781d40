const { Given, When, Then } = require('@cucumber/cucumber');
const { expect } = require('chai');

// Organization setup steps
Given('the system has the following organizations:', async function (dataTable) {
  const organizations = dataTable.hashes();
  
  for (const org of organizations) {
    const orgData = this.generateTestData('organization', org);
    
    try {
      const response = await this.makeAuthenticatedRequest('POST', '/api/v1/organizations', orgData);
      this.context.organizations.set(org.name, response.data);
    } catch (error) {
      console.log(`Note: Organization ${org.name} might already exist`);
    }
  }
});

Given('the following users exist:', async function (dataTable) {
  const users = dataTable.hashes();
  
  for (const user of users) {
    const userData = this.generateTestData('user', user);
    
    try {
      const response = await this.makeAuthenticatedRequest('POST', '/api/v1/auth/register', userData);
      this.context.users.set(user.username, { ...userData, id: response.data.id });
    } catch (error) {
      console.log(`Note: User ${user.username} might already exist`);
    }
  }
});

// Authentication steps
Given('I am authenticated as {string}', async function (username) {
  await this.authenticate(username);
});

// Custom field definition steps
Given('the following custom field definitions exist:', async function (dataTable) {
  const fields = dataTable.hashes();
  
  for (const field of fields) {
    const fieldData = this.generateTestData('customField', field);
    
    try {
      const response = await this.makeAuthenticatedRequest('POST', '/api/v1/custom-fields/definitions', fieldData);
      this.context.customFields.set(field.name, response.data);
    } catch (error) {
      console.log(`Note: Custom field ${field.name} might already exist`);
    }
  }
});

// Custom field management steps
When('I create a custom field definition with:', async function (dataTable) {
  const fieldData = {};
  const rows = dataTable.rows();
  
  for (const [key, value] of rows) {
    try {
      // Try to parse JSON values
      fieldData[key] = JSON.parse(value);
    } catch {
      // If not JSON, treat as string
      fieldData[key] = value;
    }
  }
  
  try {
    this.lastResponse = await this.makeAuthenticatedRequest('POST', '/api/v1/custom-fields/definitions', fieldData);
    this.lastField = this.lastResponse.data;
  } catch (error) {
    this.lastError = error;
  }
});

When('I attempt to create a custom field definition with:', async function (dataTable) {
  const fieldData = {};
  const rows = dataTable.rows();
  
  for (const [key, value] of rows) {
    fieldData[key] = value;
  }
  
  try {
    this.lastResponse = await this.makeAuthenticatedRequest('POST', '/api/v1/custom-fields/definitions', fieldData);
    this.lastField = this.lastResponse.data;
  } catch (error) {
    this.lastError = error;
  }
});

// Ticket management steps
When('I create a ticket with:', async function (dataTable) {
  const ticketData = {};
  const rows = dataTable.rows();
  
  for (const [key, value] of rows) {
    ticketData[key] = value;
  }
  
  try {
    this.lastResponse = await this.makeAuthenticatedRequest('POST', '/api/v1/tickets', ticketData);
    this.lastTicket = this.lastResponse.data;
    this.context.tickets.set(this.lastTicket.id, this.lastTicket);
  } catch (error) {
    this.lastError = error;
  }
});

When('I attempt to create a ticket with:', async function (dataTable) {
  const ticketData = {};
  const rows = dataTable.rows();
  
  for (const [key, value] of rows) {
    ticketData[key] = value;
  }
  
  try {
    this.lastResponse = await this.makeAuthenticatedRequest('POST', '/api/v1/tickets', ticketData);
    this.lastTicket = this.lastResponse.data;
  } catch (error) {
    this.lastError = error;
  }
});

// Custom field value steps
When('I set custom field values:', async function (dataTable) {
  const fieldValues = dataTable.hashes();
  
  if (!this.lastTicket) {
    throw new Error('No ticket to set custom field values on');
  }
  
  for (const fieldValue of fieldValues) {
    const updateData = {
      customFields: {
        [fieldValue.field_name]: fieldValue.value
      }
    };
    
    try {
      this.lastResponse = await this.makeAuthenticatedRequest('PUT', `/api/v1/tickets/${this.lastTicket.id}`, updateData);
    } catch (error) {
      this.lastError = error;
    }
  }
});

// Validation steps
Then('the custom field definition should be created successfully', function () {
  expect(this.lastResponse).to.exist;
  expect(this.lastResponse.status).to.equal(201);
  expect(this.lastField).to.exist;
  expect(this.lastField.id).to.exist;
});

Then('the ticket should be created successfully', function () {
  expect(this.lastResponse).to.exist;
  expect(this.lastResponse.status).to.equal(201);
  expect(this.lastTicket).to.exist;
  expect(this.lastTicket.id).to.exist;
});

Then('the custom field values should be stored correctly', function () {
  expect(this.lastResponse).to.exist;
  expect(this.lastResponse.status).to.be.oneOf([200, 201]);
});

Then('the request should be denied with {string}', function (errorCode) {
  expect(this.lastError).to.exist;
  expect(this.lastError.response.status).to.be.oneOf([401, 403]);
});

Then('the validation rules should be stored correctly', function () {
  expect(this.lastField).to.exist;
  expect(this.lastField.validationRules).to.exist;
});

Then('the field should be available for all account organizations', function () {
  expect(this.lastField).to.exist;
  expect(this.lastField.isActive).to.be.true;
});

// Search and filtering steps
When('I search for tickets with custom field filter:', async function (dataTable) {
  const filters = dataTable.hashes();
  const searchParams = {
    customFieldFilters: filters
  };
  
  try {
    this.lastResponse = await this.makeAuthenticatedRequest('POST', '/api/v1/tickets/search', searchParams);
    this.searchResults = this.lastResponse.data;
  } catch (error) {
    this.lastError = error;
  }
});

Then('I should see tickets:', function (dataTable) {
  const expectedTickets = dataTable.hashes();
  expect(this.searchResults).to.exist;
  
  for (const expected of expectedTickets) {
    const found = this.searchResults.find(ticket => ticket.id === expected.ticket_id);
    expect(found, `Ticket ${expected.ticket_id} not found in search results`).to.exist;
  }
});

Then('I should not see tickets:', function (dataTable) {
  const unexpectedTickets = dataTable.hashes();
  expect(this.searchResults).to.exist;
  
  for (const unexpected of unexpectedTickets) {
    const found = this.searchResults.find(ticket => ticket.id === unexpected.ticket_id);
    expect(found, `Ticket ${unexpected.ticket_id} should not be in search results`).to.not.exist;
  }
});

// Error handling steps
Then('the ticket creation should fail with validation error', function () {
  expect(this.lastError).to.exist;
  expect(this.lastError.response.status).to.equal(400);
});

Then('the error should indicate {string}', function (expectedMessage) {
  expect(this.lastError).to.exist;
  expect(this.lastError.response.data.message || this.lastError.response.data.error).to.include(expectedMessage);
});

// Step to handle missing required fields
When('I do not provide a value for required field {string}', function (fieldName) {
  // This is a no-op step - the field value is simply not provided
  // The validation will happen when the ticket is created
});

// Generic success assertions
Then('the {word} should be created successfully', function (entityType) {
  expect(this.lastResponse).to.exist;
  expect(this.lastResponse.status).to.be.oneOf([200, 201]);
});

// Placeholder steps for complex scenarios that need custom implementation
Given('a custom field definition {string} exists with status {string}', async function (fieldName, status) {
  // Implementation would query or create the field as needed
  console.log(`Setting up field ${fieldName} with status ${status}`);
});

Given('tickets exist with values for {string}', async function (fieldName) {
  // Implementation would create test tickets with the specified field values
  console.log(`Setting up tickets with values for field ${fieldName}`);
});

When('I deactivate the custom field definition {string}', async function (fieldName) {
  // Implementation would deactivate the specified field
  console.log(`Deactivating field ${fieldName}`);
});

Then('the field definition should be marked as inactive', function () {
  // Assertion that the field is inactive
  console.log('Verifying field is inactive');
});

Then('existing field values should remain intact', function () {
  // Assertion that existing values are preserved
  console.log('Verifying existing values are preserved');
});

Then('new tickets should not display the field', function () {
  // Assertion that the field is not shown for new tickets
  console.log('Verifying field is not shown for new tickets');
}); 