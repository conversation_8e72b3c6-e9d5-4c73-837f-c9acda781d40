Feature: Custom Field Usage on Tickets
  As an account organization user
  I want to set and manage custom field values on tickets
  So that I can capture and track organization-specific information

  Background:
    Given the system has the following organizations:
      | name           | type    | domain           |
      | Support Org    | SUPPORT | support.com      |
      | Account Org A  | ACCOUNT | accounta.com     |
    And the following users exist:
      | username        | email                    | organization | role            |
      | support_admin   | <EMAIL>        | Support Org  | SUPPORT_ADMIN   |
      | account_admin   | <EMAIL>       | Account Org A| ACCOUNT_ADMIN   |
      | account_user    | <EMAIL>        | Account Org A| ACCOUNT_USER    |
    And the following custom field definitions exist:
      | name            | label           | fieldType    | dataType | isRequired | fieldOptions |
      | priority_level  | Priority Level  | dropdown     | string   | true       | {"options": ["Low", "High", "Critical"]} |
      | customer_budget | Customer Budget | number       | decimal  | false      | {"min": 0, "max": 1000000} |
      | due_date        | Due Date        | date         | date     | false      | {} |
      | is_escalated    | Is Escalated    | checkbox     | boolean  | false      | {} |
      | affected_systems| Affected Systems| multi_select | string   | false      | {"options": ["Web", "Mobile", "API", "Database"]} |

  @custom-fields @usage @creation
  Scenario: Create ticket with required custom field values
    Given I am authenticated as "account_user"
    When I create a ticket with:
      | title       | Payment processing issue |
      | description | Customer cannot complete payment |
    And I set custom field values:
      | field_name     | value    |
      | priority_level | Critical |
      | customer_budget| 50000.00 |
    Then the ticket should be created successfully
    And the custom field values should be stored correctly

  @custom-fields @usage @validation-required
  Scenario: Cannot create ticket without required custom field
    Given I am authenticated as "account_user"
    When I attempt to create a ticket with:
      | title       | Login issue |
      | description | User cannot login |
    But I do not provide a value for required field "priority_level"
    Then the ticket creation should fail with validation error
    And the error should indicate "priority_level is required"

  @custom-fields @usage @validation-type
  Scenario: Validate custom field data types
    Given I am authenticated as "account_user"
    And I have a ticket with id "TICKET-001"
    When I attempt to set custom field values:
      | field_name     | value     |
      | customer_budget| invalid   |
      | due_date       | 2024-13-45|
    Then the validation should fail
    And the error should indicate "customer_budget must be a valid decimal"
    And the error should indicate "due_date must be a valid date"

  @custom-fields @usage @validation-range
  Scenario: Validate custom field value ranges
    Given I am authenticated as "account_user"
    And I have a ticket with id "TICKET-001"
    When I attempt to set custom field value:
      | field_name     | value    |
      | customer_budget| 2000000  |
    Then the validation should fail
    And the error should indicate "customer_budget exceeds maximum value of 1000000"

  @custom-fields @usage @multi-select
  Scenario: Set multi-select custom field values
    Given I am authenticated as "account_user"
    And I have a ticket with id "TICKET-001"
    When I set custom field value:
      | field_name      | value              |
      | affected_systems| ["Web", "Mobile"]  |
    Then the multi-select values should be stored correctly
    And I should be able to retrieve the values as an array

  @custom-fields @usage @update
  Scenario: Update custom field values on existing ticket
    Given I am authenticated as "account_user"
    And I have a ticket with id "TICKET-001" with custom field values:
      | field_name     | value |
      | priority_level | Low   |
      | is_escalated   | false |
    When I update the custom field values:
      | field_name     | value |
      | priority_level | High  |
      | is_escalated   | true  |
    Then the custom field values should be updated successfully
    And the ticket should show the new values

  @custom-fields @usage @permissions
  Scenario: Custom field access based on user permissions
    Given I am authenticated as "account_user"
    And there is a custom field "internal_notes" with access level "ADMIN_ONLY"
    When I attempt to view or set the "internal_notes" field
    Then the field should not be visible to me
    And any attempt to set the field should be denied

  @custom-fields @usage @inheritance
  Scenario: Custom fields inherit organization context
    Given I am authenticated as "account_user" from "Account Org A"
    And there are tickets from different organizations
    When I create or update tickets
    Then I should only see custom field definitions available to "Account Org A"
    And I should not see custom fields from other organizations

  @custom-fields @usage @conditional-logic
  Scenario: Conditional custom field display
    Given I am authenticated as "account_user"
    And there is a custom field "escalation_reason" with condition "is_escalated = true"
    When I set "is_escalated" to "true" on a ticket
    Then the "escalation_reason" field should become visible and required
    When I set "is_escalated" to "false"
    Then the "escalation_reason" field should become hidden and optional

  @custom-fields @usage @bulk-operations
  Scenario: Bulk update custom field values
    Given I am authenticated as "account_admin"
    And I have multiple tickets with custom field "priority_level" set to "Low"
    When I perform a bulk update to change "priority_level" to "Medium" for selected tickets
    Then all selected tickets should have the updated custom field value
    And the change should be tracked in ticket history

  @custom-fields @usage @history-tracking
  Scenario: Track custom field value changes
    Given I am authenticated as "account_user"
    And I have a ticket with custom field "priority_level" set to "Low"
    When I change "priority_level" from "Low" to "Critical"
    Then the change should be recorded in ticket history
    And the history should show:
      | field_name     | old_value | new_value | changed_by   | timestamp |
      | priority_level | Low       | Critical  | account_user | [current] |

  @custom-fields @usage @default-values
  Scenario: Apply default values for custom fields
    Given there is a custom field "priority_level" with default value "Medium"
    And I am authenticated as "account_user"
    When I create a ticket without specifying "priority_level"
    Then the ticket should be created with "priority_level" set to "Medium"
    And I should be able to override the default value if needed 