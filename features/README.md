# Custom Fields BDD Specifications

This directory contains comprehensive Behavior-Driven Development (BDD) specifications for the custom fields functionality in the SX ticketing system. These specifications are written in Cucumber Gherkin format and are designed to be both intention-revealing and executable.

## Overview

The custom fields system allows support organizations to define additional fields for tickets beyond the standard set, enabling account organizations to capture business-specific information relevant to their operations.

## Feature Files

### 1. Custom Field Definition Management (`custom-fields-management.feature`)
**Focus**: Administrative operations for managing custom field definitions

**Key Scenarios**:
- Creating, updating, and deactivating field definitions
- Permission-based access control for field management  
- Field grouping and organization
- Template creation and reuse
- Field versioning and lifecycle management
- Organization scope and hierarchy

**Key Tags**: `@management`, `@permissions`, `@grouping`, `@templates`, `@lifecycle`, `@versioning`

### 2. Custom Field Usage on Tickets (`custom-fields-usage.feature`)
**Focus**: End-user interactions with custom fields on tickets

**Key Scenarios**:
- Setting custom field values during ticket creation and updates
- Data validation and type checking
- Required field enforcement
- Multi-select and complex field types
- Permission-based field access
- Conditional field logic
- Bulk operations and history tracking

**Key Tags**: `@usage`, `@validation`, `@permissions`, `@conditional-logic`, `@bulk-operations`

### 3. Custom Field Search and Filtering (`custom-fields-search.feature`)
**Focus**: Querying and filtering tickets based on custom field values

**Key Scenarios**:
- Single and multiple field filtering
- Range queries for numeric and date fields
- Text search with partial matching
- Multi-select value searching
- Combined standard and custom field filters
- Sorting and pagination
- Export functionality with custom fields

**Key Tags**: `@search`, `@filtering`, `@sorting`, `@pagination`, `@export`, `@performance`

### 4. Field Type-Specific Behaviors (`custom-fields-types.feature`)
**Focus**: Type-specific validation and behavior for different field types

**Key Scenarios**:
- Text field length and format validation
- Number field range and precision validation  
- Date field constraint validation
- Dropdown option validation and custom values
- Checkbox boolean handling
- Multi-select constraint validation
- Conditional validation logic
- Data type conversion and normalization

**Key Tags**: `@types`, `@validation`, `@conversion`, `@internationalization`

### 5. Integration and Migration (`custom-fields-integration.feature`)
**Focus**: System integration, data migration, and enterprise scenarios

**Key Scenarios**:
- API consistency and format standardization
- Bulk import/export with validation
- Data migration from legacy systems
- Field definition evolution over time
- Organization merging scenarios
- External system synchronization
- Reporting and aggregation
- Audit trails and compliance

**Key Tags**: `@integration`, `@migration`, `@api-consistency`, `@reporting`, `@audit-trail`

## Test Data Model

### Organizations
```
Support Org (SUPPORT) -> Account Org A (ACCOUNT)
                      -> Account Org B (ACCOUNT)
```

### User Roles
- `SUPPORT_ADMIN`: Can manage custom field definitions
- `ACCOUNT_ADMIN`: Can manage tickets and field values within organization  
- `ACCOUNT_USER`: Can create/update tickets with custom field values

### Field Types Supported
- `TEXT`: String input with length validation
- `NUMBER`: Numeric input with range and precision validation
- `DATE`: Date picker with range constraints
- `DROPDOWN`: Single selection from predefined options
- `CHECKBOX`: Boolean true/false value
- `MULTI_SELECT`: Multiple selections from predefined options

### Data Types
- `STRING`: Text storage
- `INTEGER`: Whole number storage
- `DECIMAL`: Floating-point number storage  
- `BOOLEAN`: True/false storage
- `DATE`: Date-only storage
- `TIMESTAMP`: Date and time storage

## Execution Guidelines

### Running Individual Features
```bash
# Run all custom field tests
cucumber --tags @custom-fields

# Run only management scenarios
cucumber --tags @management

# Run validation scenarios
cucumber --tags @validation

# Run performance tests
cucumber --tags @performance
```

### Running by Priority
```bash
# Core functionality (must work)
cucumber --tags "@usage or @management"

# Advanced features  
cucumber --tags "@search or @types"

# Enterprise features
cucumber --tags "@integration or @migration"
```

### Test Environment Setup

1. **Database**: Clean state with test organizations and users
2. **Authentication**: JWT token-based authentication system
3. **Permissions**: RBAC system with proper role assignments
4. **API Endpoints**: REST API endpoints for all operations

### Step Implementation Guide

**Given Steps** (Setup):
- Organization and user creation
- Custom field definition setup
- Test data preparation
- Authentication context

**When Steps** (Actions):
- API calls for CRUD operations
- User interactions (create, update, search)
- System operations (migration, sync)
- Bulk operations

**Then Steps** (Verification):
- Response validation
- Data integrity checks
- Permission enforcement
- Performance metrics
- Audit trail verification

## Integration with Existing System

### API Endpoints Expected
```
POST   /api/v1/custom-fields/definitions
GET    /api/v1/custom-fields/definitions
PUT    /api/v1/custom-fields/definitions/{id}
DELETE /api/v1/custom-fields/definitions/{id}

POST   /api/v1/tickets
PUT    /api/v1/tickets/{id}
GET    /api/v1/tickets
GET    /api/v1/tickets/{id}

POST   /api/v1/tickets/search
POST   /api/v1/tickets/bulk-update
```

### Database Tables Expected
- `custom_field_definitions`
- `custom_field_values`  
- `custom_field_groups`
- `custom_field_conditions`
- Standard audit tables with custom field tracking

### Permission Checks
- Custom field definition management requires `SUPPORT_ADMIN` role
- Custom field value management requires appropriate organization membership
- Field visibility based on user permissions and organization context

## Validation Rules

### Field Definition Validation
- Name must be unique within organization scope
- Field type must match supported types
- Validation rules must be valid JSON
- Field options must be valid for field type

### Field Value Validation  
- Values must match field type constraints
- Required fields must have values
- Dropdown values must exist in options
- Range constraints must be respected

### Organization Scope
- Custom fields are scoped to organization hierarchy
- Support organizations can define fields for account organizations
- Account organizations cannot access other account organization fields

## Performance Considerations

### Indexing Strategy
- Index frequently filtered custom fields
- Consider composite indexes for common filter combinations
- Monitor query performance with large datasets

### Caching Strategy
- Cache custom field definitions
- Cache user permissions for field access
- Invalidate cache on definition changes

### Query Optimization
- Use appropriate join strategies for custom field queries
- Implement pagination for large result sets
- Consider denormalization for frequently accessed fields

## Future Extensibility

### New Field Types
Specifications are designed to accommodate new field types:
- File upload fields
- Rich text fields
- Geolocation fields
- Reference fields (linking to other entities)

### Advanced Features
- Field calculation and formulas
- Advanced conditional logic
- Custom field templates and inheritance
- Field-level security and encryption

## Compliance and Audit

### Data Retention
- Custom field values follow standard data retention policies
- Field definition changes are versioned and auditable
- Deleted fields maintain historical data for compliance

### Security
- Field-level access control
- Data encryption for sensitive custom fields
- Audit logging for all custom field operations

## Troubleshooting

### Common Issues
1. **Validation Failures**: Check field type and validation rules
2. **Permission Errors**: Verify user role and organization membership
3. **Performance Issues**: Check indexing and query optimization
4. **Data Migration**: Verify mapping rules and data integrity

### Debug Information
- Enable debug logging for custom field operations
- Monitor API response times
- Track database query performance
- Validate data integrity after migrations 