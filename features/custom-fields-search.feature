Feature: Custom Field Search and Filtering
  As a user
  I want to search and filter tickets based on custom field values
  So that I can find relevant tickets quickly and efficiently

  Background:
    Given the system has the following organizations:
      | name           | type    | domain           |
      | Support Org    | SUPPORT | support.com      |
      | Account Org A  | ACCOUNT | accounta.com     |
    And the following users exist:
      | username        | email                    | organization | role            |
      | support_admin   | <EMAIL>        | Support Org  | SUPPORT_ADMIN   |
      | account_user    | <EMAIL>        | Account Org A| ACCOUNT_USER    |
    And the following custom field definitions exist:
      | name            | label           | fieldType    | dataType |
      | priority_level  | Priority Level  | dropdown     | string   |
      | customer_budget | Customer Budget | number       | decimal  |
      | due_date        | Due Date        | date         | date     |
      | is_escalated    | Is Escalated    | checkbox     | boolean  |
      | affected_systems| Affected Systems| multi_select | string   |
      | customer_tier   | Customer Tier   | dropdown     | string   |
    And the following tickets exist with custom field values:
      | ticket_id | title                | priority_level | customer_budget | due_date   | is_escalated | affected_systems     | customer_tier |
      | TICKET-001| Payment issue        | Critical       | 50000.00        | 2024-02-15 | true         | ["Web", "API"]       | Enterprise    |
      | TICKET-002| Login problem        | High           | 25000.00        | 2024-02-20 | false        | ["Web"]              | Professional  |
      | TICKET-003| Database slow        | Medium         | 75000.00        | 2024-02-10 | true         | ["Database", "API"]  | Enterprise    |
      | TICKET-004| UI bug               | Low            | 10000.00        | 2024-02-25 | false        | ["Web", "Mobile"]    | Standard      |

  @custom-fields @search @single-field
  Scenario: Search tickets by single custom field value
    Given I am authenticated as "account_user"
    When I search for tickets with custom field filter:
      | field_name     | operator | value    |
      | priority_level | equals   | Critical |
    Then I should see tickets:
      | ticket_id  |
      | TICKET-001 |
    And I should not see tickets:
      | ticket_id  |
      | TICKET-002 |
      | TICKET-003 |
      | TICKET-004 |

  @custom-fields @search @multiple-fields
  Scenario: Search tickets with multiple custom field filters
    Given I am authenticated as "account_user"
    When I search for tickets with custom field filters:
      | field_name     | operator | value      |
      | priority_level | equals   | High       |
      | is_escalated   | equals   | false      |
    Then I should see tickets:
      | ticket_id  |
      | TICKET-002 |
    And I should not see other tickets

  @custom-fields @search @range-queries
  Scenario: Search tickets with numeric range filters
    Given I am authenticated as "account_user"
    When I search for tickets with custom field filter:
      | field_name     | operator           | value    |
      | customer_budget| greater_than_equal | 30000.00 |
    Then I should see tickets:
      | ticket_id  |
      | TICKET-001 |
      | TICKET-003 |
    And I should not see tickets:
      | ticket_id  |
      | TICKET-002 |
      | TICKET-004 |

  @custom-fields @search @date-range
  Scenario: Search tickets with date range filters
    Given I am authenticated as "account_user"
    When I search for tickets with custom field filter:
      | field_name | operator    | value      |
      | due_date   | between     | 2024-02-12,2024-02-22 |
    Then I should see tickets:
      | ticket_id  |
      | TICKET-001 |
      | TICKET-002 |
    And I should not see tickets:
      | ticket_id  |
      | TICKET-003 |
      | TICKET-004 |

  @custom-fields @search @multi-select
  Scenario: Search tickets by multi-select field values
    Given I am authenticated as "account_user"
    When I search for tickets with custom field filter:
      | field_name      | operator | value |
      | affected_systems| contains | API   |
    Then I should see tickets:
      | ticket_id  |
      | TICKET-001 |
      | TICKET-003 |
    And I should not see tickets:
      | ticket_id  |
      | TICKET-002 |
      | TICKET-004 |

  @custom-fields @search @text-search
  Scenario: Search tickets with text field partial matching
    Given I am authenticated as "account_user"
    And there are tickets with custom text field "customer_name":
      | ticket_id  | customer_name    |
      | TICKET-005 | Acme Corporation |
      | TICKET-006 | Beta Company     |
      | TICKET-007 | Acme Solutions   |
    When I search for tickets with custom field filter:
      | field_name    | operator | value |
      | customer_name | contains | Acme  |
    Then I should see tickets:
      | ticket_id  |
      | TICKET-005 |
      | TICKET-007 |

  @custom-fields @search @boolean-filters
  Scenario: Search tickets by boolean custom fields
    Given I am authenticated as "account_user"
    When I search for tickets with custom field filter:
      | field_name   | operator | value |
      | is_escalated | equals   | true  |
    Then I should see tickets:
      | ticket_id  |
      | TICKET-001 |
      | TICKET-003 |

  @custom-fields @search @null-values
  Scenario: Search tickets with empty custom field values
    Given I am authenticated as "account_user"
    And there is a ticket "TICKET-008" with no custom field values set
    When I search for tickets with custom field filter:
      | field_name     | operator | value |
      | priority_level | is_null  | true  |
    Then I should see tickets:
      | ticket_id  |
      | TICKET-008 |

  @custom-fields @search @combined-filters
  Scenario: Combine custom field filters with standard ticket filters
    Given I am authenticated as "account_user"
    When I search for tickets with:
      | filter_type    | field_name     | operator | value      |
      | standard       | status         | equals   | OPEN       |
      | custom         | customer_tier  | equals   | Enterprise |
      | custom         | is_escalated   | equals   | true       |
    Then I should see tickets matching all criteria
    And the results should be properly filtered by both standard and custom fields

  @custom-fields @search @sorting
  Scenario: Sort search results by custom field values
    Given I am authenticated as "account_user"
    When I search for all tickets and sort by:
      | field_name     | direction |
      | customer_budget| desc      |
    Then the tickets should be returned in order:
      | ticket_id  | customer_budget |
      | TICKET-003 | 75000.00        |
      | TICKET-001 | 50000.00        |
      | TICKET-002 | 25000.00        |
      | TICKET-004 | 10000.00        |

  @custom-fields @search @pagination
  Scenario: Paginate search results with custom field filters
    Given I am authenticated as "account_user"
    And there are 25 tickets with custom field "customer_tier" set to "Enterprise"
    When I search for tickets with custom field filter:
      | field_name    | operator | value      |
      | customer_tier | equals   | Enterprise |
    And I request page 2 with page size 10
    Then I should receive 10 tickets
    And the pagination metadata should indicate:
      | total_results | current_page | total_pages |
      | 25           | 2            | 3           |

  @custom-fields @search @performance
  Scenario: Search performance with indexed custom fields
    Given I am authenticated as "account_user"
    And there are 10000 tickets with various custom field values
    And custom field "priority_level" is indexed
    When I search for tickets with custom field filter:
      | field_name     | operator | value    |
      | priority_level | equals   | Critical |
    Then the search should complete within 2 seconds
    And the results should be accurate

  @custom-fields @search @export
  Scenario: Export search results including custom field values
    Given I am authenticated as "account_user"
    When I search for tickets with custom field filter:
      | field_name    | operator | value      |
      | customer_tier | equals   | Enterprise |
    And I export the results to CSV format
    Then the exported file should include:
      | column           |
      | ticket_id        |
      | title            |
      | priority_level   |
      | customer_budget  |
      | customer_tier    |
      | is_escalated     |
    And the custom field values should be properly formatted 