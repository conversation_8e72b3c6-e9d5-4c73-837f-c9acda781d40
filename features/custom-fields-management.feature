Feature: Custom Field Definition Management
  As a support organization administrator
  I want to manage custom field definitions for tickets
  So that account organizations can capture additional information relevant to their business

  Background:
    Given the system has the following organizations:
      | name           | type    | domain           |
      | Support Org    | SUPPORT | support.com      |
      | Account Org A  | ACCOUNT | accounta.com     |
      | Account Org B  | ACCOUNT | accountb.com     |
    And the following users exist:
      | username        | email                    | organization | role            |
      | support_admin   | <EMAIL>        | Support Org  | SUPPORT_ADMIN   |
      | account_admin_a | <EMAIL>       | Account Org A| ACCOUNT_ADMIN   |
      | account_user_a  | <EMAIL>        | Account Org A| ACCOUNT_USER    |

  @custom-fields @management @permissions
  Scenario: Support admin creates custom field definition
    Given I am authenticated as "support_admin"
    When I create a custom field definition with:
      | name           | Priority Level        |
      | label          | Priority Level        |
      | fieldType      | dropdown              |
      | dataType       | string                |
      | isRequired     | true                  |
      | isActive       | true                  |
      | fieldOptions   | {"options": ["Low", "Medium", "High", "Critical"]} |
    Then the custom field definition should be created successfully
    And the field should be available for all account organizations

  @custom-fields @management @validation
  Scenario: Create custom field with validation rules
    Given I am authenticated as "support_admin"
    When I create a custom field definition with:
      | name             | Customer Budget      |
      | label            | Customer Budget      |
      | fieldType        | number               |
      | dataType         | decimal              |
      | isRequired       | false                |
      | validationRules  | {"min": 0, "max": 1000000, "decimalPlaces": 2} |
    Then the custom field definition should be created successfully
    And the validation rules should be stored correctly

  @custom-fields @management @authorization
  Scenario: Account admin cannot create custom field definitions
    Given I am authenticated as "account_admin_a"
    When I attempt to create a custom field definition with:
      | name           | Custom Priority      |
      | label          | Custom Priority      |
      | fieldType      | text                 |
    Then the request should be denied with "INSUFFICIENT_PERMISSIONS"

  @custom-fields @management @grouping
  Scenario: Create custom field group and assign fields
    Given I am authenticated as "support_admin"
    And the following custom field definitions exist:
      | name            | label           | fieldType |
      | customer_name   | Customer Name   | text      |
      | customer_tier   | Customer Tier   | dropdown  |
    When I create a custom field group with:
      | name        | Customer Information |
      | description | Fields related to customer details |
    And I assign the custom fields to the group:
      | customer_name |
      | customer_tier |
    Then the custom field group should be created successfully
    And the fields should be associated with the group

  @custom-fields @management @templates
  Scenario: Create custom field template for reuse
    Given I am authenticated as "support_admin"
    When I create a custom field definition template with:
      | name           | incident_severity    |
      | label          | Incident Severity    |
      | fieldType      | dropdown             |
      | isTemplate     | true                 |
      | fieldOptions   | {"options": ["P1", "P2", "P3", "P4"]} |
    Then the template should be created successfully
    And the template should be available for creating new field definitions

  @custom-fields @management @lifecycle
  Scenario: Deactivate custom field definition
    Given I am authenticated as "support_admin"
    And a custom field definition "issue_category" exists with status "active"
    And tickets exist with values for "issue_category"
    When I deactivate the custom field definition "issue_category"
    Then the field definition should be marked as inactive
    And existing field values should remain intact
    And new tickets should not display the field

  @custom-fields @management @versioning
  Scenario: Update custom field definition with versioning
    Given I am authenticated as "support_admin"
    And a custom field definition "priority_level" exists with version 1
    When I update the field definition with:
      | fieldOptions | {"options": ["Low", "Normal", "High", "Urgent", "Critical"]} |
    Then a new version should be created
    And the version should be incremented to 2
    And existing tickets should continue using version 1 values
    And new tickets should use version 2 definition

  @custom-fields @management @organization-scope
  Scenario: Custom fields are scoped to organization hierarchy
    Given I am authenticated as "support_admin"
    And I create a custom field definition "escalation_path"
    When account organizations query available custom fields
    Then "Account Org A" should see the "escalation_path" field
    And "Account Org B" should see the "escalation_path" field
    But custom field definitions should not be visible across different support organizations 