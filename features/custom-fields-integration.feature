Feature: Custom Field Integration and Migration
  As a system administrator
  I want to manage custom field data integrity during system changes
  So that business continuity is maintained during migrations and integrations

  Background:
    Given the system has the following organizations:
      | name           | type    | domain           |
      | Support Org    | SUPPORT | support.com      |
      | Account Org A  | ACCOUNT | accounta.com     |
    And the following users exist:
      | username        | email                    | organization | role            |
      | support_admin   | <EMAIL>        | Support Org  | SUPPORT_ADMIN   |
      | account_admin   | <EMAIL>       | Account Org A| ACCOUNT_ADMIN   |

  @custom-fields @integration @api-consistency
  Scenario: API endpoints maintain consistent custom field structure
    Given I am authenticated as "support_admin"
    And the following custom field definitions exist:
      | name           | label          | fieldType | dataType |
      | priority_level | Priority Level | dropdown  | string   |
      | due_date       | Due Date       | date      | date     |
    When I retrieve tickets via REST API
    Then each ticket should include custom field values in consistent format:
      """
      {
        "customFields": {
          "priority_level": "High",
          "due_date": "2024-02-15"
        }
      }
      """
    When I create a ticket via API with custom fields
    Then the same format should be accepted for input
    And validation errors should be returned in standard format

  @custom-fields @integration @bulk-import
  Scenario: Bulk import tickets with custom field validation
    Given I am authenticated as "support_admin"
    And custom field definitions exist for:
      | name           | fieldType | isRequired |
      | priority_level | dropdown  | true       |
      | customer_tier  | dropdown  | false      |
    When I import tickets from CSV with data:
      | title         | priority_level | customer_tier | invalid_field |
      | Ticket 1      | High          | Enterprise    | SomeValue     |
      | Ticket 2      | InvalidPrio   | Professional  | AnotherValue  |
      | Ticket 3      |               | Standard      | ThirdValue    |
    Then the import should process as follows:
      | ticket   | status  | reason                              |
      | Ticket 1 | success | All validations passed              |
      | Ticket 2 | failed  | Invalid priority_level value       |
      | Ticket 3 | failed  | Required field priority_level missing |
    And invalid_field should be ignored with warning
    And successful tickets should be created with proper custom field values

  @custom-fields @integration @data-migration
  Scenario: Migrate existing ticket data to new custom field structure
    Given I have existing tickets without custom fields:
      | ticket_id  | title        | legacy_priority | legacy_category |
      | TICKET-001 | Issue A      | 1              | Bug             |
      | TICKET-002 | Issue B      | 2              | Feature         |
    And I create new custom field definitions:
      | name           | fieldType | fieldOptions                    |
      | priority_level | dropdown  | {"options": ["Low", "High"]}    |
      | issue_category | dropdown  | {"options": ["Bug", "Feature"]} |
    When I run data migration with mapping rules:
      | source_field    | target_field   | transformation              |
      | legacy_priority | priority_level | 1→High, 2→Low               |
      | legacy_category | issue_category | direct mapping              |
    Then the tickets should be updated with custom field values:
      | ticket_id  | priority_level | issue_category |
      | TICKET-001 | High          | Bug            |
      | TICKET-002 | Low           | Feature        |
    And the legacy fields should remain unchanged for rollback capability

  @custom-fields @integration @field-evolution
  Scenario: Handle custom field definition changes over time
    Given I am authenticated as "support_admin"
    And tickets exist with custom field "priority" using options ["Low", "Medium", "High"]
    When I update the field definition to include "Critical" option
    Then existing tickets should retain their current values
    And new tickets should have access to all four options
    When I remove "Low" option from the definition
    Then existing tickets with "Low" value should display with "(Deprecated) Low"
    And new tickets should not be able to select "Low"
    And data integrity should be maintained

  @custom-fields @integration @organization-merge
  Scenario: Handle custom fields during organization merging
    Given there are two account organizations with different custom fields:
      | organization | field_name     | field_options           |
      | Org A        | priority_level | ["Low", "High"]         |
      | Org B        | severity_level | ["Minor", "Major"]      |
    When "Org B" is merged into "Org A"
    Then a field mapping strategy should be proposed:
      | source_field   | target_field   | mapping_strategy    |
      | severity_level | priority_level | Minor→Low, Major→High |
    When the merge is executed
    Then all tickets should have consistent custom field structure
    And no data should be lost during the merge process

  @custom-fields @integration @external-system-sync
  Scenario: Synchronize custom fields with external systems
    Given I am authenticated as "support_admin"
    And there is an external CRM system with fields:
      | external_field | sx_field      | sync_direction |
      | account_value  | customer_budget | bidirectional |
      | escalation_flag| is_escalated   | from_sx       |
    When a ticket custom field is updated in SX
    Then the corresponding field should be updated in the external system
    When the external system updates a synchronized field
    Then the SX custom field should be updated accordingly
    And sync conflicts should be logged and reported

  @custom-fields @integration @reporting-aggregation
  Scenario: Generate reports with custom field aggregations
    Given I am authenticated as "support_admin"
    And tickets exist with custom field values:
      | ticket_id | priority_level | customer_budget | created_date |
      | T001      | High          | 50000          | 2024-01-15   |
      | T002      | High          | 25000          | 2024-01-16   |
      | T003      | Low           | 10000          | 2024-01-17   |
    When I generate a report aggregating by "priority_level"
    Then the report should show:
      | priority_level | ticket_count | avg_budget | total_budget |
      | High          | 2            | 37500      | 75000        |
      | Low           | 1            | 10000      | 10000        |
    And the aggregation should handle null values gracefully
    And the report should be exportable in multiple formats

  @custom-fields @integration @audit-trail
  Scenario: Maintain audit trail for custom field changes
    Given I am authenticated as "account_admin"
    And I have a ticket with custom field "priority_level" set to "Low"
    When I change "priority_level" from "Low" to "Critical"
    Then the audit trail should record:
      | timestamp | user          | action | field_name     | old_value | new_value |
      | [current] | account_admin | update | priority_level | Low       | Critical  |
    When a system migration updates custom field values
    Then the audit trail should record the migration as system action
    And the audit data should be immutable and tamper-proof

  @custom-fields @integration @performance-optimization
  Scenario: Optimize custom field queries for large datasets
    Given there are 100,000 tickets with various custom field values
    And custom field "priority_level" is frequently used in filters
    When I create an index on "priority_level" custom field
    Then queries filtering by "priority_level" should complete under 1 second
    When I perform complex queries with multiple custom field filters
    Then the query optimizer should use appropriate indexes
    And performance should remain acceptable as data volume grows

  @custom-fields @integration @backup-restore
  Scenario: Backup and restore custom field data integrity
    Given I have tickets with complex custom field values including:
      | field_type   | sample_value                    |
      | multi_select | ["Option1", "Option2"]          |
      | json         | {"nested": {"key": "value"}}    |
      | decimal      | 12345.67                        |
    When I perform a system backup
    Then all custom field definitions and values should be included
    When I restore from backup to a new environment
    Then all custom field data should be restored exactly
    And all field type behaviors should work correctly
    And data relationships should remain intact

  @custom-fields @integration @schema-versioning
  Scenario: Handle custom field schema evolution
    Given custom field definitions exist with version 1 schema
    When the system is upgraded to support new field types
    Then existing field definitions should continue to work
    And new field types should be available for new definitions
    When I migrate a field from version 1 to version 2 schema
    Then the migration should be reversible
    And no data should be lost during schema changes 