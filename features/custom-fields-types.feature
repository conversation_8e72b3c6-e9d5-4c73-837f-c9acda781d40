Feature: Custom Field Type-Specific Behaviors
  As a system administrator
  I want different custom field types to behave according to their specifications
  So that data integrity and user experience are maintained across field types

  Background:
    Given the system has the following organizations:
      | name           | type    | domain           |
      | Support Org    | SUPPORT | support.com      |
      | Account Org A  | ACCOUNT | accounta.com     |
    And the following users exist:
      | username        | email                    | organization | role            |
      | support_admin   | <EMAIL>        | Support Org  | SUPPORT_ADMIN   |
      | account_user    | <EMAIL>        | Account Org A| ACCOUNT_USER    |

  @custom-fields @types @text
  Scenario: Text field behavior and validation
    Given I am authenticated as "support_admin"
    And I create a text custom field with:
      | name            | customer_notes        |
      | label           | Customer Notes        |
      | fieldType       | text                  |
      | dataType        | string                |
      | validationRules | {"minLength": 10, "maxLength": 500} |
    When I set the field value to "Short"
    Then the validation should fail with "Value must be at least 10 characters"
    When I set the field value to a string with 600 characters
    Then the validation should fail with "Value exceeds maximum length of 500 characters"
    When I set the field value to "This is a valid customer note with sufficient length"
    Then the value should be accepted and stored correctly

  @custom-fields @types @number
  Scenario: Number field behavior and validation
    Given I am authenticated as "support_admin"
    And I create a number custom field with:
      | name            | response_time_hours   |
      | label           | Response Time (Hours) |
      | fieldType       | number                |
      | dataType        | decimal               |
      | validationRules | {"min": 0.1, "max": 168.0, "decimalPlaces": 1} |
    When I set the field value to "-1"
    Then the validation should fail with "Value must be greater than or equal to 0.1"
    When I set the field value to "200"
    Then the validation should fail with "Value must be less than or equal to 168.0"
    When I set the field value to "24.55"
    Then the validation should fail with "Value must have at most 1 decimal places"
    When I set the field value to "24.5"
    Then the value should be accepted and stored as decimal 24.5

  @custom-fields @types @date
  Scenario: Date field behavior and validation
    Given I am authenticated as "support_admin"
    And I create a date custom field with:
      | name            | target_resolution_date |
      | label           | Target Resolution Date |
      | fieldType       | date                   |
      | dataType        | date                   |
      | validationRules | {"minDate": "today", "maxDate": "today+365"} |
    When I set the field value to "2023-01-01"
    Then the validation should fail with "Date cannot be in the past"
    When I set the field value to a date 400 days from today
    Then the validation should fail with "Date cannot be more than 365 days in the future"
    When I set the field value to "invalid-date"
    Then the validation should fail with "Invalid date format"
    When I set the field value to a date 30 days from today
    Then the value should be accepted and stored correctly

  @custom-fields @types @dropdown
  Scenario: Dropdown field behavior and validation
    Given I am authenticated as "support_admin"
    And I create a dropdown custom field with:
      | name            | severity_level        |
      | label           | Severity Level        |
      | fieldType       | dropdown              |
      | dataType        | string                |
      | fieldOptions    | {"options": ["Low", "Medium", "High", "Critical"], "allowCustom": false} |
    When I set the field value to "Invalid Option"
    Then the validation should fail with "Value must be one of: Low, Medium, High, Critical"
    When I set the field value to "High"
    Then the value should be accepted and stored correctly
    And the field should support case-insensitive matching for "high"

  @custom-fields @types @dropdown-custom
  Scenario: Dropdown field with custom values allowed
    Given I am authenticated as "support_admin"
    And I create a dropdown custom field with:
      | name            | product_module        |
      | label           | Product Module        |
      | fieldType       | dropdown              |
      | dataType        | string                |
      | fieldOptions    | {"options": ["Core", "Billing", "Reporting"], "allowCustom": true} |
    When I set the field value to "Custom Module"
    Then the value should be accepted and stored correctly
    And "Custom Module" should be available as an option for future use

  @custom-fields @types @checkbox
  Scenario: Checkbox field behavior and validation
    Given I am authenticated as "support_admin"
    And I create a checkbox custom field with:
      | name            | requires_approval     |
      | label           | Requires Approval     |
      | fieldType       | checkbox              |
      | dataType        | boolean               |
    When I set the field value to "invalid"
    Then the validation should fail with "Value must be true or false"
    When I set the field value to "true"
    Then the value should be accepted and stored as boolean true
    When I set the field value to "false"
    Then the value should be accepted and stored as boolean false
    When I do not provide a value
    Then the field should default to false

  @custom-fields @types @multi-select
  Scenario: Multi-select field behavior and validation
    Given I am authenticated as "support_admin"
    And I create a multi-select custom field with:
      | name            | affected_components   |
      | label           | Affected Components   |
      | fieldType       | multi_select          |
      | dataType        | string                |
      | fieldOptions    | {"options": ["UI", "API", "Database", "Cache"], "minSelections": 1, "maxSelections": 3} |
    When I set the field value to "[]"
    Then the validation should fail with "At least 1 selection is required"
    When I set the field value to '["UI", "API", "Database", "Cache"]'
    Then the validation should fail with "Maximum 3 selections allowed"
    When I set the field value to '["InvalidComponent"]'
    Then the validation should fail with "Invalid option: InvalidComponent"
    When I set the field value to '["UI", "API"]'
    Then the value should be accepted and stored as an array
    And I should be able to retrieve the array with 2 elements

  @custom-fields @types @conditional-validation
  Scenario: Field validation based on other field values
    Given I am authenticated as "support_admin"
    And I create custom fields with conditional rules:
      | name              | label             | fieldType | conditions                        |
      | escalation_needed | Escalation Needed | checkbox  | none                              |
      | escalation_reason | Escalation Reason | text      | escalation_needed = true          |
      | escalation_level  | Escalation Level  | dropdown  | escalation_needed = true          |
    When I set "escalation_needed" to false
    And I attempt to set "escalation_reason" to "Manager approval needed"
    Then the validation should fail with "Field not applicable when escalation_needed is false"
    When I set "escalation_needed" to true
    And I do not provide "escalation_reason"
    Then the validation should fail with "Escalation reason is required when escalation is needed"
    When I set "escalation_needed" to true
    And I set "escalation_reason" to "Manager approval needed"
    And I set "escalation_level" to "Level 2"
    Then all values should be accepted and stored correctly

  @custom-fields @types @data-conversion
  Scenario: Automatic data type conversion and normalization
    Given I am authenticated as "support_admin"
    And I have custom fields of different types
    When I set a decimal field to "42"
    Then it should be stored as 42.0
    When I set a boolean field to "1"
    Then it should be stored as true
    When I set a boolean field to "0"
    Then it should be stored as false
    When I set a date field to "2024-02-15T10:30:00Z"
    Then it should be stored as date "2024-02-15"
    When I set a text field with leading/trailing whitespace
    Then the whitespace should be trimmed

  @custom-fields @types @null-handling
  Scenario: Handle null and empty values appropriately
    Given I am authenticated as "support_admin"
    And I have custom fields with different requirements:
      | name         | fieldType | isRequired |
      | required_text| text      | true       |
      | optional_text| text      | false      |
      | optional_num | number    | false      |
    When I provide null for "required_text"
    Then the validation should fail with "Field is required"
    When I provide null for "optional_text"
    Then the value should be accepted and stored as null
    When I provide empty string for "optional_text"
    Then the value should be stored as null (normalized)
    When I provide "0" for "optional_num"
    Then the value should be stored as 0 (not null)

  @custom-fields @types @performance
  Scenario: Field type performance characteristics
    Given I am authenticated as "support_admin"
    And I have 1000 tickets with various custom field types
    When I query tickets filtered by text field with LIKE operator
    Then the query should complete within reasonable time
    When I query tickets filtered by indexed dropdown field
    Then the query should complete faster than text field queries
    When I query tickets with complex multi-select filters
    Then the performance should remain acceptable

  @custom-fields @types @internationalization
  Scenario: Custom field values support internationalization
    Given I am authenticated as "support_admin"
    And I create custom fields that accept international characters
    When I set text field to "Müller & Associés - 北京办事处"
    Then the value should be stored and retrieved correctly
    When I set dropdown options with international text
    Then the options should display correctly in different locales
    And sorting should work correctly with international characters 