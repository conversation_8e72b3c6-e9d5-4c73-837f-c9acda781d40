name: Rule File Integrity Check

on:
  push:
    paths:
      - '.cursor/rules/*.mdc'
  pull_request:
    paths:
      - '.cursor/rules/*.mdc'

jobs:
  check-mdc-rules:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Check .mdc rule files
        run: |
          fail=0
          for file in .cursor/rules/*.mdc; do
            if ! grep -q '^---' "$file"; then
              echo "[ERROR] $file is missing YAML frontmatter (---) at the top."
              fail=1
            fi
            if grep -q 'alwaysApply: false' "$file"; then
              echo "[ERROR] $file disables alwaysApply. This is not allowed for critical rules."
              fail=1
            fi
            if grep -q '^description:[[:space:]]*$' "$file"; then
              echo "[ERROR] $file has an empty description. Please provide a description."
              fail=1
            fi
            if grep -q '^globs:[[:space:]]*$' "$file"; then
              echo "[ERROR] $file has an empty globs field. Please provide globs."
              fail=1
            fi
            if ! grep -q 'alwaysApply:' "$file"; then
              echo "[ERROR] $file is missing alwaysApply field."
              fail=1
            fi
          done
          if [ $fail -ne 0 ]; then
            echo "[CI RULE CHECK FAILED] Please fix the above errors."
            exit 1
          fi 