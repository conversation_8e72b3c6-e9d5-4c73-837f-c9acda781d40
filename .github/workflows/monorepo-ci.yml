name: Monorepo CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # Check rule file integrity (from existing workflow)
  check-rules:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Check .mdc rule files
        run: |
          fail=0
          for file in .cursor/rules/*.mdc; do
            if ! grep -q '^---' "$file"; then
              echo "[ERROR] $file is missing YAML frontmatter (---) at the top."
              fail=1
            fi
            if grep -q 'alwaysApply: false' "$file"; then
              echo "[ERROR] $file disables alwaysApply. This is not allowed for critical rules."
              fail=1
            fi
            if grep -q '^description:[[:space:]]*$' "$file"; then
              echo "[ERROR] $file has an empty description. Please provide a description."
              fail=1
            fi
            if grep -q '^globs:[[:space:]]*$' "$file"; then
              echo "[ERROR] $file has an empty globs field. Please provide globs."
              fail=1
            fi
            if ! grep -q 'alwaysApply:' "$file"; then
              echo "[ERROR] $file is missing alwaysApply field."
              fail=1
            fi
          done
          if [ $fail -ne 0 ]; then
            echo "[CI RULE CHECK FAILED] Please fix the above errors."
            exit 1
          fi

  # Backend submodule tests
  backend:
    runs-on: ubuntu-latest
    needs: check-rules
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: sx_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code with submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: gradle

      - name: Setup Gradle
        uses: gradle/gradle-build-action@v2
        with:
          gradle-version: wrapper

      - name: Run backend tests
        run: |
          cd backend
          ./gradlew test
        env:
          SPRING_PROFILES_ACTIVE: test
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: sx_test
          DB_USER: postgres
          DB_PASSWORD: postgres

      - name: Build backend
        run: |
          cd backend
          ./gradlew build -x test
        env:
          SPRING_PROFILES_ACTIVE: test
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: sx_test
          DB_USER: postgres
          DB_PASSWORD: postgres

  # Frontend submodule tests
  frontend:
    runs-on: ubuntu-latest
    needs: check-rules
    
    steps:
      - name: Checkout code with submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install frontend dependencies
        run: |
          cd frontend
          npm ci

      - name: Run frontend tests
        run: |
          cd frontend
          npm run test:ci
        env:
          CI: true

      - name: Build frontend
        run: |
          cd frontend
          npm run build

  # Integration tests (if any)
  integration:
    runs-on: ubuntu-latest
    needs: [backend, frontend]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: sx_integration
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code with submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: gradle

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Run integration tests
        run: |
          echo "Integration tests would run here"
          echo "This could include end-to-end tests, API contract tests, etc."
        env:
          SPRING_PROFILES_ACTIVE: integration
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: sx_integration
          DB_USER: postgres
          DB_PASSWORD: postgres

  # Security scan
  security:
    runs-on: ubuntu-latest
    needs: [backend, frontend]
    
    steps:
      - name: Checkout code with submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: gradle

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Backend security scan
        run: |
          cd backend
          ./gradlew dependencyCheckAnalyze
        continue-on-error: true

      - name: Frontend security scan
        run: |
          cd frontend
          npm audit --audit-level=moderate
        continue-on-error: true

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            backend/build/reports/dependency-check-report.html
            frontend/npm-audit.json 