---
description: "Rules for React component development and organization"
globs: ["frontend/src/components/**/*.tsx", "frontend/src/components/**/*.jsx", "frontend/src/design-system/**/*.tsx"]
alwaysApply: true
---

# Frontend Component Rules

## Component Structure
- Use functional components with hooks
- Follow component naming conventions
- Maintain proper file organization
- Implement proper prop typing

