---
description: "Frontend development and architecture rules"
globs: ["frontend/**/*.js", "frontend/**/*.jsx", "frontend/**/*.ts", "frontend/**/*.tsx", "frontend/**/*.css", "frontend/**/*.scss", "frontend/package.json", "frontend/tsconfig.json"]
alwaysApply: true
---

# Frontend Development Rules

Include the following rules:

[frontend-ux.mdc](mdc:.cursor/rules/frontend-ux.mdc)
[frontend-organization.mdc](mdc:.cursor/rules/frontend-organization.mdc)
[frontend-component-rule.mdc](mdc:.cursor/rules/frontend-component-rule.mdc)
[frontend-service.mdc](mdc:.cursor/rules/frontend-service.mdc)
[frontend-rbac.mdc](mdc:.cursor/rules/frontend-rbac.mdc)