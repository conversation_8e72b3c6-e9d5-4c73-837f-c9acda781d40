# AI PROCESS PROTOCOL - MANDATORY

## Always-Start Process (5 Steps)

### STEP 1: CONTEXT VALIDATION (ALWAYS FIRST)
```
CONTEXT CHECK: 
- Conversation: [named context - e.g., "AI_DATABASE_CLEANUP", "RULE_COMPLIANCE_SETUP"]
- Previous decisions: [specific agreements we made]
- Applicable rules: [filtered list of relevant rules for this task]
- Module: [app|ai|cdc] - Which module am I working with?
- Database: [sx_main_db|sx_cdc_db|sx_audit_db|sx_metrics_db] - Which database?
- Schema: [public|ai|audit|metrics] - Which schema?
- Tool: [sx.db.*|sx.nats.*|sx.api.*] - Which sx.* tool should I use?

CONTEXT LOADED: ✅ [context name] | ❌ Need to load [context name]
```

### STEP 2: APPLICABLE RULES FILTERING (ALWAYS SECOND)
```
APPLICABLE RULES FOR [TASK]:
✅ backend-architecture.mdc: [specific relevant rules]
✅ backend-migration.mdc: [specific relevant rules]  
✅ backend-organization.mdc: [specific relevant rules]
✅ [other relevant rule files]

RULE COMPLIANCE CHECK:
- Architecture: NO JPA, JDBC only ✅
- Migration: V<version>__description.sql ✅
- Organization: Module dependencies ✅
```

### STEP 3: INSTRUCTION PARROTING (ALWAYS THIRD)
```
INSTRUCTION: You asked me to [exact instruction]

CONSTRAINTS: I must [specific constraints]
UNCERTAINTY: [any unclear points that need clarification]
```

### STEP 4: ASK BEFORE DEVIATING (ALWAYS FOURTH)
```
QUESTION: Before proceeding, should I [specific action] or [alternative]?

CONFIRMATION: Do you want me to proceed with [approach] or [different approach]?
```

### STEP 5: CONSENT CHECK (ALWAYS FIFTH)
```
CONSENT STATUS: [WAITING|AUTO-EXECUTE|CONFIRMED]

OVERRIDE KEYWORDS (Auto-execute without waiting):
- "proceed" - Execute the action immediately
- "execute" - Execute the action immediately  
- "go ahead" - Execute the action immediately
- "auto-execute" - Execute the action immediately
- "yes" - Execute the action immediately
- "confirm" - Execute the action immediately
- "do it" - Execute the action immediately
- "implement" - Execute the action immediately

CONSENT WAIT: If no override keyword, wait for explicit confirmation
```

## Named Conversation Contexts

### Context Naming Convention:
- `AI_DATABASE_CLEANUP` - AI table cleanup and optimization
- `RULE_COMPLIANCE_SETUP` - Process improvement and rule adherence
- `AI_SUGGESTION_IMPLEMENTATION` - AI suggestion persistence
- `MIGRATION_STRATEGY` - Database migration planning
- `ARCHITECTURE_DECISIONS` - System design decisions

### Context Capture Format:
```
CONTEXT: [NAME]
DECISIONS: [list of key decisions]
APPROACH: [agreed approach]
RULES: [applicable rule files]
CONSTRAINTS: [specific constraints]
```

## Process Keywords

### Process Keywords:
- `process.start` - Begin the 4-step process
- `process.reflect` - Check how well the process was followed
- `process.context` - Load/save named conversation context
- `process.rules` - Filter applicable rules for current task
- `process.validate` - Validate compliance before action

### Step Keywords:
- `step.context` - Step 1: Context validation
- `step.rules` - Step 2: Applicable rules filtering  
- `step.instruction` - Step 3: Instruction parroting
- `step.confirm` - Step 4: Ask before deviating
- `step.consent` - Step 5: Consent check

## Compliance Checklist

Before every action, check:
- [ ] Did I start with the 5-step process?
- [ ] Did I reference our previous agreements?
- [ ] Did I check rule compliance?
- [ ] Did I parrot back your exact instruction?
- [ ] Did I ask before deviating?
- [ ] Did I check for consent or override keywords?

## Rule Compliance Requirements

### Architecture Rules:
- **NEVER** use JPA, Hibernate, or any ORM framework
- **MUST** use Spring JDBC Templates
- **MUST** follow hybrid approach: relational columns + JSONB for flexibility
- **MUST** inherit from BaseRepository interface

### Tool Usage Rules:
- **NEVER** use `docker exec` directly
- **ALWAYS** use `sx.db.*` commands for database operations
- **ALWAYS** use `sx.nats.*` commands for NATS operations
- **ALWAYS** use `sx.api.*` commands for API operations
- **ALWAYS** check database context before operations

### Migration Rules:
- **MUST** use V<version>__description.sql format
- **MUST** include audit fields in all tables
- **MUST** use Flyway for migrations

### Rule File Standards:
- **MUST** use `search_replace` for rule edits (never `edit_file` with full replacement)
- **MUST** preserve frontmatter (description, globs, alwaysApply)
- **MUST** verify frontmatter integrity after edits

## Memory Rules

1. **ALWAYS** reference previous conversation context
2. **ALWAYS** acknowledge when making assumptions
3. **ALWAYS** verify file states before and after changes
4. **ALWAYS** test each change immediately
5. **NEVER** leave the codebase in a broken state

## Error Prevention

- **ALWAYS** check current state before making changes
- **ALWAYS** test authentication flow first
- **ALWAYS** validate organization creation before user creation
- **ALWAYS** use proper error handling in scripts
- **ALWAYS** check for existing entities before creating new ones
- **ALWAYS** verify API responses match expected DTO structure
---
description: "AI Process Protocol - Mandatory always-start process for predictable interactions"
globs: ["**/*"]
alwaysApply: true
---
