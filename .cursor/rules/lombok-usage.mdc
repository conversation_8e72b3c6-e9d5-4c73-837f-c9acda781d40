---
description: "Lombok usage and standards for the project"
globs: ["backend/**/*.java"]
alwaysApply: true
---

# LOMBOK USAGE RULES - MANDATORY

## Base Class Lombok Pattern (CRITICAL):
- **MUST** use `@SuperBuilder` for inheritance hierarchies
- **MUST** use `@Data` for getters, setters, equals, hashCode, toString
- **MUST** use `@Accessors(chain = true)` for fluent setters
- **MUST** use `@NoArgsConstructor` for Spring compatibility
- **MUST** use `@AllArgsConstructor` only when needed for specific use cases
- **NEVER** use `@Builder` on abstract classes or inheritance hierarchies

## Abstract Class Lombok Rules (CRITICAL):
- **MUST** use `@SuperBuilder` instead of `@Builder` for abstract classes
- **MUST** make fields `protected` (not `private`) for subclasses to access
- **MUST** provide `protected` constructor for subclasses
- **NEVER** use `@Builder` on abstract classes with inheritance
- **NEVER** make fields `private` in abstract classes that subclasses need to access

## Inheritance Hierarchy Pattern:
```java
// Base abstract class
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
public abstract class BaseDataObject {
    protected Long id;
    protected Long organizationId;
    // ... other protected fields
}

// Concrete subclass
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
public class ConcreteClass extends BaseDataObject {
    private String specificField;
    // ... specific fields
}
```

## Builder Usage Patterns:
- **Use `@SuperBuilder`** for inheritance hierarchies
- **Use `@Builder`** only for concrete classes without inheritance
- **Always provide `@NoArgsConstructor`** for Spring dependency injection
- **Use `@Accessors(chain = true)`** for fluent API design

## Field Access Patterns:
- **Abstract classes**: Use `protected` fields for subclass access
- **Concrete classes**: Use `private` fields with Lombok-generated accessors
- **DTOs**: Use `private` fields with `@Data` for encapsulation

## Common Mistakes to Avoid:
- **NEVER** use `@Builder` on abstract classes
- **NEVER** make fields `private` in abstract classes that subclasses need
- **NEVER** forget `@NoArgsConstructor` for Spring beans
- **NEVER** use `@AllArgsConstructor` without careful consideration

## Examples from Codebase:
- `BaseDataObject.java`: Abstract base with `@SuperBuilder` and `protected` fields
- `BaseDTO.java`: Extends `BaseDataObject` with same pattern
- `UserDTO.java`: Concrete class with `@SuperBuilder` and `private` fields

## Validation Checklist:
- [ ] Abstract classes use `@SuperBuilder` (not `@Builder`)
- [ ] Abstract class fields are `protected` (not `private`)
- [ ] All classes have `@NoArgsConstructor`
- [ ] All classes have `@Accessors(chain = true)`
- [ ] Inheritance hierarchies use `@SuperBuilder` consistently
- [ ] No `@Builder` on abstract classes
