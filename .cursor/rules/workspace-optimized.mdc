---
description: "Consolidated workspace rules covering AI productivity, debugging protocols, file synchronization, test data generation, error handling, authentication, API validation, and framework-specific implementation principles"
globs: ["**/*.java", "**/*.kt", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.sql", "**/*.sh", "**/*.md"]
alwaysApply: true
---

# AI PRODUCTIVITY AND DEBUGGING RULES - MANDATORY

## Logging and Process Monitoring Protocol (CRITICAL):
- MUST: Check `sx.status` before asking about process state
- MUST: Read log files from `~/.sx/logs/` before asking for error details
- MUST: Use `lsof -i :PORT` to find processes on specific ports
- MUST: Check `docker logs CONTAINER` for containerized services
- MUST: Read git status and diff logs before asking about changes
- MUST: Check pre-commit hook logs before asking about commit failures
- MUST: Use `ps aux | grep PROCESS` to find running processes
- MUST: Never ask for copy-pasted logs when files are available
- MUST: Always check `~/.sx/logs/` directory first for any process information

## File Synchronization Protocol (CRITICAL):
- MUST: Check `git status` before making any file changes
- MUST: Resync any files that show as modified in git status
- MUST: Use `read_file` tool to get current content of modified files
- MUST: Never assume file content without reading it first
- MUST: Ask user to copy-paste file content if reading tools fail

## Test Data Generation Workflow (MANDATORY):
- MUST: Generate data in dependency order (org → users → tickets)
- MUST: Capture and reuse IDs from previous operations
- MUST: Use actual API responses to get generated IDs
- NEVER: Assume IDs or hardcode them in test scripts

## Error Handling in Test Scripts (MANDATORY):
- MUST: Check HTTP status codes in test scripts
- MUST: Validate response structure before proceeding
- MUST: Handle 400/500 errors gracefully
- MUST: Log response bodies for debugging

## Authentication Flow (CRITICAL):
- MUST: Always authenticate before data operations
- MUST: Extract and use JWT tokens from login responses
- MUST: Include Authorization headers in subsequent requests
- NEVER: Skip authentication in test data generation

## API Contract Validation (MANDATORY):
- MUST: Validate that API responses match expected DTO structures
- MUST: Ensure all required fields are present in responses
- MUST: Test both happy path and error scenarios
- NEVER: Assume API contracts without validation

## Repository Save Method Pattern (CRITICAL):
- MUST: All repository save methods return the saved object with generated ID
- MUST: Use RETURNING * with queryForObject() for INSERT operations
- MUST: Fetch updated object after UPDATE operations
- Example: Repository implementations now return complete objects with IDs

## Service Layer Data Flow (CRITICAL):
- MUST: Services always work with DTOs, never domain objects
- MUST: Services use mappers to convert between DTOs and domain objects
- MUST: Services return DTOs with complete data (including generated IDs)
- NEVER: Return incomplete objects or objects without IDs

## API Response Consistency (CRITICAL):
- MUST: All POST/PUT endpoints return the complete created/updated object
- MUST: Include generated IDs in all responses
- MUST: Return the same object structure that can be used for subsequent operations
- NEVER: Return only success messages without the actual data

## Prevention Strategies:
1. Always test the complete flow: org → user → ticket creation
2. Validate each step: Check responses before proceeding to next step
3. Use proper error handling: Don't ignore HTTP errors
4. Maintain state: Pass IDs between operations
5. Log everything: Include response bodies in debug output

## Memory Rules:
1. ALWAYS reference previous conversation context
2. ALWAYS acknowledge when making assumptions
3. ALWAYS verify file states before and after changes
4. ALWAYS test each change immediately
5. NEVER leave the codebase in a broken state

## Rule File Standards (CRITICAL):
- MUST: Use `./devenv/cursor/tools/create-rule.sh` for new rule files
- MUST: Use `search_replace` for editing existing rule files
- NEVER: Use `edit_file` with `code_edit` that replaces entire rule files
- MUST: Preserve frontmatter (description, globs, alwaysApply) in all edits
- MUST: Verify frontmatter integrity after any rule file changes
- MUST: Follow `.cursor/rules/rule-file-standards.mdc` for all rule operations

## Framework-Specific Implementation Principles (CRITICAL)
- **ALWAYS** start with the native features and idioms of the selected framework (e.g., Spring AI, LangChain4j, etc.)
- **NEVER** mix or combine implementations from different frameworks in the same code path
- **MUST** maintain a clean abstraction/interface for each major capability (prompt building, LLM interaction, function calling, etc.)
- **MUST** ensure that switching between framework implementations is easy and foolproof
- **MUST** exhaust all native capabilities of the chosen framework before considering custom code
- **MUST** make explicit, documented decisions to use custom code, and only as a utility/exception
- **ALWAYS** respect the idioms, best practices, and strengths of the specific framework in use
- **CRITICAL**: This principle applies to all frameworks (Spring AI, LangChain4j, etc.) and is mandatory for all future implementations
