---
description: "Backend development and architecture rules"
globs: ["backend/**/*.java", "backend/**/*.kt", "backend/**/*.kts", "backend/**/build.gradle.kts", "backend/**/application.properties", "backend/**/application.yml"]
alwaysApply: true
---

# Backend Development Rules

Include the following rules:

[backend-organization.mdc](mdc:.cursor/rules/backend-organization.mdc)
[backend-architecture.mdc](mdc:.cursor/rules/backend-architecture.mdc)
[backend-migration.mdc](mdc:.cursor/rules/backend-migration.mdc)