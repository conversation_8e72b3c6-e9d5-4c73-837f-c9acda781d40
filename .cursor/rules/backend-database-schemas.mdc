# BACKEND DATABASE SCHEMAS - MANDATORY

## Database Architecture Overview

### **Multi-Database Architecture:**
- **sx_main_db**: Main application data (public schema)
- **sx_cdc_db**: AI, Audit, and Metrics data (multiple schemas)
- **sx_audit_db**: Dedicated audit database
- **sx_metrics_db**: Dedicated metrics database

## Database Schema Mapping

### **sx_main_db (Main Application Database)**
- **Schema**: `public`
- **Purpose**: Core application data
- **Tables**: 
  - `organization`, `users`, `teams`, `groups`
  - `ticket`, `ticket_participants`
  - `roles`, `permissions`, `user_roles`, `user_permissions`
  - `internal_comments`, `public_comment`
  - `email_source_config`, `api_tokens`
- **Module**: `app` (main Spring Boot application)
- **Access**: Primary application database

### **sx_cdc_db (AI, Audit, Metrics Database)**
- **Schema**: `ai`
- **Purpose**: AI module data and processing
- **Tables**:
  - `configurations` - AI configuration settings
  - `suggestions` - AI-generated suggestions (JSONB + relational)
  - `thinking_logs` - Chain of Thought / Tree of Thought logs
  - `vector_store` - Vector embeddings storage
  - `tool_executions` - AI tool execution tracking
  - `ai_flyway_schema_history` - Migration history
- **Module**: `ai` (AI/ML services)
- **Access**: AI module primary database

- **Schema**: `audit`
- **Purpose**: Audit trail and change tracking
- **Tables**: Audit-related tables for change data capture
- **Module**: `cdc` (Change Data Capture)
- **Access**: CDC module audit data

- **Schema**: `metrics`
- **Purpose**: Performance metrics and monitoring
- **Tables**: Metrics and monitoring data
- **Module**: `cdc` (Change Data Capture)
- **Access**: CDC module metrics data

### **sx_audit_db (Dedicated Audit Database)**
- **Purpose**: Dedicated audit storage
- **Module**: `cdc` (Change Data Capture)
- **Access**: CDC module dedicated audit

### **sx_metrics_db (Dedicated Metrics Database)**
- **Purpose**: Dedicated metrics storage
- **Module**: `cdc` (Change Data Capture)
- **Access**: CDC module dedicated metrics

## Module-Database Mapping

### **App Module**
- **Primary Database**: `sx_main_db`
- **Schema**: `public`
- **Purpose**: User-facing APIs and core business logic

### **AI Module**
- **Primary Database**: `sx_cdc_db`
- **Schema**: `ai`
- **Purpose**: AI/ML services and processing

### **CDC Module**
- **Primary Database**: `sx_cdc_db`
- **Schemas**: `audit`, `metrics`
- **Secondary Databases**: `sx_audit_db`, `sx_metrics_db`
- **Purpose**: Change data capture and monitoring

## Context Awareness Rules

### **Before Any Database Operation:**
1. **Identify the module** you're working with
2. **Determine the correct database** from the mapping above
3. **Use the appropriate schema** for the operation
4. **Use sx.* tools** instead of direct docker exec commands

### **Database Operation Checklist:**
- [ ] Which module am I working with?
- [ ] Which database should I use?
- [ ] Which schema should I use?
- [ ] Am I using sx.* tools instead of docker exec?

## Tool Usage Requirements

### **MANDATORY: Use sx.* Tools**
- **NEVER** use `docker exec` directly
- **ALWAYS** use `sx.db.*` commands for database operations
- **ALWAYS** use `sx.nats.*` commands for NATS operations
- **ALWAYS** use `sx.api.*` commands for API operations

### **Tool Mapping:**
- `sx.db.table.main` - sx_main_db.public tables
- `sx.db.table.ai` - sx_cdc_db.ai tables
- `sx.db.table.audit` - sx_cdc_db.audit tables
- `sx.db.table.metrics` - sx_cdc_db.metrics tables
- `sx.db.table.cdc` - sx_cdc_db tables

### **Error Prevention:**
- **ALWAYS** check database context before operations
- **ALWAYS** verify schema exists before table operations
- **ALWAYS** use sx.* tools for consistency
- **NEVER** assume database/schema location

## Migration Context

### **Migration Database Mapping:**
- **App migrations**: `sx_main_db.public`
- **AI migrations**: `sx_cdc_db.ai`
- **CDC migrations**: `sx_cdc_db.audit`, `sx_cdc_db.metrics`

### **Migration Naming:**
- **App**: `V<version>__description.sql` → `sx_main_db.public`
- **AI**: `V<version>__description.sql` → `sx_cdc_db.ai`
- **CDC**: `V<version>__description.sql` → `sx_cdc_db.audit/metrics`

## Cross-Module Considerations

### **Shared Data:**
- **Organizations**: `sx_main_db.public.organization`
- **Users**: `sx_main_db.public.users`
- **Tickets**: `sx_main_db.public.ticket`

### **Module-Specific Data:**
- **AI Suggestions**: `sx_cdc_db.ai.suggestions`
- **AI Configurations**: `sx_cdc_db.ai.configurations`
- **Audit Logs**: `sx_cdc_db.audit.*`
- **Metrics**: `sx_cdc_db.metrics.*`

## Context Loading Protocol

### **Before Any Operation:**
```
CONTEXT LOAD:
- Module: [app|ai|cdc]
- Database: [sx_main_db|sx_cdc_db|sx_audit_db|sx_metrics_db]
- Schema: [public|ai|audit|metrics]
- Tool: [sx.db.table.*|sx.db.query.*|sx.db.qq.*]
```

### **Context Validation:**
- [ ] Module identified correctly?
- [ ] Database selected correctly?
- [ ] Schema selected correctly?
- [ ] Using sx.* tools?
- [ ] Cross-module dependencies considered?
---
description: "Backend Database Schemas - Comprehensive schema mapping and context awareness"
globs: ["**/*"]
alwaysApply: true
---
