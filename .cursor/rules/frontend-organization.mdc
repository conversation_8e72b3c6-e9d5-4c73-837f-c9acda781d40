---
description: "Rules for frontend codebase organization and structure"
globs: ["frontend/src/**/*.ts", "frontend/src/**/*.tsx", "frontend/src/**/*.js", "frontend/src/**/*.jsx", "frontend/src/routes/**/*", "frontend/src/services/**/*"]
alwaysApply: true
---

# Frontend Organization Rules

## Project Structure
- Maintain clear directory organization
- Follow module separation principles
- Use consistent file naming
- Implement proper routing structure



