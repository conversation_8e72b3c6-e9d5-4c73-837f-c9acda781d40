---
description: "Backend architecture rules for layer mapping and design patterns"
globs: ["backend/**/*.java"]
alwaysApply: true
---

# BACKEND ARCHITECTURE RULES - MANDATORY

## Architecture Layer Mapping (MUST FOLLOW):
Controller -> Service + Mapper -> Repository -> DB

## Controller Layer (MUST):
- MUST use Spring RestController
- MUST handle HTTP requests only
- MUST delegate business logic to Service layer
- MUST return DTOs, never domain objects
- MUST have proper exception handling
- MUST have unit tests and integration tests
- Example: [TicketController.java](mdc:backend/app/src/main/java/io/sx/ticketing/controller/TicketController.java)

## Service Layer (MUST):
- MUST have an interface for every service
- MUST inherit from BaseService interface
- MUST have implementation class in `impl` package
- MUST accept and return DTOs only
- MUST use mappers to convert between DTOs and domain objects
- MUST handle business logic and validation
- MUST have unit tests
- Example: [OrganizationService.java](mdc:backend/app/src/main/java/io/sx/directory/service/OrganizationService.java)

## Mapper Layer (MUST):
- MUST map between domain objects and DTOs
- MUST have separate methods: `toDTO()` and `toEntity()`
- MUST be used by services for all conversions
- MUST have unit tests for both mapping directions
- Example: [OrganizationMapper.java](mdc:backend/app/src/main/java/io/sx/directory/mapper/OrganizationMapper.java)

## Repository Layer (CRITICAL - NEVER VIOLATE):
- MUST use Spring JDBC Templates
- NEVER use JPA or Hibernate
- MUST inherit from BaseRepository interface
- MUST have implementation class in `impl` package
- MUST accept and return domain objects only
- MUST handle database operations only
- MUST have unit tests
- Example: [OrganizationRepository.java](mdc:backend/app/src/main/java/io/sx/directory/repository/OrganizationRepository.java)

## Database Layer:
- MUST use Flyway for migrations
- MUST follow naming convention: V<version>__description.sql
- MUST include audit fields in all tables
- MUST use PostgreSQL

## Testing Requirements (MUST):
- Unit tests for all layers
- Integration tests for controllers
- Repository tests with test database
- Mapper tests for both directions

## Authentication & Authorization Patterns (CRITICAL):
- MUST authenticate as the correct user type for each operation
- MUST understand request context and organization relationships
- MUST validate that user has permission to perform action on target organization
- MUST use proper token authentication for API calls
- MUST respect organization hierarchy: SX_ADMIN > SUPPORT_ORG_ADMIN > ACCOUNT_ORG_USER

## Specific DO NOT Rules (CRITICAL):
- DO NOT use JPA, Hibernate, or any ORM framework
- DO NOT create account organizations as SX_ADMIN (must be created by support org admin)
- DO NOT create users for account organizations as SX_ADMIN (must be created by support org admin)
- DO NOT assume organization IDs or hardcode them in scripts
- DO NOT skip authentication in test data generation
- DO NOT use @Entity, @Table, @Column annotations
- DO NOT use JpaRepository or CrudRepository interfaces
- DO NOT return domain objects from controllers or services
- DO NOT ignore HTTP status codes in test scripts
- DO NOT assume API contracts without validation

## Iterative Development Rules (MANDATORY):
- MUST test each change immediately before proceeding
- MUST verify the specific functionality you changed works
- MUST commit working changes before adding new features
- MUST use debug mode (testdata.generate.debug) to see exact API calls
- MUST check backend logs for errors after each change
- MUST validate data creation in database after script runs
- MUST start with minimal working example before expanding
- MUST handle one authentication context at a time
- MUST verify organization relationships are correct
- MUST test API endpoints directly before using in scripts

## Error Prevention Rules:
- ALWAYS check current state before making changes
- ALWAYS test authentication flow first
- ALWAYS validate organization creation before user creation
- ALWAYS use proper error handling in scripts
- ALWAYS check for existing entities before creating new ones
- ALWAYS verify API responses match expected DTO structure
- ALWAYS use correct organization types (SUPPORT vs ACCOUNT)
- ALWAYS link account organizations to support organizations properly

Here is a mapping of architecture layers

Controller -> Service + Mapper -> Repository -> DB

- Controller:
    - Uses spring RestController
    - Example: [TicketController.java](mdc:backend/app/src/main/java/io/sx/ticketing/controller/TicketController.java)
    - Test
        - Unit Test:
        - Example: [TicketControllerTest.java](mdc:backend/app/src/test/java/io/sx/ticketing/controller/TicketControllerTest.java)
        - Integration Test:
        - Example: [TicketIntegrationTest.java](mdc:backend/app/src/test/java/io/sx/ticketing/integration/TicketIntegrationTest.java)

- Service:
    - Service has an interface.
    - A Service interface should inherit from [BaseService.ts](mdc:frontend/src/services/BaseService.ts)
    - Example: [OrganizationService.java](mdc:backend/app/src/main/java/io/sx/directory/service/OrganizationService.java)
    - There is alwas an implementation for the Service Interface
    - Example: [OrganizationServiceImpl.java](mdc:backend/app/src/main/java/io/sx/directory/service/impl/OrganizationServiceImpl.java)
    - Service methods always accept and return DTOs 
    - Example: [OrganizationDTO.java](mdc:backend/app/src/main/java/io/sx/directory/dto/OrganizationDTO.java)
    - Test:
        - Unit Test:
        - [OrganizationServiceTest.java](mdc:backend/app/src/test/java/io/sx/directory/service/OrganizationServiceTest.java)
    

- Mappers:
    - Mappers map between domain objects and DTOs
    - Example: [OrganizationMapper.java](mdc:backend/app/src/main/java/io/sx/directory/mapper/OrganizationMapper.java)
    - Services make use of mappers to convert from domain object to dto
    - Example: [OrganizationServiceImpl.java](mdc:backend/app/src/main/java/io/sx/directory/service/impl/OrganizationServiceImpl.java) uses [OrganizationMapper.java](mdc:backend/app/src/main/java/io/sx/directory/mapper/OrganizationMapper.java)
    - Test:
        - should have a test for toDTO
        - should have a test for toEntity

- Repository:
    - Uses spring JDBC Templates.
    - Never use JPA
    - A Repository interface should inherit from [BaseRepository.java](mdc:backend/app/src/main/java/io/sx/base/repository/BaseRepository.java)
    - Example: [OrganizationRepository.java](mdc:backend/app/src/main/java/io/sx/directory/repository/OrganizationRepository.java)
    - There is alwas an implementation for the Repository Interface
    - Example: [OrganizationRepositoryImpl.java](mdc:backend/app/src/main/java/io/sx/directory/repository/impl/OrganizationRepositoryImpl.java).java
    - Repository methods always accept and return Domain objects
    - Example: [Organization.java](mdc:backend/common/src/main/java/io/sx/model/Organization.java).java
    - Test:
        - Unit Test:
- Always use flyway specific naming convention when generating migrations.

Format:
V<version_number>__what_migration_does.sql

Example:
[V1__create_ticket_table.sql](mdc:backend/app/src/main/resources/db/migration/V1__create_ticket_table.sql)

## Migration Version Management:
- **ALWAYS** use the `find-latest-migration.sh` tool to determine the next version number
- **Command:** `./devenv/scripts/find-latest-migration.sh`
- **Rule:** Use version number that is one more than the highest migration version across all modules (app, ai, cdc)
- **Example:** If highest is V29, use V30 for the new migration

## Migration File Requirements:
All tables will need audit fields:
- created_at / updated_at 
- created_by_id, updated_by_id 
- created_by (string)
- all tables will need on_behalf_of_id

## Migration Restrictions:
- **NEVER** use `ALTER SYSTEM` commands in Flyway migrations
- **NEVER** use commands that require superuser privileges
- **NEVER** modify PostgreSQL configuration files via migrations
- **ONLY** use DDL/DML commands that modify database schema/data
- **ONLY** use commands that work with application database user privileges

## Migration Best Practices:
- Use descriptive names that explain what the migration does
- Include comments explaining complex migrations
- Test migrations on development database before committing
- Ensure migrations are idempotent and can be rolled back

## NATS Configuration Rules (CRITICAL - NEVER VIOLATE):
- **NEVER** use lowercase enum values for NATS JetStream policies
- **MUST** use proper case for NATS enum values:
  - `ack-policy=Explicit` (NOT `explicit`)
  - `deliver-policy=All` (NOT `all`)
  - `replay-policy=Instant` (NOT `instant`)
- **ALWAYS** check CDC module configuration as reference for correct enum values
- **NEVER** assume enum values are lowercase
- **ALWAYS** verify enum values match NATS client API expectations
- **CRITICAL**: This mistake has been repeated multiple times and causes application startup failures

## NATS Enum Parsing Rule (CRITICAL - NEVER VIOLATE):
- When parsing NATS JetStream enum values (AckPolicy, DeliverPolicy, ReplayPolicy), NEVER transform the property value (e.g., do NOT use toUpperCase()).
- Always use the property value as-is (e.g., AckPolicy.valueOf(ackPolicy)).
- The correct values are: `Explicit`, `All`, `Instant` (NOT all-caps or lowercase).
- This is a repeated source of production failures. See also always_applied_workspace_rules.mdc for more details.

## Spring AI Implementation Rules (CRITICAL - NEVER VIOLATE):
- **MUST** use Spring AI's native capabilities instead of custom implementations
- **MUST** use `PromptTemplate` for template rendering (NOT custom string replacement)
- **MUST** use `ChatModel` for LLM interactions (NOT direct HTTP calls)
- **MUST** use Spring AI's structured output capabilities when available
- **MUST** use Spring AI's function calling when available
- **MUST** use Spring AI's vector store integration when available
- **NEVER** implement custom template engines when Spring AI provides them
- **NEVER** use manual JSON parsing when Spring AI has structured output
- **NEVER** use custom prompt building when Spring AI has `PromptTemplate`
- **ALWAYS** leverage Spring AI's built-in error handling and validation
- **ALWAYS** use Spring AI's native message types (SystemMessage, UserMessage, AssistantMessage)
- **ALWAYS** prefer Spring AI's chain of thought and ReAct capabilities
- **CRITICAL**: This ensures we get the full benefits of Spring AI's mature, tested, and optimized implementations

## Spring AI Priority Enforcement (CRITICAL - NEVER VIOLATE):
- **ALWAYS** start with Spring AI features first - never default to custom implementations
- **MUST** exhaust all Spring AI capabilities before considering custom code
- **NEVER** use existing custom implementations without explicit justification
- **MUST** make explicit decisions to use custom code, never default to it
- **ALWAYS** treat custom implementations as utilities, not primary solutions
- **CRITICAL**: Custom code should be the exception, not the rule - Spring AI features are the standard

