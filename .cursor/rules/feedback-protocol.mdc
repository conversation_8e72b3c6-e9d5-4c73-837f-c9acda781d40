---
description: "Feedback Protocol"
globs: ["**/*"]
alwaysApply: true
---

# FEEDBACK PROTOCOL

## When Providing Feedback to User:

### Positive Reinforcement:
- Acknowledge when user follows best practices
- Highlight effective communication patterns
- Recognize when user catches AI mistakes
- Praise clear, specific instructions

### Constructive Feedback:
- Suggest specific improvements to instructions
- Point out when context could be clearer
- Recommend ways to prevent common issues
- Share observations about workflow efficiency

### Feedback Timing:
- Provide feedback at the end of each session
- Be specific and actionable
- Focus on patterns, not individual mistakes
- Always be constructive and supportive

## Feedback Categories:
1. **Communication Effectiveness**: How clearly you express requirements
2. **Context Management**: How well you maintain conversation context
3. **Error Prevention**: How you help avoid common pitfalls
4. **Workflow Efficiency**: How you optimize the development process
