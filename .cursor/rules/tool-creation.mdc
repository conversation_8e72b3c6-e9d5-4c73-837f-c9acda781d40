---
description: "Best practices for creating well-defined, discrete dev tools with single responsibility"
globs: ["backend/**/*.java", "frontend/**/*.ts", "frontend/**/*.tsx"]
alwaysApply: true
---

# TOOL CREATION BEST PRACTICES - MANDATORY

## Single Responsibility Principle (CRITICAL):
- **MUST** have one clear, well-defined purpose
- **MUST** do one thing exceptionally well
- **MUST** be focused and cohesive
- **NEVER** combine multiple unrelated operations
- **MUST** be atomic and indivisible

## Tool Interface Design (MANDATORY):
- **MUST** define clear input parameters with validation
- **MUST** specify expected output format and structure
- **MUST** document all parameters with purpose and constraints
- **MUST** use descriptive parameter names that reveal intent
- **MUST** provide meaningful error messages for invalid inputs

## Documentation Standards (CRITICAL):
- **MUST** include comprehensive Javadoc/TSDoc for all public methods
- **MUST** document parameter validation rules and constraints
- **MUST** provide usage examples for common scenarios
- **MUST** explain error conditions and recovery strategies
- **MUST** document side effects and dependencies

## Error Handling (MANDATORY):
- **MUST** validate all inputs before processing
- **MUST** provide specific, actionable error messages
- **MUST** handle edge cases gracefully
- **MUST** log errors with sufficient context for debugging
- **MUST** never throw generic exceptions without context

## Reusability Patterns (MANDATORY):
- **MUST** be stateless and thread-safe
- **MUST** accept all necessary data as parameters
- **MUST** avoid hardcoded dependencies
- **MUST** use dependency injection for external services
- **MUST** be composable with other tools

## MCP Integration (MANDATORY):
- **MUST** implement MCP tool interface when applicable
- **MUST** provide clear tool schema with parameter definitions
- **MUST** support both MCP and direct invocation patterns
- **MUST** use consistent naming conventions for MCP tools
- **MUST** provide proper error responses for MCP clients

## Testing Requirements (CRITICAL):
- **MUST** have comprehensive unit tests covering all scenarios
- **MUST** test parameter validation and error conditions
- **MUST** test edge cases and boundary conditions
- **MUST** mock external dependencies appropriately
- **MUST** achieve minimum 90% test coverage

## Performance Considerations:
- **MUST** be efficient and avoid unnecessary operations
- **MUST** use appropriate data structures and algorithms
- **MUST** avoid blocking operations when possible
- **MUST** implement proper resource cleanup
- **MUST** be scalable and handle expected load

## Security and Validation:
- **MUST** validate and sanitize all inputs
- **MUST** implement proper authorization checks
- **MUST** avoid SQL injection and other vulnerabilities
- **MUST** log security-relevant events
- **MUST** follow principle of least privilege

## Naming Conventions:
- **MUST** use descriptive, action-oriented names
- **MUST** follow consistent naming patterns across tools
- **MUST** use clear, domain-specific terminology
- **MUST** avoid abbreviations and acronyms
- **MUST** be self-documenting through naming

## Tool Composition:
- **MUST** be designed for composition with other tools
- **MUST** provide clear interfaces for integration
- **MUST** avoid tight coupling with other tools
- **MUST** use events or callbacks for loose coupling
- **MUST** support both synchronous and asynchronous patterns
