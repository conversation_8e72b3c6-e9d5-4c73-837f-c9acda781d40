---
description: "Rules for Role-Based Access Control implementation in frontend"
globs: ["frontend/src/components/security/**/*", "frontend/src/services/auth/**/*", "frontend/src/hooks/useAuth.ts", "frontend/src/contexts/AuthContext.tsx"]
alwaysApply: true
---

# Frontend RBAC Rules

## Security Implementation
- Implement proper authentication flows
- Use role-based access control
- Secure route protection
- Handle authorization properly