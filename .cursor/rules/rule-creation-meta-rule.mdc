# RULE CREATION META-RULE - MANDATORY

## Rule File Structure Requirements (CRITICAL):

### Frontmatter Block (MANDATORY):
- **MUST** be the very first content in the file (no blank lines before)
- **MUST** start with `---` on the first line
- **MUST** contain exactly these three fields:
  - `description: "Clear description of the rule's purpose"`
  - `globs: ["file patterns the rule applies to"]`
  - `alwaysApply: true` (or `false` if conditional)
- **MUST** end with `---` on its own line
- **NEVER** include extra fields like `title:`, `version:`, etc.

### Content Structure (MANDATORY):
- **MUST** have exactly one blank line after the closing `---`
- **MUST** start with a main heading (`# Rule Name`)
- **MUST** use consistent markdown formatting
- **MUST** follow the established rule patterns in the codebase

### File Naming Convention (MANDATORY):
- **MUST** use kebab-case: `rule-name.mdc`
- **MUST** be descriptive and indicate the rule's purpose
- **MUST** end with `.mdc` extension
- **MUST** be placed in `.cursor/rules/` directory

## Pre-Commit Validation Requirements (CRITICAL):

### Frontmatter Validation:
- **MUST** have valid YAML frontmatter at the very top
- **MUST** have all required fields present
- **MUST** have proper YAML syntax (no syntax errors)
- **MUST** have `alwaysApply` field set to boolean value

### Content Validation:
- **MUST** have content after frontmatter
- **MUST** have proper markdown structure
- **MUST** not have duplicate frontmatter blocks
- **MUST** not have content before the opening `---`

## Common Failure Patterns (AVOID):

### Frontmatter Placement Errors:
- **NEVER** put frontmatter in the middle or end of file
- **NEVER** have blank lines before the opening `---`
- **NEVER** have content before the frontmatter block
- **NEVER** append frontmatter to existing content

### Field Errors:
- **NEVER** use `title:` instead of `description:`
- **NEVER** omit required fields
- **NEVER** use incorrect data types (strings for booleans)
- **NEVER** add extra fields not in the standard

### Content Errors:
- **NEVER** have multiple frontmatter blocks
- **NEVER** mix frontmatter with content
- **NEVER** use incorrect markdown syntax

## Tool Usage Requirements (MANDATORY):

### Before Creating Rules:
1. **ALWAYS** use `./devenv/cursor/tools/create-rule.sh` for new rules
2. **ALWAYS** validate the generated rule with `./devenv/cursor/tools/validate-rule-files.sh`
3. **ALWAYS** test the rule passes pre-commit before committing

### When Editing Rules:
1. **ALWAYS** use `./devenv/cursor/tools/fix-frontmatter.sh` for frontmatter issues
2. **ALWAYS** validate after any frontmatter changes
3. **NEVER** manually edit frontmatter without validation

### Validation Commands:
```bash
# Create new rule
./devenv/cursor/tools/create-rule.sh "rule-name" "description" "globs"

# Validate existing rule
./devenv/cursor/tools/validate-rule-files.sh --file .cursor/rules/rule-name.mdc

# Fix frontmatter issues
./devenv/cursor/tools/fix-frontmatter.sh .cursor/rules/rule-name.mdc
```

## Rule Content Standards (MANDATORY):

### Heading Structure:
- **MUST** use `#` for main rule title
- **MUST** use `##` for major sections
- **MUST** use `###` for subsections
- **MUST** be descriptive and action-oriented

### Content Organization:
- **MUST** start with clear purpose statement
- **MUST** group related requirements together
- **MUST** use consistent formatting patterns
- **MUST** include examples where helpful

### Language Standards:
- **MUST** use imperative mood ("MUST", "NEVER", "ALWAYS")
- **MUST** be specific and actionable
- **MUST** avoid ambiguous language
- **MUST** use consistent terminology

## Error Prevention Checklist:

### Before Committing:
- [ ] Rule file has valid frontmatter at the top
- [ ] All required fields are present and correct
- [ ] No content before the opening `---`
- [ ] No duplicate frontmatter blocks
- [ ] File passes validation tool
- [ ] File follows naming convention
- [ ] Content is properly structured

### When Creating New Rules:
- [ ] Used the rule creation tool
- [ ] Validated the generated rule
- [ ] Tested with pre-commit hook
- [ ] Followed content standards
- [ ] Used appropriate globs pattern

## Tool Integration (MANDATORY):

### Rule Creation Workflow:
1. **Create**: Use `create-rule.sh` with proper parameters
2. **Validate**: Run `validate-rule-files.sh` to check structure
3. **Test**: Run pre-commit hook to ensure compliance
4. **Commit**: Only commit after all validations pass

### Error Recovery:
1. **Identify**: Use validation tool to find issues
2. **Fix**: Use appropriate tool for the issue type
3. **Revalidate**: Run validation again
4. **Test**: Ensure pre-commit passes

## Examples:

### Correct Frontmatter:
```yaml
---
description: "Backend model and DTO creation standards"
globs: ["backend/**/*.java"]
alwaysApply: true
---
```

### Correct File Structure:
```markdown
---
description: "Rule description"
globs: ["**/*.java"]
alwaysApply: true
---

# RULE TITLE

## Section 1

Content here...

## Section 2

More content...
```

### Incorrect Patterns (AVOID):
```yaml
# WRONG: Content before frontmatter
Some content here
---
description: "Rule description"
---

# WRONG: Missing required fields
---
description: "Rule description"
---

# WRONG: Extra fields
---
title: "Rule Title"
description: "Rule description"
version: "1.0"
globs: ["**/*.java"]
alwaysApply: true
---
```

## Success Criteria:
- [ ] All rule files pass pre-commit validation
- [ ] No manual frontmatter editing required
- [ ] Consistent rule structure across all files
- [ ] Tools handle all common scenarios
- [ ] Clear error messages guide fixes
- [ ] Validation catches all issues before commit

