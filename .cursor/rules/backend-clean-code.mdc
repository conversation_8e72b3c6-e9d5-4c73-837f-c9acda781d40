---
description: "Backend clean code rules for DRY, SOLID principles, and comprehensive test coverage"
globs: ["backend/**/*.java"]
alwaysApply: true
---

# BACKE<PERSON> CLEAN CODE RULES - MANDATORY

## DRY (Don't Repeat Yourself) Principles (CRITICAL):
- **MUST** extract common functionality into reusable methods/classes
- **MUST** use base classes and interfaces for shared behavior
- **MUST** create utility classes for common operations
- **MUST** use composition over inheritance for code reuse
- **NEVER** copy-paste code blocks - always refactor to shared methods

## SOLID Principles (MANDATORY):
- **MUST** follow Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, and Dependency Inversion principles
- **MUST** keep classes and methods focused and cohesive
- **MUST** use interfaces and abstractions for extensibility
- **MUST** use dependency injection
- **NEVER** violate base class contracts or force classes to implement unused methods

## Clean Code Standards (MANDATORY):
- **MUST** use descriptive, intention-revealing names
- **MUST** keep methods small and focused
- **MUST** use proper encapsulation and minimize public methods
- **MUST** provide meaningful error messages and handle exceptions at appropriate levels

## Test Coverage Requirements (CRITICAL):
- **MUST** achieve minimum 80% line coverage and 90% branch coverage for critical paths
- **MUST** test all public methods, edge cases, and business logic paths
- **MUST** follow AAA pattern (Arrange, Act, Assert) in tests
- **MUST** use proper test data setup and cleanup
- **MUST** mock external dependencies and avoid over-mocking

## Code Quality Tools (MANDATORY):
- **MUST** use static analysis tools (e.g., SonarQube) and fix all critical/major issues
- **MUST** review all code changes for SOLID, DRY, and test coverage

## Refactoring Guidelines:
- **MUST** refactor when code duplication or SOLID violations are detected
- **MUST** have comprehensive tests before refactoring
- **MUST** refactor in small, incremental steps and run all tests after each step

## Documentation Standards:
- **MUST** document complex business logic and public APIs
- **MUST** use clear, concise comments and keep documentation up to date

## Performance Considerations:
- **MUST** avoid unnecessary object creation, optimize queries, and use appropriate data structures
- **MUST** profile code for performance bottlenecks and monitor memory usage

## Avoid Hard-Coded String Literals and Magic Numbers (MANDATORY):
- **MUST** use enums or defined constants for all repeated or significant string values (e.g., status, types, keys, event names)
- **NEVER** use magic strings directly in business logic, controllers, or services
- **MUST** centralize constants in a dedicated class or interface
- **MUST** avoid magic numbers in business logic - use named constants instead
- **MUST** use descriptive variable names for numeric values in tests and stubs
- **NEVER** use hardcoded IDs or numeric values without clear context

### Examples:
```java
// BAD - Magic numbers and strings
findOrg(1);
if (status.equals("ACTIVE")) { ... }
return new Ticket(123, "HIGH");

// GOOD - Named constants and variables
int orgId = 1;
findOrg(orgId);
if (status.equals(OrganizationStatus.ACTIVE.getValue())) { ... }
int ticketId = 123;
Priority priority = Priority.HIGH;
return new Ticket(ticketId, priority.getValue());

// OK for simple assertions
assertEquals(count, 1);
assertTrue(result.isEmpty());
```

## Object-Oriented Abstractions (MANDATORY):
- **MUST** use proper OOP principles: encapsulation, inheritance, polymorphism, and composition
- **MUST** design extensible and maintainable abstractions for core business logic
- **MUST** prefer interfaces and abstract classes for shared contracts
