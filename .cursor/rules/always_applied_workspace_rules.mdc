---
description: "Mandatory rules for AI productivity, debugging, and development workflow that must always be applied"
globs: ["**/*"]
alwaysApply: true
---

# AI PRODUCTIVITY AND DEBUGGING RULES - MANDATORY

## Logging and Process Monitoring Protocol (CRITICAL):
- MUST: Check `sx.status` before asking about process state
- MUST: Read log files from `~/.sx/logs/` before asking for error details
- MUST: Use `lsof -i :PORT` to find processes on specific ports
- MUST: Check `docker logs CONTAINER` for containerized services
- MUST: Read git status and diff logs before asking about changes
- MUST: Check pre-commit hook logs before asking about commit failures
- MUST: Use `ps aux | grep PROCESS` to find running processes
- MUST: Never ask for copy-pasted logs when files are available
- MUST: Always check `~/.sx/logs/` directory first for any process information

## File Synchronization Protocol (CRITICAL):
- MUST: Check `git status` before making any file changes
- MUST: Resync any files that show as modified in git status
- MUST: Use `read_file` tool to get current content of modified files
- MUST: Never assume file content without reading it first
- MUST: Ask user to copy-paste file content if reading tools fail

## Test Data Generation Workflow (MANDATORY):
- MUST: Generate data in dependency order (org → users → tickets)
- MUST: Capture and reuse IDs from previous operations
- MUST: Use actual API responses to get generated IDs
- NEVER: Assume IDs or hardcode them in test scripts

## Error Handling in Test Scripts (MANDATORY):
- MUST: Check HTTP status codes in test scripts
- MUST: Validate response structure before proceeding
- MUST: Handle 400/500 errors gracefully
- MUST: Log response bodies for debugging

## Authentication Flow (CRITICAL):
- MUST: Always authenticate before data operations
- MUST: Extract and use JWT tokens from login responses
- MUST: Include Authorization headers in subsequent requests
- NEVER: Skip authentication in test data generation

## API Contract Validation (MANDATORY):
- MUST: Validate that API responses match expected DTO structures
- MUST: Ensure all required fields are present in responses
- MUST: Test both happy path and error scenarios
- NEVER: Assume API contracts without validation

## Repository Save Method Pattern (CRITICAL):
- MUST: All repository save methods return the saved object with generated ID
- MUST: Use RETURNING * with queryForObject() for INSERT operations
- MUST: Fetch updated object after UPDATE operations
- Example: Repository implementations now return complete objects with IDs

## Service Layer Data Flow (CRITICAL):
- MUST: Services always work with DTOs, never domain objects
- MUST: Services use mappers to convert between DTOs and domain objects
- MUST: Services return DTOs with complete data (including generated IDs)
- NEVER: Return incomplete objects or objects without IDs

## API Response Consistency (CRITICAL):
- MUST: All POST/PUT endpoints return the complete created/updated object
- MUST: Include generated IDs in all responses
- MUST: Return the same object structure that can be used for subsequent operations
- NEVER: Return only success messages without the actual data

## Iterative Development Protocol (MANDATORY):
- MUST: Test each change immediately before proceeding to next step
- MUST: Verify specific functionality works before adding complexity
- MUST: Use debug mode (testdata.generate.debug) to see exact API calls
- MUST: Check backend logs after each change for errors
- MUST: Validate data creation in database after script runs
- MUST: Start with minimal working example before expanding
- MUST: Handle one authentication context at a time
- MUST: Commit working changes before adding new features
- MUST: Test API endpoints directly before using in scripts
- MUST: Verify organization relationships are correct

## Specific DO NOT Rules (CRITICAL):
- DO NOT use JPA, Hibernate, or any ORM framework
- DO NOT create account organizations as SX_ADMIN (must be created by support org admin)
- DO NOT create users for account organizations as SX_ADMIN (must be created by support org admin)
- DO NOT assume organization IDs or hardcode them in scripts
- DO NOT skip authentication in test data generation
- DO NOT use @Entity, @Table, @Column annotations
- DO NOT use JpaRepository or CrudRepository interfaces
- DO NOT return domain objects from controllers or services
- DO NOT ignore HTTP status codes in test scripts
- DO NOT assume API contracts without validation
- **DO NOT use lowercase NATS enum values** - MUST use `Explicit`, `All`, `Instant` (NOT `explicit`, `all`, `instant`)
- DO NOT make multiple unrelated changes in one session
- DO NOT proceed without testing each step
- DO NOT ignore backend error logs
- DO NOT assume authentication context is correct

## Prevention Strategies:
1. Always test the complete flow: org → user → ticket creation
2. Validate each step: Check responses before proceeding to next step
3. Use proper error handling: Don't ignore HTTP errors
4. Maintain state: Pass IDs between operations
5. Log everything: Include response bodies in debug output

## Memory Rules:
1. ALWAYS reference previous conversation context
2. ALWAYS acknowledge when making assumptions
3. ALWAYS verify file states before and after changes
4. ALWAYS test each change immediately
5. NEVER leave the codebase in a broken state

## Rule File Standards (CRITICAL):
- MUST: Use `./devenv/cursor/tools/create-rule.sh` for new rule files
- MUST: Use `search_replace` for editing existing rule files
- NEVER: Use `edit_file` with `code_edit` that replaces entire rule files
- MUST: Preserve frontmatter (description, globs, alwaysApply) in all edits
- MUST: Verify frontmatter integrity after any rule file changes
- MUST: Follow `.cursor/rules/rule-file-standards.mdc` for all rule operations

## Cursor AI Tools Integration (MANDATORY):
- MUST: Use `devenv/cursor/tools/show-project-structure.sh` before making architectural decisions
- MUST: Use `devenv/cursor/tools/analyze-module-dependencies.sh` before adding new dependencies
- MUST: Use `devenv/cursor/tools/sync-directories.sh` when project structure changes
- MUST: Reference tool outputs in AI conversations for context
- MUST: Validate AI suggestions against tool outputs
- MUST: Run tools to understand project context before making changes
- MUST: Use tools to prevent dependency violations and architectural errors
- NEVER: Make assumptions about project structure without running analysis tools
- NEVER: Suggest dependency changes without validating against dependency analysis
- NEVER: Proceed with architectural changes without understanding current structure

## NATS Enum Parsing Rule (CRITICAL - NEVER VIOLATE):
- When parsing NATS JetStream enum values (AckPolicy, DeliverPolicy, ReplayPolicy), NEVER transform the property value (e.g., do NOT use toUpperCase()).
- Always use the property value as-is (e.g., AckPolicy.valueOf(ackPolicy)).
- The correct values are: `Explicit`, `All`, `Instant` (NOT all-caps or lowercase).
- This is a repeated source of production failures. See also backend-architecture.mdc for more details.

## Spring AI Implementation Rules (CRITICAL - NEVER VIOLATE):
- **MUST** use Spring AI's native capabilities instead of custom implementations
- **MUST** use `PromptTemplate` for template rendering (NOT custom string replacement)
- **MUST** use `ChatModel` for LLM interactions (NOT direct HTTP calls)
- **NEVER** use `ChatClient` - ALWAYS use `ChatModel` for all LLM interactions
- **MUST** use Spring AI's structured output capabilities when available
- **MUST** use Spring AI's function calling when available
- **MUST** use Spring AI's vector store integration when available
- **NEVER** implement custom template engines when Spring AI provides them
- **NEVER** use manual JSON parsing when Spring AI has structured output
- **NEVER** use custom prompt building when Spring AI has `PromptTemplate`
- **ALWAYS** leverage Spring AI's built-in error handling and validation
- **ALWAYS** use Spring AI's native message types (SystemMessage, UserMessage, AssistantMessage)
- **ALWAYS** prefer Spring AI's chain of thought and ReAct capabilities
- **CRITICAL**: This ensures we get the full benefits of Spring AI's mature, tested, and optimized implementations

## Spring AI Priority Rules (CRITICAL - NEVER VIOLATE):
- **ALWAYS** start with Spring AI features first - never default to custom implementations
- **MUST** exhaust all Spring AI capabilities before considering custom code
- **NEVER** use existing custom implementations without explicit justification
- **MUST** make explicit decisions to use custom code, never default to it
- **ALWAYS** treat custom implementations as utilities, not primary solutions
- **CRITICAL**: Custom code should be the exception, not the rule - Spring AI features are the standard

## Framework-Specific Implementation Principles (CRITICAL)
- **ALWAYS** start with the native features and idioms of the selected framework (e.g., Spring AI, LangChain4j, etc.)
- **NEVER** mix or combine implementations from different frameworks in the same code path
- **MUST** maintain a clean abstraction/interface for each major capability (prompt building, LLM interaction, function calling, etc.)
- **MUST** ensure that switching between framework implementations is easy and foolproof
- **MUST** exhaust all native capabilities of the chosen framework before considering custom code
- **MUST** make explicit, documented decisions to use custom code, and only as a utility/exception
- **ALWAYS** respect the idioms, best practices, and strengths of the specific framework in use
- **CRITICAL**: This principle applies to all frameworks (Spring AI, LangChain4j, etc.) and is mandatory for all future implementations

- **NEVER** use existing custom implementations without explicit justification
- **MUST** make explicit decisions to use custom code, never default to it
- **ALWAYS** treat custom implementations as utilities, not primary solutions
- **CRITICAL**: Custom code should be the exception, not the rule - Spring AI features are the standard
