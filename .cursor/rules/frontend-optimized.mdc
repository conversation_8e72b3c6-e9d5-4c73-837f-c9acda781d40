---
description: "Optimized frontend rules consolidating architecture, components, services, UX, and clean code patterns"
globs: ["frontend/**/*.ts", "frontend/**/*.tsx", "frontend/**/*.css", "frontend/**/*.scss"]
alwaysApply: true
---

# FRONTEND ARCHITECTURE RULES - MANDATORY

## Project Structure (MUST FOLLOW):
```
frontend/src/
├── components/          # Reusable UI components
│   ├── common/         # Shared components (Button, Input, etc.)
│   ├── pages/          # Page-level components
│   ├── layout/         # Layout components (Header, Sidebar, etc.)
│   └── __tests__/      # Component tests
├── services/           # API services and business logic
├── hooks/              # Custom React hooks
├── contexts/           # React contexts for state management
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── styles/             # Global styles and themes
├── design-system/      # Design tokens and system
└── routes/             # Routing configuration
```

## Component Organization (MANDATORY):
- **Common Components**: Reusable UI elements in `components/common/`
- **Page Components**: Route-specific components in `components/pages/`
- **Layout Components**: Structural components in `components/layout/`
- **Feature Components**: Feature-specific components in dedicated folders
- **Test Files**: Co-located with components in `__tests__/` folders

# FRONTEND COMPONENT RULES - MANDATORY

## Component Structure (MUST):
- **MUST** use functional components with hooks
- **MUST** follow PascalCase naming convention for components
- **MUST** use TypeScript interfaces for prop definitions
- **MUST** implement proper prop validation
- **MUST** include JSDoc comments for complex components
- **MUST** co-locate test files with components

## Component Best Practices (CRITICAL):
- **MUST** keep components focused on single responsibility
- **MUST** use composition over inheritance
- **MUST** implement proper error boundaries
- **MUST** handle loading and error states
- **MUST** use React.memo for performance optimization when appropriate
- **NEVER** use class components (functional components only)

## Component Example:
```typescript
interface ButtonProps {
  /** Button text content */
  children: React.ReactNode;
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'danger';
  /** Click handler */
  onClick?: () => void;
  /** Disabled state */
  disabled?: boolean;
}

/**
 * Reusable button component with consistent styling
 */
export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  onClick,
  disabled = false,
}) => {
  return (
    <button
      className={`btn btn-${variant}`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};
```

# FRONTEND SERVICE RULES - MANDATORY

## Service Layer Architecture (MUST):
- **MUST** use TypeScript for all service files
- **MUST** inherit from BaseService for API services
- **MUST** implement proper error handling with try-catch blocks
- **MUST** use consistent error response patterns
- **MUST** implement retry mechanisms for network failures
- **MUST** use proper HTTP status code handling

## Service Implementation Patterns (CRITICAL):
- **MUST** create service interfaces for all API interactions
- **MUST** use axios or fetch with proper configuration
- **MUST** implement request/response interceptors
- **MUST** handle authentication tokens properly
- **MUST** provide loading states for async operations
- **NEVER** make direct API calls from components

## Service Example:
```typescript
interface UserService {
  getUsers(): Promise<User[]>;
  createUser(user: CreateUserRequest): Promise<User>;
  updateUser(id: string, user: UpdateUserRequest): Promise<User>;
  deleteUser(id: string): Promise<void>;
}

export class UserServiceImpl extends BaseService implements UserService {
  async getUsers(): Promise<User[]> {
    try {
      const response = await this.api.get<User[]>('/users');
      return response.data;
    } catch (error) {
      this.handleError(error, 'Failed to fetch users');
      throw error;
    }
  }

  async createUser(user: CreateUserRequest): Promise<User> {
    try {
      const response = await this.api.post<User>('/users', user);
      return response.data;
    } catch (error) {
      this.handleError(error, 'Failed to create user');
      throw error;
    }
  }
}
```

# FRONTEND HOOKS RULES - MANDATORY

## Custom Hooks (MUST):
- **MUST** use "use" prefix for all custom hooks
- **MUST** follow React hooks rules (only call at top level)
- **MUST** return consistent data structures
- **MUST** handle loading and error states
- **MUST** implement proper cleanup in useEffect
- **MUST** use TypeScript for hook definitions

## Hook Example:
```typescript
interface UseApiOptions<T> {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  body?: T;
  dependencies?: any[];
}

interface UseApiResult<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useApi<T, R = T>({
  url,
  method = 'GET',
  body,
  dependencies = []
}: UseApiOptions<T>): UseApiResult<R> {
  const [data, setData] = useState<R | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.request<R>({ url, method, data: body });
      setData(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [url, method, body]);

  useEffect(() => {
    fetchData();
  }, dependencies);

  return { data, loading, error, refetch: fetchData };
}
```

# FRONTEND STATE MANAGEMENT RULES - MANDATORY

## Context Usage (MUST):
- **MUST** use React Context for global state
- **MUST** create context providers for related state
- **MUST** implement proper context optimization
- **MUST** use useMemo and useCallback for context values
- **MUST** provide default values for contexts
- **NEVER** use prop drilling for deeply nested state

## State Management Patterns (CRITICAL):
- **MUST** separate UI state from business state
- **MUST** use reducers for complex state logic
- **MUST** implement proper state persistence when needed
- **MUST** handle state updates immutably
- **MUST** provide loading and error states for async operations

## Context Example:
```typescript
interface AuthContextType {
  user: User | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);

  const login = useCallback(async (credentials: LoginCredentials) => {
    setLoading(true);
    try {
      const userData = await authService.login(credentials);
      setUser(userData);
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = useCallback(() => {
    setUser(null);
    authService.logout();
  }, []);

  const value = useMemo(() => ({
    user,
    login,
    logout,
    loading
  }), [user, login, logout, loading]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
```

# FRONTEND UX RULES - MANDATORY

## User Experience Guidelines (MUST):
- **MUST** follow consistent design patterns across the application
- **MUST** implement responsive layouts for all screen sizes
- **MUST** ensure accessibility standards (WCAG 2.1 AA compliance)
- **MUST** maintain consistent error handling and user feedback
- **MUST** provide loading states for all async operations
- **MUST** implement proper form validation with user-friendly messages

## Design System Integration (CRITICAL):
- **MUST** use design tokens from `design-system/` folder
- **MUST** follow established color, typography, and spacing patterns
- **MUST** use consistent component variants and states
- **MUST** implement dark/light theme support
- **MUST** ensure proper contrast ratios for accessibility

## Accessibility Requirements (CRITICAL):
- **MUST** use semantic HTML elements
- **MUST** provide proper ARIA labels and roles
- **MUST** ensure keyboard navigation support
- **MUST** implement focus management
- **MUST** provide alternative text for images
- **MUST** test with screen readers

## Responsive Design (MUST):
- **MUST** use CSS Grid and Flexbox for layouts
- **MUST** implement mobile-first design approach
- **MUST** use relative units (rem, em, %) instead of fixed pixels
- **MUST** test on multiple device sizes
- **MUST** ensure touch-friendly interface elements

# FRONTEND CLEAN CODE RULES - MANDATORY

## DRY (Don't Repeat Yourself) Principles (CRITICAL):
- **MUST** extract common logic into reusable hooks, components, or utility functions
- **MUST** use shared context/providers for cross-cutting concerns
- **MUST** avoid copy-pasting code between components
- **MUST** use composition and higher-order components for reuse
- **NEVER** duplicate logic across files or components

## SOLID Principles (as applicable to TypeScript/React):
- **MUST** keep components and hooks focused on a single responsibility
- **MUST** use composition over inheritance for component reuse
- **MUST** design interfaces and types for extensibility
- **MUST** use dependency injection through props and context
- **NEVER** force components to implement unused props or methods

## Clean Code Standards (MANDATORY):
- **MUST** use descriptive, intention-revealing names for components, functions, and variables
- **MUST** keep components and functions small and focused
- **MUST** use proper TypeScript typing and avoid `any` types
- **MUST** provide meaningful error messages and handle errors gracefully

## Test Coverage Requirements (CRITICAL):
- **MUST** achieve minimum 80% line coverage and 90% branch coverage for critical paths
- **MUST** test all component props, hooks, and business logic paths
- **MUST** follow AAA pattern (Arrange, Act, Assert) in tests
- **MUST** use proper test data setup and cleanup
- **MUST** mock external dependencies and avoid over-mocking

## Code Quality Tools (MANDATORY):
- **MUST** use ESLint, Prettier, and TypeScript strict mode
- **MUST** fix all linting errors and warnings
- **MUST** review all code changes for SOLID, DRY, and test coverage

## Performance Considerations (CRITICAL):
- **MUST** use React.memo, useMemo, and useCallback appropriately
- **MUST** avoid unnecessary re-renders and optimize bundle size
- **MUST** profile components for performance bottlenecks
- **MUST** implement code splitting for large applications
- **MUST** optimize images and assets

## Avoid Hard-Coded String Literals (MANDATORY):
- **MUST** use enums, union types, or defined constants for all repeated or significant string values
- **NEVER** use magic strings directly in business logic, components, or services
- **MUST** centralize constants in a dedicated module or file
- **MUST** use internationalization (i18n) for user-facing text

## TypeScript Best Practices (CRITICAL):
- **MUST** use strict TypeScript configuration
- **MUST** define proper interfaces for all data structures
- **MUST** use generic types for reusable components
- **MUST** avoid type assertions when possible
- **MUST** use utility types (Partial, Pick, Omit, etc.)
- **NEVER** use `any` type without explicit justification

# FRONTEND TESTING RULES - MANDATORY

## Testing Strategy (MUST):
- **MUST** write unit tests for all components and hooks
- **MUST** write integration tests for service layer
- **MUST** write end-to-end tests for critical user flows
- **MUST** use React Testing Library for component tests
- **MUST** use Jest for test runner and mocking

## Component Testing (CRITICAL):
- **MUST** test component rendering and user interactions
- **MUST** test prop variations and edge cases
- **MUST** test error states and loading states
- **MUST** test accessibility features
- **MUST** use meaningful test descriptions

## Test Example:
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Click me</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

# FRONTEND SECURITY RULES - MANDATORY

## Security Best Practices (CRITICAL):
- **MUST** validate all user inputs
- **MUST** sanitize data before rendering
- **MUST** use HTTPS for all API calls
- **MUST** implement proper authentication and authorization
- **MUST** handle sensitive data securely
- **NEVER** store sensitive data in localStorage without encryption

## Authentication & Authorization (MUST):
- **MUST** implement proper token management
- **MUST** handle token expiration gracefully
- **MUST** implement secure logout functionality
- **MUST** protect routes based on user permissions
- **MUST** validate user permissions on the client side

# FRONTEND ERROR HANDLING RULES - MANDATORY

## Error Handling Patterns (CRITICAL):
- **MUST** implement error boundaries for component error handling
- **MUST** provide user-friendly error messages
- **MUST** log errors for debugging purposes
- **MUST** handle network errors gracefully
- **MUST** implement retry mechanisms for transient failures

## Error Boundary Example:
```typescript
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <button onClick={() => window.location.reload()}>
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

# FRONTEND PERFORMANCE RULES - MANDATORY

## Performance Optimization (CRITICAL):
- **MUST** implement code splitting using React.lazy and Suspense
- **MUST** optimize bundle size and reduce dependencies
- **MUST** use React.memo for expensive components
- **MUST** implement proper list virtualization for large datasets
- **MUST** optimize images and use appropriate formats
- **MUST** implement proper caching strategies

## Bundle Optimization (MUST):
- **MUST** analyze bundle size regularly
- **MUST** remove unused dependencies
- **MUST** use tree shaking effectively
- **MUST** implement proper chunk splitting
- **MUST** optimize third-party library usage

# FRONTEND DEPLOYMENT RULES - MANDATORY

## Build and Deployment (MUST):
- **MUST** use environment variables for configuration
- **MUST** implement proper build optimization
- **MUST** use CI/CD pipelines for deployment
- **MUST** implement proper versioning and rollback strategies
- **MUST** monitor application performance in production

## Environment Configuration (CRITICAL):
- **MUST** use different configurations for development, staging, and production
- **MUST** validate environment variables at build time
- **MUST** implement proper feature flags for gradual rollouts
- **MUST** use secure configuration management
