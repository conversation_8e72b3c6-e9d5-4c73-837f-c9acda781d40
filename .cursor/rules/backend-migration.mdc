---
description: "Rules for Flyway migration management and versioning"
globs: ["backend/**/*.sql", "backend/**/*.java"]
alwaysApply: true
---

Always use flyway specific naming convention when generating migrations.

Format:
V<version_number>__what_migration_does.sql

Example:
[V1__create_ticket_table.sql](mdc:backend/app/src/main/resources/db/migration/V1__create_ticket_table.sql)

## Migration Version Management:
- **ALWAYS** use the `find-latest-migration.sh` tool to determine the next version number
- **Command:** `./devenv/scripts/find-latest-migration.sh`
- **Rule:** Use version number that is one more than the highest migration version across all modules (app, ai, cdc)
- **Example:** If highest is V29, use V30 for the new migration

## Migration File Requirements:
All tables will need audit fields:
- created_at / updated_at 
- created_by_id, updated_by_id 
- created_by (string)
- all tables will need on_behalf_of_id

## Migration Restrictions:
- **NEVER** use `ALTER SYSTEM` commands in Flyway migrations
- **NEVER** use commands that require superuser privileges
- **NEVER** modify PostgreSQL configuration files via migrations
- **ONLY** use DDL/DML commands that modify database schema/data
- **ONLY** use commands that work with application database user privileges

## Migration Best Practices:
- Use descriptive names that explain what the migration does
- Include comments explaining complex migrations
- Test migrations on development database before committing
- Ensure migrations are idempotent and can be rolled back
