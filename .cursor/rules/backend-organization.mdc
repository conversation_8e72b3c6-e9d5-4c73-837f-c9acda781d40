---
description: "Backend organization rules for module structure and project layout"
globs: ["backend/**/*"]
alwaysApply: true
---
- Here is highlevel structure of the backend project

backend % tree -L 1
.
├── README.md
├── app
├── build.gradle.kts
├── common
├── cqrs
├── docker-compose.yml
├── gradle
├── gradlew
├── gradlew.bat
├── initdb
├── settings.gradle.kts
├── source
└── testdata


app, common, cqrs etc are top level modules

inside app here is the organization

src % tree -L 6
.
├── main
│   ├── java
│   │   └── io
│   │       └── sx
│   │           ├── App.java
│   │           ├── base
│   │           │   ├── repository
│   │           │   └── service
│   │           ├── config
│   │           │   ├── CustomAuthenticationProvider.java
│   │           │   ├── JacksonConfig.java
│   │           │   ├── JwtAuthenticationFilter.java
│   │           │   ├── NoSecurityConfig.java
│   │           │   ├── PasswordEncoderProvider.java
│   │           │   ├── SecurityConfig.java
│   │           │   └── WebConfig.java
│   │           ├── context
│   │           │   └── RequestContext.java
│   │           ├── controller
│   │           │   ├── EmailSourceConfigController.java
│   │           │   └── OrganizationController.java
│   │           ├── directory
│   │           │   ├── controller
│   │           │   ├── dto
│   │           │   ├── mapper
│   │           │   ├── model
│   │           │   ├── repository
│   │           │   └── service
│   │           ├── exception
│   │           │   ├── GlobalExceptionHandler.java
│   │           │   ├── NotFoundException.java
│   │           │   └── UniquenessViolationException.java
│   │           ├── service
│   │           │   ├── EmailSourceConfigService.java
│   │           │   ├── EmailSourceScheduler.java
│   │           ├── source
│   │           │   └── repository
│   │           └── ticketing
│   │               ├── TicketMapper.java
│   │               ├── client
│   │               ├── controller
│   │               ├── dto
│   │               ├── model
│   │               ├── repository
│   │               ├── scheduler
│   │               └── service
│   └── resources
│       ├── application.properties
│       └── db
│           ├── migration
│           │   ├── V10__add_org_id_to_tickets.sql
│           │   ├── V11__create_roles_and_permissions.sql
│           │   ├── V12__create_email_source_config_table.sql
│           │   ├── V1__create_ticket_table.sql
│           │   ├── V2__add_status_and_priority_columns.sql
│           │   ├── V3__add_support_organization.sql
│           │   ├── V4__create_users.sql
│           │   ├── V5__update_organization_schema.sql
│           │   ├── V6__create_internal_comments.sql
│           │   ├── V7__create_rbac_tables.sql
│           │   ├── V8__add_team_id_to_users.sql
│           │   └── V9__add_ticket_participants.sql
│           └── scripts
└── test
    ├── java
    │   └── io
    │       └── sx
    │           ├── OrganizationDbOperations.java
    │           ├── config
    │           │   └── TestConfig.java
    │           ├── directory
    │           │   ├── controller
    │           │   ├── repository
    │           │   └── service
    │           └── ticketing
    │               ├── controller
    │               ├── dao
    │               ├── integration
    │               └── service
    └── resources
        └── application-test.properties



inside app module, all rbac is organized under directory package

- backend is the root of the backend project

- project uses gradle for build [build.gradle.kts](mdc:backend/build.gradle.kts)

- all relevant project modules can be inferred from  [settings.gradle.kts](mdc:backend/settings.gradle.kts)

# CURSOR TOOLS FOR BACKEND ANALYSIS - MANDATORY

## Project Structure Analysis Tools:
- **Tool**: `devenv/cursor/tools/show-project-structure.sh`
- **Purpose**: Understand project organization for decision making
- **Usage**: Use `-b` for backend, `-m` for modules, `-s` for source code
- **Integration**: Run before making architectural decisions

## Module Dependency Analysis Tools:
- **Tool**: `devenv/cursor/tools/analyze-module-dependencies.sh`
- **Purpose**: Understand module relationships and constraints
- **Usage**: Use `-s` for summary, `-d` for detailed, `-c` for constraints
- **Integration**: Run before adding new dependencies or modules

## Directory Sync Tools:
- **Tool**: `devenv/cursor/tools/sync-directories.sh`
- **Purpose**: Keep directory structures and module information current
- **Usage**: Use `-a` for all, `-s` for structures, `-d` for dependencies
- **Integration**: Run regularly to maintain up-to-date information

## Tool Usage Patterns:
1. **Before Architectural Decisions**: Run structure and dependency analysis
2. **Before Adding Dependencies**: Analyze module relationships
3. **Regular Maintenance**: Sync directories to keep information current
4. **Problem Investigation**: Use detailed analysis for troubleshooting

# MODULE STRUCTURE RULES - MANDATORY

## Module Organization Patterns:
- **app**: Main Spring Boot application (user-facing APIs)
- **ai**: AI/ML services (separate application)
- **cdc**: Change Data Capture services (separate application)
- **common**: Shared DTOs, models, and utilities
- **cqrs**: Command Query Responsibility Segregation
- **source**: Data source integration services

## Module Dependency Rules:
- **common**: Can be depended on by ALL modules (shared code)
- **app**: Can depend on common, but NOT on ai, cdc, cqrs
- **ai**: Can depend on common, but NOT on app, cdc, cqrs
- **cdc**: Can depend on common, but NOT on app, ai, cqrs
- **cqrs**: Can depend on common, but NOT on app, ai, cdc
- **source**: Can depend on common, but NOT on app, ai, cdc, cqrs

## Shared Code Organization:
- **DTOs**: All DTOs in `common` module
- **Models**: All domain models in `common` module
- **Utilities**: Shared utilities in `common` module
- **Base Classes**: Base repositories and services in `common` module

## Module Independence:
- **app, ai, cdc**: Independent Spring Boot applications
- **No Cross-Dependencies**: These modules cannot depend on each other
- **Shared Database**: All modules share the same database schema
- **Message-Based Communication**: Use NATS for inter-module communication

## Architecture Validation:
- **Use Tools**: Run `analyze-module-dependencies.sh -s` to validate dependencies
- **Check Constraints**: Use `analyze-module-dependencies.sh -c` to check for conflicts
- **Verify Structure**: Use `show-project-structure.sh -b -m` to verify organization

# CODE QUALITY AND MODULARITY PRINCIPLES - MANDATORY

## General Code Quality Requirements:
- **MUST** be focused, parameterized, and reusable
- **MUST** have good abstractions and reusability
- **MUST** include clean and concise documentation (one-liner where possible)
- **MUST** follow single responsibility principle
- **MUST** be idempotent and handle existing entities gracefully

## Modularity Requirements:
- **MUST** separate flow from implementation details
- **MUST** use functions with clear parameters and return values
- **MUST** implement existence checks before entity creation
- **MUST** provide appropriate actions for different scenarios
- **MUST** use descriptive function names that explain their purpose

## Error Handling and Robustness:
- **MUST** check for entity existence before attempting creation
- **MUST** handle partial failures gracefully
- **MUST** provide clear error messages and logging
- **MUST** use proper exit codes and error propagation
- **MUST** validate inputs and dependencies

## Documentation Standards:
- **MUST** include one-line descriptions for all functions
- **MUST** document parameters, return values, and side effects
- **MUST** explain the purpose and behavior of each module
- **MUST** provide usage examples for complex functions

## Reusability Patterns:
- **MUST** create utility functions for common operations
- **MUST** parameterize configuration and data
- **MUST** design functions to be composable
- **MUST** avoid hardcoded values and magic numbers
- **MUST** use consistent naming conventions

## Testing and Validation:
- **MUST** include existence checks in all entity operations
- **MUST** validate entity relationships before operations
- **MUST** provide rollback mechanisms where appropriate
- **MUST** log all operations for debugging and auditing

## Examples:
- Scripts should check if organizations exist before creating them
- Functions should accept parameters instead of hardcoded values
- Error handling should provide actionable feedback
- Documentation should explain what each function does in one line

