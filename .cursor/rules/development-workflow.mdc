---
description: "Tool-driven development workflow automation rules for creating robust, deterministic tools"
globs: ["devenv/scripts/*", ".cursor/rules/*", "backend/**/*", "frontend/**/*"]
alwaysApply: true
---

# DEVELOPMENT WORKFLOW AUTOMATION RULES - MANDATORY

## Tool-Driven Development Philosophy:
- **ALWAYS** create small, focused tools for repetitive tasks
- **ALWAYS** make tools deterministic and robust
- **ALWAYS** integrate tools into rules for consistency
- **ALWAYS** validate tool outputs and handle errors gracefully

## Cursor AI Tools Integration (NEW):

### Project Analysis Tools:
- **Tool**: `devenv/cursor/tools/show-project-structure.sh`
- **Purpose**: AI-assisted project structure understanding
- **Integration**: Use before making architectural decisions
- **Usage**: `-b` (backend), `-m` (modules), `-s` (source code)

### Dependency Analysis Tools:
- **Tool**: `devenv/cursor/tools/analyze-module-dependencies.sh`
- **Purpose**: AI-assisted module relationship understanding
- **Integration**: Use before adding dependencies or modules
- **Usage**: `-s` (summary), `-d` (detailed), `-c` (constraints)

### Directory Sync Tools:
- **Tool**: `devenv/cursor/tools/sync-directories.sh`
- **Purpose**: Keep AI context current with project structure
- **Integration**: Run regularly to maintain accurate AI understanding
- **Usage**: `-a` (all), `-s` (structures), `-d` (dependencies)

### AI Workflow Integration:
1. **Before Architectural Decisions**: Run structure analysis
2. **Before Adding Dependencies**: Run dependency analysis
3. **Regular Maintenance**: Sync directories for current context
4. **Problem Investigation**: Use detailed analysis for troubleshooting

## Tool Creation Guidelines:

### 1. **Tool Characteristics (MUST):**
- **Single Responsibility**: Each tool does ONE thing well
- **Deterministic**: Same input always produces same output
- **Robust**: Handles edge cases and errors gracefully
- **Self-Validating**: Tools should validate their own outputs
- **Documented**: Clear usage instructions and examples

### 2. **Tool Integration Pattern:**
```
1. Identify repetitive/error-prone task
2. Create focused tool in devenv/scripts/
3. Add tool to relevant rule file
4. Update validation scripts to use tool
5. Document tool in help system
```

### 3. **Tool Categories:**
- **Validation Tools**: Check code quality, paths, configurations
- **Generation Tools**: Create files, scripts, configurations
- **Management Tools**: Handle state, cleanup, setup
- **Analysis Tools**: Inspect, report, diagnose

## Current Tool Examples:

### Migration Management:
- **Tool**: `find-latest-migration.sh`
- **Rule**: `backend-migration.mdc`
- **Purpose**: Determine next migration version across all modules

### Migration Status:
- **Tool**: `check-migration-status.sh`
- **Rule**: Integrated into `validate-change.sh`
- **Purpose**: Validate migration consistency across all modules

### Service Health:
- **Tool**: `check-service-health.sh`
- **Rule**: Integrated into `validate-change.sh`
- **Purpose**: Comprehensive health check of all development services

### CDC Setup:
- **Tool**: Integrated into `start-dev-deps.sh`
- **Rule**: Development workflow automation
- **Purpose**: Automatic PostgreSQL CDC configuration during infrastructure startup

### Alias Validation:
- **Tool**: `check-alias-paths.sh`
- **Rule**: Integrated into `validate-change.sh`
- **Purpose**: Ensure all project aliases resolve to valid files

### Change Validation:
- **Tool**: `validate-change.sh`
- **Rule**: Development workflow
- **Purpose**: Comprehensive validation of all changes

## Tool Integration Workflow:

### When Creating New Tools:
1. **Identify Need**: Look for repetitive tasks or error-prone operations
2. **Design Tool**: Create focused, robust script
3. **Add to Rules**: Update relevant rule file to reference tool
4. **Integrate Validation**: Add tool to validation workflows
5. **Document**: Add to help system and examples

### When Updating Rules:
1. **Check for Tools**: Always look for tool opportunities
2. **Reference Tools**: Use tools instead of manual steps
3. **Validate Integration**: Ensure tools work with existing workflows
4. **Update Documentation**: Keep help and examples current

## Opportunities for New Tools:

### Database Management:
- **Migration Status Tool**: Check which migrations are applied
- **Schema Validation Tool**: Verify schema matches migrations
- **Data Integrity Tool**: Check for orphaned records, constraints

### Development Environment:
- **Service Health Tool**: Comprehensive health check across all services
- **Port Management Tool**: Check and manage port conflicts
- **Dependency Validation Tool**: Verify all required services are running

### Code Quality:
- **Architecture Validation Tool**: Verify layer compliance
- **API Contract Tool**: Validate DTO structures
- **Test Coverage Tool**: Check test completeness

### Deployment:
- **Environment Setup Tool**: Automated environment configuration
- **Rollback Tool**: Safe rollback procedures
- **Backup Tool**: Automated backup and restore

## Tool Validation Standards:

### Error Handling:
- **Exit Codes**: Use meaningful exit codes (0=success, 1=error)
- **Error Messages**: Clear, actionable error messages
- **Logging**: Appropriate logging levels and output

### Input Validation:
- **Parameter Checking**: Validate all inputs
- **File Existence**: Check required files exist
- **Permission Checking**: Verify required permissions

### Output Validation:
- **Format Checking**: Validate output format
- **Content Validation**: Verify output content
- **Cross-Reference**: Validate against other tools/data

## Rule Integration Pattern:

### For Each Rule File:
1. **Identify Tool Opportunities**: Look for manual steps that could be automated
2. **Reference Existing Tools**: Use tools instead of manual commands
3. **Create New Tools**: When gaps are identified
4. **Update Validation**: Ensure tools are part of validation workflows

### Example Rule Updates:
```markdown
## Before (Manual):
- Check migration versions manually
- Verify paths manually
- Run commands manually

## After (Tool-Driven):
- Use `find-latest-migration.sh` to determine version
- Use `check-alias-paths.sh` to validate paths
- Use `validate-change.sh` for comprehensive validation
```

## Continuous Improvement:

### Regular Reviews:
- **Monthly**: Review existing tools for improvements
- **Quarterly**: Identify new tool opportunities
- **Release**: Validate all tools work with new features

### Tool Metrics:
- **Usage Tracking**: Monitor which tools are used most
- **Error Tracking**: Track tool failures and improve
- **Performance**: Monitor tool execution times

### Documentation:
- **Tool Catalog**: Maintain list of all available tools
- **Usage Examples**: Provide clear examples for each tool
- **Integration Guide**: Show how tools work together

## Success Criteria:
- **Zero Manual Steps**: All repetitive tasks are automated
- **Consistent Results**: Same inputs always produce same outputs
- **Error Prevention**: Tools catch and prevent common errors
- **Developer Productivity**: Faster, more reliable development workflow
