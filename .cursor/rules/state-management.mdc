---
description: "State Management Rules"
globs: ["**/*"]
alwaysApply: true
---

# STATE MANAGEMENT RULES - CRITICAL

## Before Making Changes:
1. ALWAYS check current state: `git status`, `git diff`
2. ALWAYS test current functionality before changes
3. ALWAYS create a backup branch: `git checkout -b backup/$(date +%Y%m%d-%H%M%S)`

## During Changes:
1. NEVER make multiple unrelated changes in one session
2. ALWAYS test each change immediately
3. ALWAYS verify compilation: `./gradlew compileJava` or `npm run build`

## After Changes:
1. ALWAYS test the specific functionality you changed
2. ALWAYS verify no regressions in related areas
3. ALWAYS revert experimental changes that aren't working
4. NEVER leave the codebase in a broken state

## Memory Rules:
1. ALWAYS reference previous conversation context
2. ALWAYS acknowledge when you're making assumptions
3. ALWAYS verify file states before and after changes

## Error Recovery:
1. If you make a mistake, IMMEDIATELY acknowledge it
2. IMMEDIATELY suggest how to fix it
3. NEVER pretend you didn't make the mistake
