
---
description: "AI strategy and implementation rules for Spring AI and framework-specific patterns"
globs: ["backend/ai/**/*.java", "frontend/src/components/ai/**/*.tsx", "frontend/src/services/aiApi.ts"]
alwaysApply: true
---

# AI Strategy Rules

## AI Module Architecture

### Core Principles
- AI services must be independent and modular
- All AI operations must be auditable and traceable
- AI suggestions must be explainable and transparent
- Performance metrics must be collected for all AI operations

### Implementation Guidelines
- Use the AI schema in the CDC database for all AI-related data
- Implement proper error handling for AI service failures
- Ensure AI services can be gracefully degraded
- Maintain separation between AI logic and business logic

### Data Management
- All AI embeddings must be stored in the vector_store table
- AI audit logs must be maintained in ai_audit_log table
- Performance metrics must be tracked in ai_performance_metrics table
- AI configurations must be versioned and tracked

### Integration Patterns
- AI services must communicate via NATS for real-time operations
- REST APIs must be used for synchronous AI operations
- Database operations must use the AI schema for isolation
- Error handling must include fallback mechanisms

### Testing Requirements
- Unit tests for all AI service components
- Integration tests for AI workflow end-to-end
- Performance tests for AI response times
- Security tests for AI data handling

### Monitoring and Observability
- AI service health must be monitored
- AI performance metrics must be collected
- AI error rates must be tracked
- AI usage patterns must be analyzed

## Spring AI Implementation Rules (CRITICAL - NEVER VIOLATE)

### Core Spring AI Principles
- **MUST** use Spring AI's native capabilities instead of custom implementations
- **MUST** leverage Spring AI's mature, tested, and optimized implementations
- **MUST** follow Spring AI's best practices and patterns
- **NEVER** reinvent functionality that Spring AI already provides

### Template System (CRITICAL)
- **MUST** use `PromptTemplate` for all template rendering
- **NEVER** implement custom string replacement for templates
- **MUST** use Spring AI's conditional logic (`{{#if}}`, `{{#each}}`)
- **MUST** use Spring AI's nested object access (`{{user.name}}`)
- **MUST** leverage Spring AI's template validation and error handling
- **ALWAYS** prefer Spring AI's template features over custom implementations

### LLM Interactions (CRITICAL)
- **MUST** use `ChatModel` for all LLM interactions
- **MUST** use Spring AI's native message types:
  - `SystemMessage` for system instructions
  - `UserMessage` for user inputs
  - `AssistantMessage` for AI responses
- **NEVER** make direct HTTP calls to LLM providers
- **MUST** use Spring AI's built-in error handling and retry logic

### Structured Output (CRITICAL)
- **MUST** use Spring AI's structured output capabilities when available
- **MUST** use `@JsonSchema` annotations for response validation
- **NEVER** implement manual JSON parsing when Spring AI provides structured output
- **MUST** leverage Spring AI's automatic schema validation

### Function Calling (CRITICAL)
- **MUST** use Spring AI's function calling when available
- **MUST** use `@Function` annotations for tool definitions
- **MUST** use `@Parameter` annotations for parameter validation
- **NEVER** implement custom function calling when Spring AI provides it

### Vector Store Integration (CRITICAL)
- **MUST** use Spring AI's vector store integration when available
- **MUST** use `PgVectorStore` for PostgreSQL vector operations
- **MUST** leverage Spring AI's embedding generation and search
- **NEVER** implement custom vector operations when Spring AI provides them

### Advanced Capabilities (CRITICAL)
- **MUST** use Spring AI's Chain of Thought capabilities
- **MUST** use Spring AI's ReAct (Reasoning and Acting) framework
- **MUST** use Spring AI's prompt chaining when available
- **ALWAYS** prefer Spring AI's advanced features over custom implementations

### Error Handling and Validation
- **MUST** use Spring AI's built-in error handling mechanisms
- **MUST** leverage Spring AI's template validation
- **MUST** use Spring AI's response validation when available
- **ALWAYS** prefer Spring AI's error messages and debugging

### Performance and Optimization
- **MUST** use Spring AI's caching mechanisms when available
- **MUST** leverage Spring AI's connection pooling
- **MUST** use Spring AI's async capabilities when appropriate
- **ALWAYS** prefer Spring AI's optimized implementations

### Migration Strategy
- **MUST** migrate existing custom implementations to Spring AI equivalents
- **MUST** prioritize Spring AI features over custom code
- **MUST** maintain backward compatibility during migration
- **ALWAYS** test Spring AI implementations thoroughly before deployment

### Spring AI Priority Enforcement (CRITICAL):
- **ALWAYS** start with Spring AI features first - never default to custom implementations
- **MUST** exhaust all Spring AI capabilities before considering custom code
- **NEVER** use existing custom implementations without explicit justification
- **MUST** make explicit decisions to use custom code, never default to it
- **ALWAYS** treat custom implementations as utilities, not primary solutions
- **CRITICAL**: Custom code should be the exception, not the rule - Spring AI features are the standard

## Framework-Specific Implementation Principles (CRITICAL)
- **ALWAYS** start with the native features and idioms of the selected AI framework (e.g., Spring AI, LangChain4j, etc.)
- **NEVER** mix or combine implementations from different frameworks in the same code path
- **MUST** maintain a clean abstraction/interface for each major AI capability (prompt building, LLM interaction, function calling, etc.)
- **MUST** ensure that switching between framework implementations is easy and foolproof
- **MUST** exhaust all native capabilities of the chosen framework before considering custom code
- **MUST** make explicit, documented decisions to use custom code, and only as a utility/exception
- **ALWAYS** respect the idioms, best practices, and strengths of the specific framework in use
- **CRITICAL**: This principle applies to all AI frameworks (Spring AI, LangChain4j, etc.) and is mandatory for all future implementations

- **NEVER** use existing custom implementations without explicit justification
- **MUST** make explicit decisions to use custom code, never default to it
- **ALWAYS** treat custom implementations as utilities, not primary solutions
- **CRITICAL**: Custom code should be the exception, not the rule - Spring AI features are the standard
