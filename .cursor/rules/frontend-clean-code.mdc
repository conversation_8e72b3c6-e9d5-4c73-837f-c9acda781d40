---
description: "Frontend clean code rules for DRY, SOLID principles (as applicable), and comprehensive test coverage"
globs: ["frontend/src/**/*.ts", "frontend/src/**/*.tsx"]
alwaysApply: true
---

# FRONTEND CLEAN CODE RULES - MANDATORY

## DRY (Don't Repeat Yourself) Principles (CRITICAL):
- **MUST** extract common logic into reusable hooks, components, or utility functions
- **MUST** use shared context/providers for cross-cutting concerns
- **MUST** avoid copy-pasting code between components
- **MUST** use composition and higher-order components for reuse
- **NEVER** duplicate logic across files or components

## SOLID Principles (as applicable to TypeScript/React):
- **MUST** keep components and hooks focused on a single responsibility
- **MUST** use composition over inheritance for component reuse
- **MUST** design interfaces and types for extensibility
- **MUST** use dependency injection through props and context
- **NEVER** force components to implement unused props or methods

## Clean Code Standards (MANDATORY):
- **MUST** use descriptive, intention-revealing names for components, functions, and variables
- **MUST** keep components and functions small and focused
- **MUST** use proper TypeScript typing and avoid `any` types
- **MUST** provide meaningful error messages and handle errors gracefully

## Test Coverage Requirements (CRITICAL):
- **MUST** achieve minimum 80% line coverage and 90% branch coverage for critical paths
- **MUST** test all component props, hooks, and business logic paths
- **MUST** follow AAA pattern (Arrange, Act, Assert) in tests
- **MUST** use proper test data setup and cleanup
- **MUST** mock external dependencies and avoid over-mocking

## Code Quality Tools (MANDATORY):
- **MUST** use ESLint, Prettier, and TypeScript strict mode
- **MUST** fix all linting errors and warnings
- **MUST** review all code changes for SOLID, DRY, and test coverage

## Refactoring Guidelines:
- **MUST** refactor when code duplication or SOLID violations are detected
- **MUST** have comprehensive tests before refactoring
- **MUST** refactor in small, incremental steps and run all tests after each step

## Documentation Standards:
- **MUST** document complex business logic and component APIs
- **MUST** use clear, concise comments and keep documentation up to date
- **MUST** document component props with JSDoc or TypeScript interfaces

## Performance Considerations:
- **MUST** use React.memo, useMemo, and useCallback appropriately
- **MUST** avoid unnecessary re-renders and optimize bundle size
- **MUST** profile components for performance bottlenecks

## Avoid Hard-Coded String Literals (MANDATORY):
- **MUST** use enums, union types, or defined constants for all repeated or significant string values (e.g., status, types, keys, event names)
- **NEVER** use magic strings directly in business logic, components, or services
- **MUST** centralize constants in a dedicated module or file

## Object-Oriented Abstractions (as applicable to TypeScript/React):
- **MUST** use proper OOP principles where appropriate: encapsulation, composition, and interface-based design
- **MUST** design extensible and maintainable abstractions for shared logic
- **MUST** use interfaces and types for shared contracts and props
