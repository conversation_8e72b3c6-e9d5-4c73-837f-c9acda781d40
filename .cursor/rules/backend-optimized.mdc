---
description: "Consolidated backend development rules covering architecture, organization, clean code, Lombok usage, migrations, and NATS configuration"
globs: ["backend/**/*.java", "backend/**/*.kt", "backend/**/*.sql"]
alwaysApply: true
---

# <PERSON><PERSON><PERSON><PERSON> ARCHITECTURE RULES - MANDATORY

## Architecture Layer Mapping (MUST FOLLOW):
Controller -> Service + Mapper -> Repository -> DB

## Controller Layer (MUST):
- MUST use Spring RestController
- MUST handle HTTP requests only
- MUST delegate business logic to Service layer
- MUST return DTOs, never domain objects
- MUST have proper exception handling
- MUST have unit tests and integration tests
- Example: [TicketController.java](mdc:backend/app/src/main/java/io/sx/ticketing/controller/TicketController.java)

## Service Layer (MUST):
- MUST have an interface for every service
- MUST inherit from BaseService interface
- MUST have implementation class in `impl` package
- MUST accept and return DTOs only
- MUST use mappers to convert between DTOs and domain objects
- MUST handle business logic and validation
- MUST have unit tests
- Example: [OrganizationService.java](mdc:backend/app/src/main/java/io/sx/directory/service/OrganizationService.java)

## Mapper Layer (MUST):
- MUST map between domain objects and DTOs
- MUST have separate methods: `toDTO()` and `toEntity()`
- MUST be used by services for all conversions
- MUST have unit tests for both mapping directions
- Example: [OrganizationMapper.java](mdc:backend/app/src/main/java/io/sx/directory/mapper/OrganizationMapper.java)

## Repository Layer (CRITICAL - NEVER VIOLATE):
- MUST use Spring JDBC Templates
- NEVER use JPA or Hibernate
- MUST inherit from BaseRepository interface
- MUST have implementation class in `impl` package
- MUST accept and return domain objects only
- MUST handle database operations only
- MUST have unit tests
- Example: [OrganizationRepository.java](mdc:backend/app/src/main/java/io/sx/directory/repository/OrganizationRepository.java)

## Database Layer:
- MUST use Flyway for migrations
- MUST follow naming convention: V<version>__description.sql
- MUST include audit fields in all tables
- MUST use PostgreSQL

## Testing Requirements (MUST):
- Unit tests for all layers
- Integration tests for controllers
- Repository tests with test database
- Mapper tests for both directions

# BACKEND ORGANIZATION RULES - MANDATORY

## Module Structure:
- **app**: Main Spring Boot application (user-facing APIs)
- **ai**: AI/ML services (separate application)
- **cdc**: Change Data Capture services (separate application)
- **common**: Shared DTOs, models, and utilities
- **cqrs**: Command Query Responsibility Segregation
- **source**: Data source integration services

## Module Dependency Rules:
- **common**: Can be depended on by ALL modules (shared code)
- **app**: Can depend on common, but NOT on ai, cdc, cqrs
- **ai**: Can depend on common, but NOT on app, cdc, cqrs
- **cdc**: Can depend on common, but NOT on app, ai, cqrs
- **cqrs**: Can depend on common, but NOT on app, ai, cdc
- **source**: Can depend on common, but NOT on app, ai, cdc, cqrs

## Shared Code Organization:
- **DTOs**: All DTOs in `common` module
- **Models**: All domain models in `common` module
- **Utilities**: Shared utilities in `common` module
- **Base Classes**: Base repositories and base services in `common` module

## Module Independence:
- **app, ai, cdc**: Independent Spring Boot applications
- **No Cross-Dependencies**: These modules cannot depend on each other
- **Shared Database**: All modules share the same database schema
- **Message-Based Communication**: Use NATS for inter-module communication

# BACKEND CLEAN CODE RULES - MANDATORY

## DRY (Don't Repeat Yourself) Principles (CRITICAL):
- **MUST** extract common functionality into reusable methods/classes
- **MUST** use base classes and interfaces for shared behavior
- **MUST** create utility classes for common operations
- **MUST** use composition over inheritance for code reuse
- **NEVER** copy-paste code blocks - always refactor to shared methods

## SOLID Principles (MANDATORY):
- **MUST** follow Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, and Dependency Inversion principles
- **MUST** keep classes and methods focused and cohesive
- **MUST** use interfaces and abstractions for extensibility
- **MUST** use dependency injection
- **NEVER** violate base class contracts or force classes to implement unused methods

## Clean Code Standards (MANDATORY):
- **MUST** use descriptive, intention-revealing names
- **MUST** keep methods small and focused
- **MUST** use proper encapsulation and minimize public methods
- **MUST** provide meaningful error messages and handle exceptions at appropriate levels

## Test Coverage Requirements (CRITICAL):
- **MUST** achieve minimum 80% line coverage and 90% branch coverage for critical paths
- **MUST** test all public methods, edge cases, and business logic paths
- **MUST** follow AAA pattern (Arrange, Act, Assert) in tests
- **MUST** use proper test data setup and cleanup
- **MUST** mock external dependencies and avoid over-mocking

## Avoid Hard-Coded String Literals and Magic Numbers (MANDATORY):
- **MUST** use enums or defined constants for all repeated or significant string values
- **NEVER** use magic strings directly in business logic, controllers, or services
- **MUST** centralize constants in a dedicated class or interface
- **MUST** avoid magic numbers in business logic - use named constants instead
- **NEVER** use hardcoded IDs or numeric values without clear context

# LOMBOK USAGE RULES - MANDATORY

## Base Class Lombok Pattern (CRITICAL):
- **MUST** use `@SuperBuilder` for inheritance hierarchies
- **MUST** use `@Data` for getters, setters, equals, hashCode, toString
- **MUST** use `@Accessors(chain = true)` for fluent setters
- **MUST** use `@NoArgsConstructor` for Spring compatibility
- **NEVER** use `@Builder` on abstract classes or inheritance hierarchies

## Abstract Class Lombok Rules (CRITICAL):
- **MUST** use `@SuperBuilder` instead of `@Builder` for abstract classes
- **MUST** make fields `protected` (not `private`) for subclasses to access
- **NEVER** use `@Builder` on abstract classes with inheritance
- **NEVER** make fields `private` in abstract classes that subclasses need to access

## Inheritance Hierarchy Pattern:
```java
// Base abstract class
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
public abstract class BaseDataObject {
    protected Long id;
    protected Long organizationId;
    // ... other protected fields
}

// Concrete subclass
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
public class ConcreteClass extends BaseDataObject {
    private String specificField;
    // ... specific fields
}
```

# MIGRATION RULES - MANDATORY

## Migration Version Management:
- **ALWAYS** use the `find-latest-migration.sh` tool to determine the next version number
- **Command:** `./devenv/scripts/find-latest-migration.sh`
- **Rule:** Use version number that is one more than the highest migration version across all modules (app, ai, cdc)

## Migration File Requirements:
All tables will need audit fields:
- created_at / updated_at 
- created_by_id, updated_by_id 
- created_by (string)
- all tables will need on_behalf_of_id

## Migration Restrictions:
- **NEVER** use `ALTER SYSTEM` commands in Flyway migrations
- **NEVER** use commands that require superuser privileges
- **NEVER** modify PostgreSQL configuration files via migrations
- **ONLY** use DDL/DML commands that modify database schema/data
- **ONLY** use commands that work with application database user privileges

## Migration Best Practices:
- Use descriptive names that explain what the migration does
- Include comments explaining complex migrations
- Test migrations on development database before committing
- Ensure migrations are idempotent and can be rolled back

# NATS CONFIGURATION RULES (CRITICAL - NEVER VIOLATE):
- **NEVER** use lowercase enum values for NATS JetStream policies
- **MUST** use proper case for NATS enum values:
  - `ack-policy=Explicit` (NOT `explicit`)
  - `deliver-policy=All` (NOT `all`)
  - `replay-policy=Instant` (NOT `instant`)
- **ALWAYS** check CDC module configuration as reference for correct enum values
- **NEVER** assume enum values are lowercase
- **ALWAYS** verify enum values match NATS client API expectations

## NATS Enum Parsing Rule (CRITICAL - NEVER VIOLATE):
- When parsing NATS JetStream enum values (AckPolicy, DeliverPolicy, ReplayPolicy), NEVER transform the property value (e.g., do NOT use toUpperCase()).
- Always use the property value as-is (e.g., AckPolicy.valueOf(ackPolicy)).
- The correct values are: `Explicit`, `All`, `Instant` (NOT all-caps or lowercase).
- This is a repeated source of production failures.

