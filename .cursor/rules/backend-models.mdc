---
description: "Backend model and DTO creation standards and requirements"
globs: ["backend/common/src/main/java/io/sx/model/**/*", "backend/app/src/main/java/io/sx/**/dto/**/*"]
alwaysApply: true
---
Check for existing models always before generationg

Models are always created in the the common module
as a sibling to [User.java](mdc:backend/common/src/main/java/io/sx/model/User.java)

All models and dtops must have created at /updated at created by id, updated by id and created by ( string )
- all tables will need on behalf of id.

Add them to [BaseDataObject.java](mdc:backend/common/src/main/java/io/sx/model/BaseDataObject.java)

