---
description: "Validation Checklist"
globs: ["**/*"]
alwaysApply: true
---
# VALIDATION CHECKLIST - MANDATORY

## Before Submitting Any Code:
- [ ] Does it compile without errors?
- [ ] Does it follow the architecture patterns?
- [ ] Are you using JDBC (not JPA)?
- [ ] Have you tested the specific functionality?
- [ ] Are there any unintended side effects?
- [ ] Can you revert this change if needed?

## Memory Check:
- [ ] Have you referenced previous context?
- [ ] Are you making assumptions? (If yes, state them)
- [ ] Have you verified the current state?

## Architecture Compliance:
- [ ] Controller -> Service + Mapper -> Repository -> DB pattern followed?
- [ ] DTOs used for service layer communication?
- [ ] Domain objects used in repository layer?
- [ ] Proper exception handling implemented?
- [ ] Audit fields included in new tables?

## AI Context Validation (NEW):
- [ ] Has project structure been analyzed with `show-project-structure.sh`?
- [ ] Have module dependencies been validated with `analyze-module-dependencies.sh`?
- [ ] Is AI context synchronized with current project state?
- [ ] Are architectural decisions based on current project structure?
- [ ] Have dependency relationships been validated before changes?

## Tool-Driven Validation (NEW):
- [ ] Use `devenv/cursor/tools/show-project-structure.sh -b -m` for backend structure validation
- [ ] Use `devenv/cursor/tools/analyze-module-dependencies.sh -s` for dependency validation
- [ ] Use `devenv/cursor/tools/sync-directories.sh -a` for context synchronization
- [ ] Validate tool outputs before proceeding with changes
- [ ] Ensure AI recommendations align with current project organization
