
# Rule File Standards

## Rule File Creation Requirements

### Frontmatter Requirements (CRITICAL)
- **MUST** have proper YAML frontmatter at the top of the file
- **MUST** include `description` field with non-empty string
- **MUST** include `globs` field with array of file patterns
- **MUST** include `alwaysApply` field (true/false)
- **NEVER** leave frontmatter fields empty

### Frontmatter Format
```yaml
---
description: "Clear description of what this rule covers"
globs: ["path/to/files/**/*", "another/path/**/*"]
alwaysApply: true
---
```

### Content Requirements
- **MUST** have a clear title after frontmatter
- **MUST** use proper markdown formatting
- **MUST** include specific, actionable rules
- **MUST** provide examples where appropriate
- **MUST** reference relevant files and tools

- **MUST** use proper markdown formatting
- **MUST** include specific, actionable rules
- **MUST** provide examples where appropriate
- **MUST** reference relevant files and tools
