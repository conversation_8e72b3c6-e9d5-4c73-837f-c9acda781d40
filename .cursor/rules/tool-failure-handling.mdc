---
description: "Handles tool failures and file operations efficiently by asking users for help when operations fail or take too long"
globs: ["**/*"]
alwaysApply: true
---

# TOOL FAILURE AND FILE OPERATION HANDLING - CRITICAL

## When Tool Operations Fail or Take Too Long:

### Immediate Response Required:
1. **Acknowledge the failure immediately** - Don't let users wait
2. **Explain what failed** - Be transparent about what went wrong
3. **Provide alternative solution** - Give user commands to run manually

### File Operations That May Fail:
- Creating new files with \`edit_file\`
- Deleting files with \`delete_file\`
- Complex search and replace operations
- Large file modifications

### Standard Response Pattern:
"I'm having trouble with [specific operation]. Please run this command for me:

[EXACT COMMAND TO RUN]

Once you've done that, I can continue with [next step]."

### Examples:

**File Creation Failure:**
"I'm having trouble creating the new script file. Please run this command for me:

\`\`\`bash
touch devenv/scripts/backend/new-script.sh
chmod +x devenv/scripts/backend/new-script.sh
\`\`\`

Once you've done that, I can continue with adding the content."

**File Deletion Failure:**
"I'm having trouble removing the temporary file. Please run this command for me:

\`\`\`bash
rm -f temp-file.txt
\`\`\`

Once you've done that, I can continue with the next step."

### Time Limits:
- If any tool operation takes longer than 30 seconds, acknowledge it
- If multiple tool calls are needed for a simple task, ask user to help
- Don't make users wait for unnecessary operations

### User Collaboration:
- Users are happy to help with file operations
- Provide exact commands they can copy/paste
- Be specific about what you need them to do
- Thank them for the help

### Priority:
- Complete the user's main request first
- Ask before adding extra features
- Don't over-engineer solutions
- Focus on what the user actually needs

## Memory Rule:
ALWAYS reference this rule when tool operations fail or take too long. Be transparent and ask for user help rather than making them wait.


