{"keyboard.dispatch": "keyCode", "keyboard.map": {"cmd+shift+o": "workbench.action.quickOpen", "cmd+p": "workbench.action.quickOpen", "cmd+shift+p": "workbench.action.showCommands", "cmd+shift+f": "workbench.action.findInFiles", "cmd+shift+r": "workbench.action.replaceInFiles", "cmd+shift+n": "workbench.action.quickOpen", "cmd+shift+e": "workbench.view.explorer", "cmd+shift+g": "workbench.view.scm", "cmd+shift+d": "workbench.view.debug", "cmd+shift+x": "workbench.extensions.action.showInstalledExtensions", "cmd+shift+h": "workbench.action.replaceInFiles", "cmd+shift+j": "workbench.action.togglePanel", "cmd+shift+k": "workbench.action.toggleMaximizedPanel", "cmd+shift+l": "workbench.action.toggleSidebarVisibility", "cmd+shift+m": "workbench.action.toggleMinimap", "cmd+shift+u": "workbench.action.togglePanel", "cmd+shift+v": "workbench.action.showAllEditors", "cmd+shift+w": "workbench.action.closeAllEditors", "cmd+shift+z": "workbench.action.toggleZenMode", "cmd+shift+c": "workbench.action.terminal.openNativeConsole", "cmd+shift+b": "workbench.action.tasks.build", "cmd+shift+i": "workbench.action.toggleDevTools", "cmd+shift+[": "workbench.action.previousEditor", "cmd+shift+]": "workbench.action.nextEditor", "cmd+shift+\\": "workbench.action.splitEditor", "cmd+shift+;": "workbench.action.terminal.toggleTerminal", "cmd+shift+'": "workbench.action.terminal.toggleTerminal", "cmd+shift+,": "workbench.action.openSettings", "cmd+shift+.": "workbench.action.openSettings", "cmd+shift+/": "workbench.action.showAllEditors", "cmd+shift+1": "workbench.action.openEditorAtIndex1", "cmd+shift+2": "workbench.action.openEditorAtIndex2", "cmd+shift+3": "workbench.action.openEditorAtIndex3", "cmd+shift+4": "workbench.action.openEditorAtIndex4", "cmd+shift+5": "workbench.action.openEditorAtIndex5", "cmd+shift+6": "workbench.action.openEditorAtIndex6", "cmd+shift+7": "workbench.action.openEditorAtIndex7", "cmd+shift+8": "workbench.action.openEditorAtIndex8", "cmd+shift+9": "workbench.action.openEditorAtIndex9", "cmd+shift+0": "workbench.action.openLastEditorInGroup", "cmd+shift+-": "workbench.action.navigateBack", "cmd+shift+=": "workbench.action.navigateForward", "cmd+shift+space": "editor.action.triggerSuggest", "cmd+shift+enter": "editor.action.insertLineAfter", "cmd+shift+backspace": "editor.action.deleteLines", "cmd+shift+up": "editor.action.moveLinesUpAction", "cmd+shift+down": "editor.action.moveLinesDownAction", "cmd+shift+left": "editor.action.smartSelect.expand", "cmd+shift+right": "editor.action.smartSelect.shrink", "cmd+shift+home": "editor.action.moveToFirstCharacterOfLine", "cmd+shift+end": "editor.action.moveToEndOfLine", "cmd+shift+pageup": "editor.action.moveToFirstCharacterOfLine", "cmd+shift+pagedown": "editor.action.moveToEndOfLine", "cmd+shift+delete": "editor.action.deleteLines", "cmd+shift+insert": "editor.action.clipboardPasteAction", "cmd+shift+tab": "workbench.action.previousEditorInGroup", "cmd+shift+escape": "workbench.action.toggleMaximizedPanel", "cmd+shift+f1": "workbench.action.showAllEditors", "cmd+shift+f2": "workbench.action.showAllEditors", "cmd+shift+f3": "workbench.action.showAllEditors", "cmd+shift+f4": "workbench.action.showAllEditors", "cmd+shift+f5": "workbench.action.debug.start", "cmd+shift+f6": "workbench.action.debug.restart", "cmd+shift+f7": "workbench.action.debug.stepInto", "cmd+shift+f8": "workbench.action.debug.stepOver", "cmd+shift+f9": "workbench.action.debug.stepOut", "cmd+shift+f10": "workbench.action.debug.stop", "cmd+shift+f11": "workbench.action.debug.toggleBreakpoint", "cmd+shift+f12": "workbench.action.toggleMaximizedPanel"}}