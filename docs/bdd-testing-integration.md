# BDD Testing Integration with SX Development Workflow

This document explains how the custom fields BDD tests integrate with your existing development workflow and CI/CD processes.

## Overview

The BDD (Behavior-Driven Development) tests for custom fields run in containers alongside your application services, following the same pattern as your existing e2e tests. This ensures consistency and reliability across all testing approaches.

## Integration Points

### 1. Similar to E2E Tests Pattern

The BDD tests follow the same architectural pattern as your existing e2e tests:

```
E2E Tests (Java + Gradle)          BDD Tests (Node.js + Cucumber)
├── backend/e2e-tests/             ├── bdd-tests/
│   ├── docker-compose-e2e.yml     │   ├── docker-compose-bdd.yml
│   ├── run-e2e-tests.sh           │   ├── run-bdd-tests.sh
│   └── src/test/java/...          │   ├── features/ (from ../features/)
│                                  │   ├── step_definitions/
│                                  │   └── support/
```

### 2. Shared Infrastructure

Both test suites share similar infrastructure components:
- PostgreSQL database (different ports/containers)
- NATS messaging system
- All three application services (App, AI, CDC)
- Docker Compose orchestration
- Health check patterns

### 3. Port Strategy

```
Development:     E2E Tests:      BDD Tests:
App:    8080     App:    4091    App:    4091
AI:     8081     AI:     4082    AI:     4082  
CDC:    8082     CDC:    4083    CDC:    4083
DB:     5432     DB:     4432    DB:     5432
NATS:   4222     NATS:   4422    NATS:   4222
```

## Usage in Development Workflow

### 1. Local Development

```bash
# Run specific custom field tests during development
cd bdd-tests
./run-bdd-tests.sh management

# Keep services running for debugging
./run-bdd-tests.sh --keep-running core

# Quick iteration without rebuilding
./run-bdd-tests.sh --no-build usage
```

### 2. Feature Development Cycle

```bash
# 1. Implement custom field feature
# 2. Write/update BDD scenarios in features/
# 3. Test the specific scenarios
./run-bdd-tests.sh --keep-running management

# 4. Debug and iterate
docker-compose -f docker-compose-bdd.yml logs app

# 5. Run full test suite before commit
./run-bdd-tests.sh
```

### 3. Pre-commit Validation

```bash
# Run different test levels based on changes
./run-bdd-tests.sh core      # Quick smoke test
./run-bdd-tests.sh advanced  # More comprehensive
./run-bdd-tests.sh          # Full test suite
```

## CI/CD Integration

### 1. GitHub Actions Workflow

```yaml
name: BDD Tests

on:
  pull_request:
    paths:
      - 'features/**'
      - 'backend/**'
      - 'bdd-tests/**'

jobs:
  bdd-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        test-suite: [core, advanced, enterprise]
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Docker
        uses: docker/setup-buildx-action@v2
      
      - name: Run BDD Tests
        run: |
          cd bdd-tests
          ./run-bdd-tests.sh ${{ matrix.test-suite }}
      
      - name: Upload Test Reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: bdd-reports-${{ matrix.test-suite }}
          path: bdd-tests/reports/
```

### 2. Integration with Existing E2E Pipeline

```yaml
# Add to existing workflow
- name: Run E2E Tests
  run: |
    cd backend/e2e-tests
    ./run-e2e-tests.sh

- name: Run BDD Tests (Core)
  run: |
    cd bdd-tests
    ./run-bdd-tests.sh core

- name: Run BDD Tests (Full) 
  if: github.ref == 'refs/heads/main'
  run: |
    cd bdd-tests
    ./run-bdd-tests.sh
```

## Test Strategy Recommendations

### 1. Test Pyramid Approach

```
                🔺 BDD Tests (Custom Fields)
               🔺🔺 E2E Tests (Full Flow)  
              🔺🔺🔺 Integration Tests
             🔺🔺🔺🔺 Unit Tests
```

**BDD Tests Focus:**
- Custom field business scenarios
- User acceptance criteria validation
- Cross-component integration (UI + API)

**E2E Tests Focus:**
- Complete system workflows
- AI processing and CDC flows
- Service-to-service communication

### 2. When to Run Which Tests

| Scenario | Unit | Integration | E2E | BDD |
|----------|------|-------------|-----|-----|
| Code changes in custom field logic | ✅ | ✅ | - | ✅ Core |
| API changes affecting custom fields | ✅ | ✅ | ✅ | ✅ Advanced |
| Frontend changes for custom fields | ✅ | - | - | ✅ Core |
| Database schema changes | ✅ | ✅ | ✅ | ✅ Full |
| Major releases | ✅ | ✅ | ✅ | ✅ Full |

### 3. Parallel Execution Strategy

```bash
# Run tests in parallel for faster CI
parallel ::: \
  "cd backend && ./gradlew test" \
  "cd backend/e2e-tests && ./run-e2e-tests.sh" \
  "cd bdd-tests && ./run-bdd-tests.sh core"
```

## Integration with Backend Modules

### 1. Custom Field Implementation Coverage

The BDD tests validate across all backend modules:

```
📁 backend/app/        → API endpoints and controllers
📁 backend/common/     → Custom field models and DTOs  
📁 backend/ai/         → AI integration with custom fields
📁 backend/cdc/        → Change data capture for fields
```

### 2. Cross-Module Scenarios

BDD tests cover scenarios that span multiple modules:
- **Management**: App module (field definitions)
- **Usage**: App module (field values on tickets)
- **Search**: App module (filtering and querying)
- **Integration**: All modules (data flow and consistency)

## Monitoring and Reporting

### 1. Test Results Dashboard

```bash
# Generate comprehensive reports
./run-bdd-tests.sh --generate-reports

# View HTML reports
open bdd-tests/reports/cucumber-report.html
```

### 2. Metrics Collection

Track key metrics:
- Test execution time per category
- Success/failure rates by feature
- Coverage of custom field scenarios
- Performance benchmarks

### 3. Alert Integration

```yaml
# Slack notification example
- name: Notify on BDD Test Failure
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: failure
    text: "Custom Fields BDD Tests Failed! 🚨"
    webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

## Best Practices

### 1. Test Data Management

- Use isolated test database per run
- Generate unique test data to avoid conflicts
- Clean up test data after each scenario
- Use realistic but not production data

### 2. Scenario Organization

- Group related scenarios in same feature file
- Use meaningful tags for test categorization
- Keep scenarios focused and atomic
- Include both positive and negative test cases

### 3. Maintenance

- Update BDD scenarios when requirements change
- Keep step definitions DRY and reusable
- Regular review and refactoring of test code
- Monitor test execution times and optimize

### 4. Documentation

- Document new step definitions
- Maintain feature file readability
- Include business context in scenario descriptions
- Keep README up to date

## Troubleshooting Integration Issues

### 1. Port Conflicts

```bash
# Check for conflicts with development services
lsof -i :4091 -i :4082 -i :4083 -i :5432

# Stop conflicting services
cd devenv/scripts && ./stop-dev-deps.sh
```

### 2. Database State Issues

```bash
# Reset test database
./run-bdd-tests.sh --cleanup

# Check database connections
docker exec -it sx_bdd_postgres_db psql -U sx_user -l
```

### 3. Service Synchronization

```bash
# Check service health
docker-compose -f docker-compose-bdd.yml ps

# View detailed logs
docker-compose -f docker-compose-bdd.yml logs --follow app
```

## Future Enhancements

### 1. Visual Testing Integration

```bash
# Add visual regression testing
npm install @percy/cucumber

# Include in BDD scenarios
./run-bdd-tests.sh --include-visual
```

### 2. Performance Testing

```bash
# Add performance assertions
./run-bdd-tests.sh --with-performance-metrics
```

### 3. Multi-browser Testing

```bash
# Test across different environments
./run-bdd-tests.sh --browsers chrome,firefox,safari
```

This integration ensures that your custom fields BDD tests work seamlessly with your existing development and testing infrastructure while providing comprehensive coverage of the custom fields functionality. 